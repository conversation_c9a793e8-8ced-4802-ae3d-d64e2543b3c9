from exts import Base
import uuid
from sqlalchemy import Column, String, Integer, DateTime, func
from sqlalchemy.dialects.postgresql import JSONB, UUID
from datetime import timezone


class StorageIdentity(Base):
    __tablename__ = 'storage_identity'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sid = Column(String, nullable=False)
    name = Column(String)
    email = Column(String)
    type = Column(Integer,nullable=False)
    identifier = Column(String,nullable=True)
    info = Column(JSONB,nullable=True)
    ext_info = Column(JSONB,nullable=True)
    version = Column(Integer)
    identity_type = Column(Integer)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())


    def __repr__(self):
        return (f"<StorageIdentity(id={self.id}, sid={self.sid}, "
                f"name={self.name}, email={self.email}, type={self.type},"
                f"identifier={self.identifier}, info={self.info}, ext_info = {self.ext_info}, version = {self.version}, "
                f"identity_type={self.identity_type},"
                f"created_at={self.created_at}, updated_at={self.updated_at})>")

    def to_dict(self):
        return {
            'id': str(self.id),
            'sid': str(self.sid),
            'name': self.name,
            'email': self.email,
            'type': self.type,
            'identifier': self.identifier,
            'info': self.info,
            'ext_info': self.ext_info,
            'version': self.version,
            'identity_type': self.identity_type,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at else None,
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at else None
        }


