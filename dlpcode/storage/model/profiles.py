import uuid
from exts import Base
from sqlalchemy import Column, String, Integer, DateTime
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB, UUID
from datetime import timezone

class StorageProfile(Base):
    __tablename__ = "storage_profiles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String,nullable=False)
    type = Column(Integer,nullable=False)
    identifier = Column(String,nullable=True)
    auth_info = Column(JSONB,nullable=False)
    notes = Column(String)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return (f"<MyModel(id={self.id}, name='{self.name}', "
                f"type={self.type}, identifier='{self.identifier}', auth_info={self.auth_info}, "
                f"notes={self.notes}, created_at={self.created_at}, updated_at={self.updated_at})>")

    def to_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'type':self.type,
            'identifier': self.identifier,
            'auth_info':self.auth_info,
            'notes': self.notes,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at else None,
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at else None
        }

    def Get_id(self):
        return self.id