import uuid
import traceback
from exts import Session, get_logger, Base
from sqlalchemy import Column, String, SmallInteger, DateTime, Boolean, and_, select, func, case, or_
from sqlalchemy.dialects.postgresql import UUID, JSONB, insert
from sqlalchemy.sql import func, exists
from datetime import datetime, timezone, timedelta
from psycopg2.errors import UniqueViolation
from sqlalchemy.orm.attributes import flag_modified
from typing import List
from sqlalchemy import asc, desc

logger = get_logger("fetch_storage_log")


class StorageActivity(Base):
    __tablename__ = "storage_activity"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    storage_id = Column(UUID(as_uuid=True), nullable=True)
    storage_type = Column(SmallInteger, nullable=False)
    event_time = Column(DateTime(timezone=True), nullable=False)
    event_id = Column(String, nullable=True)
    event_type = Column(String, nullable=True)
    scan_policy_fields = Column(JSONB)
    ddr_fields = Column(JSONB)
    raw_data = Column(JSONB, nullable=False)
    reserve_json = Column(JSONB)
    status = Column(SmallInteger, nullable=False, default=0)  # 0: new, 1: pending, 2: processing, 3: completed, 4: failed, 5: skipped
    scan_triggered = Column(Boolean, default=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def Get_id(self):
        return self.id

    def to_dict(self):
        return {
            'id': str(self.id),
            'storage_id': str(self.storage_id) if self.storage_id else None,
            'storage_type': self.storage_type,
            'event_time': self.event_time.isoformat() if self.event_time else None,
            'event_id': self.event_id,
            'event_type': self.event_type,
            'scan_policy_fields': self.scan_policy_fields,
            'ddr_fields': self.ddr_fields,
            'raw_data': self.raw_data,
            'reserve_json': self.reserve_json,
            'status': self.status,
            'scan_triggered': self.scan_triggered,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at.tzinfo is None else self.created_at.timestamp(),
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at.tzinfo is None else self.updated_at.timestamp(),
        }

def get_fetched_activity(id: str, logger=logger) -> StorageActivity:
    try:
        with Session() as session:
            return session.query(StorageActivity).filter(StorageActivity.id == id).first()
    except Exception as e:
        logger.error(e)
        return None

def get_fetched_activities(**kwargs) -> list:
    try:
        filters = []
        sort_method = "asc"
        sort_field = "created_at"
        for key, value in kwargs.items():
            if key.endswith('__gt'):
                field_name = key[:-4]
                filters.append(getattr(StorageActivity, field_name) > value)
            elif key.endswith('__gte'):
                field_name = key[:-5]
                filters.append(getattr(StorageActivity, field_name) >= value)
            elif key.endswith('__lt'):
                field_name = key[:-4]
                filters.append(getattr(StorageActivity, field_name) < value)
            elif key.endswith('__lte'):
                field_name = key[:-5]
                filters.append(getattr(StorageActivity, field_name) <= value)
            elif key == 'doc_id':
                filters.append(
                    func.jsonb_path_exists(
                        StorageActivity.scan_policy_fields.cast(JSONB),
                        f'$.doc_id ? (@ == "{value}")'
                    )
                )
            elif key == 'full_path':
                filters.append(
                    func.jsonb_path_exists(
                        StorageActivity.scan_policy_fields.cast(JSONB),
                        f'$.full_path ? (@ == "{value}")'
                    )
                )
            elif key == 'src_display_path':
                filters.append(
                    func.jsonb_path_exists(
                        StorageActivity.scan_policy_fields.cast(JSONB),
                        f'$.src_display_path ? (@ == "{value}")'
                    )
                )
            elif key == 'dst_display_path':
                filters.append(
                    func.jsonb_path_exists(
                        StorageActivity.scan_policy_fields.cast(JSONB),
                        f'$.dst_display_path ? (@ == "{value}")'
                    )
                )
            elif key == 'sort_method':
                sort_method = value
            elif key == 'sort_field':
                sort_field = value
            elif "__jsonb__" in key:
                col, json_key = key.split("__jsonb__")
                if value:
                    filters.append(
                        or_(
                            ~getattr(StorageActivity, col).has_key(json_key),  # key not exists → condition is True
                            getattr(StorageActivity, col)[json_key].astext.in_(value)
                        )
                    )
            else:
                if isinstance(value, (list, tuple, set)):
                    filters.append(getattr(StorageActivity, key).in_(value))
                else:
                    filters.append(getattr(StorageActivity, key) == value)

        with Session() as session:
            if sort_method == "asc":
                records = session.query(StorageActivity).filter(and_(*filters)).order_by(asc(sort_field)).all()
            else:
                records = session.query(StorageActivity).filter(and_(*filters)).order_by(desc(sort_field)).all()
            return records
    except Exception as e:
        logger.error(e)
        return []

def get_and_lock_activities_atomic(limit: int = 1000, offset: int = 0, **kwargs) -> list:
    """
    Atomically get activities and mark them as PROCESSING to avoid concurrent processing
    This prevents multiple processes from processing the same activities
    """
    from storage.util.enum import ActivityStatus
    try:
        filters = []
        for key, value in kwargs.items():
            if key.endswith('__gt'):
                field_name = key[:-4]
                filters.append(getattr(StorageActivity, field_name) > value)
            elif key.endswith('__gte'):
                field_name = key[:-5]
                filters.append(getattr(StorageActivity, field_name) >= value)
            elif key.endswith('__lt'):
                field_name = key[:-4]
                filters.append(getattr(StorageActivity, field_name) < value)
            elif key.endswith('__lte'):
                field_name = key[:-5]
                filters.append(getattr(StorageActivity, field_name) <= value)
            elif "__jsonb__" in key:
                col, json_key = key.split("__jsonb__")
                if value:
                    if json_key.endswith('__not_in'):
                        actual_key = json_key[:-8]
                        filters.append(
                            or_(
                                ~getattr(StorageActivity, col).has_key(actual_key),  # key not exists → condition is True
                                ~getattr(StorageActivity, col)[actual_key].astext.in_(value)
                            )
                        )
                    else:
                        filters.append(
                            or_(
                                ~getattr(StorageActivity, col).has_key(json_key),
                                getattr(StorageActivity, col)[json_key].astext.in_(value)
                            )
                        )
            else:
                filters.append(getattr(StorageActivity, key) == value)

        with Session() as session:
            # Use SELECT FOR UPDATE to lock the rows and prevent concurrent access
            records = (session.query(StorageActivity)
                      .filter(and_(*filters))
                      .order_by(StorageActivity.created_at.desc(), StorageActivity.id.desc())
                      .limit(limit)
                      .offset(offset)
                      .with_for_update(skip_locked=True)  # Skip already locked rows
                      .all())

            result_dicts = []
            # Atomically update status to PROCESSING to mark as being processed
            if records:
                activity_ids = [record.id for record in records]
                session.query(StorageActivity).filter(
                    StorageActivity.id.in_(activity_ids)
                ).update(
                    {"status": ActivityStatus.PROCESSING},
                    synchronize_session=False
                )
                session.commit()

                # Update the records' status in memory to reflect the change
                for record in records:
                    record.status = ActivityStatus.PROCESSING
                    result_dicts.append(record.to_dict())

            return result_dicts

    except Exception as e:
        logger.error(f"Error in atomic activity query: {e}")
        return []

def has_activity(**kwargs) -> bool:
    try:
        filters = []
        for key, value in kwargs.items():
            if key.endswith('__gt'):
                field_name = key[:-4]
                filters.append(getattr(StorageActivity, field_name) > value)
            elif key.endswith('__gte'):
                field_name = key[:-5]
                filters.append(getattr(StorageActivity, field_name) >= value)
            elif key.endswith('__lt'):
                field_name = key[:-4]
                filters.append(getattr(StorageActivity, field_name) < value)
            elif key.endswith('__lte'):
                field_name = key[:-5]
                filters.append(getattr(StorageActivity, field_name) <= value)
            elif "__jsonb__" in key:
                col, json_key = key.split("__jsonb__")
                if value:
                    if json_key.endswith('__not_in'):
                        actual_key = json_key[:-8]
                        filters.append(
                            or_(
                                ~getattr(StorageActivity, col).has_key(actual_key),  # key not exists → condition is True
                                ~getattr(StorageActivity, col)[actual_key].astext.in_(value)
                            )
                        )
                    else:
                        filters.append(
                            or_(
                                ~getattr(StorageActivity, col).has_key(json_key),  # key not exists → condition is True
                                getattr(StorageActivity, col)[json_key].astext.in_(value)
                            )
                        )
            else:
                filters.append(getattr(StorageActivity, key) == value)

        with Session() as session:
            return session.query(
                exists().where(and_(*filters))
            ).scalar()
    except Exception as e:
        logger.error(e)
        return False

def delete_fetched_activity(ids: list, logger=logger) -> bool:
    try:
        delete_names = []
        with Session() as session:
            policies = session.query(StorageActivity).filter(StorageActivity.id.in_(ids)).all()
            for policy in policies:
                delete_names.append(policy.name)
                session.delete(policy)
                session.commit()
            return delete_names
    except Exception as e:
        logger.error(e)
        return []

def create_fetched_activity(data: dict, logger=logger) -> StorageActivity:
    try:
        with Session() as session:
            if session.query(exists().where(StorageActivity.event_id == data.get('event_id') and
                                            StorageActivity.event_type == data.get('event_type'))).scalar():
                raise UniqueViolation
            policy = StorageActivity(**data)
            policy.created_at = func.now()
            policy.updated_at = func.now()
            session.add(policy)
            session.commit()
            session.refresh(policy)
            return policy
    except UniqueViolation:
        raise
    except Exception as e:
        logger.error(e)
        return None

def update_fetched_activity(id, data, logger=logger):
    try:
        session = Session()
        activity = session.query(StorageActivity).filter(StorageActivity.id == id).first()
        if not activity:
            logger.info(f"activity {id} not found")
            return None

        valid_columns = {column.name for column in StorageActivity.__table__.columns}
        for key, value in data.items():
            if key in valid_columns and value is not None:
                setattr(activity, key, value)
                flag_modified(activity, key)
                logger.info(f"update {key} to {value}")

        activity.updated_at = func.now()
        session.commit()
        return activity.to_dict()
    except Exception as e:
        session.rollback()
        logger.error(e)
    finally:
        session.close()


def add_fetched_activities(datas: List[dict], logger=logger):
    for data in datas:
        logger.info(f"add_fetched_activities: data: {data}")

    try:
        with Session() as session:
            stmt = insert(StorageActivity).values([
                {**data, "created_at": func.now(), "updated_at": func.now()}
                for data in datas
            ]).on_conflict_do_nothing(
                index_elements=["event_id"]
            )

            session.execute(stmt)
            session.commit()
    except Exception as e:
        logger.error(e)
        return False
    return True

def get_earliest_event_time(storage_id: str):
    try:
        with Session() as session:
            stmt = (
                select(func.min(StorageActivity.event_time))
                .where(StorageActivity.storage_id == uuid.UUID(storage_id))
            )
            return session.execute(stmt).scalar_one_or_none()
    except Exception as e:
        logger.error(f"Error fetching earliest event_time for storage_id={storage_id}")
        return None

def get_earliest_created_time(storage_id: str):
    try:
        with Session() as session:
            stmt = (
                select(func.min(StorageActivity.created_at))
                .where(StorageActivity.storage_id == uuid.UUID(storage_id))
            )
            return session.execute(stmt).scalar_one_or_none()
    except Exception as e:
        logger.error(f"Error fetching earliest created time for storage_id={storage_id}")
        return None

def update_activities_status(datas: List[dict], logger=logger):
    session = Session()
    try:
        ids = [data["id"] for data in datas]

        status_case = case(
            {data["id"]: data["status"] for data in datas},
            value=StorageActivity.id
        )
        scan_triggered_case = case(
            {data["id"]: data["scan_triggered"] for data in datas},
            value=StorageActivity.id
        )

        session.query(StorageActivity).filter(StorageActivity.id.in_(ids)).update(
            {
                "status": status_case,
                "scan_triggered": scan_triggered_case
            },
            synchronize_session=False
        )
        session.commit()
    except:
        session.rollback()
        logger.exception(traceback.format_exc())
    finally:
        session.close()

def clean_activities(retention_time):

    try:
        cutoff_date = datetime.utcnow() - timedelta(days=retention_time)
        with Session() as session:
            deleted_count = session.query(StorageActivity).filter(
                StorageActivity.created_at < cutoff_date
            ).delete(synchronize_session=False)
            session.commit()
            logger.info(f"clean {deleted_count} old activities")
    except Exception as e:
        logger.error(f"Error cleaning old activity {e}")
