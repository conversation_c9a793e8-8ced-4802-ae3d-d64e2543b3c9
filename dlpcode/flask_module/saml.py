import traceback, json, base64
from flask import Blueprint
from flask import request, redirect
from util.common_log import get_logger
from flask_module.controller import user as ctrl_user
from flask_module.user_reqmodel import (GetUserModel, PostUserModel, DeleteUserModel,
                                        EditUserModel, EditUserPasswordModel)
from pydantic import ValidationError
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes

from onelogin.saml2.auth import OneLogin_Saml2_Auth
from saml_service import get_saml_login_auth, get_saml_settings_data, prepare_flask_request

logger = get_logger("api")

saml = Blueprint("saml", __name__)

@saml.route("/sso", methods=["POST"])
def sso():
    # SSO login read saml config in database, need external_idp  queried by id
    # try:
    #     validated_params = PostUserModel(**request.json)
    # except ValidationError as e:
    #     return error_response(Ecodes.VALIDATION_ERROR, 400, e)

    #make setting.json file
    try:
        auth = get_saml_login_auth(request)
        login_loc = auth.login()
        #todo nancy added log info
        logger.info(f"sso_login_loc: {login_loc}")

        return redirect(login_loc)
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)



@saml.route("/metadata", methods=["POST"])
def metadata():
    pass

@saml.route("/login", methods=["POST"])# 
def login():
    try:

        saml_response_b64 = request.form.get("SAMLResponse")
        if not saml_response_b64:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        saml_response_xml = base64.b64decode(saml_response_b64).decode("utf-8")
        logger.info(f"SAMLResponse XML:\n{saml_response_xml}")


        request_data = prepare_flask_request(request)
        settings_data = get_saml_settings_data()
        auth = OneLogin_Saml2_Auth(request_data, old_settings=settings_data)

        auth.process_response()
        errors = auth.get_errors()
        if errors:
            last_error_reason = auth.get_last_error_reason()
            logger.error(f"last_error_reason = {last_error_reason}")
            return error_response(Ecodes.INTERNAL_ERROR, 500, {
                'error': last_error_reason,
                'details': errors
            })

        if not auth.is_authenticated():
            logger.warning("User not authenticated after SAML response processing.")
            return error_response(Ecodes.INTERNAL_ERROR, 500, {
                'error': 'User not authenticated'
            })

        # Extract user data from the assertion
        user_data = {
            'name_id': auth.get_nameid(),
            'attributes': auth.get_attributes()
        }

        logger.info(f"User authenticated: {json.dumps(user_data)}")

        #todo nancy setcookie event log and so on
    
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@saml.route("/logout", methods=["POST"])
def logout():
    try:
        # When logout success, the response will be updated.
        return ctrl_user.logout_user(request), 200
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)