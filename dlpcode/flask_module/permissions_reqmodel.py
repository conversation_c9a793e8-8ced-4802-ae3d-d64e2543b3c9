import uuid
from pydantic import BaseModel
from pydantic import Field

class DeletePermissionsReqModel(BaseModel):
    scan_id: uuid.UUID = Field(..., description="Scan UUID")
    file_id: uuid.UUID = Field(..., description="File UUID")
    perm_id: str = Field(..., description="Permission id")

    class Config:
        extra = "forbid"

class UpdatePermissionsReqModel(BaseModel):
    scan_id: uuid.UUID = Field(..., description="Scan UUID")
    file_id: uuid.UUID = Field(..., description="File UUID")
    perm_id: str = Field(..., description="Permission id")
    permission: str = Field(..., description="Sharepoint: read | write. Google drive: reader | writer | commenter")

    class Config:
        extra = "forbid"