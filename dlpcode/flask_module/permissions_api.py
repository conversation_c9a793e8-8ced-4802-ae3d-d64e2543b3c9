import util.err_codes as Ecodes
from flask import Blueprint, request
from flask_module import session
from pydantic import ValidationError
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from util.common_log import get_logger
from util.err_codes import error_response, success_response
from domain_model.global_cache import GlobalCache
from util.enum_ import FileAction, FileActionStatus, FileActionType
from service.permissions_service import get_perm_info

logger = get_logger("api")
permissions = Blueprint("permissions", __name__)

def _perm_add_file_action(file_id, scan_id, action, action_detail, 
                          status, failed_reason=None):
    from service.file_action_service import add_file_action
    file_action = {
        'file_id': file_id,
        'scan_id': scan_id,
        'action': action,
        'action_type': FileActionType.MANUAL,
        'action_detail': action_detail,
        'status': status,
    }
    if failed_reason:
        file_action['failed_reason'] = failed_reason
    add_file_action(file_action, logger)

@permissions.route('/', methods=['DELETE'])
def handle_permissions_delete():
    from flask_module.permissions_reqmodel import DeletePermissionsReqModel
    from service.permissions_service import delete_permissions
    try:
        args = request.args.to_dict()
        req_model = DeletePermissionsReqModel(**args)
        file_id = req_model.file_id
        scan_id = req_model.scan_id
        perm_id = req_model.perm_id
        cache = GlobalCache(scan_id)
        file = cache.get_by_file_uuid(file_id)
        if not file:
            logger.error(f"Error deleting permission, file {file_id} not found.")
            return error_response(Ecodes.NOT_FOUND, 404, "File not found")
        
        perm_type, detail = get_perm_info(file, perm_id)
        if perm_type == 'link':
            action = FileAction.REMOVE_LINK
            detail = {'links': [detail]}
        else:
            action = FileAction.REMOVE_COLLABORATOR
            detail = {'collaborator': detail}
        deleted, err_msg = delete_permissions(cache, file, [perm_id])
        if not deleted:
            _perm_add_file_action(file_id, scan_id, action, detail, 
                                  FileActionStatus.FAILED, err_msg[perm_id])
            return error_response(Ecodes.INTERNAL_ERROR, 500, err_msg[perm_id])
        
        _perm_add_file_action(file_id, scan_id, action, detail, FileActionStatus.SUCCESS)
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Delete permission from {file.get('file_name')}",
            desc="Delete permission",
            action=LogAction.DELETE.value,
            type=LogType.PERMISSIONS.value
        )
        return success_response("Permission deleted", 200)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, str(e))
    except Exception as e:
        logger.exception(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))

@permissions.route('/', methods=['PATCH'])
def handle_permissions_patch():
    from flask_module.permissions_reqmodel import UpdatePermissionsReqModel
    from service.permissions_service import modify_permission
    try:
        data = request.get_json()
        req_model = UpdatePermissionsReqModel(**data)
        file_id = req_model.file_id
        scan_id = req_model.scan_id
        cache = GlobalCache(scan_id)
        file = cache.get_by_file_uuid(file_id)
        if not file:
            logger.error(f"Error patching permission: file {file_id} not found.")
            return error_response(Ecodes.NOT_FOUND, 404, "File not found")
        
        perm_type, detail = get_perm_info(file, req_model.perm_id)
        detail['permission'] = req_model.permission
        if perm_type == 'link':
            action = FileAction.MODIFY_LINK_PERM
            detail = {'links': [detail]}
        else:
            action = FileAction.MODIFY_COLLABORATOR_PERM
            detail = {'collaborator': detail}

        updated, err_msg = modify_permission(cache, file, req_model.perm_id, req_model.permission)
        if not updated:
            _perm_add_file_action(file_id, scan_id, action, detail, 
                                  FileActionStatus.FAILED, err_msg)
            return error_response(Ecodes.INTERNAL_ERROR, 500, err_msg)
        
        _perm_add_file_action(file_id, scan_id, action, detail, FileActionStatus.SUCCESS)
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Modify permission of {file.get('file_name')}",
            desc="Modify permission",
            action=LogAction.EDIT.value,
            type=LogType.PERMISSIONS.value
        )
        return success_response("Permission updated", 200)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, str(e))
    except Exception as e:
        logger.exception(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))
