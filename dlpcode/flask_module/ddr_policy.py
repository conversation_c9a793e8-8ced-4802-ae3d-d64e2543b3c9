import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response, ModErrCode
import util.err_codes as Ecodes
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType


logger = get_logger("api")
ddr_policy = Blueprint("ddr_policy", __name__)


@ddr_policy.route('/', methods=['POST'])
def handle_ddr_policy_create():
    from flask_module.ddr_reqmodel import DDRPolicyCreateReqModel
    from ddr.model.policy import create_ddr_policy
    from psycopg2.errors import UniqueViolation

    try:
        data = request.get_json()
        try:
            reqmodel = DDRPolicyCreateReqModel(**data)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02090001, 400, custom_message=e)

        payload = reqmodel.dict(exclude_none=True)
        payload["is_predefined"] = False
        payload["enabled"] = True

        new_policy = create_ddr_policy(payload)
        if not new_policy:
            return error_response(ModErrCode.ErrCode02090002, 400)

        record_event_log(
            user=session.get_user(request), 
            level=LogLevel.INFO.value, 
            message=f"Add a new ddr policy {new_policy['name']}", 
            desc='Add ddr policy', 
            type=LogType.DDR_POLICIES.value, 
            action=LogAction.CREATE.value
            )
        return success_response({"id": new_policy['id']})
    except UniqueViolation:
        return error_response(ModErrCode.ErrCode02090003, 409)
    except Exception as e:
        return error_response(ModErrCode.ErrCode02090004, 500)


@ddr_policy.route('/', methods=['DELETE'])
def handle_ddr_policy_delete():
    from flask_module.ddr_reqmodel import DDRDeleteReqModel
    from ddr.model.policy import delete_ddr_policy, get_ddr_policy
    from ddr.service.policy import get_ddr_task_ref

    try:
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting data_classifier by args {args}")

        try:
            reqmodel = DDRDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02090005, 400, custom_message=e)

        for id in reqmodel.id:
            policy = get_ddr_policy(id=id)
            if not policy:
                return error_response(ModErrCode.ErrCode02090006, 404)
            if policy.is_predefined:
                return error_response(ModErrCode.ErrCode02090007, 400)
            ref = get_ddr_task_ref(id=id)
            if ref:
                return error_response(ModErrCode.ErrCode02090008, 400)

        delete_names = delete_ddr_policy(reqmodel.id)

        record_event_log(user=session.get_user(request), 
                         level=LogLevel.INFO.value, 
                         message=f"Delete ddr policy {delete_names}",
                         desc='Delete ddr policy', 
                         type=LogType.DDR_POLICIES.value, 
                         action=LogAction.DELETE.value
                         )
        return success_response({"deleted": delete_names})
    except Exception as e:
        return error_response(ModErrCode.ErrCode02090009, 500)


@ddr_policy.route('/', methods=['PUT'])
def handle_ddr_policy_update():
    from flask_module.ddr_reqmodel import DDRPolicyUpdateReqModel
    from ddr.service.policy import get_ddr_task_ref
    from ddr.model.policy import get_ddr_policy, update_ddr_policy
    try:
        data = request.get_json()
        try:
            reqmodel = DDRPolicyUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02090010, 400, custom_message=e)

        logger.info(f"reqmodel: {reqmodel}")

        policy = get_ddr_policy(id=reqmodel.id)
        if not policy:
            return error_response(ModErrCode.ErrCode02090011, 404)
        if policy.is_predefined:
            return error_response(ModErrCode.ErrCode02090012, 400)
        if get_ddr_task_ref(id=reqmodel.id):
            return error_response(ModErrCode.ErrCode02090013, 400)

        payload = reqmodel.dict(exclude_none=True)

        new_policy = update_ddr_policy(id=reqmodel.id, data=payload)
        if not new_policy:
            return error_response(ModErrCode.ErrCode02090014, 400)

        record_event_log(
            user=session.get_user(request), 
            level=LogLevel.INFO.value, 
            message=f"Edit ddr policy {new_policy.get('name','')}",
            desc='Edit ddr policy', 
            type=LogType.DDR_POLICIES.value, 
            action=LogAction.EDIT.value
            )
        return success_response(new_policy)
    except Exception as e:
        return error_response(ModErrCode.ErrCode02090015, 500)

@ddr_policy.route('/', methods=['GET'])
def handle_ddr_policy_query():
    from flask_module.ddr_reqmodel import DDRQueryReqModel
    from ddr.model.policy import get_ddr_policies

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        if not conditions.get("id"):
            policies = get_ddr_policies()
        else:
            try:
                reqmodel = DDRQueryReqModel(**conditions)
            except ValidationError as e:
                return error_response(ModErrCode.ErrCode02090016, 400, custom_message=e)

            logger.info(f"reqmodel: {reqmodel}")
            if reqmodel.id:
                policies = get_ddr_policies(id=reqmodel.id)
            else:
                policies = get_ddr_policies(**reqmodel.dict(exclude_none=True))

        if not policies:
            return error_response(ModErrCode.ErrCode02090018, 404)
        return success_response({"list": [record.to_dict() for record in policies]})
    except:
        return error_response(ModErrCode.ErrCode02090017, 500)


