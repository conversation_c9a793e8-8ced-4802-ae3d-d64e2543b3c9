import traceback
import util.err_codes as Ecodes
from psycopg2.errors import UniqueViolation
from flask import Blueprint, request
from pydantic import ValidationError
from flask_module import session
from util.common_log import get_logger
from util.err_codes import error_response, success_response, ModErrCode
from system.system_log import record_event_log, LogType, LogAction, LogLevel
from util.config import configs
from storage.service.profiles import get_storage_profile_by_id

logger = get_logger("api")
ddr_task = Blueprint("ddr_task", __name__)


@ddr_task.route('/', methods=['POST'])
def handle_create_ddr_task():
    from flask_module.ddr_reqmodel import DDRTaskCreateReqModel
    from ddr.service.task import check_max_ddr_task, create_ddr_task_with_index
    try:
        data = request.get_json()
        try:
            reqmodel = DDRTaskCreateReqModel(**data)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02080003, 400, custom_message=e)

        max_ddr_task = configs.get("max_ddr_task", 16)
        result = check_max_ddr_task(max_ddr_task)
        if result < 0:
            return error_response(ModErrCode.ErrCode02080014, 400, extend_message=max_ddr_task)

        payload = reqmodel.dict(exclude_none=True)
        storage_id = payload['storage_id']
        storage = get_storage_profile_by_id(storage_id)
        if not storage:
            return error_response(ModErrCode.ErrCode02080015, 500)

        task = create_ddr_task_with_index(payload, max_ddr_task)
        if not task:
            return error_response(ModErrCode.ErrCode02080001, 400)

        record_event_log(
            user=session.get_user(request), 
            level=LogLevel.INFO.value, 
            message=f"Add a new DDR task {task['name']}", 
            desc='Add DDR task', 
            type=LogType.DDR_TASK.value, 
            action=LogAction.CREATE.value
            )
        return success_response({"id": str(task['id'])})
    except UniqueViolation:
        return error_response(ModErrCode.ErrCode02080002, 409)
    except Exception:
        return error_response(ModErrCode.ErrCode02080004, 500)


@ddr_task.route('/', methods=['GET'])
def handle_get_ddr_task():
    """List ddr tasks with filtering, sorting, and pagination."""
    from flask_module.ddr_reqmodel import DDRQueryReqModel
    from ddr.service.task import get_task_by_conditions
    from ddr.model.task import get_ddr_tasks_dict
    try:
        conditions = request.args.to_dict()
        if not conditions:
            tasks = get_ddr_tasks_dict(conditions)
            total = len(tasks)
        else:
            try:
                reqmodel = DDRQueryReqModel(**conditions)
            except ValidationError as e:
                return error_response(ModErrCode.ErrCode02080006, 400, custom_message=e)

            allowed_sort_fields = ['name', 'created_at', 'updated_at', 'db_index']
            if reqmodel.sort_field not in allowed_sort_fields:
                reqmodel.sort_field = 'created_at'
            if reqmodel.sort_method not in ['asc', 'desc']:
                reqmodel.sort_method = 'desc'
            total, tasks = get_task_by_conditions(conditions)

        if not tasks:
            return error_response(ModErrCode.ErrCode02080005, 500)

        return success_response({
            'list': tasks if tasks else [],
            'page': reqmodel.page if reqmodel else 0,
            'per_page': reqmodel.per_page if reqmodel else 0,
            'total': total
        })
    except Exception:
        return error_response(ModErrCode.ErrCode02080007, 500)


@ddr_task.route('/', methods=['PUT'])
def handle_update_ddr_task():
    from flask_module.ddr_reqmodel import DDRTaskUpdateReqModel
    from ddr.model.task import get_ddr_task, update_ddr_task
    try:
        data = request.get_json()
        if not data:
            return error_response(Ecodes.VALIDATION_ERROR, 400, "Request body is required.")
        task_id = data.get('id')
        if not task_id:
            return error_response(Ecodes.VALIDATION_ERROR, 400, "id is required.")
        task = get_ddr_task(id=task_id)
        if not task:
            return error_response(ModErrCode.ErrCode02080008, 404)
        data['storage_type'] = task.storage_type

        try:
            reqmodel = DDRTaskUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000009, 400, custom_message=e)
        payload = reqmodel.dict(exclude_none=False)
        updated_task = update_ddr_task(task_id, payload)
        if not updated_task:
            return error_response(ModErrCode.ErrCode02080010, 404)

        record_event_log(
            user=session.get_user(request), 
            level=LogLevel.INFO.value, 
            message=f"Edit the DDR task {updated_task['name']}", 
            desc='Edit DDR task', 
            type=LogType.DDR_TASK.value, 
            action=LogAction.EDIT.value
            )
        return success_response(updated_task)
    except Exception:
        return error_response(ModErrCode.ErrCode02080011, 500)


@ddr_task.route('/', methods=['DELETE'])
def delete_ddr_tasks():
    from flask_module.ddr_reqmodel import DDRDeleteReqModel
    from ddr.model.task import delete_ddr_tasks, get_ddr_task
    try:
        args = request.args.to_dict(flat=False)
        try:
            reqmodel = DDRDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02080012, 400, custom_message=e)

        name_list = []
        not_found = []
        for task_id in reqmodel.id:
            task = get_ddr_task(id=task_id)
            if not task:
                not_found.append(task_id)
            else:
                name_list.append(task.name)
        if not_found:
            return error_response(ModErrCode.ErrCode02080013, 404, extend_message={not_found})

        delete_names = delete_ddr_tasks(reqmodel.id)
        # TODO check referred
        # if referred:
        #     return error_response(ModErrCode.ErrCode02080014, 400, extend_message={referred})

        name_list_str = ",".join(name_list)
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Delete ddr task {name_list_str}",
            desc="Delete ddr task",
            type=LogType.DDR_TASK.value,
            action=LogAction.DELETE.value,
        )
        return success_response({"deleted": delete_names})
    except Exception:
        return error_response(ModErrCode.ErrCode02080015, 500)