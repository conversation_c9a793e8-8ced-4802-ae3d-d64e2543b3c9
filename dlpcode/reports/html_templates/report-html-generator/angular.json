{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"report-html-generator": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/report-html-generator", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "none"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "base_sample": {"outputHashing": "none", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}}, "sourceMap": false, "namedChunks": false, "aot": true, "buildOptimizer": true, "index": "src/app/base_sample/index.html", "main": "src/app/base_sample/main.ts", "styles": ["src/use-material.scss", "src/basic.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "base_sample_dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "none", "index": "src/app/base_sample/index.html", "main": "src/app/base_sample/main.ts", "styles": ["src/use-material.scss", "src/basic.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "dlp_dashboard": {"outputHashing": "none", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}}, "sourceMap": false, "namedChunks": false, "aot": true, "buildOptimizer": true, "index": "./src/app/dlp_dashboard/index.html", "main": "./src/app/dlp_dashboard/main.ts", "styles": ["src/use-material.scss", "src/basic.scss", "src/app/dlp_dashboard/global.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "dlp_dashboard_dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "none", "index": "./src/app/dlp_dashboard/index.html", "main": "./src/app/dlp_dashboard/main.ts", "styles": ["src/use-material.scss", "src/basic.scss", "src/app/dlp_dashboard/global.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "analytics_summary": {"outputHashing": "none", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}}, "sourceMap": false, "namedChunks": false, "aot": true, "buildOptimizer": true, "index": "./src/app/analytics_summary/index.html", "main": "./src/app/analytics_summary/main.ts", "styles": ["src/use-material.scss", "src/basic.scss", "src/app/analytics_summary/global.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "analytics_summary_dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "none", "index": "./src/app/analytics_summary/index.html", "main": "./src/app/analytics_summary/main.ts", "styles": ["src/use-material.scss", "src/basic.scss", "src/app/analytics_summary/global.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "analytics_files": {"outputHashing": "none", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}}, "sourceMap": false, "namedChunks": false, "aot": true, "buildOptimizer": true, "index": "./src/app/analytics_files/index.html", "main": "./src/app/analytics_files/main.ts", "styles": ["src/use-material.scss", "src/basic.scss", "src/app/analytics_files/global.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "analytics_files_dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "none", "index": "./src/app/analytics_files/index.html", "main": "./src/app/analytics_files/main.ts", "styles": ["src/use-material.scss", "src/basic.scss", "src/app/analytics_files/global.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "analytics_scan_incidents": {"outputHashing": "none", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}}, "sourceMap": false, "namedChunks": false, "aot": true, "buildOptimizer": true, "index": "./src/app/analytics_scan_incidents/index.html", "main": "./src/app/analytics_scan_incidents/main.ts", "styles": ["src/use-material.scss", "src/basic.scss", "src/app/analytics_scan_incidents/global.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}, "analytics_scan_incidents_dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "none", "index": "./src/app/analytics_scan_incidents/index.html", "main": "./src/app/analytics_scan_incidents/main.ts", "styles": ["src/use-material.scss", "src/basic.scss", "src/app/analytics_files/global.scss"], "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [], "scripts": [], "allowedCommonJsDependencies": ["lodash", "highcharts", "moment", "moment-timezone"]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "report-html-generator:build:production"}, "development": {"browserTarget": "report-html-generator:build:development"}, "base_sample": {"browserTarget": "report-html-generator:build:base_sample_dev"}, "dlp_dashboard": {"browserTarget": "report-html-generator:build:dlp_dashboard_dev"}, "analytics_summary": {"browserTarget": "report-html-generator:build:analytics_summary_dev"}, "analytics_files": {"browserTarget": "report-html-generator:build:analytics_files_dev"}, "analytics_scan_incidents": {"browserTarget": "report-html-generator:build:analytics_scan_incidents_dev"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "report-html-generator:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": []}}}}}}