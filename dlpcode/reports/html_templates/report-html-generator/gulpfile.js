const gulp = require("gulp");
const inline = require("gulp-inline");
const rename = require("gulp-rename");
const yargs = require("yargs");

const argv = yargs.argv;
const outputName = argv.name || "index";
const outputHtmlName = `${outputName}.html`;

gulp.task("inline", function () {
  return gulp
    .src("dist/report-html-generator/index.html")
    .pipe(
      inline({
        base: "dist/report-html-generator/",
        disabledTypes: ["svg", "img", "ico"],
      })
    )
    .pipe(rename(outputHtmlName))
    .pipe(gulp.dest("dist/single-file"));
});
