# General
- This project aims to integrate all Angular outputs into one HTML file, which can be used as a present report.

# How to use
- use command: npm run new-report <new-report-name> to create a new report
  After running the command, script will generate a new folder whose path is 'src/app/<new-report-name>', developers can code in this folder;
- use command: npm run start:<report-name> to start a develop environment like 'ng serve';
- use command: npm run build:inline:<report-name> to generate an all-in-one HTML file. If success the new HTML file will in ./dest/single-file/<report-name>.html

# How it works
- Define multi projects in 'angular.json' to support multi entry index.html for different Angular project
- Generate shortcut commands in package.json file
- copy 'src/app/base_sample' as workspace for new-reports
- use gulp inline to compose html, js, css in one html file.