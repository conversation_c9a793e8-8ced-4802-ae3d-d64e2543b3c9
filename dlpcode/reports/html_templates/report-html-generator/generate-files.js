const { execSync } = require("child_process");
const args = process.argv.slice(2);
const aim = args[0];
if (aim === undefined) {
  throw "no aim project input";
}

try {
  execSync("cp ts-interface-to-md.txt ts-interface-to-md.ts");
  console.log("backup script success");
  execSync(`sed -i 's/_DOLLAR_MAGIC/${aim}/g' ts-interface-to-md.ts`);
  console.log("update script success");
  execSync("npx tsc ts-interface-to-md.ts");
  console.log("compile TS file success");
  execSync("node ./ts-interface-to-md.js");
  console.log("running convert script success");
  execSync(`mv data_sample.json ../templates/${aim}/data_sample.json`);
  execSync(`mv data_structure.md ../templates/${aim}/data_structure.md`);
  console.log("move new files success");
  execSync(`rm src/app/${aim}/mock_data.js`);
  execSync(`rm src/app/${aim}/mock_data_structure.js`);
  execSync(`rm ts-interface-to-md.js`);
  execSync(`rm ts-interface-to-md.ts`);
} catch (err) {
  console.error(err);
}
