{"name": "report-html-generator", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "new-report": "node ./generate-report.js", "generate-files": "node ./generate-files.js", "start:base_sample": "ng serve --configuration=base_sample", "build:inline:base_sample": "cp src/app/base_sample/app.component.ts src/app/base_sample/app.component.backup && sed -i '/mockData/ s/^/\\/\\//' src/app/base_sample/app.component.ts && ng build --configuration=base_sample && gulp inline --name=base_sample && mv src/app/base_sample/app.component.backup src/app/base_sample/app.component.ts && sed -i 's/\"DOLLAR_UPPER_MOCK_DATA\"/DOLLAR_UPPER_MOCK_DATA/g' dist/single-file/base_sample.html && mkdir -p ../templates/base_sample/ && mv dist/single-file/base_sample.html ../templates/base_sample/base_sample.html && node ./generate-files.js base_sample", "start:dlp_dashboard": "ng serve --configuration=dlp_dashboard --port 8200", "build:inline:dlp_dashboard": "cp src/app/dlp_dashboard/app.component.ts src/app/dlp_dashboard/app.component.backup && sed -i '/mockData/ s/^/\\/\\//' src/app/dlp_dashboard/app.component.ts && ng build --configuration=dlp_dashboard && gulp inline --name=dlp_dashboard && mv src/app/dlp_dashboard/app.component.backup src/app/dlp_dashboard/app.component.ts && sed -i 's/\"DOLLAR_UPPER_MOCK_DATA\"/DOLLAR_UPPER_MOCK_DATA/g' dist/single-file/dlp_dashboard.html && mv dist/single-file/dlp_dashboard.html ../templates/dlp_dashboard/dlp_dashboard.html && node ./generate-files.js dlp_dashboard", "start:analytics_summary": "ng serve --configuration=analytics_summary --port 8200", "build:inline:analytics_summary": "cp src/app/analytics_summary/app.component.ts src/app/analytics_summary/app.component.backup && sed -i '/mockData/ s/^/\\/\\//' src/app/analytics_summary/app.component.ts && ng build --configuration=analytics_summary && gulp inline --name=analytics_summary && mv src/app/analytics_summary/app.component.backup src/app/analytics_summary/app.component.ts && sed -i 's/\"DOLLAR_UPPER_MOCK_DATA\"/DOLLAR_UPPER_MOCK_DATA/g' dist/single-file/analytics_summary.html && mv dist/single-file/analytics_summary.html ../templates/analytics_summary/analytics_summary.html && node ./generate-files.js analytics_summary", "start:analytics_files": "ng serve --configuration=analytics_files --port 8200", "build:inline:analytics_files": "cp src/app/analytics_files/app.component.ts src/app/analytics_files/app.component.backup && sed -i '/mockData/ s/^/\\/\\//' src/app/analytics_files/app.component.ts && ng build --configuration=analytics_files && gulp inline --name=analytics_files && mv src/app/analytics_files/app.component.backup src/app/analytics_files/app.component.ts && sed -i 's/\"DOLLAR_UPPER_MOCK_DATA\"/DOLLAR_UPPER_MOCK_DATA/g' dist/single-file/analytics_files.html && mv dist/single-file/analytics_files.html ../templates/analytics_files/analytics_files.html && node ./generate-files.js analytics_files", "start:analytics_scan_incidents": "ng serve --configuration=analytics_scan_incidents --port 8200", "build:inline:analytics_scan_incidents": "cp src/app/analytics_scan_incidents/app.component.ts src/app/analytics_scan_incidents/app.component.backup && sed -i '/mockData/ s/^/\\/\\//' src/app/analytics_scan_incidents/app.component.ts && ng build --configuration=analytics_scan_incidents && gulp inline --name=analytics_scan_incidents && mv src/app/analytics_scan_incidents/app.component.backup src/app/analytics_scan_incidents/app.component.ts && sed -i 's/\"DOLLAR_UPPER_MOCK_DATA\"/DOLLAR_UPPER_MOCK_DATA/g' dist/single-file/analytics_scan_incidents.html && mkdir ../templates/analytics_scan_incidents/ -p && mv dist/single-file/analytics_scan_incidents.html ../templates/analytics_scan_incidents/analytics_scan_incidents.html && node ./generate-files.js analytics_scan_incidents"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/elements": "^16.2.12", "@angular/forms": "^16.2.0", "@angular/material": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@ngx-translate/core": "^16.0.4", "highcharts": "^12.2.0", "highcharts-angular": "^4.0.1", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.12", "@angular/cli": "^16.2.12", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "@types/lodash": "^4.17.16", "cross-env": "^7.0.3", "gulp": "^5.0.0", "gulp-inline": "^0.1.3", "gulp-rename": "^2.0.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3", "yargs": "^17.7.2"}}