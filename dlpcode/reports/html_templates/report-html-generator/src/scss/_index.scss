@use 'sass:map';
@use '@angular/material' as mat;

@import './generic/button';
@import './generic/checkbox';
@import './generic/dialog';
@import './generic/dropdown';
@import './generic/fonts';
@import './generic/form-field';
@import './generic/hidden';
@import './generic/input';
@import './generic/loading';
@import './generic/menu';
@import './generic/radio-button';
@import './generic/tab-nav';
@import './generic/table';
@import './generic/tooltip';

@mixin theme($theme) {
  $color-config: mat.get-color-config($theme);
  $primary-palette: map.get($color-config, 'primary');
  $warn-palette: map.get($color-config, 'warn');

  //Include the theme to fs custom components
  //@include asset-details.app-asset-details-theme($theme);
}
