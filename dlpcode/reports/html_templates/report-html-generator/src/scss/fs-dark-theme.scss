@use 'sass:map';
@use '@angular/material' as mat;

@import './fs-dark-colors.scss';
@import '@angular/material/theming';

$fs-dark-palette-primary: (
  50: #e5ecf7,
  100: #bed0ec,
  200: #92b1df,
  300: #6692d2,
  400: #467ac9,
  500: #2563bf,
  600: #215bb9,
  700: #1b51b1,
  800: #1647a9,
  900: #0d359b,
  A100: #cad7ff,
  A200: #97b0ff,
  A400: #6489ff,
  A700: #4b76ff,
  contrast: (
    50: #000000,
    100: #000000,
    200: #000000,
    300: #000000,
    400: #ffffff,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #000000,
    A200: #000000,
    A400: #000000,
    A700: #ffffff
  )
);

// Foreground palette for fortiscan dark theme
$fs-dark-theme-foreground: (
  base: white,
  divider: map-get($fs-dark-colors, background),
  dividers: map-get($fs-dark-colors, background),
  disabled: $light-disabled-text,
  disabled-button: rgba(white, 0.3),
  disabled-text: $light-disabled-text,
  elevation: black,
  hint-text: $light-disabled-text,
  secondary-text: $light-secondary-text,
  icon: white,
  icons: white,
  text: white,
  slider-min: white,
  slider-off: rgba(white, 0.3),
  slider-off-active: rgba(white, 0.3)
);

// Background palette for fortiscan dark theme
$fs-dark-theme-background: (
  status-bar: black,
  app-bar: map_get(mat.$grey-palette, 900),
  background: map-get($fs-dark-colors, background),
  hover: rgba(white, 0.04),
  card: map-get($fs-dark-colors, panel),
  dialog: map-get($fs-dark-colors, panel),
  disabled-button: rgba(white, 0.12),
  raised-button: map-get($fs-dark-colors, panel),
  focused-button: $light-focused,
  selected-button: map_get(mat.$grey-palette, 900),
  selected-disabled-button: map-get($fs-dark-colors, panel),
  disabled-button-toggle: black,
  unselected-chip: map-get($fs-dark-colors, panel-second),
  disabled-list-option: black
);

//$fs-dark-theme-primary: mat.define-palette(mat.$light-green-palette);
$fs-dark-theme-primary: mat.define-palette($fs-dark-palette-primary, 500);
$fs-dark-theme-accent: mat.define-palette(mat.$amber-palette, A200, A100, A400);
$fs-dark-theme-warn: mat.define-palette(mat.$light-blue-palette);

$fs-dark-theme: mat.define-dark-theme(
  (
    color: (
      primary: $fs-dark-theme-primary,
      accent: $fs-dark-theme-accent,
      warn: $fs-dark-theme-warn,
      is-dark: true,
      foreground: $fs-dark-theme-foreground,
      background: $fs-dark-theme-background
    )
  )
);

.dark-theme {
  @include mat.core-color($fs-dark-theme);
  @include mat.all-component-themes($fs-dark-theme);

  .side-nav {
    background-color: map-get($fs-dark-colors, primary);

    app-side-nav-entry.active {
      &:not(.has-children) {
        color: map-get($fs-dark-theme-foreground, text);
        background-color: map-get($fs-dark-colors, layout-background);
        font-weight: 700;

        .icon {
          color: map-get($fs-dark-theme-foreground, text);
        }
      }
    }
  }

  .search-container {
    input {
      color: rgba(255, 255, 255, 0.87);
    }
  }

  .loading-panel {
    background-color: rgba(255, 255, 255, 0.35);
  }

  tr.row-selected {
    background-color: rgb(40, 45, 55);
  }

  .kv-table {
    tr:nth-child(odd) {
      background-color: #465666; //map-get($fs-dark-colors, primary);
    }
  }

  * {
    scrollbar-color: #262626;
  }

  *::-webkit-scrollbar,
  *::-webkit-scrollbar-corner {
    background-color: #262626;
  }

  // @include components.theme(
  //    $fs-dark-theme
  // );
}
