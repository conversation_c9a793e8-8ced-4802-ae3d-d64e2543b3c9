$fs-typo-map: (
    // page-headline
    page-headline: (
        font-size: var(--big-font-size),            // 15.99px ~ 16px
        font-weight: 400,
        line-height: var(--big-line-height)         // 18.98px ~ 19px
    ),

    // page-headline-selected
    page-headline-selected: (
        font-size: var(--big-font-size),            // 15.99px ~ 16px
        font-weight: 700,
        line-height: var(--big-line-height)         // 18.98px ~ 19px
    ),

    // table-column-header
    table-column-header: (
        font-size: var(--small-font-size),          // 13px;
        font-weight: 500,
        line-height: var(--table-line-height)       // 17.93px ~ 18px
    ),

    // table-row-value
    table-row-value: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 400,
        line-height: var(--table-line-height)       // 17.93px ~ 18px
    ),

    // widget-header
    widget-header: (
        font-size: var(--medium-font-size),         // 14.04px ~ 14px
        font-weight: 700,
        line-height: var(--medium-line-height)      // 15.98px ~ 16px
    ),

    // editable-element-header
    editable-element-header: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 500,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // not-editable-element-header
    not-editable-element-header: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 500,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // navigation
    navigation: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 400,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // navigation-active
    navigation-active: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 700,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // input-field-placeholder
    input-field-placeholder: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 400,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // input-field-value
    input-field-value: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 400,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // buttons
    buttons: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 500,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // secondary-button
    secondary-button: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 500,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // values
    values: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 400,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // error-text
    error-text: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 400,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // instructions-text
    instructions: (
        font-size: var(--small-font-size),          // 13px
        font-weight: 400,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // chart-values
    chart-values: (
        font-size: var(--very-small-font-size),     // 11.05px ~ 11px
        font-weight: 400,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // chart-marker
    chart-marker: (
        font-size: var(--very-small-font-size),     // 11.05px ~ 11px
        font-weight: 500,
        line-height: var(--small-line-height)       // 14.95px ~ 15px
    ),

    // chart-labels
    chart-labels: (
        font-size: var(--small-font-size),           // 13px
        font-weight: 400,
        line-height: var(--small-line-height)        // 14.95px ~ 15px
    ),

    // important-information
    important-information: (
        font-size: var(--very-big-font-size),        // 18px
        font-weight: 400,
        line-height: var(--very-big-line-height)     // 21px
    ),
    
    // title-medium
    title-medium: (
        font-size: var(--medium-font-size),          // 14.04px ~ 14px
        font-weight: 700,
        line-height: var(--medium-line-height)       // 15.98px ~ 16px
    ),
    
    // widget-sub-headers
    widget-sub-headers: (
        font-size: var(--small-font-size),           // 13px
        font-weight: 400,
        line-height: var(--medium-line-height)       // 15.98px ~ 16px
    ),

    // key-number-big
    key-number-big: (font-size: var(--001-very-big-font-size), // 24px
        font-weight: 400,
        line-height: var(--medium-line-height) // 15.98px ~ 16px
    ),

        // widget-sub-headers
    normal-sub-headers: (
        font-size: var(--small-font-size),           // 13px
        font-weight: 500,
        line-height: var(--medium-line-height)       // 15.98px ~ 16px
    )
)