@import "./typography-vars.scss";

@mixin fs-typography($component) {
    font-size: map-get($map: map-get($map: $fs-typo-map, $key: #{$component}), $key: font-size);
    font-weight: map-get($map: map-get($map: $fs-typo-map, $key: #{$component}), $key: font-weight);
    line-height: map-get($map: map-get($map: $fs-typo-map, $key: #{$component}), $key: line-height);
}

@mixin fs-typography-core {
    @each $var in map-keys($fs-typo-map) {
        .#{$var} {
            @include fs-typography(#{$var});
        }
    }

    :root {
        // font-size vars
        --big-font-size: 1.23rem;
        --medium-font-size: 1.08rem;
        --small-font-size: 1.0rem;
        --very-small-font-size: 0.85rem;
        --very-big-font-size: 1.384615rem;
        --001-very-big-font-size: 1.85rem;  //24px

        // line-height vars
        --big-line-height: 1.46rem;
        --very-big-line-height: 1.615384rem;
        --medium-line-height: 1.23rem;
        --small-line-height: 1.15rem;
        --table-line-height: 1.38rem;
    }
}
