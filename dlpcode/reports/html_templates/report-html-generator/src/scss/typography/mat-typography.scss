@use "@angular/material" as mat;
@import "@angular/material/theming";

$fs-font-family: "Inter, sans-serif";

// Typography - Default Font Family is Inter and font size is 14px  $font-family:   'Inter, Roboto, sans-serif',

$custom-typography: mat.define-typography-config(
  $font-family: $fs-font-family,
  $headline-1:
    mat.define-typography-level(112px, 112px, 300, $letter-spacing: -0.05em),
  $headline-2:
    mat.define-typography-level(56px, 56px, 400, $letter-spacing: -0.02em),
  $headline-3:
    mat.define-typography-level(45px, 48px, 400, $letter-spacing: -0.005em),
  $headline-4: mat.define-typography-level(34px, 40px, 400),
  $headline-5: mat.define-typography-level(24px, 32px, 400),
  $headline-6: mat.define-typography-level(18px, 24px, 400),
  $subtitle-2: mat.define-typography-level(0.875rem, 1.25rem, 600),
  $subtitle-1: mat.define-typography-level(1.1rem, 1.1rem, 900),
  $body-2: mat.define-typography-level(0.875rem, 1.25rem, 400),
  $body-1: mat.define-typography-level(0.75rem, 0.75rem, 400),
  $caption: mat.define-typography-level(12px, 20px, 400),
  $button: mat.define-typography-level(14px, 14px, 500),
);

@include mat.all-component-typographies($custom-typography);
