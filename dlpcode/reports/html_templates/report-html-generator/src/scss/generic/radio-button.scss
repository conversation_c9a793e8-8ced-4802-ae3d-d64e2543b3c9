mat-radio-group {
  display: flex;
  gap: 20px;

  &.flex-column {
    flex-direction: column;
    gap: 10px;
  }
}

mat-radio-button.mat-mdc-radio-button {
  .mat-mdc-radio-touch-target {
    height: auto;
    width: auto;
    top: 0;
    left: 0;
    transform: none;
  }

  .mdc-radio {
    padding: 0;
    height: 16px;
    width: 16px;
  }

  .mdc-radio__background {
    height: 16px;
    width: 16px;

    .mdc-radio__inner-circle {
      top: -2px;
      left: -2px;
    }

    &::before {
      display: none;
    }
  }

  .mdc-label {
    display: flex;
    padding-left: 0;
    font-size: 14px;
    cursor: pointer;
  }

  .mdc-radio__native-control {
    height: auto !important;
    width: auto !important;
  }

  .mdc-form-field {
    gap: 10px;
  }
}
