.mat-mdc-tab-header {
  padding: 16px 20px;
}

.mat-mdc-tab-nav-bar {
  .mat-mdc-tab-link {
    box-sizing: border-box;
    border-radius: 28px;
    display: flex;
    flex-direction: row;
    gap: 5px;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    height: 32px !important;
    max-width: 220px;
    position: relative;
    overflow: hidden;
    margin: 0 0.2rem;

    .mdc-tab__text-label {
      font-size: 14px;
      color: #222222;
    }

    &:hover {
      background-color: #5e99f3; // Should set this value via theme variable

      .mdc-tab__text-label {
        color: #fff !important; // Should set this value via theme variable
      }
    }
  }

  .mat-tab-link-active {
    background-color: #2563bf; // Should set this value via theme variable

    .mdc-tab__text-label {
      font-weight: 700;
      color: #fff !important; // Should set this value via theme variable
    }
  }
}

.mat-mdc-tab-nav-panel {
  flex: 1;
  overflow: hidden;
}
