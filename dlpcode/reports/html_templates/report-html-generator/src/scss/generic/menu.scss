// mat-menu
.mat-mdc-menu-panel {
  overflow-x: hidden !important;
  min-height: 32px !important;

  .mat-mdc-menu-content,
  .mat-mdc-menu-content:not(:empty) {
    padding: 0px;
  }

  .mat-mdc-menu-content {
    .mat-mdc-menu-item {
      .mat-mdc-menu-item-text {
        display: flex;
        align-items: center;
        white-space: nowrap;
      }
    }
  }

  .mat-menu-item,
  .mat-mdc-menu-item {
    height: 32px;
    min-height: 32px;
    line-height: 32px;
    margin: 0px;
  }

  .menu-text {
    font-size: 14px;
  }

  .mat-icon {
    margin-right: 6px;
  }

  .col-menu-item {
    min-width: 180px;
    width: 100%;
    height: 32px;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    gap: 5px;
    align-items: center;
    white-space: nowrap;

    .mat-icon {
      margin-right: 0px;
    }
  }

  .action-menu-item {
    width: 150px;
    font-size: 14px;

    .mat-mdc-menu-item-text {
      span {
        line-height: 16px;
      }
    }
  }

  &.menu-panel {
    max-width: max-content;
  }
}

.multiple-items-popover {
  max-width: 600px !important;
  border: 1px solid #e5e5e5;
  border-radius: 2px !important;
  box-shadow: 0px 1px 8px 0px #00000026 !important;

  .mat-mdc-menu-content,
  .mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text {
    font-size: 14px;
  }

  .title {
    padding: 12px 10px;
    border-bottom: 1px solid #e5e5e5;
    font-weight: 700;
    line-height: 16px;
    min-height: 15px;
    display: flex;
    gap: 12px;

    mat-icon {
      margin: 0 0 0 auto;
      height: 14px;
      width: 14px;
      font-size: 14px;
      transition: opacity 0.15s;
      cursor: pointer;

      &:hover {
        opacity: 0.5;
      }
    }
  }

  .body {
    padding: 16px 10px;

    table {
      border-spacing: 0px;

      tr {
        &:not(:last-child) {
          td {
            padding-bottom: 8px;
          }
        }
      }

      td {
        line-height: 20px;

        &:not(:last-child) {
          padding-right: 10px;
        }
      }
    }
  }

  .chip {
    display: inline-flex;
    align-items: center;
    padding: 1px 12px;
    height: 24px;
    background-color: #d7dadd;
    border-radius: 16px;
    -webkit-user-select: none;
    user-select: none;
    white-space: nowrap;
  }
}

.scope-popover {
  max-width: 400px !important;

  .popover-content {
    min-width: 160px;
  }

  .detail {
    display: flex;
  }

  .column {
    display: flex;
    flex-direction: column;

    > span {
      padding: 10px;
      height: 11px;

      &:not(:last-child) {
        border-bottom: 1px solid #e5e5e5;
      }
    }

    &.exclusion {
      flex: 1;
    }
  }

  .title-row {
    font-weight: 700;
  }
}

.table-filter-panel-popover {
  min-width: 400px !important;
  max-width: 600px !important;

  .body {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-height: auto;

    .operations {
      display: flex;
      gap: 10px;
    }

    mat-button-toggle-group {
      border: 0;

      .mat-button-toggle-group-appearance-standard
        .mat-button-toggle-appearance-standard
        + .mat-button-toggle-appearance-standard {
        border-left: 0;
      }

      .mat-button-toggle {
        border: 1px solid #e0e0e0;

        &:not(:first-child) {
          border-left: 0;
        }

        &:last-child {
          border-radius: 4px 4px 0 0;
        }
      }

      .mat-button-toggle-label-content {
        line-height: 36px;
        min-width: 40px;
        padding: 0 6px;
      }
    }

    input {
      height: 30px;
      min-width: 50px;
      padding: 0 6px;
      font-size: 14px;
      border-radius: 4px;
      border: solid 1px #e5e5e5;
      background-color: #f7f9f9;
      outline: none;
    }

    .range-settings {
      display: flex;
      align-items: flex-start;
      gap: 6px;

      .to {
        line-height: 32px;
      }

      .setting {
        display: inline-flex;
        flex-direction: column;
        gap: 6px;
      }
    }

    .search-mapping-box {
      border: solid 1px #e5e5e5;
      border-radius: 4px;
      margin: 6px 0 0;
      max-height: 260px;

      legend {
        padding: 0 8px;
        font-weight: 600;
      }

      .search-mapping-list {
        display: flex;
        flex-direction: column;
        gap: 3px;
        max-height: 200px;
        overflow: auto;

        .label-item {
          pointer-events: none;
          font-weight: bold;
        }

        .item {
          display: inline-flex;
          align-items: center;
          padding: 4px 6px;
          font-size: 14px;
          transition: background-color 0.1s;
          cursor: pointer;
          line-height: 24px;

          span {
            max-width: 500px;
            text-overflow: ellipsis;
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
          }

          mat-icon {
            margin-left: auto;
            height: 16px;
            width: 16px;
            font-size: 16px;
          }

          &:hover,
          &.selected {
            background-color: #f0f0f0;
          }
        }
      }
    }

    .chip-set {
      background-color: #f7f9f9;
      height: 32px;
      box-sizing: border-box;
      border-radius: 4px;
      display: flex;
      align-items: center;
      border: solid 1px #e5e5e5;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: 8px;
      overflow-y: auto;
    }

    .chip-row {
      display: flex;
      flex-wrap: nowrap;
      padding: 0 12px;
      height: 26px;
      background-color: #e0e0e0;
      border-radius: 16px;
      align-items: center;
      gap: 5px;
      font-size: 0.875rem;
      cursor: pointer;
      transition: opacity 0.25s;
      -webkit-user-select: none;
      -ms-user-select: none;
      user-select: none;

      button {
        align-items: center;
        background: none;
        border: none;
        display: inline-flex;
        justify-content: center;
        outline: none;
        padding: 0;
        text-decoration: none;
        cursor: pointer;
        opacity: 0.5;

        mat-icon {
          width: 16px;
          height: 16px;
          font-size: 16px;
          margin-right: 0;
        }
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .footer {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid #e5e5e5;
    height: 64px;
    padding-left: 20px;
    padding-right: 10px;
    gap: 20px;
  }
}

.light-theme {
  .multiple-items-popover {
    .title {
      mat-icon {
        color: #47535c;
      }
    }

    .popover-content {
      color: #222222;
      background-color: #fff;
    }
  }

  .table-filter-panel-popover {
    mat-button-toggle-group {
      .mat-button-toggle-appearance-standard.mat-button-toggle-checked {
        color: #fff;
        background-color: #2563bf;
        font-weight: 600;
      }
    }
  }
}
