.light-theme {
  .mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: #f7f9f9;
  }
}

mat-select {
  &.mat-mdc-select {
    width: 100%;
    background-color: #f7f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 2px;
    color: #222222;

    &:focus {
      border-color: rgba(82, 168, 236, 0.8) !important;
      box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
      outline: none; // Set this none else a black outline will appear on focus masking the blue border color
    }
  }

  .mat-mdc-select-value {
    padding-left: 12px;
    padding-right: 12px;
    font-size: 14px;
    line-height: normal;
  }

  .mat-mdc-select-trigger {
    height: 30px;
  }

  .mat-mdc-select-arrow {
    right: 12px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  &.mat-mdc-select-disabled {
    color: #999a9a;

    &:focus {
      border-color: #e5e5e5 !important;
      box-shadow: none;
    }
  }
}

.mat-mdc-text-field-wrapper {
  padding: 0 12px !important;
  border: 1px solid #e5e5e5;
  border-radius: 2px;

  .mat-mdc-form-field-infix {
    padding: 0 !important;
    min-height: 0 !important;
  }

  .mdc-line-ripple {
    display: none;
  }

  mat-select {
    border: none;

    .mat-mdc-select-value {
      padding-left: 0;
    }

    .mat-mdc-select-arrow {
      right: 0;
    }
  }

  mat-select {
    &:focus {
      border-color: transparent !important;
      box-shadow: none;
    }
  }
}

.mat-mdc-form-field-type-mat-select {
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }

  .mat-mdc-form-field-focus-overlay {
    display: none !important;
  }

  &.mat-focused {
    .mat-mdc-text-field-wrapper {
      border-color: rgba(82, 168, 236, 0.8) !important;
      box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
      outline: none; // Set this none else a black outline will appear on focus masking the blue border color
    }
  }
}

// .mat-mdc-select-panel {
//   max-height: 50vh !important;
// }

.mat-mdc-option {
  padding: 0 12px;

  .mdc-list-item__primary-text {
    white-space: nowrap !important;
    line-height: normal !important;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-right: 0 !important;
  }

  .mat-pseudo-checkbox-minimal {
    height: 14px;
    width: 14px;
    margin-left: auto !important;
  }

  &.mdc-list-item {
    font-size: 14px;
    min-height: 32px;
  }

  &:hover:not(.mdc-list-item--disabled) {
    background-color: #eee;
  }

  &.mdc-list-item--selected {
    background-color: #e6edf8 !important;
  }
}

.mat-mdc-optgroup-label {
  font-size: 14px;
  font-weight: 700;
  min-height: 32px !important;
}
