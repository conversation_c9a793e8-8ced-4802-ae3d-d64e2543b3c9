.mat-mdc-checkbox-ripple,
.mdc-checkbox__ripple {
  display: none;
}

mat-checkbox {
  .mdc-form-field {
    display: flex;
    gap: 10px;

    > label {
      padding-left: 0;
    }
  }

  .mdc-checkbox {
    margin: 0;
    padding: 0;
    height: 16px;
    width: 16px;

    .mat-mdc-checkbox-touch-target {
      height: 100%;
      width: 100%;
      transform: translate(40%, -50%);
    }

    input {
      top: 0 !important;
      left: 0 !important;
      right: auto !important;
      height: 16px !important;
      width: 16px !important;
    }

    .mdc-checkbox__background {
      top: 0;
      left: 0;
      height: 16px;
      width: 16px;
    }
  }

  .mdc-label {
    white-space: nowrap;
    font-size: 14px;
    padding-left: 0;
  }
}

.light-theme {
  .mdc-checkbox
    .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate='true'])
    ~ .mdc-checkbox__background {
    border-width: 1px;
    border-color: #47535c !important;
  }

  .mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
  .mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background,
  .mdc-checkbox .mdc-checkbox__native-control[data-indeterminate='true']:enabled ~ .mdc-checkbox__background {
    background-color: #4a8bed !important;
    border-color: #4a8bed !important;
  }
}
