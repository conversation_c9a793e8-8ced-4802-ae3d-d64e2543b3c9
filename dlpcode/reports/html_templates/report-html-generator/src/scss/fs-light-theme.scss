@use 'sass:map';
@use '@angular/material' as mat;

@import './fs-light-colors.scss';
@import '@angular/material/theming';

/* Define custom palette for use in src/lib/core/theming/_palette.scss */
$fs-light-palette-primary: (
  50: #e5ecf7,
  100: #bed0ec,
  200: #92b1df,
  300: #6692d2,
  400: #467ac9,
  500: #2563bf,
  600: #215bb9,
  700: #1b51b1,
  800: #1647a9,
  900: #0d359b,
  A100: #cad7ff,
  A200: #97b0ff,
  A400: #6489ff,
  A700: #4b76ff,
  contrast: (
    50: #000000,
    100: #000000,
    200: #000000,
    300: #000000,
    400: #ffffff,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #000000,
    A200: #000000,
    A400: #000000,
    A700: #ffffff
  )
);

$fs-light-palette-accent: (
  50: #eae8fc,
  100: #cac5f9,
  200: #a79ff5,
  300: #8379f0,
  400: #695ced,
  500: #4e3fea,
  600: #4739e7,
  700: #3d31e4,
  800: #3529e1,
  900: #251bdb,
  A100: #ffffff,
  A200: #dedcff,
  A400: #ada9ff,
  A700: #9490ff,
  contrast: (
    50: #000000,
    100: #000000,
    200: #000000,
    300: #000000,
    400: #ffffff,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #000000,
    A200: #000000,
    A400: #000000,
    A700: #000000
  )
);

// Foreground palette for fortiscan light theme
$fs-light-theme-foreground: (
  base: black,
  divider: $dark-dividers,
  dividers: $dark-dividers,
  disabled: $dark-disabled-text,
  disabled-button: rgba(black, 0.26),
  disabled-text: $dark-disabled-text,
  hint-text: $dark-disabled-text,
  secondary-text: $dark-secondary-text,
  elevation: black,
  icon: map-get($fs-light-colors, font),
  icons: map-get($fs-light-colors, font),
  text: map-get($fs-light-colors, font),
  slider-min: rgba(black, 0.87),
  slider-off: rgba(black, 0.26),
  slider-off-active: rgba(black, 0.38)
);

// Background palette for fortiscan light theme
$fs-light-theme-background: (
  status-bar: map_get(mat.$grey-palette, 300),
  app-bar: map_get(mat.$grey-palette, 100),
  background: map-get($fs-light-colors, background),
  hover: rgba(black, 0.04),
  card: map-get($fs-light-colors, panel),
  dialog: map-get($fs-light-colors, panel),
  disabled-button: rgba(black, 0.12),
  raised-button: map-get($fs-light-colors, panel),
  focused-button: $dark-focused,
  selected-button: map_get(mat.$grey-palette, 300),
  selected-disabled-button: map_get(mat.$grey-palette, 400),
  disabled-button-toggle: map_get(mat.$grey-palette, 200),
  unselected-chip: map-get($fs-light-colors, font),
  disabled-list-option: map_get(mat.$grey-palette, 200)
);

@function fs-light-theme-definition(
  $primary,
  $accent,
  $warn: mat.define-palette(mat.$red-palette)
) {
  @return (
    primary: $primary,
    accent: $accent,
    warn: $warn,
    is-dark: false,
    foreground: $fs-light-theme-foreground,
    background: $fs-light-theme-background
  );
}

// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
//$fs-light-theme-primary: mat.define-palette(mat.$indigo-palette);
//$fs-light-theme-accent: mat.define-palette(mat.$pink-palette,A200,A100,A400);
$fs-light-theme-primary: mat.define-palette($fs-light-palette-primary, 500, A200, A400);
$fs-light-theme-accent: mat.define-palette($fs-light-palette-accent);

// The warn palette is optional (defaults to red).
$fs-light-theme-warn: mat.define-palette(mat.$red-palette);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$fs-light-theme: mat.define-light-theme(
  (
    color: (
      primary: $fs-light-theme-primary,
      accent: $fs-light-theme-accent,
      warn: $fs-light-theme-warn,
      is-dark: false,
      Foreground: $fs-light-theme-foreground,
      background: $fs-light-theme-background
    )
  )
);

.side-nav {
  background-color: map-get($fs-light-colors, side-nav);

  app-side-nav-entry {
    .entry.active {
      &:not(.has-children) {
        color: map-get($fs-light-colors, side-nav);
        background-color: map-get($fs-light-colors, background);
        font-weight: 700;

        .icon {
          color: map-get($fs-light-colors, side-nav);
        }
      }

      &.has-children {
        background-color: #1a509e;
      }
    }
  }
}

app-layout.close-side-nav {
  .side-nav {
    .entry.has-children.active {
      color: map-get($fs-light-colors, side-nav);
      background-color: map-get($fs-light-colors, background);

      .icon {
        color: map-get($fs-light-colors, side-nav);
      }
    }

    .wrapper:hover {
      .sub-entries {
        background-color: map-get($fs-light-colors, side-nav);
      }
    }
  }
}

.kv-table {
  tr:nth-child(odd) {
    background-color: map-get($fs-light-colors, input-border);
  }
}

.link-cell {
  color: map-get($fs-light-colors, primary);
}

.light-theme {
  ::placeholder {
    color: #9aa3ab;
  }

  ::-ms-input-placeholder {
    color: #9aa3ab;
  }

  .router-outlet {
    background-color: map-get($fs-light-colors, background);
  }

  .nav-path,
  .container,
  .inventory-container,
  .reports-container,
  .system-container {
    background-color: #fff;
  }

  .mat-mdc-table {
    .mat-mdc-cell,
    .mdc-data-table__row:last-child .mat-mdc-cell {
      border-bottom-color: #e5e5e5;
      border-bottom-width: 1px;
      border-bottom-style: solid;
    }
  }
}

//$fs-light-theme: fs-light-theme-definition($fs-light-theme-primary, $fs-light-theme-accent, $fs-light-theme-warn);
