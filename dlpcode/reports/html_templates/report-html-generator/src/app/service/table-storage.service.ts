import { Injectable } from '@angular/core';

const QUERY_KEY = 'search_query';
const COL_SET_KEY = 'table_column_setting';
const SORT_KEY = 'sort_setting';
const COL_ORDER_KEY = 'column_order_setting';
const HIDDEN_COL_KEY = 'hidden_columns';

@Injectable({
  providedIn: 'root',
})
export class TableStorageService {
  constructor() {}

  setColumnSetting(tableId: string, settings: any[]): void {
    this.setToStorage(COL_SET_KEY, tableId, settings);
  }

  getColumnSetting(tableId: string): any[] {
    return this.getFromStorage(COL_SET_KEY, tableId);
  }

  setQuery(tableId: string, queryArray: any[]): void {
    this.setToStorage(QUERY_KEY, tableId, queryArray);
  }

  getQuery(tableId: string): string[] {
    return this.getFromStorage(QUERY_KEY, tableId);
  }

  setSort(tableId: string, sortArray: any): void {
    this.setToStorage(SORT_KEY, tableId, sortArray);
  }

  getSort(tableId: string): any {
    return this.getFromStorage(SORT_KEY, tableId);
  }

  setColumnOrder(tableId: string, columnIds: string[]): void {
    this.setToStorage(COL_ORDER_KEY, tableId, columnIds);
  }

  getColumnOrder(tableId: string): any {
    return this.getFromStorage(COL_ORDER_KEY, tableId);
  }

  setHiddenColumns(tableId: string, columnIds: string[]): void {
    this.setToStorage(HIDDEN_COL_KEY, tableId, columnIds);
  }

  getHiddenColumns(tableId: string): any {
    return this.getFromStorage(HIDDEN_COL_KEY, tableId);
  }

  private setToStorage(key: string, tableId: string, param: any) {
    let setting = localStorage.getItem(key);
    if (!setting) setting = JSON.stringify({});

    let obj = JSON.parse(setting);
    if (Array.isArray(obj)) {
      obj = {};
    }
    obj[tableId] = param;

    localStorage.setItem(key, JSON.stringify(obj));
  }

  private getFromStorage(key: string, tableId: string) {
    const empty: any[] = [];
    const setting = localStorage.getItem(key);
    if (!setting) return empty;

    const obj = JSON.parse(setting);
    if (obj.hasOwnProperty(tableId)) return obj[tableId];

    return empty;
  }
}
