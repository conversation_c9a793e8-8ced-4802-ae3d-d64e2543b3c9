import { Injectable, OnDestroy } from '@angular/core';
import moment from 'moment';
import 'moment-timezone';

@Injectable({
  providedIn: 'root'
})
export class DateUtilService implements OnDestroy {
  locale: string = 'en-US';

  constructor() {}

  ngOnDestroy() {}

  /**
   * Convert time in long to GMT timestamp with up to minutes
   *
   * @param value GMT time
   */
  convertToGMTTimestamp(timeInms: number, timeZone) {
    const dateTime = new Date(timeInms * 1000);
    const dateTimeInTZ = this.formatDateTimeWithMinutesPrecision(dateTime, this.locale, timeZone);
    return dateTimeInTZ;
  }

  /**
   * This function takes a Date object (dateTime), a locale string, and a timeZone string.
   * It uses moment-timezone to format the date according to the specified time zone.
   * The format 'YYYY/MM/DD HH:mm' doesnt include seconds and milliseconds value
   * @param dateTime
   * @param locale
   * @param timeZone
   * @returns
   */
  formatDateTimeWithMinutesPrecision(dateTime: Date, locale: string, timeZone: string): string {
    //console.log("Input Date object:", dateTime.toISOString());

    // Check if the input Date object has millisecond precision
    // Set the locale for moment
    moment.locale(locale);

    // Format the date-time in the specified time zone
    if (timeZone === 'local browser time' || timeZone === '' || timeZone === undefined) {
      timeZone = moment.tz.guess();
    }

    //console.log("Timezone setting = ", timeZone);

    //The backend currently doesnt capture the time in msecs precision. Uncomment the line above when the support for msecs is added in the backend for event logs.
    return moment(dateTime).tz(timeZone).format('YYYY/MM/DD HH:mm:ss');
  }

  getCurrentDateTimeInSystemTimezone(timestamp?: number) {
    return new Date(timestamp || Date.now());
  }
}
