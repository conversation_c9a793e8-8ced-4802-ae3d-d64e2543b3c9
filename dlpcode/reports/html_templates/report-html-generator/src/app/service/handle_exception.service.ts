import { NotifyService } from '@/common/notify/service/notify.service';
import { TranslateService } from '@/common/translate/translate.service';
import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class HandleExceptionService {
  constructor(
    private notifyService: NotifyService,
    private translateService: TranslateService
  ) {}

  handleMessage(
    message: string | any[],
    fieldMapping: { [key: string]: string } = {}
  ) {
    if (Array.isArray(message)) {
      message.forEach((m: any) => {
        const loc = m.loc ?? [];

        let field: string = '';
        if (loc.length) field = loc[loc.length - 1];

        let msg = m.msg ?? '';

        let error = `${field}: ${msg}`;

        if (fieldMapping[field]) {
          const newField = this.translateService.lookup(fieldMapping[field]);
          error = error.replaceAll(field, newField);
        }

        this.notifyService.error(error);
      });
    } else if (typeof message == 'string') {
      this.notifyService.error(message);
    }
  }

  handleError(
    err: HttpErrorResponse,
    fieldMapping: { [key: string]: string } = {}
  ) {
    console.error('error occured : Error ', err?.error?.error);
    this.handleMessage(err?.error?.error, fieldMapping);
  }
}
