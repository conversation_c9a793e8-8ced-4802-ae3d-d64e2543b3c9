import {
  CopyQuarantineStatus,
  EScanStorageType,
} from '@/analytics_files/mock_data_structure';
import { FilterOperation, Range, SearchKey } from '@/common/table/table';
import { TranslateService } from '@/common/translate/translate.service';
import { EStorageType } from '@/dlp_dashboard/mock_data_structure';
import { Injectable } from '@angular/core';
import { isNumber } from 'lodash';
import { DateUtilService } from './date-util.service';

@Injectable({
  providedIn: 'root',
})
export class FormatService {
  constructor(
    private translateService: TranslateService,
    private dateService: DateUtilService
  ) {}

  ngOnDestroy() {}

  truncateLabel(label: string, length: number) {
    if (length < 3) length = 3;

    if (label.length < length) return label;
    else return [label.slice(0, length - 3), '...'].join('');
  }

  /**
   * This is used to convert search key to display text (ID = 'XXX')
   *
   * @param searchKey search key info
   */
  getSearchKeyDisplayText(searchKey: SearchKey, timeZone: string) {
    const { method, key, value } = searchKey;
    const columnName = this.translateService.lookup(key.label);

    switch (method) {
      case FilterOperation.Range:
        let start = (value.value as Range).start;
        let end = (value.value as Range).end;
        if (searchKey.value.convertToDateTime) {
          start = this.dateService.convertToGMTTimestamp(
            isNumber(start) ? start : parseInt(start),
            timeZone
          );
          end = this.dateService.convertToGMTTimestamp(
            isNumber(end) ? end : parseInt(end),
            timeZone
          );
        }
        return `${columnName} ${start} to ${end}`;
      default:
        let displayedValue;
        if (value.convertToDateTime) {
          const realValue = value.value;
          displayedValue = Array.isArray(realValue)
            ? realValue.map((x) =>
                this.dateService.convertToGMTTimestamp(
                  isNumber(x) ? x : parseInt(x),
                  timeZone
                )
              )
            : this.dateService.convertToGMTTimestamp(
                isNumber(realValue)
                  ? realValue
                  : parseInt(realValue.toString()),
                timeZone
              );
        } else {
          displayedValue = value.label;
        }
        return `${columnName} ${this.translateService
          .lookup(`tableFilter.${method}`)
          .toLowerCase()} ${displayedValue}`;
    }
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) {
      return '0';
    }

    if (bytes < 1024) {
      return '1 KB';
    }

    const KB = 1024;
    const MB = KB * 1024;
    const GB = MB * 1024;

    if (bytes < MB) {
      return `${Math.round(bytes / KB)} KB`;
    } else if (bytes < GB) {
      return `${Math.round(bytes / MB)} MB`;
    } else {
      return `${Math.round(bytes / GB)} GB`;
    }
  }

  /**
   * Get copy/quarantine status
   *
   * @param status status
   */
  getCopyQuarantineStatus(status: CopyQuarantineStatus) {
    switch (status) {
      case CopyQuarantineStatus.Success:
        return {
          svgIcon: 'check_circle_outline',
          color: '#60d927',
          tooltip: this.translateService.lookup('successful'),
        };
      case CopyQuarantineStatus.Failed:
        return {
          svgIcon: 'warning_outline',
          color: '#fd7614',
          tooltip: this.translateService.lookup('failed'),
        };
      case CopyQuarantineStatus.InProgress:
        return {
          matIcon: 'spinner',
          tooltip: this.translateService.lookup('in_progress'),
        };
      default:
        return '';
    }
  }

  /**
   * Get the type of scans
   */
  getScanStoreType(value: EScanStorageType): string {
    switch (value) {
      case EScanStorageType.prem:
        return this.translateService.lookup('scans.storage_type.prem');
      case EScanStorageType.smb:
        return this.translateService.lookup('scans.storage_type.smb');
      case EScanStorageType.cloud:
        return this.translateService.lookup('scans.storage_type.cloud');
      case EScanStorageType.gdrive:
        return this.translateService.lookup('scans.storage_type.gdrive');
      case EScanStorageType.aws:
        return this.translateService.lookup('scans.storage_type.aws');
      default:
        return this.translateService.lookup('scans.storage_type.unknown');
    }
  }

  /**
   * Get severity info
   *
   * @param value severity
   */
  getSeverity(value: string | number) {
    if (value === null || value === undefined)
      return { text: '- NA -', color: '' };
    switch (value) {
      case 0:
        return {
          text: 'Critical',
          color: '#ee3333',
        };
      case 1:
        return {
          text: 'High',
          color: '#ff8200',
        };
      case 2:
        return {
          text: 'Medium',
          color: '#ffd850',
        };
      case 3:
        return {
          text: 'Low',
          color: '#3fae29;',
        };
      default:
        return { text: '- NA -', color: '' };
    }
  }

  /**
   * Get severity info
   *
   * @param value severity
   */
  getSeverityByString(value: string) {
    if (value === null || value === undefined)
      return { text: '- NA -', color: '' };
    switch (value) {
      case 'High':
        return {
          text: 'High',
          color: '#ee3333',
        };
      case 'Medium':
        return {
          text: 'Medium',
          color: '#ff8200;',
        };

      case 'Low':
        return {
          text: 'Low',
          color: '#3fae29;',
        };
      default:
        return { text: '- NA -', color: '' };
    }
  }

  getIncidentStatus(status: number) {
    switch (status) {
      case 0:
        return { icon: 'fiber_new', color: '#2196F3', text: 'New' };
      case 1:
        return { icon: 'schedule', color: '#2196F3', text: 'In Process' };
      case 2:
        return { icon: 'check_circle', color: '#4CAF50', text: 'Closed' };
      case 3:
        return { icon: 'priority_high', color: '#D32F2F', text: 'Escalated' };
      default:
        return { icon: '', color: '', text: '---' };
    }
  }

  getIgnoredStatus(ignored: boolean) {
    if (ignored === null || ignored === undefined)
      return { text: '- NA -', color: '' };
    if (ignored) {
      return {
        text: 'Yes',
        svgIcon: 'check',
        tooltip: 'Marked as Ignored',
        color: '#51AE26',
      };
    } else {
      return {
        text: 'No',
        svgIcon: 'indeterminate',
        tooltip: 'Not Marked',
        color: '#9E9E9E',
      };
    }
  }

  getFalsePositiveStatus(false_positive: boolean) {
    if (false_positive === null || false_positive === undefined) return '';
    if (false_positive) {
      return {
        text: 'Yes',
        svgIcon: 'check',
        color: '#51AE26',
        tooltip: 'Marked as False Positive',
      };
    } else {
      return {
        text: 'No',
        svgIcon: 'indeterminate',
        color: '#9E9E9E',
        tooltip: 'Not False Positive',
      };
    }
  }

  getStorageType(value: EStorageType): { text: string; icon: string } {
    switch (value) {
      case EStorageType.prem: //Sharepoint OnPrem
        return {
          text: this.translateService.lookup('scans.storage_type.prem'),
          icon: 'sharepoint_onprem',
        };
      case EStorageType.cloud: //Sharepoint Cloud
        return {
          text: this.translateService.lookup('scans.storage_type.cloud'),
          icon: 'sharepoint_cloud',
        };
      case EStorageType.smb: // SMB
        return {
          text: this.translateService.lookup('scans.storage_type.smb'),
          icon: 'smb',
        };
      case EStorageType.aws: // AWS
        return {
          text: this.translateService.lookup('scans.storage_type.aws'),
          icon: 'aws',
        };
      case EStorageType.gdrive: // Google Drive
        return {
          text: this.translateService.lookup('scans.storage_type.google_drive'),
          icon: 'gdrive',
        };
      default:
        return {
          text: this.translateService.lookup('scans.storage_type.unknown'),
          icon: '',
        };
    }
  }

  /**
   * Get the HTML of popover content
   *
   * @param args the array of columns data
   */
  getPopoverContentHtml(...data: any[]) {
    const ret = ['<div class="body"><table>'];
    let max = 0;

    data.forEach((c) => {
      if (c.length > max) max = c.length;
    });
    const dataLength = data.length;

    for (let i = 0; i < max; i++) {
      ret.push('<tr class="row">');
      for (let j = 0; j < dataLength; j++) {
        ret.push(`<td>${data[j]?.[i] ?? ''}</td>`);
      }
      ret.push('</tr>');
    }
    ret.push('</table></div>');

    return ret.join('');
  }
}
