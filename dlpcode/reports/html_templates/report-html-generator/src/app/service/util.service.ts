import { NotifyService } from '@/common/notify/service/notify.service';
import { TranslateService } from '@/common/translate/translate.service';
import { Injectable } from '@angular/core';
import { AbstractControl, FormArray, FormControl, FormGroup, ValidatorFn } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class UtilService {
  constructor(private notifyService: NotifyService, private translateService: TranslateService) {}

  /**
   * Mark touched and dirty for form controls
   *
   * @param control form group, form array or form control
   * @param emitEvent trigger emitEvent or not
   */
  markFormTouchedAndDirty(control: AbstractControl<any, any>, emitEvent: boolean = false) {
    if (!control) {
      return;
    }
    if (control instanceof FormGroup || control instanceof FormArray) {
      for (const subControl of Object.values(control.controls)) {
        this.markFormTouchedAndDirty(subControl);
      }
    }
    control.markAsTouched();
    control.markAsDirty();
    control.updateValueAndValidity({ onlySelf: true, emitEvent: emitEvent });
  }

  /**
   * Get enumeration key by value
   */
  getEnumKeyByValue(enumeration: any, value: any) {
    return Object.keys(enumeration).find((x) => enumeration[x] === value);
  }

  /**
   * Check if the text-overflow ellipsis is active
   *
   * @param $element element
   * @returns true/false
   */
  isEllipsisActive($element: HTMLElement): boolean {
    if (!$element) {
      return false;
    }
    return $element?.offsetWidth < $element?.scrollWidth;
  }

  escapeCsv(value: string): string {
    let chars = '=+-@\t\r';
    let inc = chars.indexOf(value[0]) != -1;
    value = value.replace(/"/g, '""');

    if (inc) return `"'${value}"`;
    else return `"${value}"`;
  }

  /**
   * Manually check the validators for the form control
   *
   * @param value value
   * @param validators validators
   * @returns error messages
   */
  validateFormControl(value: any, validators: ValidatorFn | ValidatorFn[]): any {
    const control = new FormControl();

    control.setValue(value);
    control.setValidators(validators);
    control.updateValueAndValidity({ emitEvent: false });

    return control.errors;
  }

  /**
   * Copy a given string to the clipboard
   */
  copy(str: string, showSuccessMessage = true) {
    const input = document.createElement('textarea');
    input.value = str;
    document.body.appendChild(input);
    input.select();

    navigator.clipboard
      .writeText(input.value)
      .then(() => {
        if (showSuccessMessage) {
          this.notifyService.success(this.translateService.lookup('copy_success'));
        }
      })
      .catch((err) => {});

    document.body.removeChild(input);
  }
}
