import { Injectable } from '@angular/core';

const docServerUrl = 'https://docs.fortinet.com/document/fortidata';
const fortidataAdmin = 'administration-guide';
const docVersion = '7.6.0';

@Injectable({
  providedIn: 'root',
})
export class HelpService {
  private _verison = '';

  constructor() {
    this.version = docVersion;
  }

  set version(v: string) {
    this._verison = v.split('.').slice(0, 3).join('.');
  }

  showHelp(content?: string) {
    const url = `${docServerUrl}/${this._verison}/${fortidataAdmin}?cshid=${content}`;
    window.open(url, '_blank');
  }
}
