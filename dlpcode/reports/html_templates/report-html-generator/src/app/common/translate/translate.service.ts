import { Injectable } from '@angular/core';
import { TranslateService as NgxTranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TranslateService {
  constructor(private ngxTranslateService: NgxTranslateService) {}

  /**
   * Switch language
   *
   * @param language language
   */
  switchLanguage(language: string): Observable<any> {
    return this.ngxTranslateService.use(language);
  }

  /**
   * Get current language
   *
   * @returns current language
   */
  getCurrentLanguage(): string {
    return this.ngxTranslateService.currentLang;
  }

  /**
   * Look up the translation text for a translation key.
   *
   * @param prop proporty
   * @param args arguments
   * @returns translation text
   */
  lookup(prop: string | string[], args?: any): string | any {
    const result = prop ? this.ngxTranslateService.instant(prop, args) : '';
    // return original string when the result is an objcet
    return _.isString(result) ? result : prop;
  }
}
