import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from './translate.service';

@Pipe({
  name: 'formControlError'
})
export class FormControlErrorPipe implements PipeTransform {
  constructor(private translateService: TranslateService) {}

  transform(errors: any): any {
    if (!errors) {
      return '';
    }

    const keys = Object.keys(errors);
    // Show the first error
    return keys.length ? this.translateService.lookup(`errorMessage.${keys[0]}`, errors[keys[0]]) : '';
  }
}
