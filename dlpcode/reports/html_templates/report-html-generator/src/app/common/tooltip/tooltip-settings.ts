import { ConnectionPositionPair } from '@angular/cdk/overlay';

export type TooltipPlacement =
  | 'top'
  | 'bottom'
  | 'right'
  | 'left'
  | 'topRight'
  | 'bottomRight'
  | 'bottomLeft'
  | 'topLeft';

export enum TooltipAnchor {
  /**
   * Place the tooltip relative to the element that has the tooltip.
   */
  Element = 'element',
  /**
   * Place the tooltip relative to the cursor location.
   */
  Cursor = 'cursor'
}

export interface TooltipSettings {
  /**
   * CSS class to add to the tooltip pop-up element
   */
  contentClass?: string;
  /**
   * Max width of the tooltip content.
   */
  maxWidth?: number;
  /**
   * Max height of the tooltip content.
   */
  maxHeight?: number;
  /**
   * Showing the tooltip by a period of time when hovering.
   */
  showDelay?: number;
  /**
   * Hiding the tooltip by a period of time when hovering.
   */
  hideDelay?: number;
  /**
   * Where to place to tooltip content relative to.
   */
  anchor?: TooltipAnchor;
  /**
   * Where the tooltip content should be placed relative to the anchor.
   */
  placement?: TooltipPlacement;
  /**
   * Override the placement with custom positions
   */
  customPlacement?: ConnectionPositionPair[];
}
