import { InjectionToken } from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';
import { TooltipSettings } from '../tooltip-settings';

export interface TooltipContent {
  /**
   * Content to display.
   */
  content?: string | SafeHtml;
  /**
   * Tooltip settings
   */
  settings?: TooltipSettings;
}

export const TOOLTIP_DATA_INJECTION_TOKEN = new InjectionToken('tooltipContent');
