import { AfterViewInit, Component, Inject } from '@angular/core';
import { isString } from 'lodash';
import { TOOLTIP_DATA_INJECTION_TOKEN, TooltipContent } from './tooltip-content';

@Component({
  selector: 'fd-tooltip-content',
  templateUrl: './tooltip-content.component.html',
  styleUrls: ['./tooltip-content.component.scss']
})
export class TooltipContentComponent implements AfterViewInit {
  /**
   * Whether this component should be shown
   */
  show = false;

  constructor(@Inject(TOOLTIP_DATA_INJECTION_TOKEN) public data: TooltipContent) {}

  /**
   * @internal
   */
  ngAfterViewInit(): void {
    setTimeout(() => (this.show = true));
  }

  getContent() {
    return isString(this.data.content)
      ? this.data.content.replaceAll('\r\n', '<br>')
      : this.data.content;
  }
}
