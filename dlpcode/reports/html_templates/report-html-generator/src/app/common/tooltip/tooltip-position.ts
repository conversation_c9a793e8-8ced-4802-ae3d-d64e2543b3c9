import { ConnectionPositionPair, Overlay } from '@angular/cdk/overlay';
import { ElementRef } from '@angular/core';
import { TooltipAnchor, TooltipPlacement, TooltipSettings } from './tooltip-settings';

export interface Offsets {
  x: number;
  y: number;
}

export const CURSOR_OFFSETS: Offsets = {
  x: 10,
  y: 10
};

export const ELEMENT_OFFSETS: Offsets = {
  x: 5,
  y: 0
};

export const PLACEMENT_POSITIONS: Record<TooltipPlacement, ConnectionPositionPair> = {
  top: {
    originX: 'center',
    originY: 'top',
    overlayX: 'center',
    overlayY: 'bottom'
  },
  bottom: {
    originX: 'center',
    originY: 'bottom',
    overlayX: 'center',
    overlayY: 'top'
  },
  right: {
    originX: 'end',
    originY: 'center',
    overlayX: 'start',
    overlayY: 'center'
  },
  left: {
    originX: 'start',
    originY: 'center',
    overlayX: 'end',
    overlayY: 'center'
  },
  topRight: {
    originX: 'end',
    originY: 'top',
    overlayX: 'start',
    overlayY: 'bottom'
  },
  bottomRight: {
    originX: 'end',
    originY: 'bottom',
    overlayX: 'start',
    overlayY: 'top'
  },
  topLeft: {
    originX: 'start',
    originY: 'top',
    overlayX: 'end',
    overlayY: 'bottom'
  },
  bottomLeft: {
    originX: 'start',
    originY: 'bottom',
    overlayX: 'end',
    overlayY: 'top'
  }
};

/**
 * Creates a position for tooltip overlays
 */
export function createPosition(origin: ElementRef | { x: number; y: number }, overlay: Overlay, settings: TooltipSettings) {
  return overlay
    .position()
    .flexibleConnectedTo(origin)
    .withFlexibleDimensions(false)
    .withPush(true)
    .withViewportMargin(10)
    .withPositions(getPositions(settings));
}

function getPositions(settings: TooltipSettings): ConnectionPositionPair[] {
  const { customPlacement, placement } = settings;

  if (customPlacement) {
    return customPlacement;
  } else if (placement) {
    return getPositionsForDefinedPlacement(settings);
  } else {
    throw new Error('"placement" or "customPlacement" is not specified');
  }
}

function getPositionsForDefinedPlacement(settings: TooltipSettings) {
  const positions: ConnectionPositionPair[] = [];
  const offsets = settings.anchor === TooltipAnchor.Cursor ? CURSOR_OFFSETS : ELEMENT_OFFSETS;

  for (const placement of getDefinedPlacements(settings)) {
    switch (placement) {
      case 'top':
        positions.push({
          ...PLACEMENT_POSITIONS.top,
          offsetX: offsets.x,
          offsetY: -offsets.y
        });
        break;
      case 'bottom':
        positions.push({
          ...PLACEMENT_POSITIONS.bottom,
          offsetX: offsets.x,
          offsetY: -offsets.y
        });
        break;
      case 'right':
        positions.push({
          ...PLACEMENT_POSITIONS.right,
          offsetX: offsets.x,
          offsetY: -offsets.y
        });
        break;
      case 'left':
        positions.push({
          ...PLACEMENT_POSITIONS.left,
          offsetX: offsets.x,
          offsetY: -offsets.y
        });
        break;
      case 'topRight':
        positions.push({
          ...PLACEMENT_POSITIONS.topRight,
          offsetX: offsets.x,
          offsetY: -offsets.y
        });
        break;
      case 'bottomRight':
        positions.push({
          ...PLACEMENT_POSITIONS.bottomRight,
          offsetX: offsets.x,
          offsetY: -offsets.y
        });
        break;
      case 'topLeft':
        positions.push({
          ...PLACEMENT_POSITIONS.topLeft,
          offsetX: offsets.x,
          offsetY: -offsets.y
        });
        break;
      case 'bottomLeft':
        positions.push({
          ...PLACEMENT_POSITIONS.bottomLeft,
          offsetX: offsets.x,
          offsetY: -offsets.y
        });
        break;

      default:
        break;
    }
  }
  return positions;
}

function getDefinedPlacements(settings: TooltipSettings) {
  const { placement } = settings;
  const placements = [placement];

  switch (placement) {
    case 'top':
      placements.push('right', 'bottom', 'left');
      break;
    case 'right':
      placements.push('bottom', 'left', 'top');
      break;
    case 'bottom':
      placements.push('left', 'top', 'right');
      break;
    case 'left':
      placements.push('top', 'right', 'bottom');
      break;
    case 'bottomRight':
      placements.push('topRight', 'bottomLeft', 'topLeft');
      break;
    case 'topRight':
      placements.push('bottomLeft', 'topLeft', 'bottomRight');
      break;
    case 'bottomLeft':
      placements.push('topLeft', 'bottomRight', 'topRight');
      break;
    case 'topLeft':
      placements.push('bottomRight', 'topRight', 'bottomLeft');
      break;
    default:
      break;
  }
  return placements;
}
