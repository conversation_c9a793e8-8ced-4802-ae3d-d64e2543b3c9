import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { Portal } from '@angular/cdk/portal';
import { ElementRef, NgZone } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { createPosition } from './tooltip-position';
import { TooltipAnchor, TooltipSettings } from './tooltip-settings';

export const DEFAULT_TOOLTIP_SETTINGS: TooltipSettings = {
  maxWidth: 600,
  maxHeight: 400,
  showDelay: 0,
  hideDelay: 50,
  anchor: TooltipAnchor.Element,
  placement: 'bottom'
};

export class Tooltip {
  /**
   * Check if the tooltip is visible
   */
  isVisible = false;

  private _isHovering = false;
  private _destroyedSubject = new Subject<void>();
  private _visibleSubject = new BehaviorSubject<boolean>(this.isVisible);
  private _hoveringSubject = new BehaviorSubject<boolean>(this._isHovering);

  /**
   * Observable that is emitted when destroyed.
   */
  destroyed = this._destroyedSubject.asObservable();
  /**
   * Observable of whether the tooltip is currently visible.
   */
  visible = this._visibleSubject.asObservable();
  /**
   * Observable of whether the tooltip content is currently being hovered.
   */
  hovering = this._hoveringSubject.asObservable();

  private _overlay: Overlay;
  private _zone: NgZone;
  private _overlayRef?: OverlayRef;
  private _elementRef?: ElementRef;
  private _showEvent?: MouseEvent;
  private _content?: Portal<any>;
  private _settings: TooltipSettings;
  private _queuedShowEvent?: MouseEvent;
  private _scrollListener?: (event: Event) => void;
  private _contentMouseEnterListener?: () => void;
  private _contentMouseLeaveListener?: () => void;
  private _showTimeout?: number;
  private _hideTimeout?: number;

  constructor(
    overlay: Overlay,
    zone: NgZone,
    event?: MouseEvent,
    element?: ElementRef,
    content?: Portal<any>,
    settings?: TooltipSettings
  ) {
    this._overlay = overlay;
    this._zone = zone;
    this._content = content;
    this._settings = { ...DEFAULT_TOOLTIP_SETTINGS, ...settings };

    this._showEvent = event;
    if (this._settings.anchor === TooltipAnchor.Element && element) {
      this._elementRef = element.nativeElement;
    }

    const classes = [''];
    if (this._settings.contentClass) {
      classes.push(this._settings.contentClass);
    }
    this._overlayRef = this._overlay.create({
      //maxWidth: this._settings.maxWidth,
      panelClass: classes,
      positionStrategy: this.createPosition()
    });
  }

  /**
   * Trigger the tooltip to be shown.
   */
  show(event?: MouseEvent) {
    const ref = this._overlayRef;
    if (!ref) {
      return;
    }

    const show = () => {
      this._zone.run(() => {
        if (!ref.hasAttached()) {
          ref.attach(this._content);
        }
        if (this._queuedShowEvent) {
          this._showEvent = this._queuedShowEvent;
          this.updatePosition();
        }
        this.clearShowTimeout();
        this.isVisible = true;
        this._visibleSubject.next(true);
        this.addContentListeners();
      });
    };

    this.clearHideTimeout();
    this.addScrollListener();

    if (event) {
      this._queuedShowEvent = event;
    }
    if (!this.isVisible) {
      if (!this._showTimeout || !this._elementRef) {
        clearTimeout(this._showTimeout);
        this._showTimeout = window.setTimeout(show, this._settings.showDelay);
      }
    } else if (!this.isVisible) {
      show();
    }
  }

  /**
   * Trigger the tooltip to be hidden.
   *
   * @param forceImmediate Force the tooltip to be hidden immediately
   */
  hide(forceImmediate = false) {
    const ref = this._overlayRef;
    if (ref) {
      const hide = () => {
        this._zone.run(() => {
          ref.detach();
          this.clearHideTimeout();
          this.removeScrollListener();
          this.isVisible = false;
          this._visibleSubject.next(false);
          this._isHovering = false;
          this._hoveringSubject.next(false);
          this.removeContentListeners();
        });
      };

      this.clearShowTimeout();

      if (!forceImmediate) {
        if (!this._hideTimeout) {
          this._hideTimeout = window.setTimeout(hide, this._settings.hideDelay);
        }
      } else {
        hide();
      }
    }
  }

  /**
   * Set the content that is inside the tooltip.
   *
   * @param content Content for the tooltip.
   */
  setContent(content: Portal<any>) {
    const ref = this._overlayRef;
    this._content = content;
    if (ref && ref.hasAttached()) {
      ref.detach();
      ref.attach(this._content);
    }
  }

  /**
   * Update the position of the tooltip. Should be called if the tooltip content changes after shown.
   */
  updatePosition() {
    const ref = this._overlayRef;
    if (ref && ref.hasAttached()) {
      ref.updatePositionStrategy(this.createPosition());
      ref.updatePosition();
    }
  }

  /**
   * Set whether a child tooltip's content is currently being hovered.
   */
  setChildTooltipHovering(childHovering: boolean) {
    if (childHovering) {
      this.show();
    } else {
      this.hide();
    }
  }

  /**
   * Destroy the tooltip.
   */
  destroy() {
    this.hide(true);
    this.removeContentListeners();
    if (this._overlayRef) {
      this._overlayRef.dispose();
      this._overlayRef = undefined;
    }
    this.clearShowTimeout();
    this.clearHideTimeout();
    this.removeScrollListener();
    this._destroyedSubject.next();
  }

  /**
   * Clear show timeout
   */
  private clearShowTimeout() {
    if (this._showTimeout) {
      clearTimeout(this._showTimeout);
      this._showTimeout = undefined;
    }
    this._queuedShowEvent = undefined;
  }

  /**
   * Clear hide timeout
   */
  private clearHideTimeout() {
    if (this._hideTimeout) {
      clearTimeout(this._hideTimeout);
      this._hideTimeout = undefined;
    }
  }

  /**
   * Add scroll listener
   */
  private addScrollListener() {
    if (!this._scrollListener) {
      this._scrollListener = (event: Event) => {
        if (!this.containElement(event.target as HTMLElement)) {
          if (this.isVisible) {
            this.hide(true);
          } else {
            this.clearShowTimeout();
            this.removeScrollListener();
          }
        }
      };
      window.addEventListener('scroll', this._scrollListener, { capture: true });
    }
  }

  /**
   * Remove scroll listener
   */
  private removeScrollListener() {
    if (this._scrollListener) {
      window.removeEventListener('scroll', this._scrollListener, { capture: true });
      this._scrollListener = undefined;
    }
  }

  /**
   * Add content listeners
   */
  private addContentListeners() {
    if (this._overlayRef && !this._contentMouseEnterListener && !this._contentMouseLeaveListener) {
      this._contentMouseEnterListener = () => {
        this._isHovering = true;
        this._hoveringSubject.next(true);
        this.show();
      };
      this._contentMouseLeaveListener = () => {
        this._isHovering = false;
        this._hoveringSubject.next(false);
        this.hide();
      };
      this._overlayRef.overlayElement.addEventListener(
        'mouseenter',
        this._contentMouseEnterListener
      );
      this._overlayRef.overlayElement.addEventListener(
        'mouseleave',
        this._contentMouseLeaveListener
      );
    }
  }

  /**
   * Remove content listeners
   */
  private removeContentListeners() {
    if (!this._overlayRef) {
      return;
    }
    if (this._contentMouseEnterListener) {
      this._overlayRef.overlayElement.removeEventListener(
        'mouseenter',
        this._contentMouseEnterListener
      );
      this._contentMouseEnterListener = undefined;
    }
    if (this._contentMouseLeaveListener) {
      this._overlayRef.overlayElement.removeEventListener(
        'mouseleave',
        this._contentMouseLeaveListener
      );
      this._contentMouseLeaveListener = undefined;
    }
  }

  /**
   * Check if the tooltip contains the element
   */
  private containElement(element: HTMLElement) {
    return this._overlayRef ? this._overlayRef.overlayElement.contains(element) : false;
  }

  /**
   * Create the position
   */
  private createPosition() {
    return createPosition(
      this._elementRef || this._showEvent || { x: 0, y: 0 },
      this._overlay,
      this._settings
    );
  }
}
