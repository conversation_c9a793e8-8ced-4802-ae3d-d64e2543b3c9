import { Overlay } from '@angular/cdk/overlay';
import { ComponentPortal, Portal, TemplatePortal } from '@angular/cdk/portal';
import {
  AfterContentInit,
  Directive,
  ElementRef,
  HostListener,
  Injector,
  Input,
  NgZone,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  TemplateRef,
  ViewContainerRef
} from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { Tooltip } from './tooltip';
import { TOOLTIP_DATA_INJECTION_TOKEN, TooltipContent } from './tooltip-content/tooltip-content';
import { TooltipContentComponent } from './tooltip-content/tooltip-content.component';
import { TooltipSettings } from './tooltip-settings';

/**
 * Parameters for creating and showing a tooltip
 */
interface TooltipParams {
  /**
   * Mouse event that triggered the tooltip.
   */
  event?: MouseEvent;
  /**
   * Element that the tooltip is bound to.
   */
  element?: ElementRef;
  /**
   * Tooltip content.
   */
  content?: Portal<any>;
  /**
   * Settings for the tooltip.
   */
  settings?: TooltipSettings;
}

/**
 * Attaches a tooltip an element.
 */
@Directive({
  selector: '[fdTooltip]'
})
export class FdToolipDirective implements AfterContentInit, OnChanges, OnDestroy {
  /**
   * Template reference for the tooltip content.
   */
  @Input('fdTooltip')
  content?: TemplateRef<any> | string;

  /**
   * Enable the this tooltip?
   */
  @Input('fdTooltipDisabled')
  disabled = false;

  /**
   * Settings that specify how the tooltip should be displayed.
   */
  @Input('fdTooltipSettings')
  settings?: TooltipSettings;

  private _portal?: Portal<any>;
  private _tooltip?: Tooltip;
  private _visibleTooltips = new Set<Tooltip>();
  private _hoveringTooltip?: Tooltip;

  constructor(
    private viewContainerRef: ViewContainerRef,
    private elementRef: ElementRef,
    private injector: Injector,
    private overlay: Overlay,
    private zone: NgZone
  ) {}

  /**
   * @internal
   */
  ngAfterContentInit() {
    if (this.content instanceof TemplateRef) {
      this._portal = new TemplatePortal(this.content, this.viewContainerRef);
    } else if (this.content) {
      const data: TooltipContent = { content: this.content, settings: this.settings };
      this._portal = new ComponentPortal(
        TooltipContentComponent,
        null,
        Injector.create({
          providers: [{ provide: TOOLTIP_DATA_INJECTION_TOKEN, useValue: data }],
          parent: this.injector
        })
      );
    }
  }

  /**
   * @internal
   */
  ngOnChanges(changes: SimpleChanges) {
    if ('content' in changes) {
      this.createPortal();
      if (this._tooltip) {
        if (this._portal) {
          this._tooltip.setContent(this._portal);
        } else {
          this._tooltip.destroy();
          this._tooltip = undefined;
        }
      }
    }
    if ('disabled' in changes && this.disabled && this._tooltip) {
      this._tooltip.hide(true);
    }
  }

  /**
   * @internal
   */
  ngOnDestroy() {
    if (this._tooltip) {
      this._tooltip.destroy();
      this._tooltip = undefined;
    }
  }

  /**
   * Listen on mousemove event.
   */
  @HostListener('mousemove', ['$event'])
  mouseMoveListener(event: MouseEvent) {
    if (this._portal && !this.disabled) {
      if (!this._tooltip) {
        if (!this.disabled) {
          this._tooltip = this.showTooltip({
            event,
            element: this.elementRef,
            content: this._portal,
            settings: this.settings
          });
        }
      } else {
        if (!this.disabled) {
          this._tooltip.show(event);
        }
      }
    }
  }

  /**
   * Listen on mouseleave evemt
   */
  @HostListener('mouseleave')
  mouseLeaveListener() {
    if (this._tooltip) {
      this._tooltip.hide();
    }
  }

  /**
   * Create the protal
   */
  private createPortal() {
    this._portal = undefined;
    if (this.content instanceof TemplateRef) {
      this._portal = new TemplatePortal(this.content, this.viewContainerRef);
    } else if (this.content) {
      const data: TooltipContent = { content: this.content };
      this._portal = new ComponentPortal(
        TooltipContentComponent,
        null,
        Injector.create({
          providers: [{ provide: TOOLTIP_DATA_INJECTION_TOKEN, useValue: data }],
          parent: this.injector
        })
      );
    }
  }

  /**
   * Create and show a tooltip
   */
  private showTooltip(params: TooltipParams): Tooltip {
    const tooltip = this.createTooltip(params);
    tooltip.show();
    return tooltip;
  }

  /**
   * Create a tooltip
   */
  private createTooltip(params: TooltipParams): Tooltip {
    const tooltip = new Tooltip(
      this.overlay,
      this.zone,
      params.event,
      params.element,
      params.content,
      params.settings
    );
    let parentTooltip: Tooltip | undefined;

    tooltip.visible.pipe(takeUntil(tooltip.destroyed)).subscribe((visible) => {
      if (visible) {
        this._visibleTooltips.add(tooltip);
        if (!parentTooltip && this._hoveringTooltip && this._hoveringTooltip !== tooltip) {
          parentTooltip = this._hoveringTooltip;
        }
      } else {
        this._visibleTooltips.delete(tooltip);
      }
    });

    tooltip.hovering.pipe(takeUntil(tooltip.destroyed)).subscribe((hovering) => {
      if (hovering) {
        this._hoveringTooltip = tooltip;
        if (parentTooltip) {
          parentTooltip.setChildTooltipHovering(true);
        }
      } else if (this._hoveringTooltip === tooltip) {
        this._hoveringTooltip = undefined;
        if (parentTooltip) {
          parentTooltip.setChildTooltipHovering(false);
        }
      }
    });

    return tooltip;
  }
}
