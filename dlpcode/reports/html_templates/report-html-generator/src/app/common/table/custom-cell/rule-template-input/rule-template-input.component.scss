:host {
  ::ng-deep {
    input {
      width: 60px;
      height: 28px;
    }
  }
}

.container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  background-color: transparent;

  &.match_count {
    .input-field {
      > span {
        padding-left: 55px;
      }
    }
  }

  &.confidence {
    .input-field {
      > span {
        padding-left: 125px;
      }
    }
  }
}

.input-field {
  display: flex;
  flex-direction: row;
  gap: 6px;
  align-items: center;
  width: auto;
}
