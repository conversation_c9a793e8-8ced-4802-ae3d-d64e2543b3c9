<div [matMenuTriggerFor]="popover" (menuOpened)="onMenuOpened()"></div>
<mat-menu
  #popover="matMenu"
  yPosition="below"
  class="multiple-items-popover table-filter-panel-popover"
>
  <div (click)="$event.stopPropagation()" *ngIf="settings">
    <div class="title">
      <div>{{ settings.columnName }}</div>
      <mat-icon (click)="close()" [svgIcon]="'close'" class="close"></mat-icon>
    </div>
    <div class="body">
      <div class="operations hidden">
        <mat-button-toggle-group [formControl]="operationControl">
          <mat-button-toggle
            *ngFor="let operation of settings.filterSettings.operations"
            value="{{ operation }}"
            [disableRipple]="true"
            >{{ 'tableFilter.' + operation | translate }}</mat-button-toggle
          >
        </mat-button-toggle-group>
      </div>

      <div
        *ngIf="operationControl.value === 'range'; else commonSetting"
        class="range-settings"
        [formGroup]="rangeFormGroup"
      >
        <!-- range -->
        <ng-container *ngIf="settings.filterSettings.isDateTime; else commonRangeTpl">
          <div class="setting">
            <input
              matInput
              #userInput
              formControlName="start"
              type="datetime-local"
              [attr.step]="settings.filterSettings.showSeconds ? 1 : undefined"
              (keydown)="onKeyDown($event)"
            />
            <mat-error
              *ngIf="rangeFormGroup.get('start').dirty && rangeFormGroup.get('start').invalid"
              >{{ rangeFormGroup.get('start').errors | formControlError }}</mat-error
            >
          </div>
          <span class="to">-</span>
          <div class="setting">
            <input
              matInput
              formControlName="end"
              type="datetime-local"
              [attr.step]="settings.filterSettings.showSeconds ? 1 : undefined"
              (keydown)="onKeyDown($event)"
            />
            <mat-error
              *ngIf="rangeFormGroup.get('end').dirty && rangeFormGroup.get('end').invalid"
              >{{ rangeFormGroup.get('end').errors | formControlError }}</mat-error
            >
          </div>
        </ng-container>
        <ng-template #commonRangeTpl>
          <div class="setting">
            <input
              matInput
              #userInput
              formControlName="start"
              placeholder="{{ 'start' | translate }}"
              (keydown)="onKeyDown($event)"
            />
            <mat-error
              *ngIf="rangeFormGroup.get('start').dirty && rangeFormGroup.get('start').invalid"
              >{{ rangeFormGroup.get('start').errors | formControlError }}</mat-error
            >
          </div>
          <span class="to">-</span>
          <div class="setting">
            <input
              matInput
              formControlName="end"
              placeholder="{{ 'end' | translate }}"
              (keydown)="onKeyDown($event)"
            />
            <mat-error
              *ngIf="rangeFormGroup.get('end').dirty && rangeFormGroup.get('end').invalid"
              >{{ rangeFormGroup.get('end').errors | formControlError }}</mat-error
            >
          </div>
        </ng-template>
      </div>

      <ng-template #commonSetting>
        <!-- date time -->
        <ng-container *ngIf="settings.filterSettings.isDateTime; else commonValueTpl">
          <input
            matInput
            #userInput
            [formControl]="valueControl"
            type="datetime-local"
            [attr.step]="settings.filterSettings.showSeconds ? 1 : undefined"
            (keydown)="onKeyDown($event)"
          />
          <mat-error *ngIf="valueControl.dirty && valueControl.invalid">{{
            valueControl.errors | formControlError
          }}</mat-error>
        </ng-container>

        <ng-template #commonValueTpl>
          <ng-container *ngIf="searchMappingsInfo.availableMappings?.length; else textInputTpl">
            <!-- search mappings -->
            <div class="chip-set">
              <div
                *ngFor="let s of searchMappingsInfo.selectedMappings"
                class="chip-row"
                (click)="removeSelectedSearchMapping(s)"
              >
                {{ s.label }}
                <button>
                  <mat-icon>cancel</mat-icon>
                </button>
              </div>
            </div>

            <mat-error *ngIf="searchMappingsInfo.errors">{{
              searchMappingsInfo.errors | formControlError
            }}</mat-error>

            <!-- search mapping list -->
            <fieldset
              *ngIf="searchMappingsInfo.availableMappings?.length"
              class="search-mapping-box"
            >
              <legend>{{ 'pleaseSelectValue' | translate }}</legend>
              <div class="search-mapping-list">
                <div
                  *ngFor="let s of searchMappingsInfo.availableMappings"
                  class="item"
                  (click)="toggleSearcMappingStatus(s)"
                  [ngClass]="{ selected: s.selected, 'label-item': s.labelItem }"
                >
                  <span [matTooltip]="s.label" matTooltipPosition="right">{{ s.label }}</span>
                  <mat-icon *ngIf="s.selected">check</mat-icon>
                </div>
              </div>
            </fieldset>
          </ng-container>

          <ng-template #textInputTpl>
            <input
              matInput
              #userInput
              [formControl]="valueControl"
              placeholder="{{ valuePlaceholder | translate }}"
              (keydown)="onKeyDown($event)"
            />
            <mat-error *ngIf="valueControl.dirty && valueControl.invalid">{{
              valueControl.errors | formControlError
            }}</mat-error>
          </ng-template>
        </ng-template>
      </ng-template>
    </div>
    <div class="footer">
      <button mat-stroked-button [disableRipple]="true" (click)="close()">
        {{ 'cancel' | translate | uppercase }}
      </button>
      <button mat-flat-button color="primary" [disableRipple]="true" (click)="save()">
        {{ 'apply' | translate | uppercase }}
      </button>
    </div>
  </div>
</mat-menu>
