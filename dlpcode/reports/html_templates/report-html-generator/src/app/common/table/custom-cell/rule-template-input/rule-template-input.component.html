<div class="container {{ type }}" *ngIf="form" [formGroup]="form" (click)="click($event)">
  <div formArrayName="collection" *ngFor="let c of collection.controls; let i = index" class="row">
    <div class="input-field" [formGroup]="convertFormGroupType(c)">
      <span>≥</span>
      <input
        class="criteria-value"
        matInput
        type="text"
        formControlName="value"
        [attr.maxlength]="maxLength"
        (keydown)="onKeydown($event)"
      />
      <mat-error *ngIf="c.get('value').dirty && c.get('value').invalid">{{
        c.get('value').errors | formControlError
      }}</mat-error>
    </div>
  </div>
</div>
