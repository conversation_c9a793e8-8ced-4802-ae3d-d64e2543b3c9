/**
 * "Equal" operation
 *
 * @param data row data
 * @param columnId column ID
 * @param operation operation info
 */
export function equal(data, columnId: string, operation): boolean {
  let result = false;
  const { columnValue, searchValue } = handleInputValues(data[columnId], operation.value);

  filterLoop: for (const c of columnValue) {
    for (const s of searchValue) {
      if (c === s) {
        result = true;
        break filterLoop;
      }
    }
  }

  return result;
}

/**
 * "Like" operation
 *
 * @param data row data
 * @param columnId column ID
 * @param operation operations
 */
export function like(data, columnId: string, operation): boolean {
  let result = false;
  const { columnValue, searchValue } = handleInputValues(data[columnId], operation.value);

  filterLoop: for (const c of columnValue) {
    for (const s of searchValue) {
      if (c.includes(s)) {
        result = true;
        break filterLoop;
      }
    }
  }

  return result;
}

/**
 * "Greater than" operation
 *
 * @param data row data
 * @param columnId column ID
 * @param operation operations
 */
export function greater(data, columnId: string, operation): boolean {
  let result = false;
  const { columnValue, searchValue } = handleInputValues(data[columnId], operation.value);

  filterLoop: for (const c of columnValue) {
    for (const s of searchValue) {
      if (c > s) {
        result = true;
        break filterLoop;
      }
    }
  }

  return result;
}

/**
 * "Greater than and equal" operation
 *
 * @param data row data
 * @param columnId column ID
 * @param operation operations
 */
export function greaterEqual(data, columnId: string, operation): boolean {
  let result = false;
  const { columnValue, searchValue } = handleInputValues(data[columnId], operation.value);

  filterLoop: for (const c of columnValue) {
    for (const s of searchValue) {
      if (c >= s) {
        result = true;
        break filterLoop;
      }
    }
  }

  return result;
}

/**
 * "Less than" operation
 *
 * @param data row data
 * @param columnId column ID
 * @param operation operations
 */
export function less(data, columnId: string, operation): boolean {
  let result = false;
  const { columnValue, searchValue } = handleInputValues(data[columnId], operation.value);

  filterLoop: for (const c of columnValue) {
    for (const s of searchValue) {
      if (c < s) {
        result = true;
        break filterLoop;
      }
    }
  }

  return result;
}

/**
 * "Less than and equap" operation
 *
 * @param data row data
 * @param columnId column ID
 * @param operation operations
 */
export function lessEqual(data, columnId: string, operation): boolean {
  let result = false;
  const { columnValue, searchValue } = handleInputValues(data[columnId], operation.value);

  filterLoop: for (const c of columnValue) {
    for (const s of searchValue) {
      if (c <= s) {
        result = true;
        break filterLoop;
      }
    }
  }

  return result;
}

/**
 * "Range" operation
 *
 * @param data row data
 * @param columnId column ID
 * @param operation operations
 */
export function range(data, columnId: string, operation): boolean {
  let result = false;
  const cValue = data[columnId];
  const columnValue = Array.isArray(cValue)
    ? cValue.map((x) => x.toString().toLowerCase())
    : [cValue.toString().toLowerCase()];

  for (const c of columnValue) {
    if (c >= operation.start.toString().toLowerCase() && c <= operation.end.toString().toLowerCase()) {
      result = true;
      break;
    }
  }

  return result;
}

/**
 * Format column and search values
 *
 * @param columnValue column value
 * @param searchValue search value
 */
function handleInputValues(
  cValue: string | number | string[] | number[],
  sValue: string | number | string[] | number[]
): { columnValue: string[]; searchValue: string[] } {
  const columnValue = Array.isArray(cValue)
    ? cValue.map((x) => x.toString().toLowerCase())
    : [cValue.toString().toLowerCase()];
  const searchValue = Array.isArray(sValue)
    ? sValue.map((x) => x.toString().toLowerCase())
    : [sValue.toString().toLowerCase()];

  return { columnValue, searchValue };
}
