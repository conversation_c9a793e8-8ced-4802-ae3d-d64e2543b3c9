import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';

@Component({
  selector: 'fd-edit',
  templateUrl: './edit.component.html',
  styleUrls: ['./edit.component.scss']
})
export class EditComponent {
  @ViewChild('editControl') editControl: ElementRef;
  /**
   * Title
   */
  @Input()
  title: string;

  /**
   * Setter of the value
   */
  @Input()
  set value(text: string) {
    this._value = text ?? '';
  }

  /**
   * Getter of the value
   */
  get value(): string {
    return this._value;
  }

  /**
   * Max length
   */
  @Input()
  maxLength: number;

  /**
   * Mat icon
   */
  @Input()
  matIcon?: string;

  /**
   * SVG icon
   */
  @Input()
  svgIcon?: string;

  /**
   * Icon size
   */
  @Input()
  iconSize: number | string;

  /**
   * Whether the value text should be hidden. Default: false
   */
  @Input()
  hideText?: boolean;

  /**
   * Icon position
   */
  @Input()
  iconPosition?: 'right' | 'left' = 'right';

  /**
   * Executed when Ok button is clicking
   */
  @Input()
  ok: (value: string) => void;

  /**
   * Emitted when Ok button is clicking
   */
  @Output()
  submitted = new EventEmitter<string>();

  /**
   * MatMenuTrigger
   */
  @ViewChild(MatMenuTrigger)
  trigger: MatMenuTrigger;

  /**
   * Value
   */
  private _value: string;

  /**
   * Changed value
   */
  private _changedValue: string;

  constructor(private elementRef: ElementRef) {
    const attr = this.elementRef.nativeElement.attributes;
    ['icon'].forEach((x) => {
      if (attr[x]) {
        this[x as keyof this] = attr[x];
      }
    });
  }

  onMenuOpened() {
    const textarea: HTMLTextAreaElement = this.editControl.nativeElement;
    textarea.focus();
    textarea.selectionStart = textarea.selectionEnd = textarea.value.length;
  }

  /**
   * Save
   */
  save(): void {
    const value = this._changedValue ?? this._value ?? '';
    this?.ok?.(value);
    this.submitted.emit(value);
    this.close();
  }

  /**
   * Close
   */
  close() {
    this.trigger.closeMenu();
  }

  /**
   * Listen on value change
   *
   * @param $event DOM event
   */
  onValueChange($event: Event): void {
    this._changedValue = ($event.target as any).value;
  }
}
