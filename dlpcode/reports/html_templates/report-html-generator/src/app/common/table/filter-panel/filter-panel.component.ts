import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Output,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import { MatMenuTrigger } from '@angular/material/menu';
import { isEmpty, orderBy } from 'lodash';
import { FilterOperation, FilterSettings, Range, SearchKey } from '../table';
import { DateUtilService } from '../../../service/date-util.service';

export interface FilterParam {
  columnName: string;
  columnId: string;
  filterSettings: FilterSettings;
  searchKey?: SearchKey;
}

@Component({
  selector: 'fd-table-filter-panel',
  templateUrl: './filter-panel.component.html',
  styleUrls: ['./filter-panel.component.scss'],
})
export class FilterPanelComponent implements AfterViewInit {
  /**
   * Filter panel setting
   */
  settings: FilterParam;

  /**
   * Emitted when double-clicking the row
   */
  @Output()
  apply = new EventEmitter<any>();

  /**
   * Emitted when the filter panel was opened
   */
  @Output()
  panelOpened = new EventEmitter<void>();

  /**
   * Operation form control
   */
  operationControl = new FormControl(FilterOperation.Equal);

  /**
   * Value form control
   */
  valueControl = new FormControl('', (control: AbstractControl) => {
    if (
      !this.settings ||
      this.operationControl.value === FilterOperation.Range ||
      this.searchMappingsInfo?.availableMappings?.length
    ) {
      return null;
    }

    let validators = this.settings.filterSettings.validators ?? [];
    if (!Array.isArray(validators)) {
      validators = [validators];
    }

    const val = control.value;
    const ct = new FormControl();
    ct.setValue(val);
    ct.setValidators([Validators.required, ...validators]);
    ct.updateValueAndValidity({ onlySelf: true, emitEvent: false });

    return ct.errors;
  });

  /**
   * Range form group
   */
  rangeFormGroup = this.fb.group({
    start: new FormControl('', (control: AbstractControl) => {
      if (
        !this.settings ||
        this.operationControl.value !== FilterOperation.Range
      ) {
        return null;
      }

      let validators = this.settings.filterSettings.validators ?? [];
      if (!Array.isArray(validators)) {
        validators = [validators];
      }

      const val = control.value;
      const ct = new FormControl();
      ct.setValue(val);
      ct.setValidators([Validators.required, ...validators]);
      ct.updateValueAndValidity({ onlySelf: true, emitEvent: false });

      return ct.errors;
    }),
    end: new FormControl('', (control: AbstractControl) => {
      if (
        !this.settings ||
        this.operationControl.value !== FilterOperation.Range
      ) {
        return null;
      }

      let validators = this.settings.filterSettings.validators ?? [];
      if (!Array.isArray(validators)) {
        validators = [validators];
      }

      const val = control.value;
      const ct = new FormControl();
      ct.setValue(val);
      ct.setValidators([Validators.required, ...validators]);
      ct.updateValueAndValidity({ onlySelf: true, emitEvent: false });

      return ct.errors;
    }),
  });

  /**
   * Menu trigger
   */
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;

  /**
   * The placeholder of value input
   */
  valuePlaceholder: string = 'value';

  /**
   * The info of search mappings
   */
  searchMappingsInfo: {
    /**
     * Available search mappings
     */
    availableMappings: {
      label: string;
      value: string;
      selected: boolean;
      labelItem: boolean;
    }[];
    /**
     * Selected search mappings
     */
    selectedMappings: {
      label: string;
      value: string;
      selected: boolean;
      labelItem: boolean;
    }[];
    /**
     * Errors
     */
    errors?: { [key: string]: boolean };
  } = { availableMappings: [], selectedMappings: [] };

  /**
   * Input elements
   */
  @ViewChildren('userInput')
  inputElements: QueryList<ElementRef>;

  constructor(private fb: FormBuilder, private dateService: DateUtilService) {}

  /**
   * @internal
   */
  ngAfterViewInit(): void {
    this.trigger.menuClosed.subscribe(() => this.clear());
  }

  /**
   * Open
   */
  open(settings: FilterParam) {
    if (!settings?.filterSettings) {
      this.close();
    }

    this.initData(settings);

    this.trigger.openMenu();
  }

  /**
   * Close
   */
  close() {
    this.trigger.closeMenu();
  }

  /**
   * Apply
   */
  save() {
    if (this.validate()) {
      return;
    }

    const result = this.getData();
    if (this.settings.searchKey) {
      result.prevSearchKey = this.settings.searchKey;
    }
    this.apply.emit(result);
    this.close();
  }

  /**
   * Toggle selected status of the search mapping
   *
   * @param item search mapping item
   */
  toggleSearcMappingStatus(item) {
    item.selected = !item.selected;
    this.searchMappingsInfo.errors = null;

    if (!this.settings.filterSettings.isMultiple) {
      // unselected other items in single value mode
      const selectedItem = this.searchMappingsInfo.selectedMappings.find(
        (x) => x.selected
      );
      if (selectedItem) {
        this.removeSelectedSearchMapping(selectedItem);
      }
    }

    if (item.selected) {
      this.searchMappingsInfo.selectedMappings.push(item);
      this.checkSelectedMappingsData();
    } else {
      this.removeSelectedSearchMapping(item);
    }
  }

  /**
   * Remove selected search mapping item from selectedMappings
   *
   * @param item search mapping item
   */
  removeSelectedSearchMapping(item) {
    item.selected = false;
    this.searchMappingsInfo.selectedMappings.splice(
      this.searchMappingsInfo.selectedMappings.indexOf(item),
      1
    );
    this.checkSelectedMappingsData();
  }

  /**
   * Listen on meun opened
   */
  onMenuOpened() {
    this.panelOpened.emit();
    setTimeout(() => {
      const elem = this.inputElements?.first?.nativeElement;
      if (elem) {
        elem.focus();
      }
    });
  }

  /**
   * Listeon on KeyDown event
   */
  onKeyDown(e: KeyboardEvent) {
    if (e.key.toLowerCase() === 'enter') {
      e.stopPropagation();
      e.preventDefault();
      this.save();
    }
  }

  /**
   * Initialize the data
   *
   * @param settings filter settings
   */
  private initData(settings: FilterParam) {
    const filterSettings = settings.filterSettings;

    // not allow filtering with an array in fortidata
    settings.filterSettings.isMultiple = false;
    this.settings = settings;

    const { isDateTime, operations } = filterSettings;
    if (isDateTime) {
      const currentDateTime = this.formatDate(
        this.dateService.getCurrentDateTimeInSystemTimezone()
      );
      this.rangeFormGroup.setValue({
        start: currentDateTime,
        end: currentDateTime,
      });

      this.valueControl.setValue(currentDateTime);
    }

    this.valuePlaceholder = filterSettings.isMultiple
      ? 'multiplePlaceholder'
      : 'value';
    if (operations?.length) {
      this.operationControl.setValue(operations[0]);
    }

    this.handleSearchMappings(filterSettings);

    // handle edit data
    this.initEditData(settings);
  }

  /**
   * Initialize data if search key is not empty.
   * This is used to edit the filter.
   *
   * @param settings filter parameter
   */
  private initEditData(settings: FilterParam) {
    const { searchKey, filterSettings } = settings;
    if (!searchKey) {
      return;
    }

    const value = searchKey.value.value;
    const method = searchKey.method;
    const { isDateTime } = filterSettings;
    switch (method) {
      case FilterOperation.Range:
        if (isDateTime) {
          this.rangeFormGroup.setValue({
            start: this.formatDate(new Date(+(value as Range).start * 1000)),
            end: this.formatDate(new Date(+(value as Range).end * 1000)),
          });
        } else {
          this.rangeFormGroup.setValue({
            start: (value as Range).start.toString(),
            end: (value as Range).end.toString(),
          });
        }
        break;
      default:
        if (filterSettings.searchMapping) {
          // handle search mapping
          let val = Array.isArray(value) ? value : [value];
          const selectedMappings = [];
          val.forEach((v) => {
            const item: { label: string; value: string; selected: boolean } =
              this.searchMappingsInfo.availableMappings.find(
                (mapping) => mapping.value === v
              );
            if (item) {
              item.selected = true;
              selectedMappings.push(item);
            }
          });
          this.searchMappingsInfo.selectedMappings = selectedMappings;
        } else {
          let val;
          if (isDateTime) {
            val = this.formatDate(new Date(+value * 1000));
          } else if (Array.isArray(value)) {
            val = value.join(',');
          } else {
            val = value;
          }
          this.valueControl.setValue(val);
        }
        break;
    }

    this.operationControl.setValue(method as FilterOperation);
  }

  /**
   * Clear form controls
   */
  private clear() {
    setTimeout(() => {
      this.valueControl.reset('');
      this.rangeFormGroup.reset({ start: '', end: '' });
      this.searchMappingsInfo = {
        availableMappings: [],
        selectedMappings: [],
      };
    }, 500);
  }

  /**
   * Validate form controls
   *
   * @returns has error
   */
  private validate(): boolean {
    const startControl = this.rangeFormGroup.get('start');
    const endControl = this.rangeFormGroup.get('end');

    [this.valueControl, startControl, endControl].forEach((control) => {
      control.markAsTouched();
      control.markAsDirty();
      control.updateValueAndValidity({ onlySelf: true, emitEvent: false });
    });

    this.checkSelectedMappingsData();

    return !isEmpty({
      ...this.valueControl.errors,
      ...startControl.errors,
      ...endControl.errors,
      ...this.searchMappingsInfo.errors,
    });
  }

  /**
   * Get data
   */
  private getData() {
    const method = this.operationControl.value;
    const { isDateTime, isMultiple } = this.settings.filterSettings;
    let ret: any = {
      columnName: this.settings.columnName,
      columnId: this.settings.columnId,
      method,
    };

    if (method === FilterOperation.Range) {
      const data = this.rangeFormGroup.getRawValue();
      if (isDateTime) {
        ret.value = {
          start: Math.floor(new Date(data.start).getTime() / 1000),
          end: Math.floor(new Date(data.end).getTime() / 1000),
        };
        ret.convertToDateTime = true;
      } else {
        ret.value = this.rangeFormGroup.getRawValue();
      }
    } else {
      if (isMultiple) {
        this.handleMultipleValues(ret);
      } else {
        this.handleSingleValue(ret);
      }
    }

    return ret;
  }

  /**
   * Handle multiple values
   *
   * @param ret result
   */
  private handleMultipleValues(ret) {
    if (this.searchMappingsInfo.availableMappings?.length) {
      const mappings = this.searchMappingsInfo.selectedMappings;
      ret.displayedValue = mappings.map((x) => x.label);
      ret.value = mappings.map((x) => x.value);
      return;
    }

    const { isNumber, isLowerCase } = this.settings.filterSettings;

    ret.displayedValue = this.valueControl.value.toString().trim();
    ret.value = ret.displayedValue.split(',').map((x) => {
      const val = x.trim();
      if (isNumber) {
        return parseFloat(val);
      }

      if (isLowerCase) {
        return val.toLowerCase();
      }

      return val;
    });
  }

  /**
   * Handle single value
   *
   * @param ret result
   */
  private handleSingleValue(ret) {
    if (this.searchMappingsInfo.availableMappings?.length) {
      const mapping = this.searchMappingsInfo.selectedMappings[0];
      ret.displayedValue = mapping.label;
      ret.value = mapping.value;
      return;
    }

    const { isNumber, isLowerCase, isDateTime } = this.settings.filterSettings;
    ret.displayedValue = this.valueControl.value.toString().trim();
    const displayedValue = ret.displayedValue;
    if (isNumber) {
      ret.value = parseFloat(displayedValue);
    } else if (isLowerCase) {
      ret.value = displayedValue.toLowerCase();
    } else if (isDateTime) {
      ret.value = Math.floor(new Date(displayedValue).getTime() / 1000);
      ret.convertToDateTime = true;
    } else {
      ret.value = displayedValue;
    }
  }

  /**
   * Handle search mappings
   *
   * @param filterSettings filter settings
   */
  private handleSearchMappings(filterSettings: FilterSettings) {
    const { searchMapping, searchMappingOrder } = filterSettings;
    if (!searchMapping) {
      return;
    }

    let mappings = Object.keys(searchMapping)
      .filter((key) => isNaN(Number(key)))
      .map(
        (key) =>
          ({
            label: key,
            value: searchMapping[key],
            labelItem: Boolean(filterSettings.displayItem?.includes(key)),
          } as any)
      );

    if (searchMappingOrder) {
      if (typeof searchMappingOrder === 'string') {
        mappings = orderBy(mappings, 'value', searchMappingOrder);
      } else {
        let newSearchMappingOrder;
        if (!Array.isArray(searchMappingOrder)) {
          newSearchMappingOrder = [searchMappingOrder];
        } else {
          newSearchMappingOrder = searchMappingOrder;
        }

        const keys = [];
        const orders = [];
        newSearchMappingOrder.forEach((o) => {
          const key = Object.keys(o)[0];
          keys.push(key === 'key' ? 'label' : key);
          orders.push(o[key]);
        });
        mappings = orderBy(mappings, keys, orders);
      }
    } else {
      // by default, order search mappings by key
      mappings = orderBy(mappings, 'label');
    }

    this.searchMappingsInfo.availableMappings = mappings;
  }

  /**
   * Check selected mappings data
   */
  private checkSelectedMappingsData() {
    if (
      this.searchMappingsInfo.availableMappings.length &&
      !this.searchMappingsInfo.selectedMappings.length
    ) {
      this.searchMappingsInfo.errors = { required: true };
    } else {
      this.searchMappingsInfo.errors = null;
    }
  }

  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = this.pad(date.getMonth() + 1); // JavaScript months are 0-based
    const day = this.pad(date.getDate());
    const hours = this.pad(date.getHours());
    const mins = this.pad(date.getMinutes());
    const seconds = this.settings.filterSettings.showSeconds
      ? this.pad(date.getSeconds())
      : '00';

    return `${year}-${month}-${day}T${hours}:${mins}:${seconds}`;
  }

  private pad(number: number): string {
    return number.toString().padStart(2, '0');
  }
}
