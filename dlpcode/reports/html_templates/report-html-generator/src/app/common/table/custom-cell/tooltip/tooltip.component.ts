import { TooltipSettings } from '@/common/tooltip/tooltip-settings';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  Input,
  SimpleChanges
} from '@angular/core';

/**
 * Because MatTooltip doesn't support HTML input, we need to create a new tooltip to support it.
 * We may use this tooltip instead of MatTooltip.
 */
@Component({
  selector: 'fd-tooltip',
  templateUrl: './tooltip.component.html',
  styleUrls: ['./tooltip.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TooltipComponent implements AfterViewInit {
  /**
   * Tooltip
   */
  @Input()
  tooltip: string = '';

  /**
   * Displayed content
   */
  @Input()
  set html(value) {
    this._html = value;
  }

  get contentElem() {
    return this.elementRef.nativeElement.querySelector('.content');
  }
  /**
   * Tooltip settings
   */
  @Input()
  settings: TooltipSettings;

  private _html;

  constructor(private elementRef: ElementRef) {}

  /**
   * @internal
   */
  ngAfterViewInit(): void {
    this.contentElem.insertAdjacentHTML('beforeend', this._html);
  }

  /**
   * @internal
   */
  ngOnChanges(changes: SimpleChanges): void {
    if ('html' in changes && !changes['html'].firstChange) {
      this.contentElem.innerHTML = '';
      this.contentElem.insertAdjacentHTML('beforeend', this._html);
    }
  }
}
