:host {
  display: block; // fix firefox can't show the loading-panel
  height: 100%;
  position: relative;

  .multi-value {
    cursor: pointer;
  }

  &:focus-visible {
    outline: none;
  }

  ::ng-deep {
    .mat-mdc-table {
      width: 100%;
      max-width: 100%;
      overflow-x: auto;
      overflow-y: auto;
      box-shadow: none;

      .mat-column-_select {
        width: 46px;
        text-align: right;
        padding-left: 20px;
      }

      .mat-column-_action {
        width: 50px;
        text-align: center;
      }

      .mat-column-_expand {
        min-width: 0;
        padding: 0;
      }

      .mat-column-add_rule_label {
        fd-table-cell {
          display: flex;
          align-items: center;
        }
      }

      .mdc-checkbox__background {
        height: 16px;
        width: 16px;
      }

      .mdc-checkbox {
        flex: 1 1 16px;
      }
    }

    tr.row-selected {
      background-color: #e6edf8;
    }

    th.mat-header-cell:first-of-type,
    td.mat-cell:first-of-type,
    td.mat-footer-cell:first-of-type {
      padding-left: 12px;
    }

    th.mat-header-cell,
    td.mat-cell,
    th.mat-mdc-header-cell,
    td.mat-mdc-cell {
      white-space: nowrap;
    }

    .mat-header-row,
    .mat-mdc-header-row {
      height: 40px;
    }

    .mat-header-cell,
    .mat-mdc-header-cell {
      height: 40px !important;
      min-height: 40px;
      padding: 5px 10px;
      box-sizing: border-box;
      font-weight: 600;
      line-height: 20px;
      font-size: 14px;
    }

    .mat-cell,
    .mat-mdc-cell {
      padding: 5px 10px;
      box-sizing: border-box;
      font-weight: 400;
      line-height: 20px;
      font-size: 14px;
    }

    //Any customized settings to this selector is not working. .link-cell is generic/table.scss is taking precedence over this selector.
    .icon-circle {
      width: 20px;
      height: 20px;
      border-radius: 50%;
    }

    //Any customized settings to this selector is not working. .link-cell is generic/table.scss is taking precedence over this selector.
    .icon-label-cell {
      display: flex;
      align-items: flex-end;
    }

    //Any customized settings to this selector is not working. .link-cell is generic/table.scss is taking precedence over this selector.
    .link-cell {
      cursor: pointer;
      text-decoration: solid;
      line-height: normal;
    }

    mat-paginator {
      .mat-mdc-paginator-range-actions {
        .mat-mdc-paginator-range-label {
          margin: 0 12px;
          white-space: nowrap;
        }
      }
    }
  }
}

.out-container {
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: auto;
  flex: 1;
}

.table-container {
  height: 100%;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
}

.menu-bar-container {
  display: flex;
  align-items: center;
  padding: 16px 20px; //9px 0px 3px;
  box-sizing: border-box;
  gap: 20px;
  overflow: auto hidden;
}

mat-paginator {
  z-index: 1;

  ::ng-deep {
    .mat-mdc-paginator-container {
      justify-content: flex-start;
      min-height: 60px;
      font-size: 14px;
    }
  }
}

.custom-actions-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 20px;

  .title {
    font-size: 14px;
    line-height: 16px;
    color: grey;
  }

  .actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  button {
    padding: 4px 0;
    height: auto;
    min-height: auto;

    ::ng-deep {
      .mat-mdc-menu-item-text {
        gap: 8px;
      }

      .mat-icon {
        margin-right: 0;
      }
    }
  }
}

.menu-buttons {
  white-space: nowrap;

  button[disabled] {
    opacity: 0.4;
  }

  mat-icon {
    height: 18px;
    width: 18px;
    font-size: 18px;
    color: #47535c;
  }
}

.no-wrap {
  // Added this to handle text getting wrapped to next line if the button label contains white space. Ex without this "Add Credential" is getting displayed as wrapped text.
  white-space: nowrap;
}

.top-right-buttons {
  display: flex;
  gap: 20px;
  flex-wrap: nowrap;
}

.search-box {
  display: flex;
  width: 100%;
  height: 32px;
  align-items: center;
  gap: 10px;
}

.search-container {
  box-sizing: border-box;
  border-radius: 4px;
  display: flex;
  align-items: center;
  overflow-y: auto;
  flex: 1;
  border: solid 1px #e5e5e5;
  height: 100%;

  .grouped-conditions-drop-down {
    min-width: auto;
    border-width: 0 1px 0 0;
    height: 28px !important;
    border-radius: 0;

    ::ng-deep {
      .mat-mdc-button-touch-target {
        height: 30px;
      }
    }
  }

  .search-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    height: calc(60% - 2px);
    width: 34px;
  }

  input {
    margin: 0;
    padding-left: 0;
  }

  .search-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    height: calc(60% - 2px);
    width: 34px;
  }

  input,
  input:focus {
    border: none;
    outline: none;
    font-size: 15px;
    height: 90%;
    background: transparent;
  }

  input:focus {
    box-shadow: none;
  }

  .mat-form-field {
    flex: 1;
  }

  .mat-chip-list,
  .mat-mdc-chip-grid {
    height: 36px;
    display: flex;
    overflow-x: auto;
    margin-right: 6px;

    .mdc-evolution-chip-set .mdc-evolution-chip-set__chips {
      margin: 0px;
    }
  }
}

.chip-container {
  display: flex;
}

.info-icon {
  cursor: help;
}

.chip-set {
  height: 26px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 6px;
  flex-wrap: wrap;
  gap: 8px;
  overflow: hidden auto;
}

.chip-row {
  display: flex;
  flex-wrap: nowrap;
  padding: 0 12px;
  height: 26px;
  background-color: #e0e0e0;
  border-radius: 16px;
  align-items: center;
  gap: 5px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: opacity 0.25s;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;

  &:hover {
    opacity: 0.8;
  }

  button {
    align-items: center;
    background: none;
    border: none;
    display: inline-flex;
    justify-content: center;
    outline: none;
    padding: 0;
    text-decoration: none;
    cursor: pointer;
    opacity: 0.5;
    transition: opacity 0.25s;

    mat-icon {
      width: 18px;
      height: 18px;
      font-size: 18px;
    }

    &:hover {
      opacity: 0.9;
    }
  }
}

.input-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding-left: 12px;
}

.search-icon {
  transition: opacity 0.25s;

  &:hover {
    opacity: 0.6;
  }
}

.menu-error-message {
  padding-right: 25px;
  text-align: right;

  .message {
    font-size: 14px;
    color: #ee3333;
  }
}

.list-table {
  ::ng-deep {
    thead {
      th {
        top: 0;

        &.mat-column-blank {
          width: 100%;
        }
      }
    }

    tbody {
      tr {
        &.hide {
          opacity: 0;
          visibility: collapse;
        }
      }

      .group-row {
        cursor: pointer;
        background-color: #e7eaef;

        .group-cell {
          display: flex;
          gap: 6px;
          align-items: center;

          .expand-icon {
            transform: rotate(0deg);
            transition: transform 0.1s;
          }

          &.expand {
            .expand-icon {
              transform: rotate(-180deg);
            }
          }

          span {
            font-weight: 600;
          }

          > span {
            display: flex;
            align-items: center;
          }
        }

        &.default-cursor {
          cursor: default;
        }
      }

      .custom-clickable-row {
        &:hover {
          box-shadow: -4px 2px 5px rgba(0, 0, 0, 0.1);
          transform: scale(1);
          cursor: pointer;
        }
      }

      td {
        &.center {
          text-align: center;
          justify-content: center;

          fd-table-cell {
            > * {
              text-align: center;
              justify-content: center;
            }
          }
        }

        &.right {
          text-align: right;
          justify-content: flex-end;

          fd-table-cell {
            > * {
              text-align: right;
              justify-content: flex-end;
            }
          }
        }

        &.line-break {
          white-space: normal;
        }

        &.break-word {
          width: 100%;
          word-break: break-word;
          white-space: normal !important;
        }
      }
    }
  }
}

.cdk-drag-preview {
  opacity: 0.8;
  border: none;
  font-size: 14px;
  font-weight: 400;
}

.cdk-drag:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.light-theme {
  :host {
    ::ng-deep {
      mat-paginator {
        ::ng-deep {
          .mat-mdc-text-field-wrapper {
            background-color: #f7f9f9;

            .mdc-notched-outline__leading,
            .mdc-notched-outline__trailing {
              border: 0;
            }
          }
        }
      }

      .columns-drop-down {
        background-color: #f7f9f9;

        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
          color: rgba(0, 0, 0, 0.54);
        }
      }

      .search-container {
        background-color: #f7f9f9;
      }
    }
  }
}

@for $i from 1 to 5 {
  .expand-#{$i} {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s linear;

    td.indent {
      padding-left: $i * 25px;
    }
  }
}

.expand-icon {
  transform: rotate(-180deg);
  transition: transform 0.2s, opacity 0.2s;
  cursor: pointer;

  &.expand {
    transform: rotate(0);
  }

  &:hover {
    opacity: 0.6;
  }
}

@media (max-width: 720px) {
  :host {
    ::ng-deep mat-paginator {
      .mat-mdc-paginator-page-size-label {
        display: none;
      }
    }
  }
}

@media (max-width: 630px) {
  :host {
    ::ng-deep mat-paginator {
      .mat-mdc-paginator-range-label {
        margin: 0;
      }
    }
  }
}

@media (max-width: 575px) {
  :host {
    ::ng-deep mat-paginator {
      .mat-mdc-paginator-range-actions {
        gap: 8px;
      }

      .mat-mdc-icon-button.mat-mdc-button-base {
        width: auto;
        padding: 0;
      }

      .mat-mdc-paginator-page-size-select {
        width: 60px;
      }
    }
  }
}

::ng-deep {
  .fd-table {
    .mat-mdc-row {
      height: 40px;
    }

    .icon-label-cell {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 6px;
    }

    //circle hieght and width as per UX spec is 12px
    .icon-circle {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

    .icon-cell {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .mat-icon {
      height: 16px;
      width: 16px;
      font-size: 16px;
    }

    .limit200 {
      max-width: 200px;
      overflow: hidden;
      display: inline-block;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .link-cell {
      cursor: pointer;
      text-decoration: none;
      color: #2563bf;
    }

    .link-cell-black {
      cursor: pointer;
      transition: opacity 0.15s;

      &:hover {
        opacity: 0.5;
      }
    }

    .top-right-buttons {
      > button:not(:last-child) {
        margin-right: 5px;
      }

      button.mat-unthemed {
        border: 1px solid rgba(0, 0, 0, 0.12);
      }
    }

    > div {
      &:focus-visible {
        outline: none;
      }
    }

    .chip-cell {
      .chip {
        display: inline-flex;
        align-items: center;
        padding: 1px 12px;
        height: 24px;
        background-color: #d7dadd;
        border-radius: 16px;
        -webkit-user-select: none;
        user-select: none;
      }
    }
  }

  .table-columns-drop-down-menu {
    &.mat-mdc-menu-panel {
      padding: 16px 0;
      max-width: max-content;
      box-shadow: 0px 5px 10px 0px #00000033 !important;

      .col-menu-item {
        flex-direction: row;
        padding: 0 16px;

        .mat-mdc-menu-item-text {
          gap: 16px;
          display: flex;
          align-items: center;
        }

        &.disabled {
          opacity: 0.5;
          pointer-events: none;
        }
      }

      .top-config {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;

        .col-menu-item .menu-text,
        .reset {
          transition: opacity 0.2s;

          &:hover {
            opacity: 0.7;
          }
        }

        .col-menu-item {
          padding-right: 0;
          min-width: auto;

          .menu-text {
            font-weight: 700;
          }

          &:hover {
            background-color: inherit;
          }
        }

        .divider {
          height: 20px;
          border-left: 1px solid #222222;
        }

        .reset {
          padding-right: 16px;
          white-space: nowrap;
          cursor: pointer;
          font-weight: 700;
          color: #4a8bed;
        }
      }
    }
  }

  .table-grouped-conditions-menu {
    display: flex;
    flex-direction: column;
    padding: 8px 0;
    box-shadow: 0px 5px 10px 0px #00000033 !important;

    .item {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 0 8px;
      height: 32px;
      cursor: pointer;

      .menu-text {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: normal;
        flex: 1;
      }

      mat-icon {
        height: 16px;
        width: 16px;
        margin: 0;
        visibility: hidden;
      }

      &:hover {
        background-color: #f0f0f0;
      }

      &.selected {
        color: #2563bf;
        background-color: #e6edf8;

        mat-icon {
          visibility: visible;
        }
      }
    }
  }

  .fd-table,
  .table-columns-drop-down-menu.mat-mdc-menu-panel {
    .mdc-checkbox {
      margin: 0;
      padding: 0;
      height: 16px;
      width: 16px;

      input {
        top: 0;
        left: 0;
        right: auto;
        height: 16px;
        width: 16px;
      }
    }

    .mat-mdc-checkbox-touch-target,
    .mat-mdc-checkbox-ripple,
    .mdc-checkbox__ripple {
      display: none;
    }

    .mdc-checkbox__background {
      height: 16px;
      width: 16px;
      border-radius: 3px;
      top: 0 !important;
      left: 0 !important;
    }
  }

  .light-theme {
    .table-columns-drop-down-menu.mat-mdc-menu-panel {
      .mdc-checkbox__background {
        border: 1px solid #47535c;
      }

      .mdc-checkbox {
        .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
        .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background,
        .mdc-checkbox__native-control[data-indeterminate='true']:enabled
          ~ .mdc-checkbox__background {
          background-color: #4a8bed;
        }
      }
    }
  }
}
