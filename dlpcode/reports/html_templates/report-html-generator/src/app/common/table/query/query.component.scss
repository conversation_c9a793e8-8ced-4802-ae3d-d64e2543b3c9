::ng-deep {
  .table-query-popover {
    .mat-tab-nav-panel {
      overflow: hidden;
    }

    .mat-mdc-tab.mdc-tab--active {
      font-weight: 700;
    }

    .mat-mdc-tab-body-wrapper {
      height: 100%;
    }

    .mat-mdc-tab-labels {
      max-width: 200px;
      height: 40px;
    }

    .mat-mdc-tab-list {
      user-select: none;
      padding: 8px 0 0; /* Adjusted padding */
      transform: translateX(0) !important;

      .mat-mdc-tab:first-child {
        margin-left: 20px; /* adds margin to the first tab */
      }
    }

    .mat-mdc-tab-label-container {
      background-color: #e6f1fd;
      text-align: center;
    }

    .mat-mdc-tab {
      padding: 0 10px 0 10px;
      text-align: center;
      height: 40px !important;
      width: 140px;

      .mdc-tab-indicator__content {
        border: none;
        height: 40px;
      }

      .mdc-tab__content {
        line-height: 48px;
      }

      &.mdc-tab--active {
        border-top-left-radius: 20px;
        background-color: #fff;
      }
    }
  }
}

mat-tab-group {
  height: 100%;
  flex-grow: 1;
  overflow: hidden;
}

.list {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  gap: 3px;
  max-height: 280px;
}

.new-query {
  .input-box {
    display: flex;
    width: 100%;
    flex-direction: column;
  }

  .item {
    display: inline-flex;
    align-items: center;
    padding: 4px 6px;
    font-size: 14px;
    transition: background-color 0.1s;
    cursor: pointer;

    span {
      line-height: 24px;
      max-width: 500px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    &:hover,
    &.selected {
      background-color: #f0f0f0;
    }
  }

  &.disabled {
    .list {
      cursor: not-allowed;
    }

    .item {
      pointer-events: none;
      opacity: 0.6;
    }
  }
}

.existing-query {
  display: flex;
  flex-direction: column;
  position: relative;

  .list {
    flex: 1;
    padding: 6px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
  }

  .footer {
    display: flex;
    padding: 6px 16px;

    button {
      margin-left: auto;
    }
  }

  .no-existing-queries {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
  }
}

input {
  height: 32px;
  background-color: #f7f9f9;
  border: 1px solid #e5e5e5;
  border-radius: 2px;
}

.body {
  min-width: 460px;
  min-height: 300px;
}
