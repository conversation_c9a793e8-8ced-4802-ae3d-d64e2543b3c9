import { MaterialModule } from '@/material.module';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { PortalModule } from '@angular/cdk/portal';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonTooltipModule } from '../tooltip/tooltip.module';
import { CommonTranslateModule } from '../translate/translate.module';
import { AddQueryComponent } from './add-query/add-query.component';
import { CellComponent } from './cell/cell.component';
import { CheckboxComponent } from './custom-cell/checkbox/checkbox.component';
import { CollaboratorsComponent } from './custom-cell/collaborators/collaborators.component';
import { EditComponent } from './custom-cell/edit/edit.component';
import { IconComponent } from './custom-cell/icon/icon.component';
import { LabelIconComponent } from './custom-cell/label-icon/label-icon.component';
import { LabelsComponent } from './custom-cell/labels/labels.component';
import { MultipleItemsPopoverComponent } from './custom-cell/multiple-items-popover/multiple-items-popover.component';
import { RuleTemplateInputComponent } from './custom-cell/rule-template-input/rule-template-input.component';
import { SelectComponent } from './custom-cell/select/select.component';
import { ShareableLinksComponent } from './custom-cell/shareable-links/shareable-links.component';
import { TooltipComponent } from './custom-cell/tooltip/tooltip.component';
import { FilterPanelComponent } from './filter-panel/filter-panel.component';
import { PageSelectorComponent } from './page-selector/page-selector.component';
import { QueryComponent } from './query/query.component';
import { TableComponent } from './table/table.component';

@NgModule({
  declarations: [
    TableComponent,
    CellComponent,
    TooltipComponent,
    IconComponent,
    EditComponent,
    MultipleItemsPopoverComponent,
    FilterPanelComponent,
    QueryComponent,
    SelectComponent,
    LabelIconComponent,
    CheckboxComponent,
    LabelsComponent,
    RuleTemplateInputComponent,
    PageSelectorComponent,
    AddQueryComponent,
    CollaboratorsComponent,
    ShareableLinksComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PortalModule,
    DragDropModule,
    CommonTranslateModule,
    CommonTooltipModule,
    MaterialModule
  ],
  exports: [
    TableComponent,
    CellComponent,
    TooltipComponent,
    MultipleItemsPopoverComponent,
    IconComponent,
    EditComponent,
    FilterPanelComponent,
    SelectComponent,
    LabelIconComponent,
    CheckboxComponent,
    RuleTemplateInputComponent,
    CollaboratorsComponent,
    ShareableLinksComponent
  ]
})
export class CommonTableModule {}
