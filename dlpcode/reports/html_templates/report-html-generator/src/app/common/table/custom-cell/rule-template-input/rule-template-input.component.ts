import { Component, ElementRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, ValidatorFn } from '@angular/forms';
import { Observable, Subscription } from 'rxjs';

export interface ConditionsParam {
  action: 'change_status' | 'update_value';
  data: any;
}

export interface FocusoutParam {
  type: 'match_count' | 'confidence';
  id: string;
  value: number;
}

@Component({
  selector: 'fd-rule-template-input',
  templateUrl: './rule-template-input.component.html',
  styleUrls: ['./rule-template-input.component.scss']
})
export class RuleTemplateInputComponent implements OnInit, OnDestroy {
  /**
   * Form group
   */
  form: FormGroup;

  /**
   * Collection
   */
  get collection(): FormArray {
    return this.form.get('collection') as FormArray;
  }

  /**
   * Input data
   */
  @Input()
  data: any;

  /**
   * For observing conditions
   */
  @Input()
  conditionsObservable: Observable<ConditionsParam>;

  /**
   * Executed the function after the input lost focus
   */
  @Input()
  validators?: ValidatorFn | ValidatorFn[];

  /**
   * Max length of the input
   */
  @Input()
  maxLength: number;

  /**
   * The criteria type
   */
  @Input()
  type: 'match_count' | 'confidence';

  /**
   * Executed the function after the input blured
   */
  @Input()
  focusout: (data: { type: 'match_count' | 'confidence'; id: string; value: number }) => void;

  /**
   * The subscription of conditions
   */
  private _conditionsSubscription: Subscription;

  constructor(private fb: FormBuilder, private elementRef: ElementRef) {}

  /**
   * @internal
   */
  ngOnInit(): void {
    this.form = this.fb.group({
      collection: this.fb.array([])
    });

    this.data.types.forEach((x) => {
      this.collection.push(
        this.fb.group({
          value: new FormControl(
            {
              value:
                this.data.match_criteria[x]?.[this.type] ?? (this.type === 'match_count' ? 1 : 0),
              disabled: !this.data.editable
            },
            this.validators
          )
        })
      );
    });

    this._conditionsSubscription = this.conditionsObservable.subscribe((param: ConditionsParam) => {
      if (param.action === 'change_status') {
        this.checkConditionStatus(param.data);
      } else {
        this.checkCriteriaValue(param.data);
      }
    });
  }

  /**
   * @internal
   */
  ngAfterViewInit() {
    setTimeout(() => {
      const $inputs = this.elementRef.nativeElement.querySelectorAll('input[type="text"]');
      for (let i = 0; i < $inputs.length; i++) {
        $inputs[i].addEventListener('focusout', (e) => {
          this.updateCriteriaValue(i);
        });
      }
    });
  }

  /**
   * @internal
   */
  ngOnDestroy() {
    this._conditionsSubscription.unsubscribe();
  }

  /**
   * listen on keydown event
   *
   * @param e event
   */
  onKeydown(e): void {
    if (e.keyCode === 32) {
      e.currentTarget.stopPropagation();
    }

    if ([9, 13].includes(e.keyCode)) {
      e.currentTarget.blur();
    }
  }

  /**
   * Click
   */
  click(e) {
    e.stopPropagation();
  }

  /**
   * Convert the type to FormGroup
   */
  convertFormGroupType(c) {
    return c as FormGroup;
  }

  /**
   * Handle condition status
   */
  private checkConditionStatus(conditions: any[]) {
    const condition = conditions.find((x) => x.cond_name === this.data.cond_name);
    const status = !!condition.status;
    if (this.data.editable !== status) {
      this.data.editable = status;
      this.collection[status ? 'enable' : 'disable']();
    }
  }

  /**
   * Update value in the row
   */
  private updateCriteriaValue(index: number) {
    if (this.collection.invalid) {
      return;
    }

    // update value if there is no error
    const id = this.data.types[index];
    const value = +this.collection.controls[index].value.value;
    this.data.match_criteria[id][this.type] = value;
    this.focusout?.({
      type: this.type,
      id,
      value
    });
  }

  /**
   * Check match count/confidence if it was changed in another row
   */
  private checkCriteriaValue(data: FocusoutParam) {
    if (data.type !== this.type || !this.data.types.includes(data.id)) {
      return;
    }

    const control = this.collection.controls[this.data.types.indexOf(data.id)].get('value');
    if (control.value !== data.value) {
      control.setValue(data.value);
    }
  }
}
