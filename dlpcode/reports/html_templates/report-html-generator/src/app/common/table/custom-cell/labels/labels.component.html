<button [matMenuTriggerFor]="menu" (click)="handler($event)">
  {{ count }}
</button>
<mat-menu #menu="matMenu">
  <div class="sensitive-data-labels-tooltip">
    <div class="category" *ngIf="labels?.predefine?.length && labels.predefine.length !== 0">
      <div class="header">{{ 'sensitive_data.label_predefined' | translate }}:</div>
      <div class="items" *ngFor="let item of labels.predefine">
        {{ item }}
      </div>
    </div>
    <div class="category" *ngIf="labels?.custom?.length && labels.custom.length !== 0">
      <div class="header">{{ 'sensitive_data.label_custom' | translate }}:</div>
      <div class="items" *ngFor="let item of labels.custom">
        {{ item }}
      </div>
    </div>
    <div class="category" *ngIf="labels?.ml?.length && labels.ml.length !== 0">
      <div class="header">{{ 'sensitive_data.label_ml' | translate }}:</div>
      <div class="items" *ngFor="let item of labels.ml">
        {{ item }}
      </div>
    </div>
  </div>
</mat-menu>
