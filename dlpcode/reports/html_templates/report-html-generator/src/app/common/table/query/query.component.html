<div [matMenuTriggerFor]="popover"></div>
<mat-menu #popover="matMenu" yPosition="below" class="multiple-items-popover table-query-popover">
  <div (click)="$event.stopPropagation()" *ngIf="!loading">
    <div class="body new-query">
      <div class="input-box">
        <input
          placeholder="Query"
          #searchInput
          matInput
          class="search-input"
          aria-label="search filter"
          [formControl]="searchControl"
          (keyup)="isTyping = searchControl.value !== ''"
        />
      </div>
      <div class="list">
        <div *ngFor="let col of filteredColumns | async" class="item" (click)="selectColumn(col)">
          <span>{{ col?.filterSettings?.displayedKey ?? col.langKey ?? col.id | translate }}</span>
        </div>
        <div *ngFor="let col of individualColumns | async" class="item" (click)="selectColumn(col)">
          <span>{{ col?.filterSettings?.displayedKey ?? col.langKey ?? col.id | translate }}</span>
        </div>
      </div>
    </div>
  </div>
</mat-menu>
