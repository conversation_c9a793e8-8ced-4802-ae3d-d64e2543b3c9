import { TableComponent } from '@/common/table/table/table.component';
import { Portal } from '@angular/cdk/portal';
import { ElementRef } from '@angular/core';
import { ValidatorFn } from '@angular/forms';
import { Observable } from 'rxjs';
import { QueryObjectType } from './add-query/add-query.component';

/**
 * Restrict accetable names for SearchMappingOrder
 */
type SearchMappingOrderNames = 'key' | 'value';

/**
 * Ascending or descending
 */
export type Order = 'asc' | 'desc';

export interface DoubleClickEvent {
  /**
   * The clicked row
   */
  row: any;
  /**
   * The original event.
   */
  event: MouseEvent;
}

export interface RemoteSetting {
  /**
   * Remote observable API
   *
   * @param payload
   */
  api?: (payload: any) => Observable<any>;
  /**
   * Query parameters
   */
  payload?: any;
}

interface ToolbarSetting {
  /**
   * Executed when click the button on the toolbar
   *
   * @param selected selected rows
   * @param tableComponent table component
   */
  click: (selected, tableComponent) => void;
  /**
   * Tooltip
   */
  tooltip?: string;
  /**
   * Whether the button should be disabled
   * Default: false
   *
   * @param selected selected rows
   * @returns true/false
   */
  disabled?: boolean | ((selected) => boolean);
}

export interface ColumnSetting {
  /**
   * Column ID
   */
  id: string;
  /**
   * The language key for i18n
   */
  langKey?: string;
  /**
   * Sortable
   */
  sorting?: boolean;
  /**
   * Customized cell format
   *
   * @param cell cell data
   */
  cellFormatter?: (cell: any, elementRef?: ElementRef) => any;
  /**
   * The position to align the content.
   * Default: left.
   */
  align?: CellAlign;
  /**
   * Filter settings
   */
  filterSettings?: FilterSettings;
  /**
   * The minimum width of the column.
   */
  minWidth?: number;
  /**
   * The maximum width of the column.
   */
  maxWidth?: number;
  /**
   * The column width.
   */
  width?: string;
  /**
   * Determine whether text in the cells should break lines if it’s too long
   * Default: false
   */
  breakLines?: boolean;
  /**
   * Determine whether text in the cells should break the word if it’s too long
   * Default: false
   */
  breakWord?: boolean;
  /**
   * Determine whether the colunm is invisible on the show/hide panel.
   * Default: false
   */
  invisibleOnShowHidePanel?: boolean;
  /**
   * Determine whether the colunm should be hidden when clicking reset columns
   * Default: false
   */
  hideOnReset?: boolean;
  /**
   * Optional data passed to the template data when `cellFormatter` uses a TemplateRef.
   */
  templateData?: any;
  /**
   * If true, bypasses the ngOnChanges lifecycle logic.
   * Applicable when using the cellFormatter function.
   * Default is false.
   */
  skipOnChanges?: boolean;
}

export interface InnerColumnSetting extends ColumnSetting {
  /**
   * Whether the column should be hidden
   * Default: false
   */
  hide?: boolean;
}

export interface TableSettings {
  /**
   * Table ID
   */
  tableId: string;
  /**
   * Time zone
   */
  timeZone?: string;
  /**
   * Columns
   */
  columns: ColumnSetting[];
  /**
   * Default displayed columns.
   * All columns will be displayed if defaultDisplayedColumns didn't be configured
   */
  defaultDisplayedColumns?: string[];
  /**
   * Remote setting
   */
  remote?: RemoteSetting;
  /**
   * Toolbar setting
   */
  toolbar?: { [key: string]: ToolbarSetting };
  /**
   * Defined how to process received data if needed
   */
  postProcess?: (res, tableComponent) => void;
  /**
   * Whether the buttons on toolbar should be shown
   */
  hasMenuButtons?: boolean;
  /**
   * Whether the "Select" column should be shown.
   * Default: true.
   */
  hasSelect?: boolean;
  /**
   * A handler to check whether a row's "Select" status can be changed or not.
   * Default: undefined.
   */
  selectChangeCheck?: (row: any) => boolean;
  /**
   * Whether the menu bar should be shown.
   * Default: true.
   */
  hasMenuBar?: boolean;
  /**
   * Whether the paginator should be shown.
   * Default: true.
   */
  hasPaginator?: boolean;
  /**
   * Menu buttons
   */
  menuButtons?: (SingleMenuButton | MultipleMenuButton)[];
  /**
   * Custom actions
   */
  customActions?: CustomActionButton[];
  /**
   * If this property is not empty, "row-id" property will be added to each row
   * row-id="row-{rowIdField}"
   */
  rowIdField?: string;
  /**
   * Fixed the first row for report-subnet table.
   */
  fixFirstRow?: boolean;
  /**
   * Provided to set custom form controls on the top-left of the menu
   */
  customTemplateOnLeft?: '' | Portal<any>;
  /**
   * has select all checkbox in header when using multi-select mode
   * default is true
   */
  hasSelectAll?: boolean;
  /**
   * Select mode
   * Default: None
   */
  selectMode?: SelectMode;
  /**
   * Columns should be displayed in autocomplete input.
   * All columns will be supported if this property is empty.
   *
   * @deprecated table component will find the fields with filterSettings property
   */
  autocompleteFields?: string[];
  /**
   * Whether the drop-down of columns shown/hidden should be shown.
   * Default: true
   */
  hasHideShowCols?: boolean;
  /**
   * Whether pagination process is in client-side
   * Default is server-side pagination
   */
  isClientSidePagination?: boolean;
  /**
   * If we allow user to search some columns didn't in "columns", we could add these custom columns to this property.
   * This is only provided to search custom columns.
   */
  customSearchColumns?: ColumnSetting[];
  /**
   * Whether selected rows should be record when changing the page in server-side pagination mode.
   * If you would like to record the selected rows when switching the apge, this property shoulb set to true.
   */
  recordSelectedRows?: boolean;
  /**
   * This is used to set default sort field and direction.
   * The key should be column ID, the value should be "asc" or "desc"
   */
  defaultSortField?: { [key: string]: string };
  /**
   * The content of online help slide
   */
  onlineHelp?: string;
  /**
   * The column to display the indent for expanded rows.
   * This column should be the first and fixed.
   */
  primaryColumnForExpandedRow?: string;
  /**
   * Whether table settings should be saved to local storage. Some tables needn't to save settings. ex. tables in "Add new query" dialog.
   * Default: true
   */
  saveToStorage?: boolean;
  /**
   * Set search keys manually
   */
  searchKeys?: SearchKey[];
  /**
   * Add query object type to show Existing Query tab if the table supports
   */
  queryObjectType?: QueryObjectType;
  /**
   * Group by column
   */
  groupBy?:
    | string
    | {
        /**
         * Column ID
         */
        columnId: string;
        /**
         * If this is true, group row will use cellFormatter to generate the group label
         */
        useCellFormatter?: boolean;
        /**
         * This is used to set customized mapping. It could be a function or an object.
         *
         * ex.
         * converter: {
         *   '1': 'label 1',
         *   '2': 'label 2'
         * }
         *
         * or:
         * converter: {
         *   '1': {
         *     'title': 'lable 1',
         *     'icon': 'xxx',
         *     'color': '#111'
         *   },
         *   '2': {
         *     'title': 'lable 2',
         *     'icon': 'xxx',
         *     'color': '#222'
         *   }
         * }
         *
         * or:
         * converter: (val) => {
         *  switch (val) {
         *    case '1':
         *      return 'label 1';
         *    case '2':
         *      return 'label 2';
         *    default:
         *      return 'Others';
         *   }
         * }
         *
         * or:
         * converter: (val) => {
         *  switch (val) {
         *    case '1':
         *      return {
         *        'title': 'lable 1',
         *        'icon': 'xxx',
         *        'color': '#111'
         *      };
         *    case '2':
         *      return {
         *        'title': 'lable 2',
         *        'icon': 'xxx',
         *        'color': '#222'
         *      };
         *    default:
         *      return 'Others';
         *   }
         * }
         */
        converter?: any;
        /**
         * * This property should be set to true if you want to display the group title even when the rows are empty.
         * This is only effective when the converter is defined with a key-value object.
         */
        persist?: boolean;
        /**
         * The order of grouping sections.
         */
        order?: string[];
      };
  /**
   * Execute when clicking the row
   *
   * @param row clicked row
   */
  clickRow?: (row: any) => void;
  /**
   * Whether row click should be disabled
   */
  disableRowClick?: boolean;
  /**
   * Whether the show/hide dropdown should be disabled
   */
  disableHideShowCols?: Observable<boolean>;
  /**
   * Default page size.
   * Default: 10
   */
  pageSize?: number;
  /**
   * Customized page size options
   * Default: [10, 20, 50, 100]
   */
  pageSizeOptions?: number[];
  /**
   * Whether the table needs to refresh entire rows when calling refreshFixedData().
   * This is only used for fixed data.
   * Default: false
   */
  forceToRefresh?: boolean;
  /**
   * Whether the grouped rows should be fixed to expand.
   * If yes, the rows will not be able to collapse or expand when clicking the grouped title row.
   * Default: false
   */
  keepExpanding?: boolean;
  /**
   * Custom placeholder for search bar
   */
  searchBarPlaceholder?: string | { [key: string]: string };
  /**
   * Move private columns to a specific position
   */
  movePrivateColumns?: {
    /**
     * The ID of private column
     */
    privateColumnId: '_select' | '_action' | '_expand';
    /**
     * The column ID after which you want to insert
     */
    after: string;
  }[];
}

export interface SingleMenuButton {
  /**
   * The type of menu button.
   * Default: Single.
   */
  type?: MenuButtonType;
  /**
   * Displayed text
   */
  text: string;
  /**
   * Executed when clicking the button
   */
  click: (selection: any) => void;
  /**
   * Whether the button should be disabled
   * Default: false
   *
   * @param selected selected rows
   * @returns true/false
   */
  disabled?: boolean | ((selected) => boolean);
  /**
   * Mat-button color. This property is not available for the buttons in multi-buttons drop down
   */
  color?: string;
}

export interface MultipleMenuButton {
  /**
   * The type of menu button.
   * Default: Single.
   */
  type?: MenuButtonType;
  /**
   * Displayed text
   */
  text: string;
  /**
   * Whether the button should be disabled
   * Default: false
   *
   * @param selected selected rows
   * @returns true/false
   */
  disabled?: boolean | ((selected) => boolean);
  /**
   * Mat-button color. This property is not available for the buttons in multi-buttons drop down
   */
  color?: string;
  /**
   * This property should be configured when type is Multiple
   */
  buttons?: SingleMenuButton[];
}

export interface CustomActionButton {
  /**
   * Displayed text
   */
  text: string;
  /**
   * Executed when clicking the button
   */
  click: (row: any, table: TableComponent, $event: MouseEvent) => void;
  /**
   * Mat icon
   */
  matIcon?: string;
  /**
   * SVG icon
   */
  svgIcon?: string;
  /**
   * Icon size
   */
  iconSize?: number;
  /**
   * Whether the button should be hidden
   * Default: false
   *
   * @param selected selected row
   * @returns true/false
   */
  hidden?: boolean | ((selected) => boolean);
  /**
   * Whether the button should be disabled
   * Default: false
   *
   * @param selected selected row
   * @returns true/false
   */
  disabled?: boolean | ((selected) => boolean);
  /**
   * Button width
   */
  width?: string;
}

export enum MenuButtonType {
  Single,
  Multiple
}

export enum SelectMode {
  None,
  Single,
  Multiple
}

export enum CellAlign {
  Left = 'left',
  Center = 'center',
  Right = 'right'
}

export enum FilterOperation {
  Equal = 'equal',
  Like = 'like',
  Range = 'range',
  Greater = 'greater',
  GreaterEqual = 'greater_equal',
  Less = 'less',
  LessEqual = 'less_equal'
}

export interface FilterSettings {
  /**
   * Whether the value/values should be numeric
   * Default: false
   */
  isNumber?: boolean;
  /**
   * Whether the value/values should be lowercase
   * Default: false
   */
  isLowerCase?: boolean;
  /**
   * Whether the value should be an array
   * Default: false
   */
  isMultiple?: boolean;
  /**
   * Whether the value input should use the date-time picker
   * Defalt: false
   */
  isDateTime?: boolean;
  /**
   * This is used to map display string to specific value
   * Ex. {
   *   online: 1,
   *   offline: 0
   * }
   */
  searchMapping?: { [key: string]: any };
  /**
   * Set the order for search mappings display. By default, search mappings are sorted by key in ascending order.
   *  - If this fieds is 'asc' or 'desc, the search mappings will be sorted by key in ascending/descending order.
   *  - If you would like to sort by specified field(key or value), this field should be {key: 'asc'} or {value: 'desc'}...
   *  - It also support to sort with multiple order conditions. ex. [{key: 'asc'}, {value: 'desc'}]
   */
  searchMappingOrder?: Order | { [K in SearchMappingOrderNames]?: Order } | { [K in SearchMappingOrderNames]?: Order }[];
  /**
   * Replace the search key.
   * Ex. If the column ID is site_name, we need to replace it with site_id when filtering.
   */
  searchReplacedKey?: string;
  /**
   * Whether to display the search input for filtering search mappings.
   * This property is effective when searchMapping was configured.
   */
  filterSearchMappings?: boolean;
  /**
   * Operations the column supports.
   */
  operations?: FilterOperation[];
  /**
   * Whether to allow duplicate filters with the same key.
   * Default: false
   */
  allowDuplicate?: boolean;
  /**
   * Validators for the filter input
   */
  validators?: ValidatorFn | ValidatorFn[];
  /**
   * Customized the displayed text in the related filter configurations.
   */
  displayedKey?: string;
  /**
   * Whether the seconds in the date-time input should be displayed
   * Default: false
   */
  showSeconds?: boolean;
  /**
   * whether the specific character string matches any part of the values.
   * This is effective only in fixed data mode.
   */
  usePartialMatch?: boolean;
  /**
   * Whether the column is an individual search condition.
   * If this property is true, the other filters will be removed and disabled when this column is applied.
   * Default: false
   */
  isIndividual?: boolean;
  /**
   * Group conditions by the specific name.
   */
  filterGroupBy?: string[];
  displayItem?: string[];
}

export interface Range {
  start: number | string;
  end: number | string;
}

export interface SearchKey {
  key: { label: string; value: string };
  value: {
    /**
     * This property could be empty string when method is range
     */
    label: string;
    value: string | number | string[] | number[] | Range;
    /**
     * This property should be true if value.label needs to be converted to GMT time
     */
    convertToDateTime?: boolean;
  };
  method: string | FilterOperation;
}

export interface GroupResult {
  /**
   * It's used display the text on the group row
   */
  label: string | HTMLElement;
  /**
   * Real value of the group row
   */
  value: any;
  /**
   * Rows
   */
  rows: any[];
}
