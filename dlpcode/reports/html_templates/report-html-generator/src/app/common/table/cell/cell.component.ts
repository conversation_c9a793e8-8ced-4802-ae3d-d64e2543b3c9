/**
 * This is used to solve custom cellFormatter(fsCellFormatter) didn't update the content when data is chage.
 * We could consider to use this component instead of fsCellFormatter in fd-table
 */
import { Component, Input, OnChanges, SimpleChanges, ViewContainerRef } from '@angular/core';
import { isNil } from 'lodash';

@Component({
  selector: 'fd-table-cell',
  templateUrl: './cell.component.html',
  styleUrls: ['./cell.component.scss']
})
export class CellComponent implements OnChanges {
  /**
   * Customized formatter
   */
  @Input() formatter: (element) => any;

  /**
   * Value
   */
  @Input() value: any;

  /**
   * Cell data
   */
  @Input() cell: any;

  @Input() tableCellCache: Map<string, any>;
  @Input() cacheKeyCalculate: (rowData: any, column: string) => string;
  @Input() column: string;

  constructor(private vcRef: ViewContainerRef) {}

  /**
   * @internal
   */
  ngOnInit(): void {
    this.initData();
  }

  /**
   * @internal
   */
  ngOnChanges(changes: SimpleChanges): void {
    if ('value' in changes && !changes['value'].firstChange) {
      this.vcRef.element.nativeElement.innerHTML = '';
      this.initData();
    }
  }

  /**
   * Initialize data
   */
  private async initData() {
    const key = this.cacheKeyCalculate(this.cell, this.column);
    let formatter = this.tableCellCache.get(key);

    if (isNil(formatter)) {
      formatter = await this.formatter(this.cell);
      this.tableCellCache.set(key, formatter);
    }

    if (formatter instanceof HTMLElement) {
      this.vcRef.element.nativeElement.appendChild(formatter);
    } else if (['string', 'number'].includes(typeof formatter)) {
      this.vcRef.element.nativeElement.innerHTML = formatter;
    } else {
      this.vcRef.element.nativeElement.textContent = '';
    }
  }
}
