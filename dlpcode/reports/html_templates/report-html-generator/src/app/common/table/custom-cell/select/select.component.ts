import { Component, Input } from '@angular/core';

interface Option {
  label: string;
  value: string | number;
  icon?: string;
  color?: string;
}

@Component({
  selector: 'fd-select',
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.scss']
})
export class SelectComponent {
  /**
   * Options
   * opts should be json when using elem()
   */
  @Input() set options(opts: string | Option[]) {
    this._options = Array.isArray(opts) ? opts : JSON.parse(opts);
  }

  /**
   * Default value
   */
  @Input() value: string | number;

  /**
   * Callback function for value change
   */
  @Input() valueChange: (value: string | number) => {};

  /**
   * Width
   */
  @Input() width?: string;

  get selectedItem() {
    if (this._prevSelectedValue === this.value) {
      return this._selectedItemCache;
    }

    this._prevSelectedValue = this.value;
    const item = this.options.find((x) => x.value === this.value);
    this._selectedItemCache = {
      icon: item.icon,
      text: item.label,
      color: item.color
    };
    return this._selectedItemCache;
  }

  get options(): Option[] {
    return this._options;
  }

  private _options: Option[];

  private _prevSelectedValue: string | number;

  private _selectedItemCache;

  /**
   * Listen on value changed
   */
  onChange(value) {
    this.value = value.value;
    this.valueChange?.(this.value);
  }
}
