<div class="loading-panel" *ngIf="loading | async">
  <mat-spinner></mat-spinner>
</div>

<div class="table-container fd-table">
  <!-- table menubar -->
  <div class="menu-bar-container" *ngIf="settings.hasMenuBar">
    <ng-container
      *ngIf="settings.customTemplateOnLeft"
      [cdkPortalOutlet]="settings.customTemplateOnLeft"
    >
    </ng-container>

    <div class="icon-container menu-buttons" *ngIf="settings.hasMenuButtons">
      <button
        mat-icon-button
        aria-label="Refresh"
        (click)="refreshByQuery()"
        matTooltip="{{ 'refresh' | translate }}"
        [matTooltipPosition]="'above'"
      >
        <mat-icon>refresh</mat-icon>
      </button>
      <button
        mat-icon-button
        aria-label="Delete"
        (click)="deleteRows()"
        matTooltip="{{ settings.toolbar['delete']?.tooltip | translate }}"
        [matTooltipPosition]="'above'"
        [disabled]="
          !selection.selected.length ||
          checkButtonStatus(settings.toolbar['delete']?.disabled ?? false, selection.selected)
        "
      >
        <mat-icon>delete</mat-icon>
      </button>
    </div>

    <div class="icon-container" *ngIf="settings.hasHideShowCols">
      <button
        mat-stroked-button
        [matMenuTriggerFor]="showHideMenu"
        [disableRipple]="true"
        class="columns-drop-down"
      >
        <span class="menu-text">{{ 'columns' | translate }}</span>
        <mat-icon iconPositionEnd>arrow_drop_down</mat-icon>
      </button>
      <mat-menu #showHideMenu="matMenu" class="table-columns-drop-down-menu">
        <div class="top-config">
          <button
            mat-menu-item
            class="col-menu-item"
            (click)="showHideCols($event)"
            [disableRipple]="true"
          >
            <mat-checkbox
              (click)="showHideCols($event)"
              [checked]="isAllHideShowColumnsSelected()"
              [indeterminate]="isNoHideShowColumnsSelected()"
              [disableRipple]="true"
            ></mat-checkbox>
            <span class="menu-text">{{ 'columnName' | translate }}</span>
          </button>
          <div class="divider"></div>
          <div class="reset" (click)="resetColumns($event)">{{ 'reset_columns' | translate }}</div>
        </div>
        <ng-container *ngFor="let col of allColumns">
          <button
            #showHideButton
            *ngIf="!['_select', '_action', '_expand', 'blank'].includes(col.id)"
            mat-menu-item
            [disableRipple]="true"
            (click)="showHideColumn($event, col.id)"
            [ngClass]="{ disabled: checkShowHideButtonStatus(showHideButton) }"
            class="col-menu-item"
          >
            <mat-checkbox [checked]="!col.hide" [disableRipple]="true"></mat-checkbox>
            <span class="menu-text">{{ getColumnName(col.id) }}</span>
          </button>
        </ng-container>
      </mat-menu>
    </div>

    <div class="search-box">
      <div class="search-container">
        <button
          *ngIf="groupedConditions?.length && selectedGroupedCondition"
          mat-stroked-button
          [matMenuTriggerFor]="groupedConditionsMenu"
          [disableRipple]="true"
          class="grouped-conditions-drop-down"
        >
          <span class="menu-text">{{ selectedGroupedCondition | translate }}</span>
          <mat-icon iconPositionEnd>arrow_drop_down</mat-icon>
        </button>
        <mat-menu #groupedConditionsMenu="matMenu" class="table-grouped-conditions-menu">
          <div
            *ngFor="let groupedCondition of groupedConditions"
            class="item"
            (click)="selectGroupedCondition(groupedCondition)"
            [ngClass]="{ selected: groupedCondition === selectedGroupedCondition }"
          >
            <div
              #conditionText
              class="menu-text"
              matTooltip="{{ groupedCondition | translate }}"
              [matTooltipDisabled]="!isEllipsisActive(conditionText)"
              matTooltipPosition="right"
            >
              {{ groupedCondition | translate }}
            </div>
            <mat-icon svgIcon="check"></mat-icon>
          </div>
        </mat-menu>

        <!-- implement chips by ourself to solve the issue that first mat-row is always selected when clicking the scroll bar
        (https://github.com/angular/components/issues/15728) -->
        <div class="chip-set" *ngIf="searchKeys?.length">
          <div *ngFor="let key of searchKeys" class="chip-row" (click)="editSearchCondition(key)">
            {{ getFilterDescription(key) }}
            <button (click)="removeSearchCondition(key)">
              <!-- <mat-icon svgIcon="close"></mat-icon> -->
              <mat-icon>cancel</mat-icon>
            </button>
          </div>
        </div>

        <!-- don't bind the event (matChipInputTokenEnd)="addSearch($event)", because it will trigger autocomplete option display
                      and can't catch the input key value, use 'keyup' event instead of it
                  -->
        <div class="input-box">
          <input
            placeholder="{{ getSearchBarPlaceholder() | translate }}"
            #searchInput
            matInput
            class="search-input"
            aria-label="search filter"
            (click)="openQueryDialog()"
            readonly="readonly"
          />
          <fd-table-query (selected)="openFilterPanel($event)"></fd-table-query>
          <fd-table-filter-panel (apply)="applyFilter($event)"></fd-table-filter-panel>
        </div>

        <div
          class="search-icon"
          *ngIf="searchKeys.length"
          (click)="clearSearchConditions()"
          matTooltip="{{ 'clearAllConditions' | translate }}"
          matTooltipPosition="above"
        >
          <!-- <mat-icon svgIcon="close"></mat-icon> -->
          <mat-icon>clear</mat-icon>
        </div>
      </div>
      <!--
      <mat-icon svgIcon="info" class="info-icon" (click)="openHelp()"></mat-icon>
      -->
    </div>

    <!-- Top right buttons -->
    <div
      class="icon-container top-right-buttons"
      *ngIf="settings.menuButtons?.length || settings.queryObjectType"
    >
      <button
        mat-flat-button
        *ngIf="settings.queryObjectType"
        class="no-wrap"
        [disableRipple]="true"
        (click)="saveQueryObject()"
      >
        {{ 'save_query' | translate | uppercase }}
        <fs-table-add-query></fs-table-add-query>
      </button>

      <ng-container *ngFor="let button of settings.menuButtons ?? []">
        <!-- Single button -->
        <button
          *ngIf="button.type !== MenuButtonType.Multiple; else multiButtons"
          mat-flat-button
          class="no-wrap"
          [disableRipple]="true"
          (click)="button['click']?.(selection)"
          [color]="button.color"
          [disabled]="checkButtonStatus(button.disabled, selection.selected)"
        >
          {{ button.text | translate | uppercase }}
        </button>
        <!-- Multi-buttons -->
        <ng-template #multiButtons>
          <button
            mat-flat-button
            [matMenuTriggerFor]="multiButtomsList"
            class="no-wrap"
            [color]="button.color"
            [disableRipple]="true"
            [disabled]="checkButtonStatus(button.disabled, selection.selected)"
          >
            <span class="menu-text">{{ button.text | translate | uppercase }}</span>
            <mat-icon iconPositionEnd>keyboard_arrow_down</mat-icon>
          </button>
          <mat-menu #multiButtomsList="matMenu" xPosition="before">
            <button
              *ngFor="let b of button['buttons']"
              mat-menu-item
              (click)="b.click(selection)"
              [disabled]="checkButtonStatus(b.disabled, selection.selected)"
              [disableRipple]="true"
            >
              <span class="menu-text">{{ b.text | translate }}</span>
            </button>
          </mat-menu>
        </ng-template>
      </ng-container>
    </div>
  </div>

  <div class="menu-error-message">
    <span class="message" *ngIf="menuErrorMessage">{{ menuErrorMessage | translate }}</span>
  </div>

  <div class="out-container" tabindex="0">
    <!-- only '_select' and '_action' two columns are sticky -->
    <table
      #table
      mat-table
      [dataSource]="dataSource"
      matSort
      cdkDropList
      cdkDropListOrientation="horizontal"
      (cdkDropListDropped)="drop($event)"
      class="mat-elevation-z8 list-table"
      matSortActive=""
      matSortDirection=""
      (matSortChange)="sortData($event)"
      (contentChanged)="onContentChanged()"
    >
      <tr mat-header-row *matHeaderRowDef="getAllColumnIds(); sticky: true"></tr>

      <!-- group row -->
      <ng-container matColumnDef="groupRow">
        <td colspan="999" mat-cell *matCellDef="let groupingItem">
          <div class="group-cell" [ngClass]="{ expand: groupingItem.expand }">
            <mat-icon *ngIf="!settings.keepExpanding" class="expand-icon">{{
              'expand_more'
            }}</mat-icon>
            <span #groupingTitle>{{ setGroupingTitle(groupingTitle, groupingItem.label) }}</span>
            <span>({{ groupingItem.total }})</span>
          </div>
        </td>
      </ng-container>
      <tr
        mat-row
        *matRowDef="let row; columns: ['groupRow']; when: isGroupRow"
        (click)="clickGroupRow(row)"
        class="group-row"
        [ngClass]="{ 'default-cursor': settings.keepExpanding }"
      ></tr>

      <tr
        mat-row
        #tableRow
        *matRowDef="let row; columns: getAllColumnIds()"
        [class.row-selected]="
          selection.selected.includes(row) && settings.selectMode !== selectMode.None
        "
        class="{{ 'expand-' + (row.parent?.length ?? 0) }}"
        [ngClass]="{ hide: row.hidden, 'custom-clickable-row': settings.clickRow }"
        (click)="clickRow(row)"
        (dblclick)="dblclickRow($event, row)"
        [attr.row-id]="settings.rowIdField ? 'row-' + row[settings.rowIdField] : null"
      ></tr>

      <ng-container
        *ngFor="let col of getVisibleColumns()"
        matColumnDef="{{ col.id }}"
        sticky="{{ ['_select', '_action', '_expand', 'blank'].includes(col.id) }}"
      >
        <ng-container [ngSwitch]="col.id">
          <!-- select column -->
          <ng-container *ngSwitchCase="'_select'">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                *ngIf="settings.selectMode === selectMode.Multiple && settings.hasSelectAll"
                (change)="$event ? toggleRows() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()"
              >
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox
                (click)="$event.stopPropagation()"
                (change)="$event ? toggleRow(row) : null"
                [checked]="selection.isSelected(row)"
                [disabled]="settings.selectChangeCheck && settings.selectChangeCheck(row) === false"
              >
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- action column -->
          <ng-container *ngSwitchCase="'_action'">
            <ng-container *ngIf="settings.customActions?.length">
              <th mat-header-cell *matHeaderCellDef></th>
              <td mat-cell *matCellDef="let row">
                <mat-icon
                  [matMenuTriggerFor]="menu"
                  style="cursor: pointer"
                  (click)="$event.stopPropagation()"
                  [svgIcon]="'more_vert'"
                ></mat-icon>
                <mat-menu #menu="matMenu">
                  <!-- custom action buttons -->
                  <div class="custom-actions-panel">
                    <div class="actions">
                      <ng-container *ngFor="let action of settings.customActions">
                        <button
                          mat-menu-item
                          (click)="applyFn(action.click, row, $event)"
                          [disabled]="checkButtonStatus(action.disabled, row)"
                          [ngClass]="{ hidden: checkButtonStatus(action.hidden, row) }"
                          class="action-menu-item"
                          [disableRipple]="true"
                        >
                          <fd-icon
                            [matIcon]="action.matIcon"
                            [svgIcon]="action.svgIcon"
                            [size]="action.iconSize ?? 22"
                          ></fd-icon>
                          <span class="menu-text">{{ action.text | translate }}</span>
                        </button>
                      </ng-container>
                    </div>
                  </div>
                </mat-menu>
              </td>
            </ng-container>
          </ng-container>

          <!-- expand column -->
          <ng-container *ngSwitchCase="'_expand'">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let row">
              <ng-container *ngIf="row.hasChildren">
                <mat-icon class="expand-icon" [ngClass]="{ expand: row.expand ?? true }"
                  >expand_more</mat-icon
                >
              </ng-container>
            </td>
          </ng-container>

          <!-- blank column -->
          <ng-container *ngSwitchCase="'blank'">
            <th mat-header-cell *matHeaderCellDef></th>
          </ng-container>

          <!-- other columns -->
          <ng-container *ngSwitchDefault>
            <ng-container *ngIf="defaultColumnsMap[col.id]?.sorting; else notSortableColumn">
              <!-- sortable column -->
              <th
                mat-header-cell
                *matHeaderCellDef
                cdkDrag
                mat-sort-header
                [cdkDragDisabled]="!settings.hasMenuBar || !settings.hasHideShowCols"
                [ngStyle]="{
                  'min-width.px': defaultColumnsMap[col.id].minWidth,
                  width: defaultColumnsMap[col.id].width
                }"
              >
                {{ defaultColumnsMap[col.id].langKey ?? col.id | translate }}
              </th>
            </ng-container>

            <ng-template #notSortableColumn>
              <!-- primary column of expanded row -->
              <ng-container
                *ngIf="settings.primaryColumnForExpandedRow === col.id; else commonColumn"
              >
                <th
                  mat-header-cell
                  *matHeaderCellDef
                  [ngStyle]="{
                    'min-width.px': defaultColumnsMap[col.id].minWidth,
                    width: defaultColumnsMap[col.id].width
                  }"
                >
                  {{ defaultColumnsMap[col.id].langKey ?? col.id | translate }}
                </th>
              </ng-container>

              <ng-template #commonColumn>
                <th
                  mat-header-cell
                  *matHeaderCellDef
                  cdkDrag
                  [cdkDragDisabled]="!settings.hasMenuBar || !settings.hasHideShowCols"
                  [ngStyle]="{
                    'min-width.px': defaultColumnsMap[col.id].minWidth,
                    width: defaultColumnsMap[col.id].width
                  }"
                >
                  {{ defaultColumnsMap[col.id].langKey ?? col.id | translate }}
                </th>
              </ng-template>
            </ng-template>
          </ng-container>
        </ng-container>

        <!-- table cell -->
        <!-- *** don't use matToolTip on td, there is a performance issue -->
        <td
          mat-cell
          *matCellDef="let element; let rowIndex = index"
          class="{{ defaultColumnsMap[col.id]?.align ?? '' }} {{
            defaultColumnsMap[col.id].className
          }}"
          [ngClass]="{
            indent: settings.primaryColumnForExpandedRow === col.id,
            'line-break': defaultColumnsMap[col.id].breakLines,
            'break-word': defaultColumnsMap[col.id].breakWord
          }"
          [ngStyle]="{
            'min-width.px': defaultColumnsMap[col.id].minWidth,
            width: defaultColumnsMap[col.id].width
          }"
        >
          <fd-table-cell
            *ngIf="defaultColumnsMap[col.id]?.cellFormatter; else commonCell"
            [formatter]="defaultColumnsMap[col.id].cellFormatter"
            [cell]="element"
            [value]="element[col.id]"
            [column]="col.id"
            [tableCellCache]="customerCellElementMap"
            [cacheKeyCalculate]="calculateElementCacheKey"
          ></fd-table-cell>
          <ng-template #commonCell>{{ element[col.id] }}</ng-template>
        </td>
      </ng-container>
    </table>
  </div>

  <mat-paginator
    *ngIf="settings.hasPaginator"
    [pageSize]="settings.pageSize"
    [pageSizeOptions]="settings.pageSizeOptions"
    [hidePageSize]="false"
    [showFirstLastButtons]="true"
    (page)="onPageChange($event)"
  >
  </mat-paginator>
</div>
