import { isNil } from "lodash";

export class ElementWrapper {
  /**
   * Create an ElementWrapper. Inheriting class should pass the element instance it's planning to use
   * @param element HTML element which should be unwrapped by elem()
   */
  constructor(readonly element: HTMLElement) {}
}

interface ElementParams {
  /**
   * Attributes to add with setAttribute to the new element.
   */
  attributes?: { [key: string]: string };
  /**
   * Event listeners to add with addEventListener to the new element
   */
  events?: { [K in keyof HTMLElementEventMap]?: (event: HTMLElementEventMap[K]) => void };
  /**
   * Child nodes which will be added to the new element with appendChild
   */
  children?: Array<HTMLElement | ElementWrapper>;
}
/**
 * Utilty function for creating DOM elements.
 *
 * @param initial Tag name.
 * @param properties Properties to add to new element, or merge into and existing element (key & value).
 * @param params Other parameters.
 * @returns Created HTMLElement.
 */
export function elem(
  initial: string | HTMLElement | ElementWrapper,
  properties?: { [key: string]: any },
  params?: ElementParams
) {
  let element: HTMLElement;
  params = params || {};
  if (initial instanceof HTMLElement) {
    element = initial;
  } else if (initial instanceof ElementWrapper) {
    element = initial.element;
  } else {
    element = document.createElement(initial);
  }
  if (properties) {
    for (const [key, value] of Object.entries(properties)) {
      if (value && typeof value.valueOf() === 'object' && !isNil((<any>element)[key])) {
        Object.assign((<any>element)[key], value);
      } else {
        (<any>element)[key] = value;
      }
    }
  }
  if (params.attributes) {
    for (const [key, value] of Object.entries(params.attributes)) {
      element.setAttribute(key, value);
    }
  }
  if (params.events) {
    for (const [key, value] of Object.entries(params.events)) {
      element.addEventListener(key, <EventListenerOrEventListenerObject>value);
    }
  }
  if (Array.isArray(params.children)) {
    for (const child of params.children) {
      element.appendChild(child instanceof ElementWrapper ? child.element : child);
    }
  }
  return element;
}
