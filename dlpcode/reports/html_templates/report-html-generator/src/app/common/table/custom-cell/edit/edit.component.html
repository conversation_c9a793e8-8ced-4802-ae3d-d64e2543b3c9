<div
  [matMenuTriggerFor]="editBox"
  class="value"
  [ngClass]="{ 'row-reverse': iconPosition === 'left' }"
  (menuOpened)="onMenuOpened()"
>
  <span *ngIf="!hideText && value">{{ value }}</span>
  <fd-icon *ngIf="matIcon || svgIcon" [matIcon]="matIcon" [svgIcon]="svgIcon" [size]="iconSize"></fd-icon>
</div>
<mat-menu #editBox="matMenu">
  <div class="content" (click)="$event.stopPropagation()">
    <div class="title" *ngIf="title">
      <span>{{ title | translate }}</span>
      <mat-icon (click)="close()" [svgIcon]="'close'"></mat-icon>
    </div>
    <div class="body">
      <textarea
        matInput
        [value]="value"
        [attr.maxlength]="maxLength"
        (input)="onValueChange($event)"
        #editControl
      ></textarea>
    </div>
    <div class="btn-box">
      <div class="right">
        <mat-icon (click)="close()" [svgIcon]="'disabled_by_default'"></mat-icon>
        <mat-icon (click)="save()" [svgIcon]="'check_box'"></mat-icon>
      </div>
    </div>
  </div>
</mat-menu>
