import { AfterViewInit, Component, Input, OnDestroy } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatSelectChange } from '@angular/material/select';
import { Observable, Subscription } from 'rxjs';

export interface SpecifiablePage {
  /**
   * Current page
   */
  current: number;
  /**
   * The pages number
   */
  length: number;
}

@Component({
  selector: 'app-table-page-selector',
  templateUrl: './page-selector.component.html',
  styleUrls: ['./page-selector.component.scss']
})
export class PageSelectorComponent implements AfterViewInit, OnDestroy {
  /**
   * Current page
   */
  page: number = 1;

  @Input()
  pageObservable: Observable<SpecifiablePage>;

  @Input()
  pageChanged: (val: number) => void;

  /**
   * Available pages
   */
  availablePages: number[] = [];

  formControl = new FormControl(0);

  /**
   * The page subscription
   */
  private _pageSubscription: Subscription;

  constructor() {}

  /**
   * @internal
   */
  ngAfterViewInit() {
    this._pageSubscription = this.pageObservable.subscribe((val) => {
      if (this.availablePages.length !== val.length) {
        const pages = [];
        for (let i = 1; i <= val.length; i++) {
          pages.push(i);
        }

        this.availablePages = pages;
      }

      this.page = val.current || 1;

      this.formControl.setValue(this.page);
    });
  }

  /**
   * @internal
   */
  ngOnDestroy() {
    this._pageSubscription.unsubscribe();
  }

  /**
   * Listen on selector changed
   */
  onChange(val: MatSelectChange) {
    this.pageChanged(val.value);
  }
}
