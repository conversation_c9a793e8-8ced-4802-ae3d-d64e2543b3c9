import { Component, Input, OnInit } from '@angular/core';
import { keys, reduce } from 'lodash';

export interface ICustomCellLabels {
  custom: string[];
  ml: string[];
  predefine: string[];
}

@Component({
  selector: 'app-labels',
  templateUrl: './labels.component.html',
  styleUrls: ['./labels.component.scss']
})
export class LabelsComponent implements OnInit {
  @Input() labels: ICustomCellLabels;
  count = '';

  handler(event: MouseEvent) {
    event.preventDefault();
  }

  ngOnInit(): void {
    this.count = `${reduce(
      keys(this.labels),
      (res, current) => {
        const r = res + this.labels[current].length;
        return r;
      },
      0
    )} Labels`
  }

}
