.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 6px 0;
  width: 390px;
}

.form-field {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 16px;
  background: #fff;
  box-sizing: border-box;
  gap: 12px;

  input {
    height: 32px;
    padding-left: 12px;
    background-color: #f7f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 2px;
    font-size: 14px;
    outline: nont !important;
  }

  label {
    padding-top: 8px;
    font-weight: 700;
    color: #222222;
    white-space: nowrap;
  }
}

.input-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

textarea {
  height: 194px;
  padding-left: 12px;
  border-radius: 2px;
  background-color: #f7f9f9;
  border: 1px solid #e5e5e5;
  resize: none;
  outline: none !important;
  font-size: 14px;
}

.icon-class {
  width: 16px;
  height: 16px;
}

mat-error {
  line-height: 14px;
}

.action-btn-group {
  display: flex;
  align-items: center;
  gap: 11px;

  button {
    width: 160px;
  }
}

.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 16px 6px;
}

::ng-deep {
  .table-add-query-popover {
    .mat-mdc-input-element {
      font-size: 14px;
      padding-left: 12px;

      &:focus {
        border-color: rgba(82, 168, 236, 0.8) !important;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
        outline: none;
      }
    }

    .mat-mdc-select {
      width: 100%;
      background-color: #f7f9f9;
      border: 1px solid #e5e5e5;
      border-radius: 2px;

      &:focus {
        border-color: rgba(82, 168, 236, 0.8) !important;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
        outline: none;
      }
    }

    .mat-mdc-form-field-input-control {
      font-size: 14px !important;
    }

    .mat-mdc-select-value {
      line-height: normal;
      font-size: 14px;
      padding-left: 10px;
    }

    .mat-mdc-select-trigger {
      height: 34px;
    }

    .mat-mdc-select-arrow {
      right: 12px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .org-text {
      padding-top: 8px;
    }
  }
}
