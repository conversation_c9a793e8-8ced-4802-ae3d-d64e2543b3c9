<mat-spinner *ngIf="matIcon === 'spinner'; else icons" [diameter]="16"></mat-spinner>
<ng-template #icons>
  <mat-icon
    [svgIcon]="svgIcon"
    [fontSet]="fontSet ?? ''"
    [inline]="inline"
    [ngStyle]="{ height: size, width: size, fontSize: size, color }"
    class="material-icons {{ className }}"
    [matTooltip]="tooltip"
    matTooltipPosition="above"
  >
    {{ matIcon }}</mat-icon
  >
</ng-template>
<span *ngIf="text">{{ text }}</span>
