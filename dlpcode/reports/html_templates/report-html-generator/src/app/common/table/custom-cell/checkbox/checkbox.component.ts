import { Component, Input } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { Observable } from 'rxjs';

interface Option {
  label: string;
  value: string | number | boolean;
  icon?: string;
  color?: string;
}

@Component({
  selector: 'fd-checkbox',
  templateUrl: './checkbox.component.html',
  styleUrls: ['./checkbox.component.scss'],
})
export class CheckboxComponent {
  /**
   * Label
   */
  @Input() label: string;

  /**
   * Default value
   */
  @Input() value?: boolean;

  /**
   * Callback function for value change
   */
  @Input() valueChange?: (value: boolean) => void | Observable<boolean>;

  /**
   * Icon
   */
  @Input() icon?: string;

  /**
   * Icon color
   */
  @Input() iconColor?: string;

  /**
   * disable checkbox
   */
  @Input() disabled?: boolean = false;

  /**
   * Listen on value changed
   */
  onChange($event: MatCheckboxChange) {
    const res = this.valueChange?.($event.checked);
    if (res) {
      res.subscribe((r) => {
        this.value = r;
      });
    }
  }
}
