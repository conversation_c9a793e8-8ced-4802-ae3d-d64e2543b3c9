import { TranslateService } from '@/common/translate/translate.service';
import { Injectable } from '@angular/core';
import * as _ from 'lodash';
import { ColumnSetting, FilterOperation } from '../table';
import * as filterUtil from './filter.util';

@Injectable({
  providedIn: 'root'
})
export class TableService {
  constructor(private translateService: TranslateService) {}

  /**
   * Get real value from searchMapping property
   *
   * @param searchValue search value
   * @param searchMapping search mapping object
   */
  getValueFromSearchMapping(searchValue: any, searchMapping: any) {
    if (!searchMapping) {
      return searchValue;
    }

    if (Array.isArray(searchValue)) {
      const ret = [];
      searchValue.forEach((x) => {
        for (const [key, value] of Object.entries(searchMapping)) {
          if (key.toLowerCase() === x.toString().toLowerCase()) {
            ret.push(value);
            break;
          }
        }
      });
      return ret;
    } else {
      for (const [key, value] of Object.entries(searchMapping)) {
        if (key.toLowerCase() === searchValue.toString().toLowerCase()) {
          return value;
        }
      }
    }
  }

  /**
   * Check if the input value matches with the value in searchMapping
   *
   * @param value the value entered by user
   * @param column column setting
   * @returns true/false
   */
  checkQueryValue(value: any, column: ColumnSetting): string | null {
    const searchMapping = column.filterSettings?.searchMapping;
    if (!searchMapping) {
      return null;
    }

    const inputValue = Array.isArray(value) ? value : [value];
    const acceptableValues = Object.keys(searchMapping);
    const unacceptableValues = inputValue.filter((x) => !acceptableValues.includes(x));
    if (unacceptableValues.length) {
      return this.translateService.lookup('errorMessage.invalidTableQueryValue', {
        column: this.translateService.lookup(column.id || column.langKey),
        values: acceptableValues.sort().join('/')
      });
    }

    return null;
  }

  /**
   * Filter fixed data
   */
  filterFixedData(data: any[], filters: any, columnsMap: any) {
    let ret = data;

    if (_.isEmpty(filters)) {
      return ret;
    }

    const filtersDetail = _.omit(filters, 'sort_field', 'sort_method');
    if (!_.isEmpty(filtersDetail)) {
      ret = data.filter((d) => this.isMatch(d, filtersDetail, columnsMap));
    }

    // sort the data
    if (filters.hasOwnProperty('sort_field') && filters.hasOwnProperty('sort_method')) {
      ret = _.orderBy(ret, filters.sort_field, filters.sort_method);
    }

    return ret;
  }

  /**
   * Check if the data matches the filters
   *
   * @param data row data
   * @param filters filters
   */
  private isMatch(data: any, filters: any, columnsMap: any): boolean {
    let result = true;
    filterLoop: for (const [key, operations] of Object.entries(filters)) {
      for (let operation of operations as any[]) {
        if (
          operation.method === FilterOperation.Equal &&
          columnsMap[key].filterSettings.usePartialMatch
        ) {
          if (!filterUtil.like(data, key, operation)) {
            result = false;
            break filterLoop;
          }
        } else if (!filterUtil[operation.method ?? FilterOperation.Range](data, key, operation)) {
          result = false;
          break filterLoop;
        }
      }
    }

    return result;
  }
}
