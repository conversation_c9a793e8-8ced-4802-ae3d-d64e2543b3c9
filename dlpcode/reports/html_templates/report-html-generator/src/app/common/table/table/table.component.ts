import { SelectionModel } from '@angular/cdk/collections';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  ViewChild,
  ViewChildren
} from '@angular/core';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort, Sort, SortDirection } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import * as _ from 'lodash';
import { Observable, Subject, Subscription } from 'rxjs';
import { FormatService } from '../../../service/format.service';
import { HandleExceptionService } from '../../../service/handle_exception.service';
import { HelpService } from '../../../service/help.service';
import { TableStorageService } from '../../../service/table-storage.service';
import { UtilService } from '../../../service/util.service';
import { NotifyService } from '../../notify/service/notify.service';
import { TranslateService } from '../../translate/translate.service';
import { AddQueryComponent, QueryObjectType } from '../add-query/add-query.component';
import { elem } from '../element';
import { FilterPanelComponent } from '../filter-panel/filter-panel.component';
import { SpecifiablePage } from '../page-selector/page-selector.component';
import { QueryComponent } from '../query/query.component';
import {
  ColumnSetting,
  DoubleClickEvent,
  FilterOperation,
  GroupResult,
  InnerColumnSetting,
  MenuButtonType,
  SearchKey,
  SelectMode,
  TableSettings
} from '../table';
import { TableService } from './table.service';

@Component({
  selector: 'common-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TableComponent implements OnInit, AfterViewInit, OnDestroy {
  /**
   * The setter of table settings
   */
  @Input()
  set settings(param) {
    this._settings = {
      hasSelect: true,
      hasMenuBar: true,
      hasMenuButtons: true,
      hasPaginator: true,
      hasHideShowCols: true,
      saveToStorage: true,
      hasSelectAll: true,
      selectMode: SelectMode.None,
      pageSize: 10,
      pageSizeOptions: [10, 20, 50, 100],
      ...param
    };
  }

  /**
   * The getter of table settings
   */
  get settings() {
    return this._settings;
  }

  /**
   * The setter for fixed data
   */
  @Input()
  set values(data: any[]) {
    this._originalfixedData = _.cloneDeep(data);
    this._fixedData = true;
    this.loading.next(false);
    this.clearTableElementCache();
    if (this.tableElement) {
      this.tableElement.nativeElement.scrollIntoView(true);
    }
  }

  /**
   * Error message under menu buttons
   */
  menuErrorMessage: string;

  /**
   * Emitted when double-clicking the row
   */
  @Output()
  doubleClickRow = new EventEmitter<DoubleClickEvent>();

  /**
   * Emitted when table content changed
   */
  @Output()
  contentChanged = new EventEmitter<void>();

  /**
   * Emitted when selected rows changed
   */
  @Output()
  selectedChanged = new EventEmitter<any[]>();

  /**
   * Emitted when a header was dropped
   */
  @Output()
  headerDropped = new EventEmitter<void>();

  /**
   * Emitted when a column was shown/hidden
   */
  @Output()
  columnShowHideChanged = new EventEmitter<void>();

  /**
   * Emitted when filter was changed
   */
  @Output()
  filterChanged = new EventEmitter<SearchKey[]>();

  /**
   * Table ID
   */
  tableId: string = '';

  /**
   * Separator key codes
   */
  separatorKeyCodes: number[] = [];

  /**
   * Table element
   */
  @ViewChild('table', { read: ElementRef }) tableElement: ElementRef;

  /**
   * Sort
   */
  @ViewChild(MatSort) sort: MatSort = new MatSort();

  /**
   * Paginator
   */
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  /**
   * Search keys
   */
  searchKeys: SearchKey[] = [];

  /**
   * Loading
   */
  loading = new Subject<boolean>();

  /**
   * Sorting field
   */
  matSortField: string = '';

  /**
   * Sorting direction
   */
  matSortDirection: string = '';

  /**
   * Selected options from the table
   */
  selection = new SelectionModel<any>(true, []);

  /**
   * Table data
   */
  data: any[] = [];

  /**
   * Data source that accepts a client-side data array and includes native support of filtering.
   */
  dataSource = new MatTableDataSource(this.data);

  /**
   * Settings of all columns.
   * This is used to control columns status
   */
  allColumns: InnerColumnSetting[] = [];

  /**
   * All default columns property defination.
   * It's used to record default settings, should NOT make any change in defaultColumns
   */
  defaultColumns: ColumnSetting[] = [];

  /**
   * Columns mapping of defaultColumns
   */
  defaultColumnsMap: any = {};

  /**
   * Enum of menu button type
   */
  MenuButtonType = MenuButtonType;

  /**
   * Data length
   */
  get dataLength(): number {
    return this.dataSource.data?.length ?? 0;
  }

  /**
   * now only valid for server side data paginator/query, when use server side paginator, this value should equal to this.paginator.length
   */
  total_number: number = 0;

  /**
   * Select mode enum
   */
  selectMode = SelectMode;

  /**
   * Check if the user is typing in search text
   */
  isTyping: boolean = false;

  /**
   * Used to cache customer defined element, to accelerate data render when move or add columns
   */
  customerCellElementMap: Map<string, any> = new Map<string, any>();

  /**
   * Filter panel settings
   */
  filterPanelSettings: any;

  /**
   * FilterPanelComponent
   */
  @ViewChild(FilterPanelComponent) filterPanelComponent: FilterPanelComponent;

  /**
   * Query component
   */
  @ViewChild(QueryComponent) queryComponent: QueryComponent;

  /**
   * Add query component
   */
  @ViewChild(AddQueryComponent) addQueryComponent: AddQueryComponent;

  /**
   * Grouped conditions
   */
  groupedConditions: string[];

  /**
   * Selected grouped condition
   */
  selectedGroupedCondition: string;

  /**
   * Elements of rendered rows
   */
  @ViewChildren('tableRow', { read: ElementRef }) renderedBodyRows: QueryList<
    ElementRef<HTMLTableRowElement>
  >;

  /**
   * The data into groups
   */
  private _groupResult: GroupResult[];

  /**
   * Table settings
   */
  private _settings: TableSettings;

  /**
   * Supported columns cannot be configured in settings.columns. Ex. '_select', '_action'
   */
  private _supportedPrivateColumns: ColumnSetting[] = [];

  /**
   * Whether the data is fixed
   */
  private _fixedData = false;

  /**
   * Original data cache
   */
  private _originalfixedData: any[];

  /**
   * Used to record previous page size for server-side pagination
   */
  private _previousPageSize: number;

  /**
   * HTTP request subscription and observable. To prevent that too many requests was sent at the same.
   */
  private _httpRequestSubscription: Subscription;
  private _httpObservable: Observable<any>;

  /**
   * Visible columns
   */
  private _visibleColumns: InnerColumnSetting[];

  /**
   * For page specificing
   */
  private _pageSubject: Subject<SpecifiablePage> = new Subject<SpecifiablePage>();
  private _pageObservable = this._pageSubject.asObservable();

  constructor(
    protected elementRef: ElementRef,
    protected cdf: ChangeDetectorRef,
    protected storageService: TableStorageService,
    protected tableService: TableService,
    protected handleExceptionService: HandleExceptionService,
    protected translateService: TranslateService,
    protected notifyService: NotifyService,
    protected helpService: HelpService,
    protected formatService: FormatService,
    protected util: UtilService
  ) {}

  /**
   * @internal
   */
  ngOnInit() {
    this.initSettings();
    this.clearTableElementCache();
  }

  /**
   * @internal
   */
  async ngAfterViewInit() {
    this.dataSource.sort = this.sort;
    if (this.settings.hasPaginator && this.settings.isClientSidePagination) {
      this.dataSource.paginator = this.paginator;
      this._previousPageSize = this.paginator.pageSize;
    }
    // To avoid #0990631. That's because mat-sort always sorts data by itself.
    this.dataSource.sortData = (data, sort) => {
      return data;
    };

    this.initSortField();
    this.cdf.detectChanges();

    await Promise.resolve();
    this.filterChanged.emit(this.searchKeys);

    const navElem = this.elementRef.nativeElement.querySelector(
      '.mat-mdc-paginator-outer-container .mat-mdc-paginator-range-actions .mat-mdc-paginator-navigation-next'
    );

    const newElem = elem('fd-n-table-page-selector', {
      page: 1,
      pageObservable: this._pageObservable,
      pageChanged: (val) => {
        this.specifyPage(val);
      }
    });

    if (navElem) {
      navElem.insertAdjacentElement('beforebegin', newElem);
    }
  }

  /**
   * @internal
   */
  ngOnDestroy(): void {
    this.cancelHttpRequest();
    this.loading.complete();
  }

  /**
   * Initialize table settings
   */
  initSettings() {
    this.tableId = this.settings.tableId;

    // handle defaultColumns
    this.initDefaultColumns();

    // handle allColumns
    this.initAllColumns();

    // handle searchKeys
    this.initSearchKeys();

    // group conditions by the specific name
    this.initGroupedConditions();
  }

  /**
   * Sort data
   *
   * @param sort sort state
   */
  async sortData(sort: any) {
    this.dataSource.data = [];
    this.matSortField = sort.active;
    this.matSortDirection = sort.direction;
    const sortSetting = {};
    sortSetting[sort.active] = sort.direction;
    if (this.settings.saveToStorage) {
      this.storageService.setSort(this.tableId, sortSetting);
    }
    this.refreshByQuery();
  }

  /**
   * Batch processing shows and hides
   *
   * @param settings {[column ID]: [is show]}
   */
  batchProcessingShowHide(settings: { [key: string]: boolean }) {
    for (const [key, value] of Object.entries(settings)) {
      const column = this.allColumns.find((col) => col.id === key);
      if (column) {
        column.hide = !value;
      }
    }
  }

  /**
   * Refresh the table according to query conditions
   *
   * @param keepSelected selection.selected should not be cleared if keepSelected is true
   */
  refreshByQuery(keepSelected = false) {
    const payload: any = {};
    this.handleSortSettingParam(payload);
    this.handleFilters(payload);

    if (this._fixedData) {
      this.refreshFixedData(payload);
    } else {
      for (const [key, value] of Object.entries(payload)) {
        if (Array.isArray(value)) {
          // Add multiple values separated by commas
          payload[key] = value.map((x) => x.value).join(',');
        }
      }

      this.refresh(payload, keepSelected);
    }
  }

  /**
   * Update fixed data
   * This is used to resolve the issue of the refreshByQuery function executing before the data is updated via the @Input() value.
   */
  updateFixedData(data: any[]) {
    this._originalfixedData = _.cloneDeep(data);
    this.clearTableElementCache();
    this.refreshByQuery();
  }

  /**
   * Executed when column was dropped
   *
   * @param evt dropped column
   */
  drop(evt: CdkDragDrop<string[]>) {
    // displayColumns has two '_select' and '_action' columns not in allColumns array, so every index in displayColumn need to add the summand
    let summand = this._supportedPrivateColumns.length;

    // check primary column status if primaryColumnForExpandedRow is not empty
    if (
      this.allColumns.find((x) => x.id === this.settings.primaryColumnForExpandedRow && !x.hide)
    ) {
      summand += 1;
    }

    const shownColumns = this.allColumns.filter((x) => !x.hide);
    const previousIndex = this.allColumns.findIndex(
      (x) => x === shownColumns[evt.previousIndex + summand]
    );
    const currentIndex = this.allColumns.findIndex(
      (x) => x === shownColumns[evt.currentIndex + summand]
    );

    moveItemInArray(this.allColumns, previousIndex, currentIndex);
    if (this.settings.saveToStorage) {
      this.storageService.setColumnOrder(
        this.tableId,
        this.allColumns
          .filter((x) => !['_select', '_action', '_expand'].includes(x.id))
          .map((x) => x.id)
      );
    }
    this.cdf.detectChanges();
    this.headerDropped.emit();
  }

  /**
   * Show/Hide the column
   *
   * @param id column ID
   */
  showHideColumn(event: MouseEvent, id: string) {
    event.stopPropagation();
    const col = this.allColumns.find((c) => c.id === id);
    if (col) {
      col.hide = !col.hide;
      if (this.settings.saveToStorage) {
        this.storageService.setHiddenColumns(
          this.tableId,
          this.allColumns.filter((x) => x.hide).map((x) => x.id)
        );
      }
      this.cdf.detectChanges();
      this.columnShowHideChanged.emit();
    }
  }

  /**
   * Reset columns settings
   */
  resetColumns(event: MouseEvent) {
    event.stopPropagation();

    this.storageService.setColumnOrder(this.tableId, []);
    this.storageService.setHiddenColumns(this.tableId, []);
    this.allColumns = [];
    this.cdf.detectChanges();
    this.initAllColumns();

    const currentSortSetting = this.storageService.getSort(this.tableId);
    if (!_.isEmpty(currentSortSetting)) {
      if (this.settings.saveToStorage) {
        this.storageService.setSort(this.tableId, {});
      }
      this.initSortField();
    }
    this.cdf.markForCheck();
    this.columnShowHideChanged.emit();
  }

  /**
   * Check if there is no all show/hide columns selected
   */
  isNoHideShowColumnsSelected() {
    const privateColumnIds = this._supportedPrivateColumns.map((x) => x.id);
    return this.allColumns.filter((x) => !privateColumnIds.includes(x.id) && !x.hide).length === 0;
  }

  /**
   * Check if all show/hide columns are selected
   */
  isAllHideShowColumnsSelected() {
    const privateColumnIds = this._supportedPrivateColumns.map((x) => x.id);
    const cols = this.allColumns.filter((x) => !privateColumnIds.includes(x.id));
    return (
      cols.filter((x) => !x.hide).length + this._supportedPrivateColumns.length ===
      this.allColumns.length
    );
  }

  /**
   * Show/Hide columns
   */
  showHideCols(event: MouseEvent) {
    event.stopPropagation();
    const privateColumnIds = this._supportedPrivateColumns.map((x) => x.id);
    const cols = this.allColumns.filter((x) => !privateColumnIds.includes(x.id));
    const isAllColumnsSelected = this.isAllHideShowColumnsSelected();

    cols.forEach((col) => {
      col.hide = isAllColumnsSelected;
    });

    // at lease 1 column must be shown
    if (isAllColumnsSelected) {
      cols[0].hide = false;
    }

    if (this.settings.saveToStorage) {
      this.storageService.setColumnSetting(this.tableId, this.allColumns);
    }
    this.cdf.detectChanges();
    this.columnShowHideChanged.emit();
  }

  /**
   * Check whether show/hide button should be disabled
   */
  checkShowHideButtonStatus(element: any) {
    const privateColumnIds = this._supportedPrivateColumns.map((x) => x.id);
    const cols = this.allColumns.filter(
      (x) => !privateColumnIds.includes(x.id) && x.id !== 'blank'
    );
    const hiddenCols = cols.filter((x) => x.hide);
    return (
      hiddenCols.length === cols.length - 1 &&
      element._elementRef.nativeElement.querySelector('input').checked
    );
  }

  /**
   * Check if all rows are selected
   */
  isAllSelected(): boolean {
    const selectedRows = this.selection.selected;
    const currentRows = this.dataSource.data;
    if (!this.settings.hasPaginator) {
      return currentRows.length === selectedRows.length;
    }

    if (!this.paginator) {
      return false;
    }

    const displayedRows = this.getDisplayedRows();
    // check if displayed rows are selected
    let selectedCount = 0;
    // *This might reduce the performance.
    displayedRows.forEach((row) => {
      if (selectedRows.find((x) => JSON.stringify(x) === JSON.stringify(row))) {
        selectedCount += 1;
      }
    });
    return displayedRows.length === selectedCount;
  }

  /**
   * Toggle all rows status
   */
  toggleRows() {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      const displayedRows = this.settings.hasPaginator
        ? this.getDisplayedRows()
        : this.dataSource.data.slice().filter((x) => !x.isGroupRow);
      this.selection.select(...displayedRows);
    }

    if (![undefined, SelectMode.None].includes(this.settings.selectMode)) {
      this.selectedChanged.emit(this.selection.selected);
    }
  }

  /**
   * Toggle row status
   *
   * @param row selected row
   */
  toggleRow(row: any) {
    if (this.settings.selectChangeCheck && this.settings.selectChangeCheck(row) === false) {
      return;
    }
    switch (this.settings.selectMode) {
      case SelectMode.Single:
        if (this.selection.selected.length && this.selection.selected[0] !== row) {
          // unselect the selected row
          this.selection.toggle(this.selection.selected[0]);
        }
        this.selection.toggle(row);
        break;
      case SelectMode.Multiple:
        this.selection.toggle(row);
        break;
    }
    if (![undefined, SelectMode.None].includes(this.settings.selectMode)) {
      this.selectedChanged.emit(this.selection.selected);
    }
  }

  /**
   * Toggle expanded status
   *
   * @param row clicked row
   */
  toggleExpandedStatus(row: any) {
    row.expand = !(row.expand ?? true);
    const children = this.dataSource.data.filter((x) => x.parent?.length && x.parent.includes(row));
    children.forEach((c) => {
      if (c.expand === false) {
        c.expand = true;
      }
      c.hidden = !row.expand;
    });
  }

  /**
   * Delete rows
   */
  deleteRows() {
    const fn = this.settings.toolbar['delete']?.click;
    if (fn) {
      fn(this.selection.selected, this);
    }
  }

  /**
   * Delete clicked row
   *
   * @param row clicked row
   */
  deleteRow(row) {
    const fn = this.settings.toolbar['delete']?.click;
    if (fn) {
      fn([row], this);
    }
  }

  /**
   * Check if the query ID is valid
   *
   * @param colId column ID
   * @returns true/false
   */
  isValidQueryKey(colId: string): boolean {
    if (['_select', '_action', '_expand'].includes(colId)) return false;

    let auto_fields = this.settings.columns.filter((x) => x.filterSettings).map((x) => x.id);

    if (this.settings.customSearchColumns?.length) {
      auto_fields = [...auto_fields, ...this.settings.customSearchColumns.map((x) => x.id)];
    }
    if (auto_fields.length) return auto_fields.includes(colId);

    return this.allColumns.map((c) => c.id).includes(colId);
  }

  /**
   * Remove all search conditions
   */
  clearSearchConditions() {
    this.searchKeys = [];
    if (this.settings.saveToStorage) {
      this.storageService.setQuery(this.tableId, this.searchKeys);
    }
    this.filterChanged.emit(this.searchKeys);
    this.refreshByQuery();
  }

  /**
   * Edit search condition
   *
   * @param searchKey search key
   */
  editSearchCondition(searchKey: SearchKey) {
    this.openFilterPanel({
      columnName: this.translateService.lookup(searchKey.key.label),
      columnId: searchKey.key.value,
      searchKey
    });
  }

  /**
   * Remove search condition
   *
   * @param condition search condition
   */
  removeSearchCondition(condition: SearchKey) {
    const index = this.searchKeys.findIndex((searchKey) => searchKey === condition);
    if (index === -1) {
      return;
    }

    this.searchKeys.splice(index, 1);
    if (this.settings.saveToStorage) {
      this.storageService.setQuery(this.tableId, this.searchKeys);
    }
    this.filterChanged.emit(this.searchKeys);
    this.refreshByQuery();
  }

  /**
   * Open query dialog
   */
  openQueryDialog() {
    let supportFilteringColumns = this.getSupportFilteringColumns();
    if (!supportFilteringColumns.length) {
      return;
    }

    if (this.groupedConditions?.length) {
      // filter columns by selected group condition
      supportFilteringColumns = supportFilteringColumns.filter(
        (x) =>
          !x.filterSettings.filterGroupBy ||
          x.filterSettings.filterGroupBy.includes(this.selectedGroupedCondition)
      );
    }

    // check if there is any column available
    const notAllowedDuplicateColumns = supportFilteringColumns.filter(
      (x) => !x.filterSettings.allowDuplicate
    );
    notAllowedDuplicateColumns.forEach((notAllowedDuplicateColumn) => {
      // remove the column from supportFilteringColumns if it has been added to searchKeys
      if (
        this.searchKeys.findIndex(
          (searchKey) => searchKey.key.value === notAllowedDuplicateColumn.id
        ) > -1
      ) {
        supportFilteringColumns.splice(
          supportFilteringColumns.findIndex((c) => c.id === notAllowedDuplicateColumn.id),
          1
        );
      }
    });

    if (supportFilteringColumns.length === 0) {
      this.notifyService.warning(this.translateService.lookup('errorMessage.no_filter_available'));
    } else {
      this.queryComponent.open(this.settings, supportFilteringColumns, this.searchKeys);
    }
  }

  /**
   * Open filter panel
   */
  openFilterPanel(param: { columnName: string; columnId: string; searchKey?: SearchKey }) {
    this.filterPanelComponent.open({
      ...param,
      filterSettings: this.defaultColumnsMap[param.columnId].filterSettings
    });
  }

  /**
   * Apply the filter
   *
   * @param filter filter from the filter panel
   */
  applyFilter(filter: {
    columnName: string;
    columnId: string;
    method: string;
    value: any;
    displayedValue: any;
    convertToDateTime?: boolean;
    prevSearchKey?: SearchKey;
  }) {
    const { columnId, value, method, prevSearchKey, displayedValue } = filter;
    if (!this.isValidQueryKey(columnId)) {
      return;
    }

    let replacedIndex;
    if (prevSearchKey) {
      // set replacedIndex for updating previous search key
      replacedIndex = this.searchKeys.findIndex((x) => x === prevSearchKey);
    }

    const currentColumnsSetting = this.defaultColumnsMap[columnId];
    const filterSettings = currentColumnsSetting?.filterSettings;
    let searchKey;
    if (filter.method === FilterOperation.Range) {
      const columnLabel = filterSettings?.displayedKey ?? currentColumnsSetting.langKey ?? columnId;
      searchKey = {
        key: { label: columnLabel, value: columnId },
        value: {
          label: value,
          value,
          convertToDateTime: filter.convertToDateTime
        },
        method
      };
    } else {
      const message = this.tableService.checkQueryValue(
        displayedValue,
        this.defaultColumnsMap[columnId]
      );
      if (message) {
        // show the warning to let user know what values are acceptable
        this.notifyService.warning(message);
        return;
      }

      const columnLabel = filterSettings?.displayedKey ?? currentColumnsSetting.langKey ?? columnId;
      searchKey = {
        key: { label: columnLabel, value: columnId },
        value: {
          label: displayedValue,
          value,
          convertToDateTime: filter.convertToDateTime
        },
        method
      };
    }
    if (replacedIndex > -1) {
      // update
      this.searchKeys[replacedIndex] = searchKey;
    } else {
      const groupedConditions = this.getGroupConditions();
      if (groupedConditions.length && this.searchKeys.length) {
        // clear search keys that do not belong to the current selectedGroupedCondition
        const supportFilteringColumns = this.getSupportFilteringColumns();
        _.cloneDeep(this.searchKeys).forEach((s) => {
          const filterGroupBy = supportFilteringColumns.find((x) => x.id === s.key.value)
            ?.filterSettings?.filterGroupBy;
          if (!filterGroupBy || !filterGroupBy.includes(this.selectedGroupedCondition)) {
            const index = this.searchKeys.findIndex(
              (searchKey) => searchKey.key.value === s.key.value
            );
            if (index === -1) {
              return;
            }
            this.searchKeys.splice(index, 1);
          }
        });
      }

      this.searchKeys.push(searchKey);
    }

    if (this.settings.saveToStorage) {
      this.storageService.setQuery(this.tableId, this.searchKeys);
    }
    this.filterChanged.emit(this.searchKeys);

    this.resetPage();
    this.refreshByQuery();
  }

  /**
   * Get the description of filter
   *
   * @param searchKey search key item
   */
  getFilterDescription(searchKey: SearchKey): string {
    return this.formatService.getSearchKeyDisplayText(searchKey, this.settings.timeZone);
  }

  /**
   * Listen on double-click event
   *
   * @param event mouse event
   * @param row clicked row
   */
  dblclickRow(event: MouseEvent, row: any) {
    this.doubleClickRow.emit({ event, row });
  }

  /**
   * Get IDs of allColumns
   */
  getAllColumnIds(): string[] {
    return this.allColumns.filter((c) => !c.hide).map((c) => c.id);
  }

  /**
   * Get visible columns from allColumns
   */
  getVisibleColumns(): InnerColumnSetting[] {
    const visibleColumns = this.allColumns.filter((c) => !c.hide);
    if (
      !this._visibleColumns ||
      JSON.stringify(visibleColumns) !== JSON.stringify(this._visibleColumns)
    ) {
      this._visibleColumns = visibleColumns;
    }
    return this._visibleColumns;
  }

  /**
   * Execute the custom function
   *
   * @param fn function
   * @param row selected row
   * @param $event mouse event
   */
  applyFn(
    fn: (row, table: TableComponent, $event?: MouseEvent) => void,
    row: any,
    $event: MouseEvent
  ) {
    fn(row, this, $event);
  }

  /**
   * Check button status
   *
   * @param param parameters (true/false/funcion)
   * @param row selected row/rows
   * @returns true/false
   */
  checkButtonStatus(param: boolean | ((selected) => boolean), row: any | any[]): boolean {
    if (typeof param === 'undefined') {
      return false;
    }
    if (typeof param === 'boolean') {
      return param;
    }
    return param(row);
  }

  /**
   * Listen on content changed event
   */
  async onContentChanged() {
    await Promise.resolve();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.length = this.getDataLength();
    }
    this.clearOldElementCache();
    this.contentChanged.emit();
  }

  /**
   * Listen on paginator page change event
   *
   * @param param page event
   */
  onPageChange(param: PageEvent) {
    if (!this.settings.isClientSidePagination) {
      // when using server-side pagination, selection.selected should not be cleared after changing the page index or page size
      const keepSelected =
        this.settings.recordSelectedRows &&
        (param.previousPageIndex !== param.pageIndex || this._previousPageSize !== param.pageSize);
      this.refreshByQuery(keepSelected);
    } else {
      this.updatePageSpecifyEditor();
    }

    if (param.previousPageIndex !== param.pageIndex) {
      this.tableElement.nativeElement.scrollIntoView(true);
    }
    this.clearTableElementCache();
  }

  /**
   * Get data length for the paginator
   */
  getDataLength(): number {
    return this.dataSource.data.length;
  }

  /**
   * Reset page index to 0 if the paginator exists
   */
  resetPage() {
    if (this.paginator && this.settings.hasPaginator) {
      this.paginator.pageIndex = 0;
    }
  }

  /**
   * Open online help
   */
  openHelp() {
    const helpContent =
      this.settings?.onlineHelp ??
      `<p>Later software versions will display page-specific help text in this space.</p>`;
    this.helpService.showHelp(helpContent);
  }

  /**
   * Set the error message under menu buttons
   */
  setMenuErrorMessage(message: string) {
    this.menuErrorMessage = message;
  }

  /**
   * Get column name
   *
   * @param id column ID
   */
  getColumnName(id: string) {
    const col = this._settings.columns.find((x) => x.id === id);
    return this.translateService.lookup(col.langKey) || col.id;
  }

  /**
   * Save query object
   */
  saveQueryObject() {
    if (!QueryObjectType[this.settings.queryObjectType]) {
      return;
    }

    if (!this.searchKeys.length) {
      this.notifyService.warning(this.translateService.lookup('errorMessage.noQuery'));
      return;
    }

    const payload = {};
    this.handleFilters(payload);
    this.addQueryComponent.open(payload, this.settings.queryObjectType);
  }

  /**
   * Check if the clicked row is group row
   *
   * @param index row index
   * @param row row
   */
  isGroupRow(index, row): boolean {
    return row.isGroupRow;
  }

  /**
   * Click group row
   *
   * @param row clicked row
   */
  clickGroupRow(row) {
    if (this.settings.keepExpanding) {
      return;
    }

    row.expand = !row.expand;
    const rows = this._groupResult.find((x) => x.value === row.key)?.rows;
    if (rows) {
      rows.forEach((r) => (r.hidden = row.expand));
    }
  }

  /**
   * Set the title for the grouping row
   *
   * @param $element element for label
   * @param label label
   */
  setGroupingTitle($element: HTMLElement, label: string | HTMLElement) {
    if ($element.innerHTML !== '') {
      // prevent duplicate element rendering
      return;
    }

    if (label instanceof HTMLElement) {
      $element.appendChild(label);
    } else {
      $element.innerHTML = label;
    }
  }

  /**
   * Click row
   *
   * @param row clicked row
   */
  clickRow(row: any) {
    if (Boolean(this.settings.disableRowClick)) {
      return;
    }
    if (this.settings.clickRow) {
      this.settings.clickRow(row);
    } else {
      this.settings.primaryColumnForExpandedRow
        ? this.toggleExpandedStatus(row)
        : this.toggleRow(row);
    }
  }

  /**
   * Check if the text-overflow ellipsis is active
   *
   * @param $element element
   * @returns true/false
   */
  isEllipsisActive($element: HTMLElement): boolean {
    return this.util.isEllipsisActive($element);
  }

  /**
   * Select grouped condition
   *
   * @param condition grouped condition
   */
  selectGroupedCondition(condition: string) {
    this.selectedGroupedCondition = condition;
  }

  /**
   * Get search bar placeholder
   */
  getSearchBarPlaceholder() {
    let defaultPlaceholder = 'search_query';
    const searchBarPlaceholder = this.settings.searchBarPlaceholder;
    if (!searchBarPlaceholder) {
      return defaultPlaceholder;
    }

    if (typeof searchBarPlaceholder === 'string') {
      return searchBarPlaceholder;
    } else {
      const customPlaceholder = searchBarPlaceholder[this.selectedGroupedCondition];
      return customPlaceholder ?? defaultPlaceholder;
    }
  }

  /**
   * Check this table and its children
   */
  detectChanges() {
    this.cdf.detectChanges();
  }

  /**
   * Refresh the table
   *
   * @param payload payload
   * @param keepSelected selection.selected should not be cleared if keepSelected is true
   */
  private refresh(payload: any, keepSelected = false) {
    const api = this.settings?.remote?.api;

    if (!api) {
      this.loading.next(false);
      return;
    }

    this.loading.next(true);
    this.clearTableElementCache();

    const isServerSidePagination =
      this.settings.hasPaginator && !this.settings.isClientSidePagination;

    let remotePayload = this.settings.remote.payload;
    if (!_.isEmpty(remotePayload)) {
      payload = _.extend(payload, remotePayload);
    }

    if (typeof remotePayload === 'function') {
      remotePayload = remotePayload(this);
      payload = _.extend(payload, remotePayload);
    }

    if (isServerSidePagination) {
      _.extend(payload, this.getPaginationRange());
    }

    this.cancelHttpRequest();

    this._httpObservable = api(payload);
    this._httpRequestSubscription = this._httpObservable.subscribe({
      next: (res: any) => {
        const { postProcess, hasPaginator } = this.settings;
        if (res.hasOwnProperty('error')) {
          this.handleExceptionService.handleMessage(res.error);
        } else if (res.hasOwnProperty('ErrorCode') && res.ErrorCode !== 0) {
          this.handleExceptionService.handleMessage(res?.ErrorMessage);
        } else {
          if (isServerSidePagination) {
            const total = res.total ?? 0;
            this.paginator.length = total;
            this.total_number = total;
          }

          let resData;
          if (postProcess) {
            resData = postProcess(res, this);
          } else {
            resData = res.list ?? [];
          }

          if (
            this.settings.groupBy &&
            (resData.length || (this.settings.groupBy as any)?.persist)
          ) {
            resData = this.handleRowGrouping(resData);
          }

          this.dataSource.data = resData;

          if (isServerSidePagination) {
            this.checkSelectedRows();
          }
        }

        if (this.dataSource.paginator && hasPaginator) {
          this.dataSource.paginator.firstPage();
        }

        if (!isServerSidePagination || !keepSelected) {
          this.selection.clear();
        }

        this.loading.next(false);
        this.cdf.detectChanges();

        if (hasPaginator) {
          setTimeout(() => this.updatePageSpecifyEditor());
        }
      },
      error: (err: any) => {
        this.loading.next(false);
        this.cdf.detectChanges();
        this.handleExceptionService.handleError(err);
      }
    });
  }

  /**
   * Get the start and end index of pagination
   */
  private getPaginationRange(): { page: number; per_page: number } {
    const { pageIndex, pageSize } = this.paginator;
    return { page: pageIndex + 1, per_page: pageSize };
  }

  /**
   * Get displayed rows according to paginator value
   */
  private getDisplayedRows() {
    let rows;
    if (this.settings.hasPaginator && !this.settings.isClientSidePagination) {
      rows = this.dataSource.data.slice();
    } else {
      const range = this.getPaginationRange();
      const start = range.page * range.per_page;
      rows = this.dataSource.data.slice(start - 1, start + range.per_page);
    }
    return rows.filter((x) => !x.isGroupRow);
  }

  /**
   * Initialize defaultColumns
   */
  private initDefaultColumns() {
    if (this.settings.hasSelect && this.settings.selectMode !== SelectMode.None) {
      this._supportedPrivateColumns.push({ id: '_select' });
    }
    if (this.settings.customActions?.length) {
      this._supportedPrivateColumns.push({ id: '_action' });
    }
    if (this.settings.primaryColumnForExpandedRow) {
      this._supportedPrivateColumns.push({ id: '_expand' });
    }
    this.defaultColumns = [
      ...this._supportedPrivateColumns.map((c) => c),
      ...this.settings.columns.map((c: any) => c)
    ];
  }

  /**
   * Initialize allColumns
   */
  private initAllColumns() {
    const hiddenColumns = this.storageService.getHiddenColumns(this.tableId);
    const columnOrder = this.storageService.getColumnOrder(this.tableId);
    let allColumns: any = _.cloneDeep(this.settings.columns);

    if (hiddenColumns?.length) {
      hiddenColumns.forEach((c) => {
        const col = allColumns.find((x) => x.id === c);
        if (col) {
          col.hide = true;
        }
      });
    } else if (this.settings.defaultDisplayedColumns?.length) {
      // display columns which are configured in defaultDisplayedColumns
      allColumns.forEach((col: any) => {
        if (!this.settings.defaultDisplayedColumns.includes(col.id)) {
          col.hide = true;
        }
      });
    }

    if (columnOrder?.length) {
      const newColumns = [];

      ['_select', '_action', '_expand', ...columnOrder].forEach((c) => {
        const col = allColumns.find((x) => x.id === c);
        if (col) {
          newColumns.push(col);
        }
      });
      allColumns.forEach((x) => {
        if (!newColumns.includes(x)) {
          newColumns.push(x);
        }
      });
      allColumns = newColumns;
    }

    this.allColumns = _.cloneDeep([...this._supportedPrivateColumns, ...allColumns]);
    this.defaultColumns.forEach((c: any) => {
      this.defaultColumnsMap[c.id] = c;
    });

    if (this.settings.movePrivateColumns?.length) {
      this.handlePrivateColumnMovement();
    }

    if (this.settings.customSearchColumns?.length) {
      // Add customSearchColumns to maps
      this.settings.customSearchColumns.forEach((c: any) => {
        this.defaultColumnsMap[c.id] = c;
      });
    }
  }

  /**
   * Refresh the table with fixed data
   *
   * @param filters filters
   */
  private refreshFixedData(filters) {
    const filteredData = this.tableService.filterFixedData(
      this._originalfixedData,
      filters,
      this.defaultColumnsMap
    );
    if (this.settings.forceToRefresh) {
      this.dataSource.data = [];
    }
    this.dataSource.data =
      this.settings.groupBy && (filteredData.length || (this.settings.groupBy as any)?.persist)
        ? this.handleRowGrouping(filteredData)
        : filteredData;
    this.selection.clear();

    if (this.settings.hasPaginator) {
      setTimeout(() => this.updatePageSpecifyEditor());
    }
  }

  /**
   * Get search keys from local storage and check if keys are still in the column settings
   */
  private initSearchKeys() {
    const searchKeys = [];
    let cachedSearchKeys;
    if (this.settings.searchKeys?.length) {
      cachedSearchKeys = this.settings.searchKeys;
    } else {
      cachedSearchKeys = [...this.storageService.getQuery(this.tableId)] as any;
    }

    cachedSearchKeys.forEach((cachedSearchKey) => {
      if (this.defaultColumnsMap[cachedSearchKey.key.value]) {
        searchKeys.push(cachedSearchKey);
      }
    });

    if (searchKeys.length !== cachedSearchKeys.length && this.settings.saveToStorage) {
      this.storageService.setQuery(this.tableId, searchKeys);
    }
    this.searchKeys = searchKeys;
  }

  /**
   * Check if selected rows should be re-selected
   *
   * If the table is using server-side pagination, the rows of current page will be new when switching the page.
   * So, rows in selection.selected will not be selected on the tabe.
   * We need to check if the displayed rows are in selection.selected and re-select them.
   */
  private checkSelectedRows() {
    if (!this.settings.isClientSidePagination) {
      this.dataSource.data.forEach((displayedRow) => {
        const idx = displayedRow.isGroupRow
          ? -1
          : this.selection.selected.findIndex(
              (selectedRow) => JSON.stringify(selectedRow) === JSON.stringify(displayedRow)
            );
        if (idx > -1) {
          this.selection.selected.splice(idx, 1);
          this.selection.select(displayedRow);
        }
      });
    }
  }

  /**
   * Initialize sort field if settings.defaultSortField is not empty
   */
  private initSortField() {
    const sortSetting = this.storageService.getSort(this.tableId);

    if (!_.isEmpty(sortSetting)) {
      this.handleSortFields(sortSetting);
    } else if (!_.isEmpty(this.settings.defaultSortField)) {
      this.handleSortFields(this.settings.defaultSortField);
    } else {
      this.matSortField = '';
      this.matSortDirection = '';
      this.sort.active = '';
      this.refreshByQuery();
    }
  }

  /**
   * Handle sort fields
   *
   * @param sortSetting sort setting
   */
  private handleSortFields(sortSetting) {
    // for now, the sorting function can only support one column
    const key = Object.keys(sortSetting)[0];
    const column = this.allColumns.find((x) => x.id === key);

    if (!column || column.hide) {
      // sort field should not be processed when initializing if the field was hidden
      this.refreshByQuery();
      return;
    }

    const sortState: Sort = {
      active: key,
      direction: sortSetting[key] as SortDirection
    };
    if (this.sort.active === sortState.active && this.sort.direction === sortState.direction) {
      return;
    }

    this.sort.active = sortState.active;
    this.sort.direction = sortState.direction;
    this.sort.sortChange.emit(sortState);
  }

  /**
   * Handle sort setting parameters before querying
   *
   * @param payload query parameter
   */
  private handleSortSettingParam(payload) {
    if (this.matSortDirection === '') {
      return;
    }
    // check whether the column was hidden, if yes, the sort setting should not be added
    const column = this.allColumns.find((x) => x.id === this.matSortField);
    if (!column || column.hide) {
      return;
    }

    _.extend(payload, {
      sort_field: this.matSortField,
      sort_method: this.matSortDirection
    });
  }

  /**
   * Convert search keys to search object backend supports
   *
   * @param payload query parameter
   */
  private handleFilters(payload) {
    this.searchKeys.forEach((s) => {
      if (!s.method) {
        return;
      }

      const columnId = s.key.value;
      let value = s.value.value;

      if ([columnId, value].includes('')) {
        return;
      }

      const columnSettings = this.defaultColumnsMap[columnId];
      const searchKey = columnSettings.filterSettings?.searchReplacedKey ?? columnId;

      if (!payload.hasOwnProperty(searchKey)) {
        payload[searchKey] = [];
      }

      payload[searchKey].push(
        s.method === FilterOperation.Range ? value : { value, method: s.method }
      );
    });
  }

  /**
   * Cancel HTTP request
   */
  private cancelHttpRequest() {
    if (this._httpRequestSubscription) {
      this._httpRequestSubscription.unsubscribe();
      this._httpRequestSubscription = undefined;
    }

    if (this._httpObservable) {
      this._httpObservable['cancelRequest']?.();
      this._httpObservable = undefined;
    }
  }

  /**
   * Handle row-grouping
   */
  private handleRowGrouping(resData: any[]): any[] {
    const { groupBy } = this.settings;
    let ret: GroupResult[] = [];
    if (_.isString(groupBy)) {
      for (const [name, rows] of Object.entries(_.groupBy(resData, groupBy))) {
        ret.push({ label: name, value: name, rows });
      }
    } else {
      const columnId = groupBy.columnId;
      const groupResult = _.groupBy(resData, columnId);

      if (groupBy.useCellFormatter) {
        const cellFormatter = this.settings.columns.find((x) => x.id === columnId)?.cellFormatter;
        if (cellFormatter) {
          for (const [name, rows] of Object.entries(groupResult)) {
            const param = {};
            param[columnId] = name;
            ret.push({ label: cellFormatter(param), value: name, rows });
          }
        }
      } else {
        const { converter } = groupBy;
        if (_.isFunction(converter)) {
          for (const [name, rows] of Object.entries(groupResult)) {
            ret.push({ label: converter(name), value: name, rows });
          }
        } else if (_.isObject(converter)) {
          for (const [name, rows] of Object.entries(groupResult)) {
            ret.push({
              label: this.getGroupingTitle(converter[name]),
              value: name,
              rows
            });
          }
          if (groupBy.persist) {
            const emptyTitles = _.xor(
              Object.keys(converter),
              ret.map((x) => x.value)
            );
            emptyTitles.forEach((x) => {
              ret.push({
                label: this.getGroupingTitle(converter[x]),
                value: x,
                rows: []
              });
            });
          }
        } else {
          for (const [name, rows] of Object.entries(groupResult)) {
            ret.push({ label: name, value: name, rows });
          }
        }
      }
    }

    if ((groupBy as any)?.order?.length) {
      const newRet = [];
      (groupBy as any).order.forEach((val) => {
        const item = ret.find((x) => x.value === val);
        if (item) {
          newRet.push(item);
        }
      });
      ret = newRet;
    }

    this._groupResult = ret;
    let result = [];
    ret.forEach((groupResult) => {
      result = [
        ...result,
        {
          isGroupRow: true,
          label: groupResult.label,
          key: groupResult.value,
          total: groupResult.rows.length,
          expanded: true
        },
        ...groupResult.rows
      ];
    });

    return result;
  }

  /**
   * Get grouping title
   *
   * @param title title mapping
   */
  private getGroupingTitle(title: string | { title: string; icon?: string; color?: string }) {
    if (_.isString(title)) {
      return title;
    }

    return elem('fd-n-icon', {
      svgIcon: title.icon,
      color: title.color,
      text: title.title
    });
  }

  /**
   * Group conditions by the specific name
   */
  private initGroupedConditions() {
    const groupedConditions = this.getGroupConditions();
    if (!groupedConditions.length) {
      return;
    }

    this.groupedConditions = groupedConditions;
    this.selectedGroupedCondition = groupedConditions[0];

    // need to set corresponding selectedGroupedCondition if searchKeys is not empty
    if (this.searchKeys.length) {
      const supportFilteringColumns = this.getSupportFilteringColumns();

      // collect all filterGroups by searchKeys
      let filterGroupsInSearchKeys = [];
      this.searchKeys.forEach((searchKey) => {
        const filterGroupBy = supportFilteringColumns.find((x) => x.id === searchKey.key.value)
          ?.filterSettings?.filterGroupBy;
        if (filterGroupBy) {
          filterGroupsInSearchKeys = [...filterGroupsInSearchKeys, ...filterGroupBy];
        }
      });

      // the filterGroupBy name that appears the most should be set as the selectedGroupedCondition
      let currentSelectedGroupedCondition;
      let num = 0;
      for (const [key, value] of Object.entries(_.groupBy(filterGroupsInSearchKeys))) {
        const currNum = value.length;
        if (currNum > num) {
          num = currNum;
          currentSelectedGroupedCondition = key;
        }
      }

      if (this.settings.searchBarPlaceholder[currentSelectedGroupedCondition]) {
        this.selectedGroupedCondition = currentSelectedGroupedCondition;
      }
    }
  }

  /**
   * Get group conditions
   */
  private getGroupConditions() {
    return _.uniq(
      _.flatten(
        _.without(
          this.allColumns.map((item) => _.get(item, 'filterSettings.filterGroupBy')),
          undefined
        )
      )
    );
  }

  /**
   * Get columns that support filtering
   */
  private getSupportFilteringColumns() {
    return [
      ...(this.allColumns.filter((x) => x.filterSettings) ?? []),
      ...(this.settings.customSearchColumns ?? [])
    ];
  }

  /**
   * Handle the movement of private columns
   */
  private handlePrivateColumnMovement() {
    this.settings.movePrivateColumns.forEach((mColumn) => {
      const mColumnIdx = this.allColumns.findIndex((x) => x.id === mColumn.privateColumnId);
      const afterColumn = this.allColumns.find((x) => x.id === mColumn.after);
      if (mColumnIdx > -1 && afterColumn) {
        const tmpColumn = _.cloneDeep(this.allColumns[mColumnIdx]);
        this.allColumns.splice(mColumnIdx, 1);

        const afterColumnIdx = this.allColumns.findIndex((x) => x.id === mColumn.after);
        this.allColumns.splice(afterColumnIdx + 1, 0, tmpColumn);
      }
    });
  }

  /**
   * Specify the page
   */
  private specifyPage(value: number) {
    this.paginator.pageIndex = value - 1;
    this.paginator.page.next({
      pageIndex: value - 1,
      pageSize: this.paginator.pageSize,
      length: this.paginator.length
    });
  }

  clearTableElementCache() {
    this.customerCellElementMap.clear();
  }
  calculateElementCacheKey(rowData: any, column: string): string {
    // TODO if JSON a rowData is too slow, we may select a primary key in the columns
    const res = `${JSON.stringify(rowData)}-${column}`;
    return res;
  }
  clearOldElementCache() {
    const dataKeys: string[] = [];
    this.dataSource.data.forEach((rData) => {
      this.getVisibleColumns().forEach((column) => {
        if (!rData.isGroupRow) {
          dataKeys.push(this.calculateElementCacheKey(rData, column.id));
        }
      });
    });
    for (let testKey of Array.from(this.customerCellElementMap.keys())) {
      if (!dataKeys.includes(testKey)) {
        this.customerCellElementMap.delete(testKey);
      }
    }
  }

  /**
   * Update the page specify editor
   */
  private updatePageSpecifyEditor() {
    this._pageSubject.next({
      current: this.paginator.pageIndex + 1,
      length: Math.ceil(this.paginator.length / this.paginator.pageSize)
    });
  }
}
