<div (click)="$event.stopPropagation()">
  <mat-select
    [disableRipple]="true"
    (selectionChange)="onChange($event)"
    [value]="value"
    [panelWidth]="null"
    [ngStyle]="{width}"
  >
    <mat-select-trigger>
      <fd-icon
        [svgIcon]="selectedItem.icon"
        text="{{ selectedItem.text | translate }}"
        [color]="selectedItem.color"
      ></fd-icon>
    </mat-select-trigger>
    <mat-option *ngFor="let option of options" [value]="option.value">
      <mat-icon *ngIf="option.icon" [svgIcon]="option.icon" [ngStyle]="{ color: option.color }"></mat-icon>
      <span>{{ option.label | translate }}</span>
    </mat-option>
  </mat-select>
</div>
