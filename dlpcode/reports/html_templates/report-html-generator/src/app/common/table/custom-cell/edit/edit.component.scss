.value {
  display: inline-flex;
  align-items: flex-start;
  min-width: 20px;
  min-height: 20px;
  cursor: pointer;
  gap: 3px;
  justify-content: center;

  &.row-reverse {
    flex-direction: row-reverse;
  }

  &:hover {
    transition: background-color, opacity 0.2s;
    opacity: 0.75;
    background-color: #eee;
  }
}

.title {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ececec;

  span {
    width: 100%;
    font-size: 14px;
    font-weight: 700;
    line-height: 16.41px;
    letter-spacing: 0px;
  }

  mat-icon {
    height: 14px;
    width: 14px;
    font-size: 14px;
    color: #47525c;
  }
}

.body {
  padding: 12px 21px 0;
  display: flex;
}

textarea {
  resize: none;
  width: 309px;
  height: 96px;
}

.btn-box {
  padding: 0 21px 6px;
  display: flex;

  mat-icon {
    height: 24px;
    width: 24px;
    font-size: 24px;
    color: #47535c;
  }

  .right {
    display: inline-flex;
    margin-top: -1px;
    margin-left: auto;
    padding: 6px 10px;
    gap: 6px;
    border: 1px solid #ececec;
    border-radius: 0px, 0px, 2px, 2px;
    box-shadow: 0px 1px 10px 0px #00000033;
  }
}

.title,
.btn-box {
  mat-icon {
    cursor: pointer;
    margin: 0;

    &:hover {
      transition: opacity 0.2s;
      opacity: 0.5;
    }
  }
}
