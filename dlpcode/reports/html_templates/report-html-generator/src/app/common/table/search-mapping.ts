export const Status = {
  Online: 1,
  Offline: 0
};

export const TaskStatus = {
  Scheduled: 0,
  Scanning: 1,
  Scanned: 2,
  Failed: 3,
  Paused: 4,
  Canceled: 5
};

export const SourceType = {
  CloudServer: 0,
  Detector: 1,
  User: 2,
  Task: 3,
  Connection: 4
};

export const EventType = {
  System: 0,
  Detector: 1,
  User: 2
};

export const SoftwareStatus = {
  Up: 1,
  Down: 0
};

export const Transport = {
  arp: 0,
  icmp: 1,
  tcp: 2,
  udp: 3
};

export const SoftwareTransport = {
  Other: -1,
  ARP: 0,
  ICMP: 1,
  TCP: 2,
  UDP: 3
};

export const HighestSeverity = {
  Unknown: 0,
  Info: 1,
  Low: 2,
  Medium: 3,
  High: 4,
  Critical: 5
};

export const SourceStatus = {
  Online: 1,
  Offline: 0
};

export const DestinationStatus = {
  Online: 1,
  Offline: 0
};

export const AlarmLevel = {
  Notice: 1,
  Warning: 2,
  Critical: 3
};

export const AlarmAckStatus = {
  Unacknowledged: 0,
  Acknowledged: 1
};

export const DLPPolicyStatus = {
  Enabled: 1,
  Disabled: 0
};


export const TagStatus = {
  Enabled: 1,
  Disabled: 0
}

export type LogServerType = {
  [key: string]: string;
};

export const LogServerTypes: LogServerType = {
  Syslog: "syslog",
  'FortiAnalyzer': "faz"
};

export const LogServerStatus = {
  Enabled: 1,
  Disabled: 0
};

export const LogStatus = {
  Enabled: true,
  Disabled: false
};

export const ConnectionStatus = {
  Disconnected: 0,
  Connected: 1,
  'Needs Authorization': 2
};

export const ApiKeyStatus = {
  Active: true,
  InActive: false
};