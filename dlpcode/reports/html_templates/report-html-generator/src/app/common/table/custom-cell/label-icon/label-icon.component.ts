import { Component, Input } from '@angular/core';

@Component({
  selector: 'fd-label-icon',
  templateUrl: './label-icon.component.html',
  styleUrls: ['./label-icon.component.scss']
})
export class LabelIconComponent {
  /**
   * Displayed text
   */
  @Input() text: string | number;

  /**
   * Color
   */
  @Input()
  color?: string = '#47535c';

  /**
   * Icon height
   */
  @Input()
  set height(value: number | string) {
    this._height = typeof value === 'string' ? value : `${value}px`;
  }

  get height(): string {
    return this._height;
  }

  /**
   * Icon width
   */
  @Input()
  set width(value: number | string) {
    this._width = typeof value === 'string' ? value : `${value}px`;
  }

  get width(): string {
    return this._width;
  }

  private _height: string = '32px';
  private _width: string = '50px';
}
