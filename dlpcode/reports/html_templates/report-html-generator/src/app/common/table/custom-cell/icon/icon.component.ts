import { Component, HostBinding, Input } from '@angular/core';

@Component({
  selector: 'fd-icon',
  templateUrl: './icon.component.html',
  styleUrls: ['./icon.component.scss']
})
export class IconComponent {
  /**
   * Angular material icon name
   */
  @Input()
  matIcon: string = '';

  /**
   * SVG icon name
   */
  @Input()
  svgIcon: string = '';

  /**
   * Color
   */
  @Input()
  color?: string;

  /**
   * Font set
   */
  @Input()
  fontSet?: string;

  /**
   * Inline
   */
  @Input()
  inline?: boolean;

  /**
   * Class name
   */
  @Input()
  className?: string;

  /**
   * Show the tooltip when hovering the icon
   */
  @Input()
  tooltip?: string;

  /**
   * Text
   */
  @Input()
  text?: string;

  /**
   * Set row-reverse class
   */
  @HostBinding('class.row-reverse') reverse: boolean = false;

  /**
   * Set the gap
   */
  @HostBinding('style.gap') customGap: string;

  /**
   * Icon position
   */
  @Input()
  set iconPosition(position: 'right' | 'left') {
    this.reverse = position === 'right';
  }

  /**
   * Gap between icon and text
   */
  @Input()
  set gap(value: string) {
    this.customGap = value;
  }

  /**
   * Setter of icon size
   */
  @Input()
  set size(value: number | string) {
    this._size = typeof value === 'string' ? value : `${value}px`;
  }

  /**
   * Getter of icon size
   */
  get size(): string {
    return this._size;
  }

  /**
   * Icon size
   */
  private _size: string;
}
