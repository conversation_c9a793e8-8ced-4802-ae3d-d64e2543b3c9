<div [matMenuTriggerFor]="popover"></div>
<mat-menu
  #popover="matMenu"
  yPosition="below"
  class="multiple-items-popover table-add-query-popover"
>
  <div (click)="$event.stopPropagation()" *ngIf="formGroup" [formGroup]="formGroup" class="content">
    <div class="loading-panel" *ngIf="loading"><mat-spinner></mat-spinner></div>
    <div class="form-field">
      <label>{{ 'query_object.query_obj_name' | translate }} *</label>
      <div class="input-field">
        <input matInput type="text" formControlName="query_obj_name" />
        <mat-error
          *ngIf="formGroup.get('query_obj_name').dirty && formGroup.get('query_obj_name').invalid"
          >{{ formGroup.get('query_obj_name').errors | formControlError }}</mat-error
        >
      </div>
    </div>
    <div class="form-field">
      <label>{{ 'query_object.description' | translate }}</label>
      <div class="input-field">
        <textarea matInput formControlName="description"></textarea>
        <mat-error
          *ngIf="formGroup.get('description').dirty && formGroup.get('description').invalid"
          >{{ formGroup.get('description').errors | formControlError }}</mat-error
        >
      </div>
    </div>
    <mat-divider></mat-divider>
    <div class="footer">
      <div class="action-btn-group">
        <button mat-stroked-button [disableRipple]="true" (click)="close()">
          {{ 'cancel' | translate | uppercase }}
        </button>
        <button mat-flat-button color="primary" [disableRipple]="true" (click)="save()">
          {{ 'save_query' | translate | uppercase }}
        </button>
      </div>
    </div>
  </div>
</mat-menu>
