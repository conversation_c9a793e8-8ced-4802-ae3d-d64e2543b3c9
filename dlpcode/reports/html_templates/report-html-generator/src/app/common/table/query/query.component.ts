import { Component, ElementRef, EventEmitter, Output, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatMenuTrigger } from '@angular/material/menu';
import { Observable, map, startWith } from 'rxjs';
import { ColumnSetting, InnerColumnSetting, SearchKey, TableSettings } from '../table';
import { TranslateService } from '../../translate/translate.service';

@Component({
  selector: 'fd-table-query',
  templateUrl: './query.component.html',
  styleUrls: ['./query.component.scss']
})
export class QueryComponent {
  /**
   * Loading
   */
  loading = true;

  /**
   * Filtered columns
   */
  filteredColumns: Observable<any[]> = new Observable();

  /**
   * Individual columns
   */
  individualColumns: Observable<any[]> = new Observable();

  /**
   * Menu trigger
   */
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;

  /**
   * Search input
   */
  @ViewChild('searchInput') searchInput!: ElementRef<HTMLInputElement>;

  /**
   * Emitted when a column name was selected
   */
  @Output()
  selected = new EventEmitter<{ columnName: string; columnId: string }>();

  /**
   * Search form control
   */
  searchControl = new FormControl('');

  /**
   * Is typing
   */
  isTyping = false;

  /**
   * Table settings
   */
  settings: TableSettings;

  /**
   * Settings of all columns.
   * This is used to control columns status
   */
  allColumns: InnerColumnSetting[] = [];

  /**
   * Conditions of selected query object
   */
  conditions: any[];

  /**
   * Whether individual columns has added to search keys
   */
  hasIndividualColumn: boolean = false;

  constructor(private translateService: TranslateService) {}

  /**
   * @internal
   */
  ngOnInit() {
    this.filteredColumns = this.searchControl.valueChanges.pipe(
      startWith(''),
      map((value) => this.filterColumns(value || ''))
    );

    this.individualColumns = this.searchControl.valueChanges.pipe(
      startWith(''),
      map((value) => this.filterColumns(value || '', true))
    );
  }

  /**
   * @internal
   */
  ngAfterViewInit(): void {
    this.trigger.menuClosed.subscribe(() => (this.loading = true));
  }

  /**
   * Open
   */
  open(settings: TableSettings, allColumns: InnerColumnSetting[], searchKeys: SearchKey[]) {
    this.settings = settings;
    this.allColumns = allColumns;

    this.checkIndividualColumns(settings, searchKeys);

    this.trigger.openMenu();
    this.loading = false;
  }

  /**
   * Select column
   */
  selectColumn(column: ColumnSetting) {
    this.searchControl.setValue('');
    this.isTyping = false;

    this.selected.emit({
      columnName: this.getColumnName(column),
      columnId: column.id
    });
    this.trigger.closeMenu();
  }

  /**
   * Filter columnms
   *
   * @param value entered text
   */
  private filterColumns(value: string, isIndividual = false): InnerColumnSetting[] {
    const filterValue = value.toLowerCase();
    const autoFields = this.allColumns
      .filter((x) => x.filterSettings && !!x.filterSettings.isIndividual === isIndividual)
      .map((x) => x.id);

    if (!autoFields.length) {
      return [];
    }

    const ret = this.allColumns.filter((c) => {
      const id = c.id;
      if (['_select', '_action', '_expand'].includes(id)) {
        return false;
      }

      const filterField = this.getColumnName(c);
      return filterField.toLowerCase().includes(filterValue) && (!autoFields.length || autoFields.includes(id));
    });
    return ret;
  }

  /**
   * Get displayed column name
   *
   * @param column column
   */
  private getColumnName(column): string {
    const langKey = column.filterSettings?.displayedKey ?? column.langKey;
    return langKey ? this.translateService.lookup(langKey) : column.id;
  }

  /**
   * Check whether individual column is added
   *
   * @param settings table settings
   * @param searchKeys search keys
   */
  private checkIndividualColumns(settings: TableSettings, searchKeys: SearchKey[]) {
    this.hasIndividualColumn = false;
    const individualColumns = [
      ...(settings.columns ?? []).filter((x) => x.filterSettings?.isIndividual),
      ...(settings.customSearchColumns ?? []).filter((x) => x.filterSettings?.isIndividual)
    ];

    if (!individualColumns.length) {
      return;
    }

    mainLoop: for (const searchKey of searchKeys) {
      for (const individualColumn of individualColumns) {
        if (searchKey.key.value === individualColumn.id) {
          this.hasIndividualColumn = true;
          break mainLoop;
        }
      }
    }
  }
}
