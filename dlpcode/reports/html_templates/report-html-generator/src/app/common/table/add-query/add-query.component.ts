import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatMenuTrigger } from '@angular/material/menu';
import { UtilService } from '../../../service/util.service';
import { NotifyService } from '../../notify/service/notify.service';
import { HandleExceptionService } from '../../../service/handle_exception.service';
import { TranslateService } from '../../translate/translate.service';
import { textValidator } from '../../validators/text-validators';

export enum QueryObjectType {
  Files = 1,
  ScanIncidents = 2,
}

@Component({
  selector: 'fs-table-add-query',
  templateUrl: './add-query.component.html',
  styleUrls: ['./add-query.component.scss'],
})
export class AddQueryComponent implements OnInit, AfterViewInit {
  /**
   * Loadnig
   */
  loading: boolean = false;

  /**
   * Menu trigger
   */
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;

  /**
   * Form group
   */
  formGroup: FormGroup = this.fb.group({
    org_id: '',
    query_obj_name: ['', [Validators.required, textValidator()]],
    description: ['', textValidator()],
  });

  /**
   * Conditions
   */
  private _conditions: { [key: string]: any[] };

  /**
   * Query object type
   */
  private _queryObjectType: QueryObjectType;

  constructor(
    private fb: FormBuilder,
    private util: UtilService,
    private notifyService: NotifyService,
    private handleExceptionService: HandleExceptionService,
    private translateService: TranslateService //private queriesService: QueriesService
  ) {}

  /**
   * @internal
   */
  ngOnInit() {}

  /**
   * @internal
   */
  ngAfterViewInit(): void {
    this.trigger.menuClosed.subscribe(() => {
      this.formGroup.setValue({
        org_id: '',
        query_obj_name: '',
        description: '',
      });
      this.formGroup.markAsPristine();
    });
  }

  /**
   * Open menu
   */
  open(conditions: { [key: string]: any[] }, queryObjectType: QueryObjectType) {
    this._conditions = conditions;
    this._queryObjectType = queryObjectType;
    this.trigger.openMenu();
  }

  /**
   * Close
   */
  close() {
    this.trigger.closeMenu();
  }

  /**
   * Save query object
   */
  save() {
    if (!this.validate()) {
      return;
    }

    const values = {
      ...this.formGroup.value,
      type: this._queryObjectType,
    };
    //values[this.queriesService.getConditionName(this._queryObjectType)] = this._conditions;
    delete values.org_id;
    const payload = {
      info: values,
    };

    this.loading = true;
  }

  /**
   * Validate form group
   */
  validate() {
    this.util.markFormTouchedAndDirty(this.formGroup);
    return this.formGroup.valid;
  }
}
