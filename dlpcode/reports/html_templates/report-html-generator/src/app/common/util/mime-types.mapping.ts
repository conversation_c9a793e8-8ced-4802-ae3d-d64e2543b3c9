export interface MimeTypeInfo {
    mime: string;
    extensions: string[];
    description: string;
}
  
export const MIME_TYPE_MAP: MimeTypeInfo[] = [
    { mime: 'text/plain', extensions: ['.txt'], description: 'Plain text' },
    { mime: 'text/x-makefile', extensions: ['Makefile'], description: 'Build Automation' },
    { mime: 'application/pdf', extensions: ['.pdf'], description: 'PDF document' },
    { mime: 'application/msword', extensions: ['.doc'], description: 'Microsoft Word' },
    {
      mime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      extensions: ['.docx'],
      description: 'Word (OpenXML)'
    },
    { mime: 'application/vnd.ms-excel', extensions: ['.xls'], description: 'Excel spreadsheet' },
    {
      mime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      extensions: ['.xlsx'],
      description: 'Excel (OpenXML) spreadsheet'
    },
    { mime: 'application/vnd.ms-powerpoint', extensions: ['.ppt'], description: 'PowerPoint presentation' },
    {
      mime: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      extensions: ['.pptx'],
      description: 'PowerPoint (OpenXML)'
    },
    { mime: 'application/rtf', extensions: ['.rtf'], description: 'Rich Text Format' },
    { mime: 'application/epub+zip', extensions: ['.epub'], description: 'EPUB eBook' },
    { mime: 'text/csv', extensions: ['.csv'], description: 'Comma-Separated Values' },
    { mime: 'text/rtf', extensions: ['.rtf'], description: 'Rich Text Format' },
    { mime: 'text/tab-separated-values', extensions: ['.tsv'], description: 'Tab-Separated Values' },
    { mime: 'application/json', extensions: ['.json'], description: 'JSON data' },
    { mime: 'application/xml', extensions: ['.xml'], description: 'XML data' },
    { mime: 'image/png', extensions: ['.png'], description: 'PNG image' },
    { mime: 'image/jpeg', extensions: ['.jpg', '.jpeg'], description: 'JPEG image' },
    { mime: 'image/gif', extensions: ['.gif'], description: 'GIF image' },
    { mime: 'image/tiff', extensions: ['.tif'], description: 'High quality raster image' },
    { mime: 'image/bmp', extensions: ['.bmp'], description: 'Bitmap image' },
    { mime: 'image/webp', extensions: ['.webp'], description: 'WebP image' },
    { mime: 'image/svg+xml', extensions: ['.svg'], description: 'SVG vector image' },
    { mime: 'video/mp4', extensions: ['.mp4'], description: 'MPEG-4 video' },
    { mime: 'video/x-msvideo', extensions: ['.avi'], description: 'AVI video' },
    { mime: 'video/webm', extensions: ['.webm'], description: 'WebM video' },
    { mime: 'video/quicktime', extensions: ['.mov'], description: 'QuickTime video' },
    { mime: 'video/x-matroska', extensions: ['.mkv'], description: 'Matroska video' },
    { mime: 'audio/mpeg', extensions: ['.mp3'], description: 'MP3 audio' },
    { mime: 'audio/wav', extensions: ['.wav'], description: 'Waveform audio' },
    { mime: 'audio/ogg', extensions: ['.ogg'], description: 'Ogg Vorbis audio' },
    { mime: 'audio/aac', extensions: ['.aac'], description: 'AAC audio' },
    { mime: 'audio/webm', extensions: ['.webm'], description: 'WebM audio' },
    { mime: 'text/x-script.python', extensions: ['.py'], description: 'Python script files' },
    { mime: 'application/javascript', extensions: ['.js'], description: 'Java script files' },
    { mime: 'text/x-c', extensions: ['.c'], description: 'C source file' },
    { mime: 'text/x-cpp', extensions: ['.cpp'], description: 'C++ source file' },
    { mime: 'text/x-go', extensions: ['.go'], description: 'Golang source file' },
    { mime: 'text/x-rustsrc', extensions: ['.rs'], description: 'Rust source file' },
    { mime: 'text/x-terraform', extensions: ['.tf'], description: 'Infra-as-code for cloud provisioning' },
    { mime: 'text/x-dockerfile', extensions: ['Dockerfile'], description: 'Instructions to build docker image' },
    { mime: 'application/x-yaml', extensions: ['.yaml/.yml'], description: 'Generic config (K8s, CI, etc.)' },
    { mime: 'application/x-yaml', extensions: ['.yml'], description: 'Kubernetes resource manifests file' },
    { mime: 'text/x-java-source', extensions: ['.py'], description: 'Java source file' },
    { mime: 'application/x-typescript', extensions: ['.ts'], description: 'typescript files' },
    { mime: 'application/vnd.oasis.opendocument.text', extensions: ['.odt'], description: 'OpenDocument Text' },
    { mime: 'application/vnd.oasis.opendocument.presentation', extensions: ['.pptx'], description: 'OpenDocument Presentation' },
    { mime: 'application/vnd.oasis.opendocument.spreadsheet', extensions: ['.ods'], description: 'OpenDocument Spreadsheet' },
    {
        mime: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        extensions: ['.pptx'],
        description: 'Microsoft PowerPoint Open XML Presentation'
    }
];
  