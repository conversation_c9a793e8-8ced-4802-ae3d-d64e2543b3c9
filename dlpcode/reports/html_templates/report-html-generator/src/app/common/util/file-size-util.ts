export function formatBytes(bytes: number): string {
    if (bytes === 0) {
      return "0";
    }
  
    if (bytes < 1024) {
      return "1 KB";
    }
  
    const KB = 1024;
    const MB = KB * 1024;
    const GB = MB * 1024;
  
    if (bytes < MB) {
      return `${Math.round(bytes / KB)} KB`;
    } else if (bytes < GB) {
      return `${Math.round(bytes / MB)} MB`;
    } else {
      return `${Math.round(bytes / GB)} GB`;
    }
}


// Validates minSizeBytes and maxSizeBytes
export function isValidFileSizeRange(minSizeBytes: number, maxSizeBytes: number): boolean {
    const MAX_SIZE = 50 * 1024 * 1024; // 50MB in bytes

    return minSizeBytes >= 0 && maxSizeBytes <= MAX_SIZE;
}


// Validates minSizeBytes and maxSizeBytes with meaningful error messages
export function validateFileSizeRange(minSizeBytes: number, maxSizeBytes: number): string | null {
    const MAX_SIZE = 50 * 1024 * 1024; // 50MB in bytes

    //console.log(" min size = ", minSizeBytes, " max size = ", maxSizeBytes);

    if (minSizeBytes < 0) {
      return "Minimum file size cannot be less than 0 bytes.";
    }

    if (maxSizeBytes > MAX_SIZE) {
      return "Maximum file size cannot exceed 50 MB.";
    }

    if (maxSizeBytes === minSizeBytes) {
      return "Maximum file size must be greater than minimum file size.";
    }

    return null; // No validation errors
}