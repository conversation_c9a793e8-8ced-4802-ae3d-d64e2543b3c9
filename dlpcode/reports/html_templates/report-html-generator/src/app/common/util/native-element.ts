import { createCustomElement, NgElementConfig } from '@angular/elements';
import { CheckboxComponent } from '../table/custom-cell/checkbox/checkbox.component';
import { CollaboratorsComponent } from '../table/custom-cell/collaborators/collaborators.component';
import { EditComponent } from '../table/custom-cell/edit/edit.component';
import { IconComponent } from '../table/custom-cell/icon/icon.component';
import { LabelIconComponent } from '../table/custom-cell/label-icon/label-icon.component';
import { LabelsComponent } from '../table/custom-cell/labels/labels.component';
import { MultipleItemsPopoverComponent } from '../table/custom-cell/multiple-items-popover/multiple-items-popover.component';
import { SelectComponent } from '../table/custom-cell/select/select.component';
import { ShareableLinksComponent } from '../table/custom-cell/shareable-links/shareable-links.component';
import { TooltipComponent } from '../table/custom-cell/tooltip/tooltip.component';
import { PageSelectorComponent } from '../table/page-selector/page-selector.component';

const COMPONENTS: any = {
  'fd-n-icon': IconComponent,
  'fd-n-edit': EditComponent,
  'fd-n-multiple-items-popover': MultipleItemsPopoverComponent,
  'fd-n-select': SelectComponent,
  'fd-n-label-icon': LabelIconComponent,
  'fd-n-checkbox': CheckboxComponent,
  'fd-n-labels': LabelsComponent,
  'fd-n-tooltip': TooltipComponent,
  'fd-n-table-page-selector': PageSelectorComponent,
  'fd-n-collaborators': CollaboratorsComponent,
  'fd-n-shareable-links': ShareableLinksComponent
};

let diInstance: any = {};

export const setDiInstance = (object = {}) => {
  diInstance = {
    ...diInstance,
    ...object
  };
};

export const getDiInstance = (key: string) => {
  const result = diInstance[key];
  return result || undefined;
};

export const registerNativeElements = (params: NgElementConfig) => {
  Object.keys(COMPONENTS).forEach((k) => {
    const el = createCustomElement(COMPONENTS[k], params);
    customElements.define(k, el);
  });
};
