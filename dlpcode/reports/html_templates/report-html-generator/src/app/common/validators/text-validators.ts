import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Common validator for numbers, letters, underscores, spaces, hyphens
 */
export function textValidator(option?: { removeHyphens?: boolean; removeSpaces?: boolean }): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    if (value === '') {
      return null;
    }

    let regex = 'a-zA-Z0-9_';
    let errorKey = 'number_letter_underscore';
    if (option?.removeHyphens !== false) {
      regex += '\\-';
      errorKey += '_hyphen';
    }
    if (option?.removeSpaces !== false) {
      regex += ' ';
      errorKey += '_space';
    }

    const errorObj: any = {};
    errorObj[errorKey] = true;
    return new RegExp(`^[${regex}]*$`).test(value) ? null : errorObj;
  };
}