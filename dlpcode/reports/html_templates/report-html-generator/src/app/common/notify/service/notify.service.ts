import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { Injectable, Injector, OnDestroy, TemplateRef } from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';
import * as _ from 'lodash';
import { BehaviorSubject, Subject } from 'rxjs';
import { NotificationListComponent, NotificationListData } from '../notification-list/notification-list.component';
import { ActiveNotification, MessageLevel } from '../notification/notification.component';

/**
 * Notification parameters.
 */
export interface NotificationParams {
  /**
   * How long the notification should be displayed for in milliseconds.
   * Set to zero to to display indefinitely.
   */
  duration?: number;
  /**
   * Hide the default icon. May be needed when creating custom content.
   */
  hideIcon?: boolean;
}

/**
 * Default durations to display for each notification level
 */
const DEFAULT_DURATIONS = new Map<MessageLevel, number>([
  [MessageLevel.Info, 5000],
  [MessageLevel.Success, 3000],
  [MessageLevel.Warning, 0],
  [MessageLevel.Error, 0]
]);
@Injectable({
  providedIn: 'root'
})
export class NotifyService implements OnDestroy {
  /**
   * The maps of active notifications
   */
  private _active = new Map<string, ActiveNotification>();

  /**
   * Active notification subject
   */
  private _activeSubject = new BehaviorSubject<ActiveNotification[]>([]);

  /**
   * Whether notifications list is opened
   */
  private _isListOpened = false;

  /**
   * Notifications list overlay
   */
  private _listOverlayRef?: OverlayRef;

  /**
   * Use this subject to unsubscribe observables when the component is destroyed
   */
  private _destroyed = new Subject<void>();

  /**
   * Observable of active notifications.
   */
  activeNotifications = this._activeSubject.asObservable();

  constructor(private overlay: Overlay, private injector: Injector) {}

  /**
   * Display an informative message
   */
  info(message: string | SafeHtml | TemplateRef<any> | ComponentPortal<any>, params: NotificationParams = {}) {
    this.createNotification(message, MessageLevel.Info, params);
  }

  /**
   * Display a warning message
   */
  warning(message: string | SafeHtml | TemplateRef<any> | ComponentPortal<any>, params: NotificationParams = {}) {
    this.createNotification(message, MessageLevel.Warning, params);
  }

  /**
   * Display a error message
   */
  error(message: string | SafeHtml | TemplateRef<any> | ComponentPortal<any>, params: NotificationParams = {}) {
    this.createNotification(message, MessageLevel.Error, params);
  }

  /**
   * Display a success message
   */
  success(message: string | SafeHtml | TemplateRef<any> | ComponentPortal<any>, params: NotificationParams = {}) {
    this.createNotification(message, MessageLevel.Success, params);
  }

  /**
   * Clear all active notifications
   */
  clearAll(): void {
    this._active = new Map();
    this.updateActive();
  }

  /**
   * Get the default icon name that should be used for the provided notify level
   */
  getDefaultIcon(level: MessageLevel): string {
    switch (level) {
      case MessageLevel.Success:
        return 'check_circle';
      case MessageLevel.Info:
        return 'info';
      case MessageLevel.Warning:
        return 'warning';
      case MessageLevel.Error:
        return 'error';
    }
  }

  /**
   * @internal
   */
  ngOnDestroy() {
    if (this._listOverlayRef) {
      this._listOverlayRef.dispose();
      this._listOverlayRef = undefined;
    }
    this._isListOpened = false;
    this._destroyed.next();
  }

  /**
   * Create a notification
   *
   * @param message message
   * @param level message level
   * @param params notification parameters
   */
  private createNotification(
    message: string | SafeHtml | TemplateRef<any> | ComponentPortal<any>,
    level: MessageLevel,
    params: NotificationParams
  ) {
    const id = `fd-notification${new Date().getTime()}${_.uniqueId()}`;
    const notification: ActiveNotification = {
      id,
      createdTime: Date.now(),
      level,
      message,
      matIcon: this.getDefaultIcon(level),
      iconSize: 22,
      duration: typeof params.duration === 'undefined' ? DEFAULT_DURATIONS.get(level) || 0 : params.duration,
      close: () => {
        if (this._active.has(notification.id)) {
          this._active.delete(notification.id);
          this.updateActive();
        }
      },
      hideIcon: params.hideIcon
    };
    if (notification.duration) {
      notification.hideTimeout = window.setTimeout(notification.close, notification.duration);
    }
    this._active.set(notification.id, notification);
    this.updateActive();
  }

  /**
   * Update active notification
   */
  private updateActive() {
    const sorted = Array.from(this._active.values()).sort((a, b) => b.createdTime - a.createdTime);
    this._activeSubject.next(sorted);
    if (sorted.length && !this._isListOpened) {
      this.openList();
    }
  }

  /**
   * Open notifications list
   */
  private openList() {
    const portal = new ComponentPortal(NotificationListComponent, null, this.getInjector());
    const positionStrategy = this.overlay.position().global().bottom('0').right('0');
    this._listOverlayRef = this.overlay.create({
      panelClass: 'fd-notifications-overlay-pane',
      positionStrategy
    });
    this._listOverlayRef.hostElement.classList.add('fd-notifications-overlay-wrapper');
    this._listOverlayRef.attach(portal);
    this._isListOpened = true;
  }

  /**
   * Get the injector
   */
  private getInjector(): Injector {
    const data = new NotificationListData(this.activeNotifications);
    return Injector.create({
      providers: [{ provide: NotificationListData, useValue: data }],
      parent: this.injector
    });
  }
}
