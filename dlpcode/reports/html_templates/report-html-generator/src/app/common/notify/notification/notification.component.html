<mat-icon
  *ngIf="!notification?.hideIcon"
  [ngStyle]="{
    'height.px': notification?.iconSize,
    'width.px': notification?.iconSize,
    'font-size.px': notification?.iconSize
  }"
  >{{ notification?.matIcon }}</mat-icon
>
<div
  *ngIf="simpleMessageContent"
  class="message-content"
  [innerHTML]="simpleMessageContent"
></div>
<div *ngIf="portalMessageContent" class="message-content">
  <ng-container [cdkPortalOutlet]="portalMessageContent"> </ng-container>
</div>
<mat-icon class="close-icon">close</mat-icon>
