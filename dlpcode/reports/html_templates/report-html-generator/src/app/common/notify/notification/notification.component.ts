import { ComponentPortal, TemplatePortal } from '@angular/cdk/portal';
import {
  ChangeDetectionStrategy,
  Component,
  HostBinding,
  HostListener,
  Input,
  OnChanges,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';

/**
 * Message types that have associated styles.
 */
export enum MessageLevel {
  Info = 'info',
  Warning = 'warning',
  Error = 'error',
  Success = 'success',
}

/**
 * An active notification.
 *
 * @internal
 */
export interface ActiveNotification {
  /**
   * Notification ID.
   */
  id: string;
  /**
   * Time when the notification was created.
   */
  createdTime: number;
  /**
   * The level of the notification.
   */
  level: MessageLevel;
  /**
   * Message to be displayed.
   */
  message: string | SafeHtml | TemplateRef<any> | ComponentPortal<any>;
  /**
   * Mat icon.
   */
  matIcon?: string;
  /**
   * Icon size.
   */
  iconSize?: number;
  /**
   * Executed when closing the notification.
   */
  close: () => void;
  /**
   * Hide the default icon?
   */
  hideIcon?: boolean;
  /**
   * Duration to show this notification for in milliseconds.
   */
  duration: number;
  /**
   * Timeout that will trigger the notification to hide.
   */
  hideTimeout?: number;
}

@Component({
  selector: 'fd-notification',
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationComponent implements OnChanges {
  /**
   * The notification to be displayed.
   */
  @Input()
  notification?: ActiveNotification;

  /**
   * Simple message content
   */
  simpleMessageContent?: string | SafeHtml;

  /**
   * Protal message content
   */
  portalMessageContent?: TemplatePortal<any> | ComponentPortal<any>;

  constructor(private viewContainerRef: ViewContainerRef) {}

  /**
   * @internal
   */
  @HostBinding('class')
  get class() {
    return this.notification?.level ?? '';
  }

  /**
   * @internal
   */
  @HostListener('click')
  click() {
    if (this.notification) {
      this.notification.close();
    }
  }
  /**
   * @internal
   */
  ngOnChanges() {
    if (!this.notification) {
      return;
    }

    if (this.notification.message instanceof TemplateRef) {
      this.portalMessageContent = new TemplatePortal(
        this.notification.message,
        this.viewContainerRef
      );
      this.simpleMessageContent = undefined;
    } else if (this.notification.message instanceof ComponentPortal) {
      this.portalMessageContent = this.notification.message;
      this.simpleMessageContent = undefined;
    } else {
      this.simpleMessageContent = this.notification.message;
      this.portalMessageContent = undefined;
    }
  }
}
