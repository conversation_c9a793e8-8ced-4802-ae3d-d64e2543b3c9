import { OverlayModule } from '@angular/cdk/overlay';
import { PortalModule } from '@angular/cdk/portal';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MaterialModule } from '@/material.module';
import { NotificationListComponent } from './notification-list/notification-list.component';
import { NotificationComponent } from './notification/notification.component';

@NgModule({
  declarations: [NotificationListComponent, NotificationComponent],
  imports: [CommonModule, PortalModule, OverlayModule, MaterialModule],
})
export class NotifyModule {}
