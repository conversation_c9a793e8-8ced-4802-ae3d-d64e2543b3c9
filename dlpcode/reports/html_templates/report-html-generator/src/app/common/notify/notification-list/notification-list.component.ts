import { animate, keyframes, style, transition, trigger } from '@angular/animations';
import { Component, HostBinding, OnDestroy } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { ActiveNotification } from '../notification/notification.component';

export class NotificationListData {
  /**
   * Observable of the list of active notifications.
   */
  activeNotifications: Observable<ActiveNotification[]>;

  constructor(activeNotifications: Observable<ActiveNotification[]>) {
    this.activeNotifications = activeNotifications;
  }
}

@Component({
  selector: 'fd-notification-list',
  templateUrl: './notification-list.component.html',
  styleUrls: ['./notification-list.component.scss'],
  animations: [
    trigger('fadeInOut', [
      transition(':enter', [style({ opacity: 0 }), animate(300, style({ opacity: 1 }))]),
      transition(':leave', [
        animate(
          500,
          keyframes([
            style({ maxHeight: '1000px', opacity: 1, offset: 0 }),
            style({ maxHeight: '1000px', opacity: 0, offset: 0.6 }),
            style({ maxHeight: '0px', opacity: 0, offset: 1 })
          ])
        )
      ])
    ])
  ]
})
export class NotificationListComponent implements OnDestroy {
  /**
   * The active notifications.
   */
  notifications: Observable<ActiveNotification[]>;
  @HostBinding('style.marginRight')
  leftAdjustment = '0px';

  /**
   * Use this subject to unsubscribe observables when the component is destroyed
   */
  private _destroyed = new Subject<void>();

  constructor(listData: NotificationListData) {
    this.notifications = listData.activeNotifications;
  }

  /**
   * @internal
   */
  ngOnDestroy() {
    this._destroyed.next();
  }
}
