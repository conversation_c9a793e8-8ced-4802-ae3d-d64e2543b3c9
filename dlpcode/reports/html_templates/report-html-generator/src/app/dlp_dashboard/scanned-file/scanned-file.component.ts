import { Component, Input, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import { TranslateService } from '@/common/translate/translate.service';
import { IFilestoScan, EStorageType } from '../mock_data_structure';

@Component({
  selector: 'app-scanned-file',
  templateUrl: './scanned-file.component.html',
  styleUrls: ['./scanned-file.component.css'],
})
export class ScannedFileComponent implements OnInit {
  @Input() data: IFilestoScan;

  Highcharts: typeof Highcharts = Highcharts;
  chartOptions: any = {};

  total_files: number = 0;
  total_percentage: number = 0;
  baseData: any[] = [
    {
      storage_type: EStorageType.cloud,
      storagecount: 0,
      label: this.translateService.lookup('dashboard.storage_types.cloud'),
      percentage: 0,
      color: '#2BC1E3',
      icon: 'sharepoint_cloud',
    },
    {
      storage_type: EStorageType.prem,
      storagecount: 0,
      label: this.translateService.lookup('dashboard.storage_types.prem'),
      percentage: 0,
      color: '#279CEF',
      icon: 'sharepoint_onprem',
    },
    {
      storage_type: EStorageType.aws,
      storagecount: 0,
      label: this.translateService.lookup('dashboard.storage_types.aws'),
      percentage: 0,
      color: '#227ADF',
      icon: 'aws',
    },
    {
      storage_type: EStorageType.smb,
      storagecount: 0,
      label: this.translateService.lookup('dashboard.storage_types.smb'),
      percentage: 0,
      color: '#63DCC8',
      icon: 'smb',
    },
    {
      storage_type: EStorageType.gdrive,
      storagecount: 0,
      label: this.translateService.lookup('dashboard.storage_types.gdrive'),
      percentage: 0,
      color: '#48C8B0',
      icon: 'gdrive',
    },
  ];

  constructor(private translateService: TranslateService) {}

  ngOnInit(): void {
    this.total_files = this.data.total_files || 0;
    this.total_percentage = this.data.total_percentage || 0.0;

    let seriesData = [];

    this.data.files_to_scan.forEach((r) => {
      this.baseData.forEach((d) => {
        if (d.storage_type == r.storage_type) {
          d.percentage = r.percentage;
          d.storagecount = r.storagecount;
        }
      });
    });

    this.baseData.forEach((d) => {
      seriesData.push({
        name: d.label,
        y: d.storagecount,
      });
    });

    this.chartOptions = {
      credits: {
        enabled: false,
      },
      chart: {
        plotShadow: false,
        backgroundColor: 'transparent',
        spacing: [0, 0, 0, 0],
      },
      plotOptions: {
        pie: {
          shadow: false,
          center: ['50%', '50%'],
          size: '100%',
          innerSize: '70%',
          dataLabels: {
            enabled: false,
          },
        },
      },
      accessibility: {
        enabled: false,
      },
      title: undefined,
      colors: this.baseData.map(v=>v.color),
      subtitle: {
        useHTML: true,
        verticalAlign: 'middle',
        style: {
          color: '#222222',
        },
        text:
          '<div style="font-size: 20px; font-weight: 600; text-align: center;">' +
          this.total_files.toLocaleString() +
          '</div>',
      },
      tooltip: {
        enabled: true,
        headerFormat: '',
        useHTML: true,
        formatter: function () {
          return `${this.point.name} ${this.y.toLocaleString()}`;
        },
      },
      series: [
        {
          type: 'pie',
          name: 'label',
          data: seriesData,
        },
      ],
    };
  }
}
