:host {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .body {
    display: flex;
    gap: 40px;
  }

  .circle {
    display: inline-block;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    margin-right: 4px;
  }

  .content {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: flex-start;
    justify-content: center;

    .chart-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 160px;
    }

    mat-icon {
      width: 12px;
      height: 12px;
      font-size: 12px;
      margin-right: 4px;
    }

    highcharts-chart {
      display: inline-block;
      height: 160px;
      width: 160px;
    }

    .info-table {
      font-size: 12px;
      border-spacing: 10px;
      margin-top: 10px;

      .info {
        display: flex;
        align-items: center;
      }

      .total {
        font-weight: bold;
      }
    }

    .align-right {
      text-align: right;
    }
  }
}
