import { Component, Input, OnInit } from '@angular/core';
import { IAiCategories } from '../mock_data_structure';

@Component({
  selector: 'app-ai-categories',
  templateUrl: './ai-categories.component.html',
  styleUrls: ['./ai-categories.component.css'],
})
export class AiCategoriesComponent implements OnInit {
  @Input() data: IAiCategories;

  total_count: number = 0;
  total_percentage: number = 0;
  total_ai_count: number = 0;
  total_scanned_files: number = 0;

  loaded: boolean = false;

  baseData: any[] = [];

  constructor() {}

  ngOnInit(): void {
    this.total_count = 0;
    this.total_percentage = 0;
    this.total_ai_count = 0;
    this.total_scanned_files = 0;
    this.baseData = [];

    this.loaded = true;

    this.total_ai_count = this.data?.total_ai_count || 0;
    this.total_percentage = this.data?.total_percentage || 0;
    this.total_scanned_files = this.data?.total_scanned_files || 0;

    let max_percent = 0;
    const files_to_scan = this.data?.files_to_scan || [];
    files_to_scan.forEach((r) => {
      if (r?.percentage > max_percent) {
        max_percent = r?.percentage;
      }
      this.baseData.push({
        percentage: r.percentage || 0,
        categorycount: r.categorycount,
        main_category: r.main_category,
        category: r.sub_category,
      });
    });

    this.total_percentage = parseFloat(this.total_percentage.toFixed(2));

    this.baseData.forEach((d) => {
      d.real_percent =
        max_percent != 0
          ? parseFloat(((d.percentage / max_percent) * 100).toFixed(2))
          : 0;
    });

    console.log('top10 data:', this.baseData);
  }

  category_tooltip(main: string, sub: string): string {
    return `${main} - ${sub}`;
  }

  category_label(main: string, sub: string): string {
    if (sub == 'Other') return `${main} - ${sub}`;
    else return sub;
  }
}
