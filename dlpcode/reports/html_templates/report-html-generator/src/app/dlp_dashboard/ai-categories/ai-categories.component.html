<div class="title">
  {{ "dashboard.files_in_top10_ai_categories" | translate }}
</div>
<div class="body">
  <div class="content">
    <div *ngIf="!loaded; else loadedContent" class="non-loaded">
      {{ "no_data" | translate }}
    </div>
    <ng-template #loadedContent>
      <table class="info-table">
        <tr *ngFor="let data of baseData">
          <td
            [title]="category_tooltip(data.main_category, data.category)"
            class="pointer"
          >
            {{ category_label(data.main_category, data.category) }}
          </td>
          <td class="align-right">
            {{ data.categorycount.toLocaleString() }}
          </td>
          <td class="align-right">{{ data.percentage }}%</td>
          <td class="percent-cell">
            <div
              class="percent-bar"
              [ngStyle]="{ width: data.real_percent + '%' }"
            ></div>
          </td>
        </tr>

        <tr>
          <td>
            {{ "dashboard.top10_total" | translate }}
          </td>

          <td class="align-right">
            {{ total_ai_count.toLocaleString() }}
          </td>
          <td class="align-right">{{ total_percentage.toLocaleString() }}%</td>
          <td>of {{ total_scanned_files.toLocaleString() }} scanned files</td>
        </tr>
      </table>
    </ng-template>
  </div>
</div>
