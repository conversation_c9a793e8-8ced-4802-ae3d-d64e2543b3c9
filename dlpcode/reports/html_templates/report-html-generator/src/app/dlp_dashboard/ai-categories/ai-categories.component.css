:host {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .body {
    display: flex;
    flex-direction: column;
    gap: 40px;
    flex: 1;
  }

  .content {
    width: 100%;
    flex: 1;

    .non-loaded {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: #666666;
    }

    .info-table {
      width: 100%;
      font-size: 12px;
      border-spacing: 2px;
      border-collapse: collapse;
      /* height: 100%; */

      tr:last-child {
        border-top: solid 1px #cccccc;
      }

      .percent-cell {
        width: 50%;
      }

      .percent-bar {
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        background-color: #aedff8;
        height: 10px;
      }
    }
  }
}
