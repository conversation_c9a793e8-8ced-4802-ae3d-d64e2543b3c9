export const TRANSLATIONS_EN = {
  dashboard: {
    show_more: 'Show More',
    blocked_network_files: 'Blocked Network Files, by Severity',
    alerted_network_files: 'Alerted Network Files, by Severity',
    system_information: 'System Information',
    system_license: 'License Status',
    disk_information: 'Disk Information',
    resource_usage: 'Resource Usage',
    device_ports: 'Device Ports',
    reset_tip: 'Reset Dashboard for default setting',
    total: 'Total',
    critical: 'Critical',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    host_name: 'Host Name',
    serial_number: 'Serial Number',
    system_time: 'System Time',
    firmware_version: 'Firmware Version',
    uptime: 'Uptime',
    cpu: 'CPU',
    memory: 'Memory',
    disk: 'Disk',
    used: 'Used',
    free: 'Free',
    viewEventTip: 'View {{type}} events',
    events: 'Events',
    confirm_reboot: 'Confirm Reboot',
    confirm_shutdown: 'Confirm Shutdown',
    confirm_reboot_tip: 'Are you sure you want to reboot the device?',
    confirm_shutdown_tip: 'Are you sure you want to shutdown the device?',
    wait_reboot_tip:
      'System rebooting: please try to refresh the browser manually about 1 minute later, or it will be refreshed automatically after {{countdown}} seconds.',
    shutdown_success:
      'This device has been shutdown successfully. You can access this device after restart it.',
    upload_firmware: 'Upload Firmware',
    upload_license: 'Upload License',
    upload_warning_title: 'Confirm Import',
    upload_warning_context:
      'System will reboot after importing license. Are you sure want to import license and reboot?',
    select_file: 'Select File',
    license_file_tip: 'Allowed file types: [.lic]',
    file_tip: 'Allowed file types: [ .out ]',
    upgrade_status: 'Upgrade Status',
    upgrading:
      'This device is upgrading. This process will take some time, please wait patiently until the page automatically refresh.',
    hostname: 'Host Name',
    confirm_modify_hostname: 'Are you sure you want to modify the host name?',
    modify_hostname: 'Modify Host Name',
    hostname_tip:
      'The legal characters are numbers(0-9), letters(A-Z,a-z) and special characters - and _',
    hostname_tip2: 'The hyphen - cannot be used as the first character',
    col: {
      total: 'Total',
      pii: 'PII',
      pci: 'PCI',
      phi: 'PHI',
    },
    confidential: 'Confidential',
    high_confidential: 'Highly Confidential',
    confidential_file: 'Catalogued Files',
    confidential_and_high: 'Confidential & Highly Confidential',
    compliance: 'Files by Compliance',
    file_types: 'Compliance File Types',
    catalogued: 'Recently Catalogued',
    analyzed_files: 'Files',
    tooltipcol: {
      type: 'Storage',
      total: 'Total Files',
      percent: '%',
      text: 'Text Files',
      docs: 'Office Docs',
      source: 'Source Code',
      images: 'Images',
    },
    storage_type: {
      aws: 'AWS Bucket',
      smb: 'SMB',
      prem: 'SP On Prem',
      cloud: 'SP Cloud',
      gdrive: 'Google Drive',
    },
    cataloged_period: {
      total: 'Total',
      threehourcount: 'Last 3 hours',
      onedaycount: 'Last 24 hours',
      oneweekcount: 'Last 7 days',
    },
    sensitive: {
      title: 'Catalogued Files',
      total_catalogued: 'Total Catalogued',
      total_uncatalogued: 'Total Uncatalogued',
      smb: 'SMB',
      aws: 'AWS Bucket',
      prem: 'SharePoint On Prem',
      cloud: 'SharePoint Cloud',
    },
    files_to_scan: 'Files to Scan',
    of_discovered_files: 'of Discovered Files',
    discovered_files: 'Discovered Files',
    daily_file_scan_results: 'File Scan',
    files_per_day: 'Files',
    newly_scanner: 'Scanned',
    sensitive_label: 'Sensitive',
    sensitive_files_labeled: 'Sensitive Files',
    total_labeled_files: 'Total Sensitive Files',
    total_scanned_files: 'Total Scanned Files',
    onedaycount: 'Last 24 hours',
    oneweekcount: 'Last 7 days',
    twoweekcount: 'Last 14 days',
    sensitive_file_distribution: 'Sensitive File Distribution',
    sensitive_files: 'Sensitive Files',
    other_sensitive_files: 'Other Sensitive Files',
    compliance_files: 'Compliance Files',
    compliance_files_title: 'Top 5 Compliance Types',
    files_in_top10_ai_categories: 'Top 10 ML Classified Document Types',
    sensitive_file_owners: 'Sensitive File Owners',
    scanned_file_distribution: 'Scanned File Distribution',
    scanned_files: 'Scanned Files',
    files_scanned: 'Files Scanned',
    storage_types: {
      aws: 'AWS S3',
      smb: 'SMB',
      prem: 'SharePoint On Prem',
      cloud: 'SharePoint Cloud',
      gdrive: 'Google Drive',
    },
    top10_total: 'Top 10 Total',
    file_count: 'File Count',
    of_total_compliance_files: 'of Total Compliance Files',
    compliance_type: 'Compliance Type',
    all_compliance_files: 'All Compliance Files',
    others: 'Others',
  },
};
