<div class="report-title">
  <mat-icon svgIcon="fortidata"></mat-icon>
  <div class="title">FortiData Report for Dashboard</div>
</div>
<div class="report-info">
  <div class="item column-6">
    <label>Report Name:</label>
    <span>{{ reportData.report_info.name }}</span>
  </div>
  <div class="item column-6">
    <label>Period:</label>
    <span>{{ reportData.report_info.period }}</span>
  </div>
  <div class="item column-6">
    <label>Timestamp:</label>
    <span>{{ reportData.report_info.generated_on }}</span>
  </div>
  <div class="item column-6">
    <label>Schedule:</label>
    <span>{{ reportData.report_info.schedule }}</span>
  </div>
  <div class="item column-6">
    <label>Created by:</label>
    <span>{{ reportData.report_info.created_by }}</span>
  </div>
  <div class="item column-12">
    <label>Notes:</label>
    <span>{{ reportData.report_info.notes }}</span>
  </div>
  <div class="item column-12">
    <label>Firmware Version:</label>
    <span>{{ reportData.report_info.firmware_version }}</span>
  </div>
</div>
<div [class]="{widgets: true, a4: globalService.isPrinting}">
  <div class="column-4 widget">
    <app-scanned-file-summary [data]="reportData.scannedfiles"></app-scanned-file-summary>
  </div>
  <div class="column-4 widget">
    <app-sensitive-file-labeled [data]="reportData.sensitivefileslabeled"></app-sensitive-file-labeled>
  </div>
  <div class="column-4 widget">
    <app-sensitive-file-owners [data]="reportData.sensitivefileowners"></app-sensitive-file-owners>
  </div>
  <div class="column-12 widget">
    <app-scan-results [data]="reportData.dailyScannedFiles"></app-scan-results>
  </div>
  <div class="column-6 widget">
    <app-scanned-file [data]="reportData.filestoscan"></app-scanned-file>
  </div>
  <div class="column-6 widget">
    <app-sensitive-file [data]="reportData.sensitivefilesdistribution"></app-sensitive-file>
  </div>
  <div class="column-6 widget">
    <app-ai-categories [data]="reportData.aicategories"></app-ai-categories>
  </div>
  <div class="column-6 widget">
    <app-compliance-files [data]="{
        data: reportData.compliancefiles,
        allData: reportData.compliancefilesshowmore
      }"></app-compliance-files>
  </div>
  <div class="column-12 widget" *ngIf="appService.isPrinting">
    <app-dashboard-all-compliance-files
      [data]="reportData.compliancefilesshowmore"></app-dashboard-all-compliance-files>
  </div>
</div>