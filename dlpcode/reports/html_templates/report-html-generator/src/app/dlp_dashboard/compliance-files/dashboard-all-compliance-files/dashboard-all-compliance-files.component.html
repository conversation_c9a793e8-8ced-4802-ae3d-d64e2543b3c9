<div class="title" *ngIf="dashboardService.isPrinting">
  {{ "dashboard.all_compliance_files" | translate }}
</div>
<div class="main-table">
  <table class="info-table">
    <tr class="bold">
      <td>{{ "dashboard.compliance_type" | translate }}</td>
      <td class="align-right">{{ "dashboard.file_count" | translate }}</td>
      <td class="align-right">%</td>
      <td class="percent-cell">
        {{ "dashboard.of_total_compliance_files" | translate }}
      </td>
    </tr>

    <tr *ngFor="let data of compliance_data">
      <td>
        {{ data.compliance }}
      </td>
      <td class="align-right">
        {{ data.count.toLocaleString() }}
      </td>
      <td class="align-right">{{ data.percentage }}%</td>
      <td class="percent-cell">
        <div
          class="percent-bar"
          [ngStyle]="{ width: data.real_percent + '%' }"
        ></div>
      </td>
    </tr>

    <tr style="border-bottom: none">
      <td rowspan="2" class="bold">
        {{ "dashboard.total" | translate }}
      </td>
      <td rowspan="2" class="bold align-right">
        {{ total_compliance_count.toLocaleString() }}
      </td>
      <td class="align-right">{{ total_compliance_percent }}%</td>
      <td>
        {{ "dashboard.of_total_compliance_files" | translate }}
      </td>
    </tr>

    <tr>
      <td class="align-right">{{ total_scanned_percent }}%</td>
      <td>
        of {{ total_scanned_count.toLocaleString() }}
        {{ "dashboard.scanned_files" | translate }}
      </td>
    </tr>
  </table>
</div>
