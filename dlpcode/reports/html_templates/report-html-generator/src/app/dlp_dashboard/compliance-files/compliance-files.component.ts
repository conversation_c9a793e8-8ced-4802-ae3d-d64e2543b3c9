import { Component, Input, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import {
  IComplianceFiles,
  IComplianceFilesShowMore,
} from '../mock_data_structure';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { DialogComponent } from './dialog/dialog.component';
import { AppGlobalService } from '../app-global.service';

@Component({
  selector: 'app-compliance-files',
  templateUrl: './compliance-files.component.html',
  styleUrls: ['./compliance-files.component.css'],
})
export class ComplianceFilesComponent implements OnInit {
  @Input() data: {
    data: IComplianceFiles[];
    allData: IComplianceFilesShowMore;
  };

  Highcharts: typeof Highcharts = Highcharts;
  colors = ['#9FCEFA', '#53ACFF', '#4481DD', '#1D63B0', '#173F6B'];
  other_color: string = '#E3E6E9';
  chartOptions: Highcharts.Options = {};

  baseData: any[] = [];

  other_count: number = 0;
  other_percent: number = 0.0;
  total_count: number = 0;
  total_percent: number = 0.0;

  constructor(
    private dialog: MatDialog,
    public dashboardService: AppGlobalService
  ) {}

  ngOnInit(): void {
    this.other_count = 0;
    this.other_percent = 0.0;
    this.total_count = 0;
    this.total_percent = 0.0;

    this.baseData = [];
    const data = this.data.data;

    const real_base = data.filter(
      (r) => r.compliance != 'Others' && r.compliance != 'Total'
    );

    const other_entry = data.find((r) => r.compliance == 'Others');
    if (other_entry) {
      this.other_count = other_entry?.count || 0;
      this.other_percent = other_entry?.percentage || 0;
    }

    const total_entry = data.find((r) => r.compliance == 'Total');
    if (total_entry) {
      this.total_count = total_entry?.count || 0;
      this.total_percent = total_entry?.percentage || 0;
    }

    let seriesData = [];

    real_base.forEach((r, i) => {
      let entry = {
        label: r?.compliance || '',
        count: r?.count || 0,
        percent: r?.percentage || 0,
        color: this.colors[i],
      };

      this.baseData.push(entry);

      seriesData.push({
        name: r?.compliance || '',
        y: r?.count || 0,
      });
    });

    seriesData.push({
      name: 'Others',
      y: this.other_count,
      color: this.other_color,
    });

    this.chartOptions = {
      credits: {
        enabled: false,
      },
      chart: {
        plotShadow: false,
        backgroundColor: 'transparent',
        spacing: [0, 0, 0, 0],
      },
      plotOptions: {
        pie: {
          shadow: false,
          center: ['50%', '50%'],
          size: '100%',
          innerSize: '70%',
          dataLabels: {
            enabled: false,
          },
        },
      },
      accessibility: {
        enabled: false,
      },
      title: undefined,
      colors: this.colors, //['#53ACFF', '#4481DD', '#1D63B0', '#E3E6E9'],
      subtitle: {
        useHTML: true,
        verticalAlign: 'middle',
        style: {
          color: '#222222',
        },
        text:
          '<div style="font-size: 20px; font-weight: 600; text-align: center;">' +
          this.total_count.toLocaleString() +
          '</div>',
      },
      tooltip: {
        enabled: true,
        headerFormat: '',
        useHTML: true,
        formatter: function () {
          return `${this.name} ${this.y.toLocaleString()}`;
        },
      },
      series: [
        {
          type: 'pie',
          name: 'label',
          data: seriesData,
        },
      ],
    };
  }

  show_all_files = () => {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.restoreFocus = false;
    dialogConfig.data = this.data.allData;
    dialogConfig.width = '35vw';
    dialogConfig.height = '550px';

    const dialogRef = this.dialog.open(DialogComponent, dialogConfig);
  };
}
