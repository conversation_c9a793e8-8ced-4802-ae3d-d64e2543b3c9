import { Component, Input, OnInit } from '@angular/core';
import { IComplianceFilesShowMore } from '@/dlp_dashboard/mock_data_structure';
import { AppGlobalService } from '@/dlp_dashboard/app-global.service';

@Component({
  selector: 'app-dashboard-all-compliance-files',
  templateUrl: './dashboard-all-compliance-files.component.html',
  styleUrls: ['./dashboard-all-compliance-files.component.scss'],
})
export class DashboardAllComplianceFilesComponent implements OnInit {
  @Input() data: IComplianceFilesShowMore;

  total_scanned_count: number = 0;
  total_scanned_percent: number = 0;
  total_compliance_count: number = 0;
  total_compliance_percent: number = 0;

  compliance_data: any[] = [];

  constructor(public dashboardService: AppGlobalService) {}

  ngOnInit() {
    this.compliance_data = [];
    this.total_scanned_count = 0;
    this.total_scanned_percent = 0;
    this.total_compliance_count = 0;
    this.total_compliance_percent = 0;

    this.total_scanned_count = this.data.total_scanned_count || 0;
    this.total_scanned_percent = this.data.total_scanned_percent || 0;
    this.total_compliance_count = this.data.total_compliance_count || 0;
    this.total_compliance_percent = this.data.total_compliance_percent || 0;

    const data = this.data.compliance_data || [];

    let max_percent = 0;
    data.forEach((d) => {
      this.compliance_data.push({
        compliance: d?.compliance || '',
        count: d?.count || 0,
        percentage: d?.percentage || 0,
      });

      if (max_percent < d?.percentage) max_percent = d?.percentage;
    });

    this.compliance_data.forEach((d) => {
      if (max_percent != 0) d.real_percent = (d.percentage / max_percent) * 100;
    });
  }
}
