import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DashboardAllComplianceFilesComponent } from '../dashboard-all-compliance-files/dashboard-all-compliance-files.component';
import { IComplianceFilesShowMore } from '@/dlp_dashboard/mock_data_structure';

@Component({
  selector: 'app-dialog',
  templateUrl: './dialog.component.html',
  styleUrls: ['./dialog.component.css'],
})
export class DialogComponent {
  allData: IComplianceFilesShowMore;

  constructor(
    public dialogRef: MatDialogRef<DashboardAllComplianceFilesComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IComplianceFilesShowMore
  ) {
    this.allData = data;
  }

  close() {
    this.dialogRef.close();
  }
}
