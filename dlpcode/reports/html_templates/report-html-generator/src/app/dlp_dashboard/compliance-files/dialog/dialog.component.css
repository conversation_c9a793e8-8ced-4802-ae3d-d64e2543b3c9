:host {
  .dialog-content {
    overflow-y: visible;
    width: 100%;
    height: 99%;
    display: flex;
    flex-direction: column;
  }

  .content {
    flex: 1;
  }

  .title-panel {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    font-weight: bold;
    font-size: 16px;
    margin-left: 20px;
    padding: 14px 20px 14px 20px;

    .info {
      width: 16px;
      height: 16px;
      cursor: help;
    }

    .close {
      margin-left: auto;
      cursor: pointer;
      transition: opacity 0.15s;
    }

    span {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }
  }

  .bold {
    font-weight: bold;
  }

  .align-right {
    text-align: right;
  }

  .footer {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    height: 48px;
    padding-left: 20px;
    padding-right: 20px;
    gap: 20px;

    button {
      height: 32px;
      background: white;
      border: 1px solid;
      border-radius: 4px;
      &:hover {
        cursor: pointer;
        background-color: #f0f0f0;
      }
    }
  }
}
