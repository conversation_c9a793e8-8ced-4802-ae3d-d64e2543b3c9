<div class="title">{{ "dashboard.compliance_files_title" | translate }}</div>
<div class="body">
  <div class="content">
    <div class="chart-box" style="padding-top: 24px;">
      <highcharts-chart [Highcharts]="Highcharts" [options]="chartOptions"></highcharts-chart>
      <div class="small-title">
        {{ "" }}
      </div>
    </div>

    <table class="info-table">
      <tr>
        <td class="info">
          <mat-icon svgIcon="storageicon"></mat-icon>
          <span style="white-space: nowrap;" class="total">{{
            "dashboard.compliance_files" | translate
            }}</span>
        </td>

        <td class="align-right">
          {{ total_count.toLocaleString() }}
        </td>
        <td class="align-right">{{ total_percent.toLocaleString() }}%</td>
      </tr>

      <tr *ngFor="let base of baseData">
        <td>
          <i class="circle" [ngStyle]="{ 'background-color': base.color }"></i>
          {{ base.label }}
        </td>
        <td class="align-right">
          {{ base.count.toLocaleString() }}
        </td>
        <td class="align-right">{{ base.percent }}%</td>
      </tr>
      <tr>
        <td>
          <i class="circle" style="background-color: #e3e6e9"></i>
          {{ "dashboard.others" | translate }}
        </td>
        <td class="align-right">
          {{ other_count.toLocaleString() }}
        </td>
        <td class="align-right">{{ other_percent }}%</td>
      </tr>

      <tr *ngIf="!dashboardService.isPrinting">
        <td colspan="3" class="align-right">
          <span class="link" (click)="show_all_files()">{{ "dashboard.show_more" | translate }} ></span>
        </td>
      </tr>
    </table>
  </div>
</div>