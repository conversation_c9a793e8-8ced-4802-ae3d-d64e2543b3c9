:host {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .body {
    display: flex;
    gap: 40px;
  }

  .circle {
    display: inline-block;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    margin-right: 4px;
  }

  .content {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: flex-start;
    justify-content: center;

    .chart-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 160px;
    }

    highcharts-chart {
      display: inline-block;
      height: 160px;
      width: 160px;
    }

    .info-table {
      font-size: 12px;
      border-spacing: 10px;

      .link {
        cursor: pointer;
        color: #2564bf;
      }

      .info {
        display: flex;
        align-items: center;
      }

      .total {
        font-weight: bold;
      }

      mat-icon {
        width: 12px;
        height: 12px;
        font-size: 12px;
        margin-right: 4px;
      }
    }
  }
}
