:host {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .body {
    flex: 1;
    display: flex;
  }

  .content {
    width: 100%;
    display: flex;
    gap: 24px;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .total-info {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .total-count {
      font-size: 24px;
      text-align: center;
    }

    .mat-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
    }
  }

  .detail-info {
    width: 100%;
    font-size: 12px;
    border-spacing: 10px;
    max-width: 300px;
  }

  .row {
    .mat-icon {
      width: 12px;
      height: 12px;
    }

    .count {
      text-align: right;
    }

    .small {
      font-size: 12px;
    }
  }

  .desc {
    display: flex;
    align-items: center;
    gap: 5px;
  }
}
