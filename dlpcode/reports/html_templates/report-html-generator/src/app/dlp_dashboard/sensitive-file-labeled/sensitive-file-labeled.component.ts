import { Component, Input, OnInit } from '@angular/core';
import { ISensitiveFilesLabeled } from '../mock_data_structure';

@Component({
  selector: 'app-sensitive-file-labeled',
  templateUrl: './sensitive-file-labeled.component.html',
  styleUrls: ['./sensitive-file-labeled.component.css'],
})
export class SensitiveFileLabeledComponent implements OnInit {
  @Input() data: ISensitiveFilesLabeled;

  alltimecount: number = 0;
  onedaycount: number = 0;
  oneweekcount: number = 0;
  twoweekcount: number = 0;

  statisic_count: any[] = [];

  constructor() {}

  ngOnInit(): void {
    this.statisic_count = [
      {
        field: 'onedaycount',
        icon: 'access_time',
        count: 0,
      },
      {
        field: 'oneweekcount',
        icon: 'today',
        count: 0,
      },
      {
        field: 'twoweekcount',
        icon: 'access_alarm',
        count: 0,
      },
    ];

    ['alltimecount', 'onedaycount', 'oneweekcount', 'twoweekcount'].forEach(
      (f) => {
        this[f] = this.data?.[f] || 0;

        this.statisic_count.forEach((s) => {
          if (s.field == f) {
            s.count = this.data?.[f] || 0;
          }
        });
      }
    );
  }
}
