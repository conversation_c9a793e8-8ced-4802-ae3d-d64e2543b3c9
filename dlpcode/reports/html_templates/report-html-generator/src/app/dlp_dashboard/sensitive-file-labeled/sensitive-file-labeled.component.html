<div class="body">
  <div class="content">
    <div class="total-info">
      <div class="total-count">
        {{ alltimecount | number : "1.0-0" }}
      </div>
      <div class="desc">
        <mat-icon svgIcon="loyalty"></mat-icon>
        <span>{{ "dashboard.total_labeled_files" | translate }}</span>
      </div>
    </div>
    <table class="detail-info">
      <tr *ngFor="let s of statisic_count" class="row">
        <td class="desc">
          <mat-icon class="small" [svgIcon]="s.icon"></mat-icon>
          <span>{{ "dashboard." + s.field | translate }}</span>
        </td>

        <td class="count">
          {{ s.count | number : "1.0-0" }}
        </td>
      </tr>
    </table>
  </div>
</div>
