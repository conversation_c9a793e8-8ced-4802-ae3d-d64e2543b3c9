import { Injectable } from '@angular/core';
import { isNil } from 'lodash';

@Injectable({
  providedIn: 'root',
})
export class AppGlobalService {
  isPrinting: boolean = false;
  timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  constructor() {}

  setTimezone = (tz: string) => {
    if (isNil(tz)) {
      return;
    }
    try {
      Intl.DateTimeFormat(undefined, { timeZone: tz });
      this.timezone = tz;
    } catch (e) {
      console.error('Failed to get timezone');
    }
  };
}
