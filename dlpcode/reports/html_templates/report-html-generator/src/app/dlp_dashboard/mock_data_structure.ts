export enum EStorageType {
  aws = 1,
  cloud = 2,
  prem = 3,
  smb = 4,
  gdrive = 6
}

export enum EPeriod {
  Hour24 = 0,
  Day7 = 1,
  Day14 = 2
}

interface IFilestoScanDetail {
  percentage: number;
  storage_type: EStorageType;
  storagecount: number;
}

export interface IFilestoScan {
  files_to_scan: IFilestoScanDetail[];
  total_files: number;
  total_percentage: number;
}

interface ISensitiveFiles {
  percentage: number;
  storage_type: EStorageType;
  storagecount: number;
}

export interface ISensitiveFilesDistribution {
  sensitive_files: ISensitiveFiles[];
  total_files: number;
  total_percentage: number;
}

interface ILineChartPoint {
  count: number;
  time: number;
}

export interface IDailyScannedFiles {
  period: EPeriod;
  dailyfilescanscannedresults: ILineChartPoint[];
  dailyfilescansensitiveresults: ILineChartPoint[];
}

export interface ISensitiveFilesLabeled {
  alltimecount: number;
  onedaycount: number;
  oneweekcount: number;
  twoweekcount: number;
}

interface IAICategoryFiles {
  categorycount: number;
  percentage: number;
  main_category: string;
  sub_category: string;
}

export interface IAiCategories {
  files_to_scan: IAICategoryFiles[];
  total_ai_count: number;
  total_percentage: number;
  total_scanned_files: number;
}

export interface IComplianceFiles {
  compliance: string;
  count: number;
  percentage: number;
}

export interface IComplianceFilesShowMore {
  total_scanned_count: number;
  total_scanned_percent: number;
  total_compliance_count: number;
  total_compliance_percent: number;
  compliance_data: IComplianceFiles[];
}

export interface IReportInfo {
  name: string;
  /** When the report generated */
  generated_on: string;
  schedule: string;
  period: string;
  created_by: string;
  notes: string;
  /** IANA format */
  timezone: string;
  firmware_version: string;
}

export interface IScannedFiles {
  alltimecount: number;
  onedaycount: number;
  oneweekcount: number;
  twoweekcount: number;
}

export interface IDashboardData {
  /** data for Scanned File Distribution */
  filestoscan: IFilestoScan;
  /** data for Sensitive File Distribution */
  sensitivefilesdistribution: ISensitiveFilesDistribution;
  /** data for Daily File Scan Results */
  dailyScannedFiles: IDailyScannedFiles[];
  /** data for Sensitive Files Labeled */
  sensitivefileslabeled: ISensitiveFilesLabeled;
  /** data for scanned file Distribution */
  scannedfiles: IScannedFiles;
  /** data for sensitive file Owners number */
  sensitivefileowners: number;
  /** data for Files in Top 10 AI Categories */
  aicategories: IAiCategories;
  /** data for Compliance Files */
  compliancefiles: IComplianceFiles[];
  /** data for Compliance Files Show More */
  compliancefilesshowmore: IComplianceFilesShowMore;
  /** report info */
  report_info: IReportInfo;
  /** generate PDF format report */
  is_printing: boolean;
}
