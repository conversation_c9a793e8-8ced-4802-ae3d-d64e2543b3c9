import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppComponent } from './app.component';

import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { Observable, of } from 'rxjs';
import { TRANSLATIONS_EN } from './i18n_en';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateService } from '@/common/translate/translate.service';
import { HighchartsChartModule } from 'highcharts-angular';
import { ScannedFileComponent } from './scanned-file/scanned-file.component';
import { SensitiveFileComponent } from './sensitive-file/sensitive-file.component';
import { SensitiveFileLabeledComponent } from './sensitive-file-labeled/sensitive-file-labeled.component';
import { ScanResultsComponent } from './scan-results/scan-results.component';
import { ComplianceFilesComponent } from './compliance-files/compliance-files.component';
import { AiCategoriesComponent } from './ai-categories/ai-categories.component';
import { MatIconModule } from '@angular/material/icon';
import { DashboardAllComplianceFilesComponent } from './compliance-files/dashboard-all-compliance-files/dashboard-all-compliance-files.component';
import { DialogComponent } from './compliance-files/dialog/dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { ScannedFileSummaryComponent } from './scanned-file-summary/scanned-file-summary.component';
import { SensitiveFileOwnersComponent } from './sensitive-file-owners/sensitive-file-owners.component';

class InlineTranslateLoader implements TranslateLoader {
  getTranslation(lang: string): Observable<any> {
    switch (lang) {
      case 'en':
        return of(TRANSLATIONS_EN);
      default:
        return of({});
    }
  }
}

function appInitializerFactory(translateService: TranslateService) {
  return () => translateService.switchLanguage('en');
}

@NgModule({
  declarations: [
    AppComponent,
    ScannedFileComponent,
    SensitiveFileComponent,
    SensitiveFileLabeledComponent,
    ScanResultsComponent,
    ComplianceFilesComponent,
    AiCategoriesComponent,
    DashboardAllComplianceFilesComponent,
    DialogComponent,
    ScannedFileSummaryComponent,
    SensitiveFileOwnersComponent,
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HighchartsChartModule,
    MatIconModule,
    MatDividerModule,
    MatDialogModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useClass: InlineTranslateLoader,
      },
    }),
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true,
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
