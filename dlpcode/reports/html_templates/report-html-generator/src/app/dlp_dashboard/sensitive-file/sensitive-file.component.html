<div class="title">
  {{ "dashboard.sensitive_file_distribution" | translate }}
</div>
<div class="body">
  <div class="content">
    <div class="chart-box">
      <highcharts-chart
        [Highcharts]="Highcharts"
        [options]="chartOptions"
      ></highcharts-chart>
      <div class="small-title">
        {{ "dashboard.sensitive_files" | translate }}
      </div>
    </div>

    <table class="info-table">
      <tr>
        <td class="info">
          <mat-icon svgIcon="storageicon"></mat-icon>
          <span class="total">{{
            "dashboard.sensitive_files" | translate
          }}</span>
        </td>
        <td class="align-right">
          {{ total_count.toLocaleString() }}
        </td>
        <td class="align-right">{{ total_percent.toLocaleString() }}%</td>
      </tr>
      <tr *ngFor="let data of baseData">
        <td class="info">
          <i class="circle" [ngStyle]="{ 'background-color': data.color }"></i>
          <mat-icon [svgIcon]="data.icon"></mat-icon>
          {{ data.label }}
        </td>
        <td class="align-right">
          {{ data.storagecount.toLocaleString() }}
        </td>
        <td class="align-right">{{ data.percentage }}%</td>
      </tr>
    </table>
  </div>
</div>
