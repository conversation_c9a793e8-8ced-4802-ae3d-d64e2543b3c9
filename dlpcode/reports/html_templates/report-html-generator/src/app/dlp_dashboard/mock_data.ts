import { IDashboardData } from './mock_data_structure';

export const mockData: IDashboardData = {
  filestoscan: {
    files_to_scan: [
      {
        percentage: 20,
        storage_type: 1,
        storagecount: 100,
      },
      {
        percentage: 30,
        storage_type: 2,
        storagecount: 150,
      },
      {
        percentage: 25,
        storage_type: 3,
        storagecount: 125,
      },
      {
        percentage: 25,
        storage_type: 4,
        storagecount: 125,
      },
      {
        percentage: 25,
        storage_type: 6,
        storagecount: 125,
      },
    ],
    total_files: 500,
    total_percentage: 100,
  },
  sensitivefilesdistribution: {
    sensitive_files: [
      {
        percentage: 20,
        storage_type: 1,
        storagecount: 100,
      },
      {
        percentage: 30,
        storage_type: 2,
        storagecount: 150,
      },
      {
        percentage: 25,
        storage_type: 3,
        storagecount: 125,
      },
      {
        percentage: 25,
        storage_type: 4,
        storagecount: 125,
      },
      {
        percentage: 25,
        storage_type: 6,
        storagecount: 125,
      },
    ],
    total_files: 500,
    total_percentage: 100,
  },
  sensitivefileslabeled: {
    alltimecount: 1679817,
    onedaycount: 73196,
    oneweekcount: 585379,
    twoweekcount: 1198623,
  },
  scannedfiles: {
    alltimecount: 1679817,
    onedaycount: 73196,
    oneweekcount: 585379,
    twoweekcount: 1198623,
  },
  sensitivefileowners: 123,
  dailyScannedFiles: [
    {
      period: 0,
      dailyfilescanscannedresults: [
        {
          count: 27065,
          time: 1744070400,
        },
        {
          count: 105299,
          time: 1744156800,
        },
        {
          count: 99120,
          time: 1744243200,
        },
        {
          count: 92543,
          time: 1744329600,
        },
        {
          count: 88397,
          time: 1744416000,
        },
        {
          count: 89243,
          time: 1744502400,
        },
        {
          count: 80468,
          time: 1744588800,
        },
        {
          count: 57154,
          time: 1744675200,
        },
      ],
      dailyfilescansensitiveresults: [
        {
          count: 26612,
          time: 1744070400,
        },
        {
          count: 99567,
          time: 1744156800,
        },
        {
          count: 76701,
          time: 1744243200,
        },
        {
          count: 83305,
          time: 1744329600,
        },
        {
          count: 83415,
          time: 1744416000,
        },
        {
          count: 83787,
          time: 1744502400,
        },
        {
          count: 77124,
          time: 1744588800,
        },
        {
          count: 53480,
          time: 1744675200,
        },
      ],
    },
  ],
  compliancefiles: [
    {
      compliance: 'top1',
      count: 1755827,
      percentage: 73.35,
    },
    {
      compliance: 'top2',
      count: 637940,
      percentage: 26.65,
    },
    {
      compliance: 'top3',
      count: 637940,
      percentage: 26.65,
    },
    {
      compliance: 'top4',
      count: 637940,
      percentage: 26.65,
    },
    {
      compliance: 'top5',
      count: 637940,
      percentage: 26.65,
    },
    {
      compliance: 'Others',
      count: 0,
      percentage: 0.0,
    },
    {
      compliance: 'Total',
      count: 2393767,
      percentage: 100.0,
    },
  ],
  compliancefilesshowmore: {
    compliance_data: [
      {
        compliance: 'GDPR',
        count: 5067,
        percentage: 27,
      },
      {
        compliance: 'CCPA',
        count: 4267,
        percentage: 18,
      },
      {
        compliance: 'SOX',
        count: 3190,
        percentage: 13,
      },
      {
        compliance: 'GLBA',
        count: 2600,
        percentage: 11,
      },
      {
        compliance: 'Apps',
        count: 730,
        percentage: 3,
      },
    ],
    total_scanned_count: 23679,
    total_scanned_percent: 100,
    total_compliance_count: 23679,
    total_compliance_percent: 42,
  },
  aicategories: {
    files_to_scan: [
      {
        categorycount: 1070492,
        percentage: 39.41,
        main_category: 'main-1',
        sub_category: 'sub-1',
      },
      {
        categorycount: 33011,
        percentage: 1.22,
        main_category: 'main-2',
        sub_category: 'sub-2',
      },
      {
        categorycount: 32724,
        percentage: 1.2,
        main_category: 'main-3',
        sub_category: 'sub-3',
      },
      {
        categorycount: 14016,
        percentage: 0.52,
        main_category: 'main-4',
        sub_category: 'sub-4',
      },
      {
        categorycount: 213,
        percentage: 0.01,
        main_category: 'main-5',
        sub_category: 'sub-5',
      },
      {
        categorycount: 288,
        percentage: 0.01,
        main_category: 'main-6',
        sub_category: 'sub-6',
      },
      {
        categorycount: 2,
        percentage: 0.0,
        main_category: 'main-7',
        sub_category: 'sub-7',
      },
      {
        categorycount: 3,
        percentage: 0.0,
        main_category: 'main-8',
        sub_category: 'sub-8',
      },
      {
        categorycount: 18,
        percentage: 0.0,
        main_category: 'main-9',
        sub_category: 'sub-9',
      },
      {
        categorycount: 64,
        percentage: 0.0,
        main_category: 'main-10',
        sub_category: 'sub-10',
      },
    ],
    total_ai_count: 1150831,
    total_percentage: 42.37,
    total_scanned_files: 2716185,
  },
  report_info: {
    name: 'Ethan_test_report',
    schedule: 'Weekly Monday, at 11:20 AM PDT',
    period: 'Last 7 days',
    created_by: 'Ethan Liu',
    generated_on: '2023-10-01 12:00 PM',
    notes: 'This is a test report for sensitive files.',
    timezone: 'America/Vancouver',
    firmware_version: 'FortiData-KVM 2.1.0 build0001 20250807'
  },
  is_printing: true,
};
