:host {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .conditions {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .time-period-selector {
    width: 150px;
  }

  .legend {
    display: flex;
    gap: 5px;
    align-items: center;
    font-size: 12px;

    .scanned {
      background-color: #4a8bed;
    }

    .sensitive {
      background-color: #ec5077;
    }

    .label {
      white-space: nowrap;
    }

    .line {
      width: 15px;
      height: 3px;
      margin: 0 auto;
    }
  }

  .body {
    display: flex;
    gap: 40px;
    flex: 1;
  }

  .content {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: flex-start;
    justify-content: center;

    .chart-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 200px;
      width: 100%;
    }

    highcharts-chart {
      display: inline-block;
      width: 100%;
    }
  }
}
