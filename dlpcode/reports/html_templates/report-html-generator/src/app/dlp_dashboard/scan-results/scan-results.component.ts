import { Component, Input, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import { EPeriod, IDailyScannedFiles } from '../mock_data_structure';
import { TranslateService } from '@/common/translate/translate.service';

@Component({
  selector: 'app-scan-results',
  templateUrl: './scan-results.component.html',
  styleUrls: ['./scan-results.component.css'],
})
export class ScanResultsComponent implements OnInit {
  @Input() data: IDailyScannedFiles[];
  Highcharts: typeof Highcharts = Highcharts;
  chartOptions: any = {};
  selectPeriod: EPeriod = EPeriod.Hour24;

  constructor(private translateService: TranslateService) {}

  updateChartOption = (period: EPeriod) => {
    const aimPeriod = this.data[0];
    let scanned = aimPeriod?.dailyfilescanscannedresults || [];
    let sensitive = aimPeriod?.dailyfilescansensitiveresults || [];
    const that = this;

    let seriesData = [];

    let scannedData = [];
    scanned.forEach((s) => {
      scannedData.push([s?.time, s?.count]);
    });

    let sensitiveData = [];
    sensitive.forEach((s) => {
      sensitiveData.push([s?.time, s?.count]);
    });

    seriesData.push(
      {
        name: 'scanned',
        color: '#4A8BED',
        data: scannedData,
      },
      {
        name: 'sensitive',
        color: '#EC5077',
        data: sensitiveData,
      }
    );

    const intervals = scannedData.map((v) => v[0]);

    this.chartOptions = {
      credits: {
        enabled: false,
      },
      legend: {
        enabled: false,
      },
      accessibility: {
        enabled: false,
      },
      title: undefined,
      subtitle: undefined,
      chart: {
        plotShadow: false,
        backgroundColor: 'transparent',
        type: 'spline',
      },
      series: seriesData,
      yAxis: {
        allowDecimals: false,
        title: {
          text: 'Files',
        },
        labels: {
          formatter: function () {
            return this.value.toLocaleString();
          },
        },
      },
      xAxis: {
        type: 'datetime',
        ordinal: false,
        tickPositioner: function () {
          return intervals;
        },
        labels: {
          formatter: function () {
            const timestr = that.transformXAxisValueToTime(Number(this.value));
            return that.getXAxisLabel(
              that.transformToDateTime(timestr),
              that.selectPeriod
            );
          },
          rotation: intervals.length > 14 ? -45 : 0,
          overflow: 'justify',
          style: {
            whiteSpace: 'nowrap',
            textOverflow: 'clip',
          },
        },
      },
      tooltip: {
        enabled: true,
        headerFormat: '',
        formatter: function () {
          const timestr = that.transformXAxisValueToTime(Number(this.x));
          return `${this.series.name.replace(/( |^)[a-z]/g, (l) =>
            l.toUpperCase()
          )}
          : <b>${this.y.toLocaleString()}</b><br>${that.formatDate(
            that.transformToDateTime(timestr)
          )}`;
        },
      },
      plotOptions: {
        spline: {
          lineWidth: 1,
          marker: {
            enabled: false,
          },
        },
      },
    };
  };

  ngOnInit(): void {
    this.selectPeriod = this.data[0].period;
    this.updateChartOption(this.selectPeriod);
  }

  transformXAxisValueToTime(dateTime: number): string {
    const date = new Date(dateTime * 1000);
    const dateStr = `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date
      .getDate()
      .toString()
      .padStart(2, '0')}`;
    return `${dateStr} ${date.getHours().toString().padStart(2, '0')}`;
  }

  transformToDateTime(value: string): Date {
    const [datePart, hourPart] = value.split(' ');
    const [monthStr, dayStr] = datePart.split('/');

    const now = new Date();
    const year = now.getFullYear();
    const month = parseInt(monthStr, 10) - 1;
    const day = parseInt(dayStr, 10);
    const hour = parseInt(hourPart, 10);

    return new Date(year, month, day, hour);
  }

  getXAxisLabel(date: Date, periodType: EPeriod): string {
    switch (periodType) {
      case EPeriod.Day14:
      case EPeriod.Day7:
        return new Intl.DateTimeFormat('en-US', {
          month: 'short',
          day: 'numeric',
        }).format(date);
      default:
        return new Intl.DateTimeFormat('en-US', {
          hour: 'numeric',
          hour12: true,
        }).format(date);
    }
  }

  formatDate(date: Date): string {
    const month = new Intl.DateTimeFormat('en-US', { month: 'short' }).format(
      date
    ); // "Apr"
    const day = date.getDate(); // 14
    const hour = date.getHours(); // 0–23

    const hour12 = hour % 12 === 0 ? 12 : hour % 12; // 12-hour format
    const ampm = hour >= 12 ? 'PM' : 'AM';

    return `${month} ${day}, ${hour12} ${ampm}`;
  }

  getPeriodStr(period: EPeriod): string {
    switch (period) {
      case EPeriod.Hour24:
        return this.translateService.lookup('dashboard.onedaycount');
      case EPeriod.Day7:
        return this.translateService.lookup('dashboard.oneweekcount');
      case EPeriod.Day14:
        return this.translateService.lookup('dashboard.twoweekcount');
      default:
        return `${period}`;
    }
  }
}
