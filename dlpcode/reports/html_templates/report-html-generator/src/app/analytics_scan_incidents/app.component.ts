import { elem } from '@/common/table/element';
import { FilterOperation, TableSettings } from '@/common/table/table';
import { TableComponent } from '@/common/table/table/table.component';
import { DateUtilService } from '@/service/date-util.service';
import { FormatService } from '@/service/format.service';
import { Component, ElementRef, OnInit, Renderer2, ViewChild } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { isNil, keys, last, reduce } from 'lodash';
import { AppGlobalService } from './app-global.service';
import { TranslateService } from '@/common/translate/translate.service';
//import { DataTypeService } from './data_type.service';
import { ICONS } from './icons';
import { mockData } from './mock_data';
import { IScanIncident, IScanIncidentEntry, EScanStorageType } from './mock_data_structure';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  
  reportData: IScanIncident = 'DOLLAR_UPPER_MOCK_DATA' as any;

  tableSettings: TableSettings;

  selectedScan;

  @ViewChild(TableComponent)
  tableComponent!: TableComponent;

  tableData: any[];

  detailData: any;

  allDetails: any[];

  get isPrinting() {
    return this.globalService.isPrinting;
  }

  constructor(
    private iconRegistry: MatIconRegistry,
    private translateService: TranslateService,
    private sanitizer: DomSanitizer,
    public globalService: AppGlobalService,
    private el: ElementRef,
    private renderer: Renderer2,
    private dateutilService: DateUtilService,
    private formatService: FormatService,

  ) {
    this.reportData = mockData; // Mock data import

    globalService.setTimezone(this.reportData.report_info.timezone);
    //globalService.isPrinting = this.reportData.is_printing || false;

    globalService.isPrinting = false;  // Disable isPrinting for Scan Incidents by default. 

    // Below code to set style to A4 size is Not required for the Scan Incidents report
    if (this.reportData.is_printing) {
      this.renderer.setStyle(this.el.nativeElement, 'max-width', '210mm');
    }

    this.selectedScan = this.reportData.report_info;

    this.registerMatIcons();
  }

  ngOnInit(): void {
    //this.dataTypeService.initData(this.reportData);
    this.tableData = this.reportData.table_data;
    this.initTableSettings();

    if (this.isPrinting) {
      //this.initAllDetails();   //TODO
    }
  }

  initTableSettings() {

    this.tableSettings = {

      tableId: 'scan_incidents_table',
      hasSelect: false,
      saveToStorage: false,
      hasMenuButtons: false,
      hasHideShowCols: false,
      hasPaginator: true,
      pageSize: 10,
      forceToRefresh: true,
      hasMenuBar: !this.isPrinting,
      isClientSidePagination: true,
      timeZone: this.globalService.timezone,      

      columns: [
        {
          id: 'create_time',
          langKey: 'scan_incidents.incident_time',
          sorting: true,
          cellFormatter: (entry: any) => {
            let incidentTime = entry.create_time;
            if (!incidentTime || incidentTime == '0') return '';
            //return this.dateutilService.convertToGMTTimestamp(incidentTime, this.globalService.timezone);
            const timestamp = this.dateutilService.convertToGMTTimestamp(incidentTime, this.globalService.timezone);
            const [datePart, timePart] = timestamp.split(' ');
            return `${datePart}<br>${timePart}`;            
          }
        },
        {
          id: 'severity',
          langKey: 'scan_incidents.risk',
          sorting: true,
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMappingOrder: { key: 'desc' },
            searchMapping: {
              Critical: 0,
              High: 1,
              Medium: 2,
              Low: 3
            }
          },
          cellFormatter: (entry: any) => {
            const data = this.formatService.getSeverity(Number(entry.risk));
            return elem(
              'div',
              {
                className: 'icon-label-cell'
              },
              {
                children: [
                  elem(
                    'div',
                    {
                      className: 'icon-circle'
                    },
                    {
                      attributes: {
                        style: `background: ${data.color}`
                      }
                    }
                  ),
                  elem('span', {
                    //className: 'link-cell',
                    innerText: data.text
                  })
                ]
              }
            );
          }
        },
        {
          id: 'status',
          langKey: 'scan_incidents.incident_status',
          sorting: true,
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMappingOrder: { key: 'desc' },
            searchMapping: {
              New: 0,
              'In Process': 1,
              Closed: 2,
              Escalated: 3
            }
          },
          cellFormatter: (entry: any) => {
            //const attributes = entry.attributes;
            const data = this.formatService.getIncidentStatus(Number(entry.status));
            return elem(
              'div',
              {
                className: 'icon-label-cell'
              },
              {
                children: [
                  elem(
                    'fd-n-icon',
                    {
                      matIcon: data.icon
                    },
                    {
                      attributes: {
                        style: `color: ${data.color}`
                      }
                    }
                  ),
                  elem('span', {
                    innerText: data.text
                  })
                ]
              }
            );
          }
        },        
        {
          id: 'file_name',
          langKey: 'scan_incidents.file_name',
          sorting: true,
          //breakLines: true,
          filterSettings: {
            operations: [FilterOperation.Equal,FilterOperation.Like],
            usePartialMatch: true
          }
        },
        {
          id: 'file_owner',
          langKey: 'scan_incidents.file_owner',
          cellFormatter: (entry: any) => {
            const name = entry.file_owner?.name;
            return !name || name.toLowerCase() === 'unknown' ? '' : name;
          }
        },
        {
          id: 'ignored',
          langKey: 'scan_incidents.ignored',
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMappingOrder: { key: 'desc' },
            searchMapping: {
              Yes: true,
              No: false
            }
          },
          cellFormatter: (entry: any) => {
            const ignored_setting = this.formatService.getIgnoredStatus(entry.ignored);
            return elem('fd-n-icon', ignored_setting);
          }
        },
        {
          id: 'false_positive',
          langKey: 'scan_incidents.false_positive',
          //sorting: true,
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMappingOrder: { key: 'desc' },
            searchMapping: {
              Yes: true,
              No: false
            }
          },
          cellFormatter: (entry: any) => {
            const setting = this.formatService.getFalsePositiveStatus(entry.false_positive);
            const colSetting = setting === ''? setting : elem('fd-n-icon', setting);
            return colSetting;
          }
        },
        {
          id: 'label',
          langKey: 'scan_incidents.labels',       
          cellFormatter: (entry: any) => {
            const labels = entry.labels;
            if (isNil(labels)) {
              return '';
            }
            const labelCounts = reduce(
              keys(labels),
              (res, current) => {
                const r = res + labels[current].length;
                return r;
              },
              0
            );
            if (labelCounts === 0) {
              return '';
            }
            return elem('fd-n-labels', { labels: labels });
          }
        },
        {
          id: 'extension',
          langKey: 'scan_incidents.extension',
          cellFormatter: (row: IScanIncidentEntry) => last(row?.file_name.split('.'))          
        },
        {
          id: 'file_size',
          langKey: 'scan_incidents.file_size',
          cellFormatter: (row: IScanIncidentEntry) => this.formatService.formatBytes(row?.size * 1024)
        },
        {
          id: 'full_path',
          langKey: 'scan_incidents.file_path',
          //breakLines: true,
          filterSettings: {
            operations: [FilterOperation.Equal, FilterOperation.Like],
            usePartialMatch: true
          },
          cellFormatter: (entry: any) => {
            //const fileInfo = entry.file_info;
            const fullPath = entry.full_path;

            return fullPath;
            // return fullPath && fullPath.length > 20
            // ? fullPath.slice(0, 17) + '...'
            // : fullPath;

            // Insert a line break after every 20 characters
            // const wrapped = fullPath.match(/.{1,20}/g)?.join('<br>') || '';
            // return wrapped;
          }
        },
        {
          id: 'policy_name',
          langKey: 'scan_incidents.policy_name',
          cellFormatter: (entry: any) => {
            const pname = entry.policy_name;
            return pname;
          }
        },
        {
          id: 'rule_name',
          langKey: 'scan_incidents.rule_name',
          breakLines: true,
          cellFormatter: (entry: any) => {
            const rname = entry.rule_name;
            return rname;
          }
        },
        {
          id: 'action',
          langKey: 'scan_incidents.scan_action',
          cellFormatter: (entry: any) => {
            //const attributes = entry.attributes;
            const actions = entry.action?.join(',');
            return actions;
          }
        },
        {
          id: 'scan_name',
          langKey: 'scan_incidents.scan_name',
          cellFormatter: (entry: any) => {
            const sname = entry.scan_name;
            return sname;
          }
        },
        // {
        //   id: 'scan_id',
        //   langKey: 'scan_incidents.scan_id',
        //   cellFormatter: (entry: any) => {
        //     const policy = entry.pol_details;
        //     return policy.sid;
        //   }
        // },
        // {
        //   id: 'policy_id',
        //   langKey: 'scan_incidents.policy_id',
        //   cellFormatter: (entry: any) => {
        //     const policy = entry.pol_details;
        //     return policy.pid;
        //   }
        // },
        // {
        //   id: 'id',
        //   langKey: 'scan_incidents.id',
        // }
      ],
      // customActions: this.isPrinting
      //   ? null
      //   : [
      //       {
      //         text: 'scan_incidents.view_details',
      //         matIcon: 'vertical_split',
      //         iconSize: 16,
      //         width: 'auto',
      //         click: (row: any) => {
      //           this.detailData = {
      //             ...row,
      //             storage: this.getStorageLabel(),
      //             storage_type: this.selectedScan.storage_type,
      //             scan_policy_id: this.selectedScan.id
      //           };
      //           //this.selectedChanged(row);
      //         }
      //       }
      //     ]      
    };
  }

  registerMatIcons() {
    for (const [key, value] of Object.entries(ICONS)) {
      this.iconRegistry.addSvgIconLiteral(key, this.sanitizer.bypassSecurityTrustHtml(value));
    }
  }  

  getStorageType() {
    const scan = this.selectedScan;
    if (!scan) return '';
    if ([-1 || 'All'].includes(scan?.storage_type)) return 'All';
    return this.formatService.getScanStoreType(scan.storage_type);
  }

  getStorageLabel(): string {
    const scan = this.selectedScan;
    if (!scan) return '';
    if ([-1 || 'All'].includes(scan?.storage_type)) return 'All';
    return scan.target ?? scan.scan_storage;
  } 

}
