import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppComponent } from './app.component';

import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { Observable, of } from 'rxjs';
import { TRANSLATIONS_EN } from './i18n_en';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateService } from '@/common/translate/translate.service';
import { NotifyModule } from '@/common/notify/notify.module';
import { CommonTableModule } from '@/common/table/table.module';
import { MaterialModule } from '@/material.module';
import { HighchartsChartModule } from 'highcharts-angular';

class InlineTranslateLoader implements TranslateLoader {
  getTranslation(lang: string): Observable<any> {
    switch (lang) {
      case 'en':
        return of(TRANSLATIONS_EN);
      default:
        return of({});
    }
  }
}

function appInitializerFactory(translateService: TranslateService) {
  return () => translateService.switchLanguage('en');
}

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    MaterialModule,
    NotifyModule,
    CommonTableModule,
    HighchartsChartModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useClass: InlineTranslateLoader,
      },
    }),
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true,
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
