import { FilterOperation, TableSettings } from '@/common/table/table';
import { Component, ElementRef, Renderer2 } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { ITest } from './mock_data_structure';
import { mockData } from './mock_data';
import { AppGlobalService } from './app-global.service';
import { TranslateService } from '@/common/translate/translate.service';
import { of } from 'rxjs';
import * as Highcharts from 'highcharts';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
})
export class AppComponent {
  reportData: ITest = 'DOLLAR_UPPER_MOCK_DATA' as any;
  Highcharts: typeof Highcharts = Highcharts;
  chartOptions: any = {
    credits: {
      enabled: false,
    },
    chart: {
      plotShadow: false,
      backgroundColor: 'transparent',
      spacing: [0, 0, 0, 0],
    },
    plotOptions: {
      pie: {
        shadow: false,
        center: ['50%', '50%'],
        size: '100%',
        innerSize: '70%',
        dataLabels: {
          enabled: false,
        },
      },
    },
    accessibility: {
      enabled: false,
    },
    title: undefined,
    colors: ['#2BC1E3', '#279CEF', '#227ADF', '#63DCC8'],
    tooltip: {
      enabled: true,
      headerFormat: '',
      useHTML: true,
      formatter: function () {
        return `${this.point.name} ${this.y.toLocaleString()}`;
      },
    },
    series: [
      {
        type: 'pie',
        name: 'label',
        data: [
          { x: 1, y: 20 },
          { x: 2, y: 30 },
          { x: 3, y: 40 },
          { x: 4, y: 50 },
        ],
      },
    ],
  };

  constructor(
    private iconRegistry: MatIconRegistry,
    private translateService: TranslateService,
    private sanitizer: DomSanitizer,
    public globalService: AppGlobalService,
    private el: ElementRef,
    private renderer: Renderer2
  ) {
    this.reportData = mockData; // Mock data import

    globalService.setTimezone(this.reportData.report_info.timezone);
    globalService.isPrinting = this.reportData.is_printing || false;

    if (this.reportData.is_printing) {
      this.renderer.setStyle(this.el.nativeElement, 'max-width', '210mm');
    }

    this.registerMatIcons();
  }

  tableSettings: TableSettings = {
    tableId: 'custom_dtype_group_table',
    hasSelect: true,
    hasMenuButtons: false,
    hasHideShowCols: false,
    hasPaginator: true,
    columns: [
      {
        id: 'column1',
        langKey: 'sample.col1',
        filterSettings: {
          operations: [FilterOperation.Equal],
        },
      },
      {
        id: 'column2',
        langKey: 'sample.col2',
      },
    ],
    remote: {
      api: (_payload: any) => {
        return of({
          list: this.reportData.tableData,
          total: this.reportData.tableData.length,
        });
      },
    },
  };

  registerMatIcons = () => {
    const closeSvg = `<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#1f1f1f"><path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/></svg>`;
    this.iconRegistry.addSvgIconLiteral(
      'close',
      this.sanitizer.bypassSecurityTrustHtml(closeSvg)
    );
    const fortidataSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"> <path d="M0 0 C3.3 0 6.6 0 10 0 C10 1.98 10 3.96 10 6 C6.7 6 3.4 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(11,21)" /> <path d="M0 0 C3.3 0 6.6 0 10 0 C10 1.98 10 3.96 10 6 C6.7 6 3.4 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(11,5)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,13)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(0,13)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C8.125 4.875 8.125 4.875 7 6 C4.667 6.041 2.333 6.042 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,21)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C5.625 6.125 5.625 6.125 2 6 C1.34 5.34 0.68 4.68 0 4 C0 2.68 0 1.36 0 0 Z" fill="#EE3124" transform="translate(0,21)" /> <path d="M0 0 C3.375 -0.125 3.375 -0.125 7 0 C9 2 9 2 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,5)" /> <path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.063 C3.438 2.043 3.438 4.023 3.438 6.063 C0.468 6.063 -2.503 6.063 -5.563 6.063 C-4.488 0.079 -4.488 0.079 0 0 Z" fill="#EE3124" transform="translate(5.5625,4.9375)" /> </svg>`;
    this.iconRegistry.addSvgIconLiteral(
      'fortidata',
      this.sanitizer.bypassSecurityTrustHtml(fortidataSvg)
    );
  };
}
