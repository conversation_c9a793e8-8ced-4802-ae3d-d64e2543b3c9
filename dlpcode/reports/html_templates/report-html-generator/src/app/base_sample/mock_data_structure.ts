export interface IReportInfo {
  /** Report name */
  name: string;
  /** When the report generated */
  generated_on: string;
  /** IANA format */
  timezone: string;
}

export enum ECol2 {
  like,
  dislike,
}

export interface ITableData {
  /** Column1 */
  column1: string;
  /** Column2 */
  column2: ECol2;
}

export interface ITest {
  tableData: ITableData[];
  /** report info */
  report_info: IReportInfo;
  /** used to generate PDF format report */
  is_printing: boolean;
}
