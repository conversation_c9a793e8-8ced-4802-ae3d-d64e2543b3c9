import { Component, Input, OnInit } from '@angular/core';
import { ISharedSensitiveFileOwnerAll } from '../mock_data_structure';
import { FormControl } from '@angular/forms';
import { FormatService } from '@/service/format.service';
import { max } from 'lodash';
import { AppGlobalService } from '../app-global.service';

@Component({
  selector: 'app-shared-sensitive-file-by-owner-more',
  templateUrl: './shared-sensitive-file-by-owner-more.component.html',
  styleUrls: ['./shared-sensitive-file-by-owner-more.component.css'],
})
export class SharedSensitiveFileByOwnerMoreComponent implements OnInit {
  @Input() data: ISharedSensitiveFileOwnerAll;
  @Input() type: string;

  baseData: any[] = [];

  share_types: any[] = [
    {
      label: 'All Types',
      value: 'all',
    },
    {
      label: 'Public',
      value: 'public',
    },
    {
      label: 'External',
      value: 'external',
    },
    {
      label: 'Internal',
      value: 'internal',
    },
  ];

  typeControl: FormControl;

  constructor(
    private formatService: FormatService,
    public globalService: AppGlobalService
  ) {}

  ngOnInit(): void {
    this.typeControl = new FormControl(this.type || 'all');
    if (this.globalService.isPrinting) {
      this.typeControl.disable();
    }
    this.typeControl.valueChanges.subscribe((value) => {
      this.initData(value);
    });
    this.initData(this.type || 'all');
  }

  initData(type: string) {
    let maxFiles = 0;
    this.baseData = this.data[type].map((item) => {
      const storageInfo = this.formatService.getStorageType(item.storage.type);
      maxFiles = Math.max(maxFiles, item.files);
      return {
        owner: item.user_name,
        name: item.storage.name,
        type: item.storage.type,
        files: item.files,
        real_percent: 0,
        icon: storageInfo.icon,
      };
    });

    this.baseData.forEach((item) => {
      item.real_percent = maxFiles ? (item.files / maxFiles) * 100 : 0;
    });
  }

  truncateLabel(label: string) {
    return this.formatService.truncateLabel(
      label,
      this.globalService.isPrinting ? 25 : 15
    );
  }

  getStorageLabel(type) {
    return this.formatService.getStorageType(type).text;
  }
}
