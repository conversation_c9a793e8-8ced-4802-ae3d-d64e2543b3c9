:host {
  display: flex;
  flex-direction: column;
  height: 100%;

  .content {
    overflow-y: none;
    width: 100%;
    height: 99%;
    display: flex;
    flex-direction: column;
  }

  .title-panel {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    font-weight: bold;
    font-size: 16px;
    padding: 14px 20px 14px 20px;

    .info {
      width: 16px;
      height: 16px;
      cursor: help;
    }

    .close {
      margin-left: auto;
      cursor: pointer;
      transition: opacity 0.15s;
    }

    span {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }
  }

  .bold {
    font-weight: bold;
  }

  .align-right {
    text-align: right;
  }

  .align-center {
    text-align: center;
  }

  .main-table {
    width: 98%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin: 20px 10px;
    overflow-x: hidden;
    overflow-y: auto;
    flex: 1;
  }

  .tip-row-space {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0px 15px;
  }

  .tip-row {
    display: flex;
    align-items: center;
    gap: 6px;
    justify-content: flex-end;
    padding: 15px 15px 0px 15px;

    .mat-icon {
      width: 16px;
      height: 16px;
      font-size: 16px;
    }
  }

  .info-table {
    width: 98%;
    border-collapse: collapse;
    font-size: 14px;
    table-layout: fixed;

    td {
      padding-right: 5px;
      white-space: nowrap;
    }

    tr {
      height: 30px;
      border-bottom: solid 1px #cccccc;
    }

    .flex-cell {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .mat-icon {
      width: 16px;
      height: 16px;
      font-size: 16px;
    }

    .link {
      cursor: pointer;
      color: #2564bf;
    }

    .percent-cell {
      width: 40%;
    }

    .percent-cell-35 {
      width: 35%;
    }

    .percent-cell-30 {
      width: 30%;
    }

    .percent-cell-25 {
      width: 25%;
    }

    .percent-cell-22 {
      width: 22%;
    }

    .percent-cell-20 {
      width: 20%;
    }

    .percent-cell-15 {
      width: 15%;
    }

    .percent-cell-short {
      width: 45%;
    }

    .percent-bar {
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
      background-color: #aedff8;
      height: 10px;
    }

    span {
      white-space: nowrap;
    }
  }

  .footer {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    height: 48px;
    padding-left: 20px;
    padding-right: 20px;
    gap: 20px;
  }
  .header-field {
    display: flex;
    gap: 15px;
    align-items: center;

    label {
      min-width: 100px;
      font-weight: 700;
      white-space: nowrap;
    }
  }

  .tip-row {
    padding: 0px !important;
  }

  ::ng-deep mat-select {
    min-width: 120px !important;
    height: 20px !important;
    line-height: 20px !important;

    .mat-mdc-select-trigger {
      height: 20px;
    }

    .mat-mdc-select-value {
      font-size: 12px;
    }
  }
}
