:host {
  display: block;
  padding: 24px;

  .a4 {
    max-width: 210mm;
  }

  .report-title {
    display: flex;
    align-items: center;
    gap: 12px;

    mat-icon {
      width: 24px;
      height: 24px;
      font-size: 24px;
    }

    .title {
      font-size: 24px;
      font-weight: 600;
    }
  }

  .report-info {
    display: grid;
    width: 850px;
    gap: 12px;
    margin: 16px 0;

    .item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    label {
      font-size: 14px;
      font-weight: 600;
    }
  }

  .widgets {
    display: grid;
    grid-template-columns: repeat(12, minmax(60px, 1fr));
    gap: 20px;

    .widget {
      padding: 16px;
      background-color: #fff;
      position: relative;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
    }
  }
  .column-2 {
    grid-column: span 2;
  }

  .column-3 {
    grid-column: span 3;
  }

  .column-4 {
    grid-column: span 4;
  }

  .column-5 {
    grid-column: span 5;
  }

  .column-6 {
    grid-column: span 6;
  }

  .column-8 {
    grid-column: span 8;
  }
  .column-12 {
    grid-column: span 12;
  }

  .widget-box {
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    position: relative;
    border: 1px solid #e6e6e6;
    border-radius: 4px;

    .title {
      padding: 16px;
      padding-bottom: 0px;
    }
  }

  .no-border {
    border: none !important;
  }
}

@media (max-width: 680px) {
  .widgets {
    display: flex;
    flex-direction: column;
  }
}
