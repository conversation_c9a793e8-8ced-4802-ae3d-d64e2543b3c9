import { Component, Input, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import { AppGlobalService } from '../app-global.service';
import { isEmpty, isNil, keys } from 'lodash';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { EPeriod, IScanIncidentsBySeverity } from '../mock_data_structure';
import { TranslateService } from '@/common/translate/translate.service';

@Component({
  selector: 'app-incidents-by-severity',
  templateUrl: './incidents-by-severity.component.html',
  styleUrls: ['../base.scss', './incidents-by-severity.component.css'],
})
export class IncidentsBySeverityComponent implements OnInit {
  @Input() data: IScanIncidentsBySeverity;
  period: EPeriod;
  hasData = true;

  Highcharts: typeof Highcharts = Highcharts;
  chartOptions = { ...this.globalService.defaultLineChartCfg };
  colors = {
    critical: '#ee3333',
    high: '#ff8200',
    medium: '#ffba00',
    low: '#3fae29',
  };

  displayTypeOptions = Object.keys(this.colors).map((x) => ({
    label: `dashboard.${x}`,
    value: x,
    color: this.colors[x],
    nodata: false,
  }));

  severitys = {
    critical: true,
    high: false,
    medium: false,
    low: false,
  };

  constructor(
    public globalService: AppGlobalService,
    private translateService: TranslateService
  ) {
    keys(this.severitys).forEach((k) => {
      this.severitys[k] = true;
    });
  }

  onOptionChanged(_selectedOption: MatCheckboxChange, value: any) {
    if (isNil(value)) {
      return;
    }

    this.severitys[value] = !this.severitys[value];
    this.refreshChart();
  }

  refreshChart = () => {
    const that = this;
    const seriesData = [];
    for (let key in this.severitys) {
      const dataArray = [];

      let item = this.data[key];
      if (isNil(item)) {
        continue;
      }

      item.forEach((i) => {
        dataArray.push([i?.time, i?.count]);
      });

      let series = {
        name: key,
        color: this.colors[key],
        data: dataArray,
        visible: this.severitys[key] ? true : false,
      };

      const tmp = this.displayTypeOptions.find((v) => v.value === key);
      if (tmp) {
        tmp.nodata = isEmpty(item);
      }

      seriesData.push(series);
    }
    const intervals = seriesData[0]?.data.map((v) => v[0]) || [];

    this.chartOptions = {
      ...this.globalService.defaultLineChartCfg,
      noData: {
        style: {
          fontWeight: 'bold',
          fontSize: '24px',
        },
      },
      lang: {
        noData: this.translateService.lookup('no_data'),
      },
      chart: {
        type: 'spline',
      },
      yAxis: {
        allowDecimals: false,
        title: {
          text: 'Incidents',
        },
        labels: {
          formatter: function () {
            return this.value.toLocaleString();
          },
        },
      },
      xAxis: {
        ordinal: false,
        tickPositioner: function () {
          return intervals;
        },
        labels: {
          formatter: function () {
            const timestr = that.globalService.transformXAxisValueToTime(
              Number(this.value)
            );
            return that.globalService.getXAxisLabel(
              that.globalService.transformToDateTime(timestr),
              that.period
            );
          },
          rotation: intervals.length > 14 ? -45 : 0,
          overflow: 'justify',
          style: {
            whiteSpace: 'nowrap',
            textOverflow: 'clip',
          },
        },
      },
      tooltip: {
        enabled: true,
        headerFormat: '',
        formatter: function () {
          const timestr = that.globalService.transformXAxisValueToTime(
            Number(this.x)
          );
          return `${this.series.name.replace(/( |^)[a-z]/g, (l) =>
            l.toUpperCase()
          )}
          : <b>${this.y.toLocaleString()}</b><br>${that.globalService.formatDate(
            that.globalService.transformToDateTime(timestr)
          )}`;
        },
      },
      plotOptions: {
        spline: {
          lineWidth: 1,
          marker: {
            enabled: false,
          },
        },
      },
      series: seriesData,
    };
  };

  ngOnInit(): void {
    this.period = this.data.period;
    this.refreshChart();
    keys(this.severitys).forEach((k) => {
      const tmp = this.displayTypeOptions.find((v) => v.value === k);
      if (!tmp.nodata) {
        this.hasData = true;
      } else {
        this.hasData = false;
      }
      this.severitys[k] = !tmp.nodata;
    });
  }
}
