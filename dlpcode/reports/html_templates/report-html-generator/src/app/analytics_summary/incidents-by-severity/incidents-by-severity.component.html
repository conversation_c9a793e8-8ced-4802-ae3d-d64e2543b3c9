<div class="title">
  {{ "sensitive_data.scan_incidents_by_severity" | translate }}
</div>

<div class="body">
  <div class="content">
    <div class="chart-box">
      <highcharts-chart
        *ngIf="hasData"
        [Highcharts]="Highcharts"
        [options]="chartOptions"
        style="width: 100%"
      ></highcharts-chart>
      <div class="no-data" *ngIf="!hasData">No Data</div>
    </div>

    <div class="footer">
      <div
        *ngFor="let opt of displayTypeOptions; let i = index"
        class="small-group"
      >
        <mat-checkbox
          (change)="onOptionChanged($event, opt.value)"
          [disabled]="globalService.isPrinting || opt.nodata"
          [ngModel]="severitys[opt.value]"
        >
        </mat-checkbox>
        <i
          class="circle-icon"
          [ngStyle]="{ 'background-color': opt.color }"
        ></i>
        <span [ngClass]="{ 'no-data-span': opt.nodata }">{{
          opt.label | translate
        }}</span>
      </div>
    </div>
  </div>
</div>
