import { EPeriod } from '@/analytics_summary/mock_data_structure';
import { Injectable } from '@angular/core';
import { isNil } from 'lodash';

@Injectable({
  providedIn: 'root',
})
export class AppGlobalService {
  isPrinting: boolean = false;
  timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  defaultPieChartCfg: Highcharts.Options = {
    credits: {
      enabled: false,
    },
    chart: {
      plotShadow: false,
      backgroundColor: 'transparent',
      spacing: [0, 0, 0, 0],
    },
    plotOptions: {
      pie: {
        shadow: false,
        center: ['50%', '50%'],
        size: '120%',
        innerSize: '80%',
        dataLabels: {
          enabled: false,
        },
      },
    },
    accessibility: {
      enabled: false,
    },
    title: undefined,
  };

  defaultLineChartCfg: Highcharts.Options = {
    credits: {
      enabled: false,
    },
    chart: {
      plotShadow: false,
      backgroundColor: 'transparent',
    },
    yAxis: {
      title: {
        text: undefined,
      },
    },
    xAxis: {
      title: {
        text: undefined,
      },
    },
    legend: {
      enabled: false,
    },
    plotOptions: {},
    accessibility: {
      enabled: false,
    },
    title: undefined,
    subtitle: undefined,
  };

  constructor() {}

  setTimezone = (tz: string) => {
    if (isNil(tz)) {
      return;
    }
    try {
      Intl.DateTimeFormat(undefined, { timeZone: tz });
      this.timezone = tz;
    } catch (e) {
      console.error('Failed to get timezone');
    }
  };

  transformXAxisValueToTime(dateTime: number): string {
    const date = new Date(dateTime * 1000);
    const dateStr = `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date
      .getDate()
      .toString()
      .padStart(2, '0')}`;
    return `${dateStr} ${date.getHours().toString().padStart(2, '0')}`;
  }

  transformToDateTime(value: string): Date {
    const [datePart, hourPart] = value.split(' ');
    const [monthStr, dayStr] = datePart.split('/');

    const now = new Date();
    const year = now.getFullYear();
    const month = parseInt(monthStr, 10) - 1;
    const day = parseInt(dayStr, 10);
    const hour = parseInt(hourPart, 10);

    return new Date(year, month, day, hour);
  }
  getXAxisLabel(date: Date, periodType: EPeriod): string {
    switch (periodType) {
      case EPeriod.Day14:
      case EPeriod.Day7:
        return new Intl.DateTimeFormat('en-US', {
          month: 'short',
          day: 'numeric',
        }).format(date);
      default:
        return new Intl.DateTimeFormat('en-US', {
          hour: 'numeric',
          hour12: true,
        }).format(date);
    }
  }

  formatDate(date: Date): string {
    const month = new Intl.DateTimeFormat('en-US', { month: 'short' }).format(
      date
    );
    const day = date.getDate();
    const hour = date.getHours();

    const hour12 = hour % 12 === 0 ? 12 : hour % 12;
    const ampm = hour >= 12 ? 'PM' : 'AM';

    return `${month} ${day}, ${hour12} ${ampm}`;
  }
}
