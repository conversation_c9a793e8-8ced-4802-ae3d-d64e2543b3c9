import { Component, Input, OnInit } from '@angular/core';
import { AppGlobalService } from '../app-global.service';
import { TranslateService } from '@/common/translate/translate.service';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { EPeriod, IScanIncidentsTopn } from '../mock_data_structure';
import { defaultTo, isNil, reverse, sortBy } from 'lodash';
import { IncidentsByPolicyDialogComponent } from './incidents-by-policy-dialog/incidents-by-policy-dialog.component';
import * as Highcharts from 'highcharts';

interface IsummaryData {
  discover_policy: string;
  name: string;
  count: number;
  percentage: number;
  color?: string;
  trendData: { time: number; count: number }[];
  trendOptions: any;
}

@Component({
  selector: 'app-incidents-by-policy',
  templateUrl: './incidents-by-policy.component.html',
  styleUrls: ['../base.scss', './incidents-by-policy.component.css'],
})
export class IncidentsByPolicyComponent implements OnInit {
  @Input() data: IScanIncidentsTopn;
  Highcharts: typeof Highcharts = Highcharts;
  chartOptions = { ...this.globalService.defaultPieChartCfg };
  pieChartNoData = false;

  colors = ['#759BEC', '#7AB2FC', '#5B9DF3', '#79DAF4', '#51C7F1'];
  other_color = '#E3E6E9';
  period: EPeriod;

  discovery_policy_count: number = 0;
  total_count: number = 0;
  total_percentage: number = 0;
  top5_length: number = 0;
  top5_count: number = 0;
  top5_percentage: number = 0;

  summary_data: IsummaryData[] = [];
  selected_index: number = -1;

  show_more: boolean = true;

  constructor(
    public globalService: AppGlobalService,
    private dialog: MatDialog,
    private translateService: TranslateService
  ) {
    Highcharts.setOptions({
      noData: {
        style: {
          fontWeight: 'bold',
          fontSize: '24px',
        },
      },
      navigation: {
        buttonOptions: {
          enabled: false,
        },
      },
      lang: {
        noData: translateService.lookup('no_data'),
      },
      chart: {
        style: {
          fontFamily: 'Inter, sans-serif',
        },
      },
    });
  }

  ngOnInit(): void {
    this.discovery_policy_count = 0;
    this.total_count = 0;
    this.total_percentage = 0;
    this.top5_length = 0;
    this.top5_count = 0;
    this.top5_percentage = 0;
    this.selected_index = -1;
    this.summary_data = [];

    this.period = this.data.period;
    const data = reverse(sortBy(this.data.topn, 'count'));

    this.discovery_policy_count = defaultTo(
      this.data.total_discovery_policy_count,
      0
    );
    this.total_count = defaultTo(this.data.total_count.count, 0);
    this.total_percentage = defaultTo(this.data.total_count.percentage, 0);
    const seriesData = [];

    for (let i = 0; i < 5 && i < data.length; i++) {
      this.top5_count += data[i].count;
      this.top5_percentage += data[i].percentage;
    }

    data.forEach((item, index) => {
      this.summary_data.push({
        discover_policy: defaultTo(item.discovery_policy, ''),
        name: defaultTo(item.discovery_policy, ''),
        count: defaultTo(item.count, 0),
        percentage: defaultTo(item.percentage, 0),
        trendData: item.chartData,
        trendOptions: this.getChartOption(item.chartData),
      });

      if (index >= 5) {
        return;
      }
      seriesData.push({
        name: defaultTo(item.discovery_policy, ''),
        y: defaultTo(item.count, 0),
      });
    });

    this.summary_data.sort((a, b) => b.count - a.count);

    this.summary_data.forEach((s, i) => {
      s.color = this.colors[i];
    });

    seriesData.forEach((s, i) => {
      s.color = this.colors[i];
    });
    if (!isNil(this.data.top5_others)) {
      this.summary_data.push({
        discover_policy: 'Others',
        name: 'All Others',
        count: this.data.top5_others.count || 0,
        percentage: this.data.top5_others.percentage || 0,
        color: this.other_color,
        trendData: this.data.top5_others.chartData,
        trendOptions: this.getChartOption(this.data.top5_others.chartData),
      });

      seriesData.push({
        name: 'All Others',
        y: this.data.top5_others.count || 0,
        color: this.other_color,
      });
    }

    this.selected_index = 0;

    this.top5_length =
      this.summary_data.length - (isNil(this.data.top5_others) ? 0 : 1);
    let top5_count = 0;
    seriesData.forEach((s) => {
      top5_count += s.y;
    });

    if (top5_count) {
      this.pieChartNoData = false;
      this.chartOptions = {
        ...this.globalService.defaultPieChartCfg,
        colors: ['#53ACFF', '#4481DD', '#1D63B0', '#E3E6E9'],
        chart: {
          animation: this.globalService.isPrinting ? false : true,
          backgroundColor: 'transparent',
        },
        subtitle: {
          useHTML: true,
          verticalAlign: 'middle',
          align: 'center',
          style: {
            textAlign: 'center',
            color: '#222222',
          },
          text:
            '<div style="width: 180px; display: flex; flex-direction: column; align-items: center; justify-content: center;">' +
            '<div style="font-size: 20px; font-weight: 600; text-align: center;">' +
            this.top5_count.toLocaleString() +
            '</div>' +
            '<div style="text-align: center; padding-bottom: 4px;">' +
            this.translateService.lookup('sensitive_data.incidents') +
            '</div>' +
            '<div style="font-size: 10px;">' +
            this.top5_percentage.toFixed(2) +
            '% ' +
            this.translateService.lookup('sensitive_data.of_all_incidents') +
            '</div>' +
            '</div>',
        },
        tooltip: {
          enabled: true,
          headerFormat: '',
          useHTML: true,
          formatter: function () {
            return `<b>${
              this.name
            }: </b><span>${this.y.toLocaleString()}</span>`;
          },
        },
        series: [
          {
            type: 'pie',
            name: 'label',
            data: seriesData,
          },
        ],
      };
    } else {
      this.pieChartNoData = true;
    }
  }

  showMoreTitle = `${this.translateService.lookup(
    'sensitive_data.scan_incidents'
  )}: ${this.translateService.lookup(
    'sensitive_data.top10_discovery_policies_trends'
  )}`;
  show_all() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.restoreFocus = false;

    dialogConfig.data = {
      data: this.data,
      title: this.showMoreTitle,
    };
    dialogConfig.width = '40vw';
    dialogConfig.height = '550px';

    const dialogRef = this.dialog.open(
      IncidentsByPolicyDialogComponent,
      dialogConfig
    );
  }

  selectRow(_evt, index: number) {
    if (this.selected_index === index) {
      return;
    }

    this.selected_index = index;
  }

  getChartOption(policy_trend: any) {
    const that = this;
    const seriesData = [];
    const trend = [];

    policy_trend.forEach((t, i) => {
      trend.push({
        x: t?.time,
        y: t?.count,
      });
    });

    seriesData.push({
      data: trend,
      type: 'spline',
      name: 'Count',
    });

    const tmp = seriesData[0]?.data.map((v) => v.x) || [];
    const intervals = [];
    if (tmp.length > 8 && this.globalService.isPrinting) {
      const step = Math.floor(tmp.length / 8);
      tmp.forEach((t, index) => {
        if (index % step === 0) {
          intervals.push(t);
        }
      });
    } else {
      intervals.push(...tmp);
    }

    const trendChartOptions = {
      ...this.globalService.defaultLineChartCfg,
      chart: this.globalService.isPrinting
        ? {
            type: 'spline',
            width: 360,
            height: 150,
          }
        : { type: 'spline' },
      yAxis: {
        allowDecimals: false,
        title: {
          text: this.translateService.lookup('sensitive_data.scan_incidents'),
        },
        labels: {
          formatter: function () {
            return this.value.toLocaleString();
          },
        },
      },
      xAxis: {
        ordinal: false,
        tickPositioner: function () {
          return intervals;
        },
        labels: {
          formatter: function () {
            const timestr = that.globalService.transformXAxisValueToTime(
              Number(this.value)
            );
            return that.globalService.getXAxisLabel(
              that.globalService.transformToDateTime(timestr),
              that.period
            );
          },
          rotation: this.globalService.isPrinting
            ? -45
            : intervals.length > 14
            ? -45
            : 0,
          overflow: 'justify',
          style: {
            whiteSpace: 'nowrap',
            textOverflow: 'clip',
          },
        },
      },
      tooltip: {
        enabled: true,
        headerFormat: '',
        formatter: function () {
          const timestr = that.globalService.transformXAxisValueToTime(
            Number(this.x)
          );
          return `${this.series.name.replace(/( |^)[a-z]/g, (l) =>
            l.toUpperCase()
          )}
          : <b>${this.y.toLocaleString()}</b><br>${that.globalService.formatDate(
            that.globalService.transformToDateTime(timestr)
          )}`;
        },
      },
      plotOptions: {
        spline: {
          lineWidth: 1,
          marker: {
            enabled: false,
          },
        },
      },
      series: seriesData,
    };
    return trendChartOptions;
  }

  renderSvg(rawData) {
    //console.log('render svg:', rawData)

    let td = document.querySelector(
      `.info-table tr[index="${rawData.index}"] .svg-content`
    );
    if (!td) return;

    const tempDiv = document.createElement('div');
    // remove original widht and height attibute and reset them
    tempDiv.innerHTML = rawData.svg
      .replace(/(<svg[^>]*?)\swidth\s*=\s*["'][^"']*["']/i, '$1')
      .replace(/(<svg[^>]*?)\sheight\s*=\s*["'][^"']*["']/i, '$1')
      .replace(/<svg/, '<svg width="100%" height="100%" ');

    // dynamic add the svg element
    const svgElement = tempDiv.querySelector('svg');
    if (!svgElement) return;

    // add click event to svg element
    svgElement.style.pointerEvents = 'bounding-box';
    svgElement.addEventListener('click', (event) => {
      this.selectRow(event, rawData.index);
    });

    td.innerHTML = '';
    td.appendChild(svgElement);
  }

  getTop5Summary = () => {
    if (this.summary_data.length <= 5) {
      return this.summary_data;
    }
    return [
      ...this.summary_data.slice(0, 5),
      ...this.summary_data.slice(
        this.summary_data.length - 1,
        this.summary_data.length
      ),
    ];
  };
}
