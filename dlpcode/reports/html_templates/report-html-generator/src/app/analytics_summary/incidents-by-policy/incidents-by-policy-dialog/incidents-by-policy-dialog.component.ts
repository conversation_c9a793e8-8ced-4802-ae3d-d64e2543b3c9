import { IScanIncidentsTopn } from '@/analytics_summary/mock_data_structure';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-incidents-by-policy-dialog',
  templateUrl: './incidents-by-policy-dialog.component.html',
  styleUrls: ['./incidents-by-policy-dialog.component.css'],
})
export class IncidentsByPolicyDialogComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { data: IScanIncidentsTopn; title: string }
  ) {}
}
