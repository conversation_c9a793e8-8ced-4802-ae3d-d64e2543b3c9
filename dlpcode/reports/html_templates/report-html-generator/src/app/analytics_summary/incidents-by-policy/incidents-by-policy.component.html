<div [class]="{
    'common-layout': true,
    'printing-layout': globalService.isPrinting
  }">
  <div class="policy-summary" style="page-break-inside: avoid; break-inside: avoid;"
    [ngStyle]="globalService.isPrinting ? {} : {'width': '100%'}">
    <div class="title">
      {{ "sensitive_data.scan_incidents" | translate }}:
      {{ "sensitive_data.top5_discovery_policies" | translate }}
    </div>
    <div class="body">
      <div class="content-container">
        <div class="content">
          <div class="chart-box">
            <highcharts-chart *ngIf="!pieChartNoData" [Highcharts]="Highcharts"
              [options]="chartOptions"></highcharts-chart>
            <div class="no-data" *ngIf="pieChartNoData">No Data</div>
          </div>
          <div class="bold info">
            {{ "sensitive_data.all_scan_incidents" | translate }}:
            {{ total_count.toLocaleString() }}
          </div>
          <div class="info">
            {{ "sensitive_data.discovery_policies" | translate }}:
            {{ top5_length }} of {{ discovery_policy_count }}
          </div>
        </div>

        <div class="table-container">
          <table class="info-table">
            <tr>
              <td class="pl-10">
                {{ "sensitive_data.policy_name" | translate }}
              </td>
              <td colspan="2" class="align-right">
                {{ "sensitive_data.incidents" | translate }}
              </td>
              <td class="align-center">
                {{ "sensitive_data.trend" | translate }}
              </td>
            </tr>

            <tr *ngFor="let summary of getTop5Summary(); let i = index" class="pointer" [ngClass]="{
                selected: !globalService.isPrinting && selected_index == i
              }" [attr.index]="i" (click)="selectRow($event, i)">
              <td class="pl-10 pointer">
                <i class="circle" [ngStyle]="{ 'background-color': summary.color }"></i>
                {{ summary.name }}
              </td>

              <td class="align-right">
                {{ summary.count }}
              </td>

              <td class="align-right">
                {{ summary.percentage.toFixed(2) }}%
              </td>
              <td class="svg-cell">
                <app-chart-to-svg [data]="summary.trendData" [width]="80" [height]="20">
                </app-chart-to-svg>
              </td>
            </tr>

            <tr *ngIf="show_more && !globalService.isPrinting">
              <td colspan="4" class="align-right">
                <span class="link" (click)="show_all()">{{ "show_more" | translate }} ></span>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="show-more-table" *ngIf="globalService.isPrinting && data.topn.length > 5">
    <div class="title">
      {{ showMoreTitle }}
    </div>
    <app-incidents-by-policy-show-more [data]="data"></app-incidents-by-policy-show-more>
  </div>

  <div class="policy-trend content" *ngIf="!globalService.isPrinting">
    <div class="title">
      {{ summary_data[selected_index].name }}
    </div>
    <highcharts-chart [Highcharts]="Highcharts" [options]="summary_data[selected_index].trendOptions"
      class="trend-chart"></highcharts-chart>
  </div>
  <div *ngIf="globalService.isPrinting" class="title">Trends by Discovery Policy</div>
  <div class="policy-trend-print" *ngIf="globalService.isPrinting">
    <div *ngFor="let item of summary_data">
      <div class="trend-print" *ngIf="item.trendOptions !== undefined">
        <div class="title">
          {{ item.name }}
        </div>
        <highcharts-chart [Highcharts]="Highcharts" [options]="item.trendOptions"
          class="trend-chart"></highcharts-chart>
      </div>
    </div>
  </div>
</div>