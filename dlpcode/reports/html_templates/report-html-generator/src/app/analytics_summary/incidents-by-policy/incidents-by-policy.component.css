:host {
  highcharts-chart {
    display: inline-block;
    height: 200px;
    width: 200px;
  }
  .common-layout {
    display: flex;
    flex-direction: row;
    gap: 0px;
    width: 100%;
    height: 100%;
    padding: 0px;
  }

  .printing-layout {
    display: flex;
    flex-direction: column;
  }

  .content {
    padding: 16px;
  }

  .no-data {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    color: gray;
    justify-content: center;
    font-size: 20px;
    font-weight: 700;
  }

  .body {
    width: 100%;
  }

  .info {
    width: 100%;
    text-align: center;
    white-space: nowrap;
  }

  .table-container {
    flex: 1;

    .display-none {
      display: none;
    }

    .info-table {
      border-spacing: 0px;
      tr {
        height: 30px;
      }

      .pl-10 {
        padding-left: 10px;
      }

      .selected {
        background-color: #ffffff;
      }

      tr.selected td:first-child {
        border-left: 2px solid #2563bf;
      }

      tr:not(:last-child) td {
        border-bottom: 1px solid #cccccc;
      }

      .svg-cell {
        width: 40%;

        .svg-content {
          display: flex;
          align-items: center;
          justify-content: center;

          width: 100%;
          height: 30px;
          object-fit: contain;

          svg {
            max-width: 100%;
            height: 100%;
            width: auto;
            display: block;
            pointer-events: none;
          }

          svg * {
            pointer-events: none;
          }
        }
      }
    }
  }

  .show-more-table {
    display: flex;
    flex-direction: column;
    .title {
      margin: 16px 0 0 16px;
    }
  }

  .trend-chart {
    width: 100%;
    flex: 1;
  }
}

.content-container {
  display: flex;
  gap: 10px;
  width: 100%;

  .content {
    width: 35%;
  }
}

.policy-summary {
  background-color: #f5f6fc;
  padding-top: 4px;
}

.policy-trend {
  height: 350px;
}
.policy-trend-print {
  display: grid;
  grid-template-columns: 1fr 1fr;
  highcharts-chart {
    display: block;
    width: 100%;
  }

  .trend-print {
    break-inside: avoid;
    display: block;
    width: 380px;
    height: 200px;
  }
  padding: 16px 0;
}
