<div class="tip-row">
    <span>*</span>
    <mat-icon svgIcon="sharepoint_cloud"></mat-icon>
    <span>{{ 'scans.storage_type.cloud' | translate }}</span>
    <span>,</span>
    <mat-icon svgIcon="gdrive"></mat-icon>
    <span>{{ 'scans.storage_type.google_drive'| translate }}</span>
    <span>,</span>
    <mat-icon svgIcon="aws"></mat-icon>
    <span>{{ 'scans.storage_type.aws' | translate }}</span>
    <span>&</span>
    <mat-icon svgIcon="smb"></mat-icon>
    <span>{{ 'scans.storage_type.smb' | translate }}</span>
</div>

<div class="main-table">
    <table class="info-table">
        <tr class="bold">
            <td class="percent-cell-20">
                {{ 'sensitive_data.detail_file_name' | translate }}
            </td>
            <td class="percent-cell-15">
                {{ 'sensitive_data.owner' | translate }}
            </td>
            <td class="percent-cell-15">
                {{ 'sensitive_data.storage' | translate }}
            </td>
            <td class="align-right">
                {{ 'sensitive_data.last_accessed' | translate }}
            </td>
            <td class="align-right">
                {{ 'sensitive_data.days_idle' | translate }}
            </td>
            <td class="percent-cell-25">
            </td>
        </tr>

        <tr *ngFor="let data of baseData">
            <td class="percent-cell-20">
                <span [title]="data.file_name">
                    {{truncateLabel(data.file_name) }}
                </span>
            </td>
            <td class="percent-cell-15">
                <span [title]="data.owner.join(',\r\n')">
                    {{ data.owner.length ?truncateLabel(data.owner[0]) : '' }}
                </span>
                <span *ngIf="data.owner.length > 1">
                    +{{ data.owner.length-1 }}
                </span>
            </td>
            <td class="percent-cell-15">
                <div class="flex-cell">
                    <mat-icon [svgIcon]="data.icon"></mat-icon>
                    <span [title]="data.name">
                        {{truncateLabel(data.name) }}
                    </span>
                </div>
            </td>
            <td class="align-right">
                {{ data.last_access_time }}
            </td>
            <td class="align-right">
                {{ data.days_idle.toLocaleString() }}
            </td>
            <td class="percent-cell-25">
                <div class="percent-bar" [ngStyle]="{'width': data.real_percent + '%'}"></div>
            </td>
        </tr>

    </table>
</div>