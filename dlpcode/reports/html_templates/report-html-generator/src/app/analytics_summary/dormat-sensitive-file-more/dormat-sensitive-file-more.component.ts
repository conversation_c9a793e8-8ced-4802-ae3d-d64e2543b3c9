import { Component, Input, OnInit } from '@angular/core';
import { IDormantSensitiveFile } from '../mock_data_structure';
import { FormatService } from '@/service/format.service';
import { AppGlobalService } from '../app-global.service';

@Component({
  selector: 'app-dormat-sensitive-file-more',
  templateUrl: './dormat-sensitive-file-more.component.html',
  styleUrls: ['./dormat-sensitive-file-more.component.css'],
})
export class DormatSensitiveFileMoreComponent implements OnInit {
  @Input() data: IDormantSensitiveFile[] = [];
  baseData: any[] = [];

  constructor(
    public formatService: FormatService,
    public globalService: AppGlobalService
  ) {}

  ngOnInit(): void {
    let maxDays = 0;

    this.baseData = this.data.map((item) => {
      maxDays = Math.max(maxDays, item.idle_days);
      return {
        file_name: item.file_name,
        owner: item.owner,
        name: item.storage.name,
        days_idle: item.idle_days,
        last_access_time: item.last_accessed,
        icon: this.formatService.getStorageType(item.storage.type).icon,
        type: item.storage.type,
      };
    });
    this.baseData.forEach((item) => {
      item.real_percent = maxDays ? (item.days_idle / maxDays) * 100 : 0;
    });
  }

  truncateLabel(label: string) {
    return this.formatService.truncateLabel(
      label,
      this.globalService.isPrinting ? 25 : 15
    );
  }
}
