import { TranslateService } from '@/common/translate/translate.service';
import { Component, Input, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import { AppGlobalService } from '../app-global.service';
import { EPeriod, IComplianceFile } from '../mock_data_structure';
import { isEmpty } from 'lodash';

@Component({
  selector: 'app-files-by-type',
  templateUrl: './files-by-type.component.html',
  styleUrls: ['./files-by-type.component.css', '../base.scss'],
})
export class FilesByTypeComponent implements OnInit {
  Highcharts: typeof Highcharts = Highcharts;
  chartOptions = { ...this.globalService.defaultLineChartCfg };
  period: EPeriod;
  @Input() data: IComplianceFile;
  compliances: string[] = [];
  hasData = true;

  colors = ['#865DFF', '#2FD4F9', '#3289FD', '#8FE44E', '#FFEC79'];
  constructor(
    public globalService: AppGlobalService,
    private translateService: TranslateService
  ) {
    Highcharts.setOptions({
      chart: {
        type: 'areaspline',
        style: {
          fontFamily: 'Inter, sans-serif',
        },
      },
    });
  }
  ngOnInit(): void {
    const that = this;
    const seriesData = [];
    this.period = this.data.period;
    this.compliances = this.data.data.map((v) => v.type);
    this.hasData = !isEmpty(this.compliances);

    this.data.data.forEach((item) => {
      const array = item.chartData.map((e) => {
        return {
          x: e.time,
          y: e.count,
        };
      });

      let series = {
        name: item.type,
        data: array,
        fillOpacity: 0.3,
        type: 'areaspline',
        linecap: 'round',
        lineWidth: 1,
        marker: {
          enabled: false,
        },
        color: this.colors[seriesData.length],
      };

      seriesData.push(series);
    });

    const intervals = seriesData[0]?.data.map((v) => v.x) || [];

    this.chartOptions = {
      ...this.globalService.defaultLineChartCfg,
      noData: {
        style: {
          fontWeight: 'bold',
          fontSize: '24px',
        },
      },
      lang: {
        noData: this.translateService.lookup('no_data'),
      },
      chart: {
        type: 'areaspline',
      },
      yAxis: {
        allowDecimals: false,
        title: {
          text: this.translateService.lookup('sensitive_data.files'),
        },
        labels: {
          formatter: function () {
            return this.value.toLocaleString();
          },
        },
      },
      xAxis: {
        ordinal: false,
        tickPositioner: function () {
          return intervals;
        },
        labels: {
          formatter: function () {
            const timestr = that.globalService.transformXAxisValueToTime(
              Number(this.value)
            );
            return that.globalService.getXAxisLabel(
              that.globalService.transformToDateTime(timestr),
              that.period
            );
          },
          rotation: intervals.length > 14 ? -45 : 0,
          overflow: 'justify',
          style: {
            whiteSpace: 'nowrap',
            textOverflow: 'clip',
          },
        },
      },
      tooltip: {
        enabled: true,
        headerFormat: '',
        formatter: function () {
          const timestr = that.globalService.transformXAxisValueToTime(
            Number(this.x)
          );
          return `${this.series.name.replace(/( |^)[a-z]/g, (l) =>
            l.toUpperCase()
          )}
          : <b>${this.y.toLocaleString()}</b><br>${that.globalService.formatDate(
            that.globalService.transformToDateTime(timestr)
          )}`;
        },
      },
      plotOptions: {
        area: {
          lineWidth: 1,
          marker: {
            enabled: false,
          },
        },
      },

      series: seriesData,
    };
  }
}
