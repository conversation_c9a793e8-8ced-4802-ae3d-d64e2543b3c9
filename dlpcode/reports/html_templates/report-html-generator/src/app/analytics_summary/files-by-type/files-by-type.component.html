<div class="title">
  {{ "sensitive_data.top5_compliance_file_types" | translate }}
</div>

<div class="body">
  <div class="content">
    <div class="chart-box">
      <highcharts-chart
        *ngIf="hasData"
        [Highcharts]="Highcharts"
        [options]="chartOptions"
        style="width: 100%"
      ></highcharts-chart>
      <div *ngIf="!hasData" class="no-data">No Data</div>
    </div>

    <div class="footer">
      <div *ngFor="let com of compliances; let i = index" class="small-group">
        <i
          class="circle-icon"
          [ngStyle]="{ 'background-color': colors[i] }"
        ></i>
        <span>{{ com | translate }}</span>
      </div>
    </div>
  </div>
</div>
