import { FormatService } from '@/service/format.service';
import { Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ISharedSensitiveFileOwnerAll } from '../mock_data_structure';
import { TranslateService } from '@/common/translate/translate.service';
import { SharedSensitiveShowMoreDialogComponent } from './show-more-dialog/show-more-dialog.component';

@Component({
  selector: 'app-shared-sensitive-file-by-owner',
  templateUrl: './shared-sensitive-file-by-owner.component.html',
  styleUrls: ['./shared-sensitive-file-by-owner.component.css'],
})
export class SharedSensitiveFileByOwnerComponent {
  @Input() data: ISharedSensitiveFileOwnerAll;

  baseData: any[] = [];

  share_types: any[] = [
    {
      label: 'All Types',
      value: 'all',
    },
    {
      label: 'Public',
      value: 'public',
    },
    {
      label: 'External',
      value: 'external',
    },
    {
      label: 'Internal',
      value: 'internal',
    },
  ];

  typeControl: FormControl;

  constructor(
    private router: Router,
    private formatService: FormatService,
    private dialog: MatDialog,
    private translateService: TranslateService
  ) {}

  ngOnInit() {
    this.typeControl = new FormControl('all');
    this.typeControl.valueChanges.subscribe((value) => {
      this.initData(value);
    });
    this.initData('all');
  }

  initData(type: string) {
    let maxCount = 0;
    this.baseData = this.data[type].slice(0, 10).map((item) => {
      if (item.files > maxCount) {
        maxCount = item.files;
      }
      return {
        owner: item.user_name,
        name: item.storage.name,
        type: item.storage.type,
        files: item.files,
        real_percent: 0,
        icon: this.formatService.getStorageType(item.storage.type).icon,
      };
    });

    this.baseData.forEach((item) => {
      item.real_percent = maxCount ? (item.files / maxCount) * 100 : 0;
    });
  }

  getStorageLabel(type) {
    return this.formatService.getStorageType(type).icon;
  }

  truncateLabel(label: string) {
    return this.formatService.truncateLabel(label, 15);
  }

  show_all() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.restoreFocus = false;

    dialogConfig.data = {
      data: this.data,
      title: this.translateService.lookup(
        'sensitive_data.top100_shared_sensitive_file_owners'
      ),
    };
    dialogConfig.width = '40vw';
    dialogConfig.height = '650px';

    this.dialog.open(SharedSensitiveShowMoreDialogComponent, dialogConfig);
  }
}
