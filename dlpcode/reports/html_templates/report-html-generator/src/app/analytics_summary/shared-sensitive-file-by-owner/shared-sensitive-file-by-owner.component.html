<div class="title">
    <label style="width: 700px;">{{ 'sensitive_data.top10_shared_sensitive_file_owners' | translate }} </label>
    <mat-select [disableRipple]="true" [formControl]="typeControl">
        <mat-option *ngFor="let type of share_types" [value]="type.value">
            {{ type.label }}
        </mat-option>
    </mat-select>
</div>

<div class="body">
    <div class="content">
        <div class="content">
            <table class="info-table">
                <tr class="bold">
                    <td class="percent-cell-22">
                        {{ 'sensitive_data.username' | translate }}
                    </td>
                    <td class="percent-cell-22">
                        {{ 'sensitive_data.storage' | translate }}
                    </td>
                    <td class="align-right">
                        {{ 'sensitive_data.files' | translate }}
                    </td>
                    <td class="percent-cell-short">
                    </td>
                </tr>

                <tr *ngFor="let data of baseData">
                    <td class="percent-cell-22">
                        <span [title]="data.owner">
                            {{ truncateLabel(data.owner) }}
                        </span>
                    </td>
                    <td class="percent-cell-22">
                        <div class="flex-cell">
                            <mat-icon [svgIcon]="data.icon" [title]="getStorageLabel(data.type)"></mat-icon>
                            <span [title]="data.name">
                                {{ truncateLabel(data.name) }}
                            </span>
                        </div>
                    </td>
                    <td class="align-right">
                        <span>
                            {{ data.files.toLocaleString() }}
                        </span>
                    </td>
                    <td class="percent-cell-short">
                        <div class="percent-bar" [ngStyle]="{'width': data.real_percent + '%'}"></div>
                    </td>
                </tr>

                <tr class="last-row">
                    <td colspan="3">
                        <div class="flex-cell-space" style="display: flex; gap: 8px;">
                            <div class="flex-cell">
                                <span>*</span>
                                <mat-icon svgIcon="sharepoint_cloud"></mat-icon>
                                <span>{{ 'scans.storage_type.sharepoint' | translate }}</span>
                                <span>&</span>
                                <mat-icon svgIcon="gdrive"></mat-icon>
                                <span>{{ 'scans.storage_type.google_drive'| translate }}</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="link" (click)="show_all()">{{ 'show_more' | translate}} ></span>
                    </td>
                </tr>

            </table>
        </div>
    </div>
</div>