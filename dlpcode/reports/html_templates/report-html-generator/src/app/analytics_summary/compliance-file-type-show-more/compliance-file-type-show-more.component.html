<div class="main-table" [ngClass]="{ 'print-table': globalService.isPrinting }">
  <table class="info-table">
    <tr class="bold">
      <td>{{ "sensitive_data.file_types" | translate }}</td>
      <td class="align-right">{{ "sensitive_data.files" | translate }}</td>
      <td class="align-right">%</td>
      <td>
        of {{ total_scanned_files.toLocaleString() }}
        {{ "sensitive_data.scanned_files" | translate }}
      </td>
      <td class="percent-cell"></td>
    </tr>

    <tr *ngFor="let type of file_type_info" [ngClass]="{ bold: type.is_main }">
      <td [ngStyle]="{ 'padding-left': type.is_main ? '0px' : '20px' }">
        {{ type.type }}
      </td>
      <td class="align-right">
        {{ type.count.toLocaleString() }}
      </td>
      <td class="align-right" style="padding-left: 16px;">{{ type.percentage.toFixed(2) }}%</td>
      <td style="padding-left: 16px">
        {{ type.desc }}
      </td>
      <td class="percent-cell">
        <div
          class="percent-bar"
          [ngStyle]="{ width: type.percentage + '%' }"
        ></div>
      </td>
    </tr>

    <tr class="bold">
      <td>
        {{ "sensitive_data.total_sensitive_files" | translate }}
      </td>
      <td class="align-right">
        {{ data.total.count.toLocaleString() }}
      </td>
      <td class="align-right">{{ data.total.percentage.toLocaleString() }}%</td>
      <td></td>
      <td></td>
    </tr>
  </table>
</div>
