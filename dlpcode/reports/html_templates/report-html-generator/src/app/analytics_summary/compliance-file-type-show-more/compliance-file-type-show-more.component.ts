import { Component, Input, OnInit } from '@angular/core';
import { ISensitiveFile, ISensitiveFileData } from '../mock_data_structure';
import { AppGlobalService } from '../app-global.service';
import { flatten, reverse, sortBy } from 'lodash';

interface IFileType extends ISensitiveFileData {
  is_main: boolean;
}

@Component({
  selector: 'app-compliance-file-type-show-more',
  templateUrl: './compliance-file-type-show-more.component.html',
  styleUrls: ['../base.scss', './compliance-file-type-show-more.component.css'],
})
export class ComplianceFileTypeShowMoreComponent implements OnInit {
  @Input() data: ISensitiveFile;
  file_type_info: IFileType[] = [];
  total_scanned_files: number = 0;

  constructor(public globalService: AppGlobalService) {}

  ngOnInit(): void {
    this.total_scanned_files = this.data.total_scan_count;
    this.file_type_info = reverse(
      sortBy(flatten(this.data.data.map((v) => v.sub_types)), 'count')
    ).map((v) => ({ ...v, is_main: false }));
  }
}
