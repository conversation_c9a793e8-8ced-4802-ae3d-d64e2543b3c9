import { ISummaryData } from './mock_data_structure';

export const mockData: ISummaryData = {
  report_info: {
    name: 'Ethan_test_report',
    schedule: 'Weekly Monday, at 11:20 AM PDT',
    period: 'Last 7 days',
    created_by: '<PERSON>',
    generated_on: '2023-10-01 12:00 PM',
    notes: 'This is a test report for sensitive files.',
    scan_name: 'Test Scan',
    scan_storage: '10.255.255.176',
    timezone: 'America/Vancouver',
    firmware_version: 'FortiData-KVM 2.1.0 build0001 20250807',
  },
  scanincidentsbyseverity: {
    period: 2,
    critical: [
      {
        count: 27065,
        time: 1744070400,
      },
      {
        count: 105299,
        time: 1744156800,
      },
      {
        count: 99120,
        time: 1744243200,
      },
      {
        count: 92543,
        time: 1744329600,
      },
      {
        count: 88397,
        time: 1744416000,
      },
      {
        count: 89243,
        time: 1744502400,
      },
      {
        count: 80468,
        time: 1744588800,
      },
      {
        count: 57154,
        time: 1744675200,
      },
    ],
    high: [
      {
        count: 26612,
        time: 1744070400,
      },
      {
        count: 99567,
        time: 1744156800,
      },
      {
        count: 76701,
        time: 1744243200,
      },
      {
        count: 83305,
        time: 1744329600,
      },
      {
        count: 83415,
        time: 1744416000,
      },
      {
        count: 83787,
        time: 1744502400,
      },
      {
        count: 77124,
        time: 1744588800,
      },
      {
        count: 53480,
        time: 1744675200,
      },
    ],
    medium: [
      {
        count: 27065,
        time: 1744070400,
      },
      {
        count: 105299,
        time: 1744156800,
      },
      {
        count: 99120,
        time: 1744243200,
      },
      {
        count: 92543,
        time: 1744329600,
      },
      {
        count: 88397,
        time: 1744416000,
      },
      {
        count: 89243,
        time: 1744502400,
      },
      {
        count: 80468,
        time: 1744588800,
      },
      {
        count: 57154,
        time: 1744675200,
      },
    ],
    low: [
      {
        count: 26612,
        time: 1744070400,
      },
      {
        count: 99567,
        time: 1744156800,
      },
      {
        count: 76701,
        time: 1744243200,
      },
      {
        count: 83305,
        time: 1744329600,
      },
      {
        count: 83415,
        time: 1744416000,
      },
      {
        count: 83787,
        time: 1744502400,
      },
      {
        count: 77124,
        time: 1744588800,
      },
      {
        count: 53480,
        time: 1744675200,
      },
    ],
  },
  compliances: {
    period: 1,
    data: [
      {
        type: 'PCI-DSS',
        chartData: [
          {
            count: 26612,
            time: 1744070400,
          },
          {
            count: 9567,
            time: 1744156800,
          },
          {
            count: 6701,
            time: 1744243200,
          },
          {
            count: 8305,
            time: 1744329600,
          },
          {
            count: 8415,
            time: 1744416000,
          },
          {
            count: 8378,
            time: 1744502400,
          },
          {
            count: 7712,
            time: 1744588800,
          },
          {
            count: 5348,
            time: 1744675200,
          },
        ],
      },
      {
        type: 'HIPAA',
        chartData: [
          {
            count: 27065,
            time: 1744070400,
          },
          {
            count: 105299,
            time: 1744156800,
          },
          {
            count: 99120,
            time: 1744243200,
          },
          {
            count: 923,
            time: 1744329600,
          },
          {
            count: 8897,
            time: 1744416000,
          },
          {
            count: 8923,
            time: 1744502400,
          },
          {
            count: 8468,
            time: 1744588800,
          },
          {
            count: 7154,
            time: 1744675200,
          },
        ],
      },
      {
        type: 'CCPA',
        chartData: [
          {
            count: 275,
            time: 1744070400,
          },
          {
            count: 105299,
            time: 1744156800,
          },
          {
            count: 9920,
            time: 1744243200,
          },
          {
            count: 92543,
            time: 1744329600,
          },
          {
            count: 8897,
            time: 1744416000,
          },
          {
            count: 8943,
            time: 1744502400,
          },
          {
            count: 8068,
            time: 1744588800,
          },
          {
            count: 5754,
            time: 1744675200,
          },
        ],
      },
      {
        type: 'GLBA',
        chartData: [
          {
            count: 2706,
            time: 1744070400,
          },
          {
            count: 10529,
            time: 1744156800,
          },
          {
            count: 9912,
            time: 1744243200,
          },
          {
            count: 9254,
            time: 1744329600,
          },
          {
            count: 8897,
            time: 1744416000,
          },
          {
            count: 8243,
            time: 1744502400,
          },
          {
            count: 468,
            time: 1744588800,
          },
          {
            count: 5754,
            time: 1744675200,
          },
        ],
      },
      {
        type: 'GDPR',
        chartData: [
          {
            count: 10000,
            time: 1744070400,
          },
          {
            count: 20000,
            time: 1744156800,
          },
          {
            count: 30000,
            time: 1744243200,
          },
          {
            count: 40000,
            time: 1744329600,
          },
          {
            count: 50000,
            time: 1744416000,
          },
          {
            count: 60000,
            time: 1744502400,
          },
          {
            count: 70000,
            time: 1744588800,
          },
          {
            count: 80000,
            time: 1744675200,
          },
        ],
      },
    ],
  },
  scan_incident: {
    period: 1,
    total_discovery_policy_count: 11,
    topn: [
      {
        discovery_policy: 'policy1',
        count: 55003,
        percentage: 10,
        chartData: [
          { count: 40990, time: 1744070400 },
          { count: 80990, time: 1744156800 },
          { count: 70990, time: 1744243200 },
          { count: 40990, time: 1744329600 },
          { count: 50990, time: 1744416000 },
          { count: 60990, time: 1744502400 },
          { count: 70990, time: 1744588800 },
          { count: 80990, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy2',
        count: 55002,
        percentage: 10,
        chartData: [
          { count: 15000, time: 1744070400 },
          { count: 25000, time: 1744156800 },
          { count: 35000, time: 1744243200 },
          { count: 45000, time: 1744329600 },
          { count: 55000, time: 1744416000 },
          { count: 65000, time: 1744502400 },
          { count: 75000, time: 1744588800 },
          { count: 85000, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy3',
        count: 55001,
        percentage: 10,
        chartData: [
          { count: 104000, time: 1744070400 },
          { count: 204000, time: 1744156800 },
          { count: 304000, time: 1744243200 },
          { count: 404000, time: 1744329600 },
          { count: 504000, time: 1744416000 },
          { count: 604000, time: 1744502400 },
          { count: 704000, time: 1744588800 },
          { count: 804000, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy4',
        count: 55000,
        percentage: 10,
        chartData: [
          { count: 10000, time: 1744070400 },
          { count: 20000, time: 1744156800 },
          { count: 30000, time: 1744243200 },
          { count: 40000, time: 1744329600 },
          { count: 50000, time: 1744416000 },
          { count: 60000, time: 1744502400 },
          { count: 70000, time: 1744588800 },
          { count: 80000, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy5',
        count: 55000,
        percentage: 10,
        chartData: [
          { count: 10000, time: 1744070400 },
          { count: 20000, time: 1744156800 },
          { count: 30000, time: 1744243200 },
          { count: 40000, time: 1744329600 },
          { count: 50000, time: 1744416000 },
          { count: 60000, time: 1744502400 },
          { count: 70000, time: 1744588800 },
          { count: 80000, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy6',
        count: 55000,
        percentage: 10,
        chartData: [
          { count: 10000, time: 1744070400 },
          { count: 20000, time: 1744156800 },
          { count: 30000, time: 1744243200 },
          { count: 40000, time: 1744329600 },
          { count: 50000, time: 1744416000 },
          { count: 60000, time: 1744502400 },
          { count: 70000, time: 1744588800 },
          { count: 80000, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy7',
        count: 55000,
        percentage: 10,
        chartData: [
          { count: 10000, time: 1744070400 },
          { count: 20000, time: 1744156800 },
          { count: 30000, time: 1744243200 },
          { count: 40000, time: 1744329600 },
          { count: 50000, time: 1744416000 },
          { count: 60000, time: 1744502400 },
          { count: 70000, time: 1744588800 },
          { count: 80000, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy8',
        count: 55000,
        percentage: 10,
        chartData: [
          { count: 10000, time: 1744070400 },
          { count: 20000, time: 1744156800 },
          { count: 30000, time: 1744243200 },
          { count: 40000, time: 1744329600 },
          { count: 50000, time: 1744416000 },
          { count: 60000, time: 1744502400 },
          { count: 70000, time: 1744588800 },
          { count: 80000, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy9',
        count: 55000,
        percentage: 10,
        chartData: [
          { count: 10000, time: 1744070400 },
          { count: 20000, time: 1744156800 },
          { count: 30000, time: 1744243200 },
          { count: 40000, time: 1744329600 },
          { count: 50000, time: 1744416000 },
          { count: 60000, time: 1744502400 },
          { count: 70000, time: 1744588800 },
          { count: 80000, time: 1744675200 },
        ],
      },
      {
        discovery_policy: 'policy10',
        count: 55000,
        percentage: 10,
        chartData: [
          { count: 10000, time: 1744070400 },
          { count: 20000, time: 1744156800 },
          { count: 30000, time: 1744243200 },
          { count: 40000, time: 1744329600 },
          { count: 50000, time: 1744416000 },
          { count: 60000, time: 1744502400 },
          { count: 70000, time: 1744588800 },
          { count: 80000, time: 1744675200 },
        ],
      },
    ],
    top5_others: {
      discovery_policy: 'Others',
      count: 55000,
      percentage: 10,
      chartData: [
        { count: 10000, time: 1744070400 },
        { count: 20000, time: 1744156800 },
        { count: 30000, time: 1744243200 },
        { count: 40000, time: 1744329600 },
        { count: 50000, time: 1744416000 },
        { count: 60000, time: 1744502400 },
        { count: 70000, time: 1744588800 },
        { count: 80000, time: 1744675200 },
      ],
    },
    top10_others: {
      discovery_policy: 'Others',
      count: 55000,
      percentage: 10,
      chartData: [
        { count: 10000, time: 1744070400 },
        { count: 20000, time: 1744156800 },
        { count: 30000, time: 1744243200 },
        { count: 40000, time: 1744329600 },
        { count: 50000, time: 1744416000 },
        { count: 60000, time: 1744502400 },
        { count: 70000, time: 1744588800 },
        { count: 80000, time: 1744675200 },
      ],
    },
    total_count: { count: 300000, percentage: 100 },
  },
  sensitive_files: {
    data: [
      {
        type: 'Office Documents',
        count: 17965,
        percentage: 32,
        sub_types: [
          { type: '.DOCX', count: 10256, percentage: 18, desc: 'Word' },
          {
            type: '.PDF',
            count: 4447,
            percentage: 8,
            desc: 'Portable Document Format',
          },
          { type: '.XLSX', count: 1768, percentage: 3, desc: 'Excel' },
          { type: '.PPTX', count: 768, percentage: 1, desc: 'PowerPoint' },
          { type: '.DOC', count: 459, percentage: 1, desc: 'Word' },
          { type: '.XLS', count: 235, percentage: 0.4, desc: 'Excel' },
          { type: '.PPT', count: 32, percentage: 0.1, desc: 'PowerPoint' },
          { type: '.ODT', count: 0, percentage: 0, desc: 'Open Document Text' },
          {
            type: '.ODS',
            count: 0,
            percentage: 0,
            desc: 'Open Document Spreadsheet',
          },
          {
            type: '.ODP',
            count: 0,
            percentage: 0,
            desc: 'Open Document Presentation',
          },
        ],
      },
      {
        type: 'Text Files',
        count: 1377,
        percentage: 2,
        sub_types: [
          {
            type: '.CSV',
            count: 976,
            percentage: 2,
            desc: 'Comma Separated Values',
          },
          { type: '.TXT', count: 401, percentage: 1, desc: 'Plain Text' },
          { type: '.RTF', count: 0, percentage: 0, desc: 'Rich Text Format' },
          { type: '.MD', count: 0, percentage: 0, desc: 'Markdown Format' },
        ],
      },
      {
        type: 'Source Code',
        count: 156,
        percentage: 0.3,
        sub_types: [
          { type: '.PY', count: 42, percentage: 0.1, desc: 'Python' },
          { type: '.CPP', count: 35, percentage: 0.1, desc: 'C++' },
          { type: '.C', count: 26, percentage: 0.05, desc: 'C' },
          { type: '.JS', count: 25, percentage: 0.05, desc: 'JavaScript' },
          { type: '.JAVA', count: 23, percentage: 0.05, desc: 'Java' },
          { type: '.GO', count: 4, percentage: 0, desc: 'Golang' },
          { type: '.KT', count: 1, percentage: 0, desc: 'Kotlin' },
          { type: '.SH', count: 0, percentage: 0, desc: 'Shell Script' },
          { type: '.PHP', count: 0, percentage: 0, desc: 'PHP' },
        ],
      },
      {
        type: 'Images',
        count: 1123,
        percentage: 2,
        sub_types: [
          { type: '.PNG', count: 623, percentage: 1, desc: '' },
          { type: '.JPEG', count: 251, percentage: 0.4, desc: '' },
          { type: '.GIF', count: 102, percentage: 0.2, desc: '' },
          { type: '.JPG', count: 75, percentage: 0.1, desc: '' },
          { type: '.BMP', count: 23, percentage: 0, desc: '' },
          { type: '.WEBP', count: 17, percentage: 0, desc: '' },
          { type: '.TIF', count: 8, percentage: 0, desc: '' },
          { type: '.AVIF', count: 0, percentage: 0, desc: '' },
        ],
      },
    ],
    top5_total: {
      type: 'Top 5 Totals',
      count: 18838,
      percentage: 33,
    },
    total: {
      type: 'Total Sensitive Files',
      count: 20621,
      percentage: 37,
    },
    total_scan_count: 55834,
  },
  ai_categories: {
    data: [
      {
        category: 'Finance',
        count: 17102,
        percentage: 31,
        sub_categories: [
          { category: 'Transaction Record', count: 4560, percentage: 8 },
          { category: 'Financial Report', count: 5320, percentage: 10 },
          {
            category: 'Credit and Risk Management',
            count: 3219,
            percentage: 6,
          },
          { category: 'Strategy and Research', count: 46, percentage: 0.1 },
          { category: 'Contracts and Agreements', count: 82, percentage: 0.1 },
          { category: 'Other Financial', count: 3875, percentage: 7 },
        ],
      },
      {
        category: 'Healthcare',
        count: 14342,
        percentage: 26,
        sub_categories: [
          { category: 'Clinical Records', count: 4952, percentage: 9 },
          { category: 'Medication Management', count: 4126, percentage: 7 },
          { category: 'Insurance and Settlement', count: 2973, percentage: 5 },
          { category: 'Legal and Compliance', count: 237, percentage: 0.4 },
          { category: 'Research', count: 496, percentage: 0.9 },
          { category: 'Other Healthcare', count: 1558, percentage: 3 },
        ],
      },
      {
        category: 'Information Technology',
        count: 1283,
        percentage: 2,
        sub_categories: [
          { category: 'User Documentation', count: 45, percentage: 0.1 },
          {
            category: 'Development Documentation',
            count: 107,
            percentage: 0.2,
          },
          { category: 'Test Documentation', count: 34, percentage: 0.1 },
          { category: 'Operational Documentation', count: 76, percentage: 0.1 },
          { category: 'Source Code', count: 156, percentage: 0.3 },
          { category: 'Training Materials', count: 752, percentage: 1 },
          { category: 'Configuration Files', count: 91, percentage: 0.2 },
          {
            category: 'Other Information Technology',
            count: 22,
            percentage: 0,
          },
        ],
      },
      {
        category: 'Education',
        count: 14,
        percentage: 0,
      },
      {
        category: 'Manufacture',
        count: 27,
        percentage: 0,
      },
      {
        category: 'Other',
        count: 66,
        percentage: 0.1,
      },
    ],
    total: {
      category: 'Total Categorized Files',
      count: 32834,
      percentage: 59,
    },
    total_scan_count: 55834,
  },
  sensitive_file_owners: [
    {
      user_name: 'Ethan Liu - long name test test test test test',
      storage: {
        type: 1,
        name: 'aws - long name test test test test test test',
      },
      files: 100,
    },
    {
      user_name: 'John Doe',
      storage: { type: 2, name: 'cloud' },
      files: 200,
    },
    {
      user_name: 'Jane Smith',
      storage: { type: 3, name: 'prem' },
      files: 150,
    },
    {
      user_name: 'Ethan Liu',
      storage: { type: 1, name: 'aws' },
      files: 100,
    },
    {
      user_name: 'John Doe',
      storage: { type: 2, name: 'cloud' },
      files: 200,
    },
    {
      user_name: 'Jane Smith',
      storage: { type: 3, name: 'prem' },
      files: 150,
    },
    {
      user_name: 'Ethan Liu',
      storage: { type: 1, name: 'aws' },
      files: 100,
    },
    {
      user_name: 'John Doe',
      storage: { type: 2, name: 'cloud' },
      files: 200,
    },
    {
      user_name: 'Jane Smith',
      storage: { type: 3, name: 'prem' },
      files: 150,
    },
    {
      user_name: 'Ethan Liu',
      storage: { type: 1, name: 'aws' },
      files: 100,
    },
    {
      user_name: 'John Doe',
      storage: { type: 2, name: 'cloud' },
      files: 200,
    },
    {
      user_name: 'Jane Smith',
      storage: { type: 3, name: 'prem' },
      files: 150,
    },
    {
      user_name: 'Ethan Liu',
      storage: { type: 1, name: 'aws' },
      files: 100,
    },
    {
      user_name: 'John Doe',
      storage: { type: 2, name: 'cloud' },
      files: 200,
    },
    {
      user_name: 'Jane Smith',
      storage: { type: 3, name: 'prem' },
      files: 150,
    },
    {
      user_name: 'Ethan Liu',
      storage: { type: 1, name: 'aws' },
      files: 100,
    },
    {
      user_name: 'John Doe',
      storage: { type: 2, name: 'cloud' },
      files: 200,
    },
    {
      user_name: 'Jane Smith',
      storage: { type: 3, name: 'prem' },
      files: 150,
    },
    {
      user_name: 'Ethan Liu',
      storage: { type: 1, name: 'aws' },
      files: 100,
    },
    {
      user_name: 'John Doe',
      storage: { type: 2, name: 'cloud' },
      files: 200,
    },
    {
      user_name: 'Jane Smith',
      storage: { type: 3, name: 'prem' },
      files: 150,
    },
  ],
  shared_sensitive_file_owners: {
    all: [
      {
        user_name: 'Ethan Liu - long name test test test test test',
        storage: {
          type: 1,
          name: 'aws - long name test test test test test test',
        },
        files: 120,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 250,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 180,
      },
      {
        user_name: 'Ethan Liu - long name test test test test test',
        storage: {
          type: 1,
          name: 'aws - long name test test test test test test',
        },
        files: 120,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 250,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 180,
      },
      {
        user_name: 'Ethan Liu - long name test test test test test',
        storage: {
          type: 1,
          name: 'aws - long name test test test test test test',
        },
        files: 120,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 250,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 180,
      },
      {
        user_name: 'Ethan Liu - long name test test test test test',
        storage: {
          type: 1,
          name: 'aws - long name test test test test test test',
        },
        files: 120,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 250,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 180,
      },
      {
        user_name: 'Ethan Liu - long name test test test test test',
        storage: {
          type: 1,
          name: 'aws - long name test test test test test test',
        },
        files: 120,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 250,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 180,
      },
      {
        user_name: 'Ethan Liu - long name test test test test test',
        storage: {
          type: 1,
          name: 'aws - long name test test test test test test',
        },
        files: 120,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 250,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 180,
      },
    ],
    public: [
      {
        user_name: 'Ethan Liu',
        storage: { type: 1, name: 'aws' },
        files: 70,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 150,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 100,
      },
    ],
    internal: [
      {
        user_name: 'Ethan Liu',
        storage: { type: 1, name: 'aws' },
        files: 30,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 70,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 50,
      },
    ],
    external: [
      {
        user_name: 'Ethan Liu',
        storage: { type: 1, name: 'aws' },
        files: 20,
      },
      {
        user_name: 'John Doe',
        storage: { type: 2, name: 'cloud' },
        files: 30,
      },
      {
        user_name: 'Jane Smith',
        storage: { type: 3, name: 'prem' },
        files: 30,
      },
    ],
  },
  dormant_sensitive_files: [
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
    {
      file_name: 'old_report.pdf',
      storage: { type: 1, name: 'aws' },
      last_accessed: '2023-01-15',
      owner: ['Ethan Liu'],
      idle_days: 3,
    },
    {
      file_name: 'unused_data.csv',
      storage: { type: 2, name: 'cloud' },
      last_accessed: '2023-02-20',
      owner: ['John Doe'],
      idle_days: 5,
    },
    {
      file_name: 'archive.zip',
      storage: { type: 3, name: 'prem' },
      last_accessed: '2023-03-10',
      owner: ['Jane Smith'],
      idle_days: 10,
    },
  ],
  is_printing: true,
};
