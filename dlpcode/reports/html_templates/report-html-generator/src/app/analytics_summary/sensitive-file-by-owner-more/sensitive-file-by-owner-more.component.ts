import { Component, Inject, inject, Input, OnInit } from '@angular/core';
import { AppGlobalService } from '../app-global.service';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ISensitiveFileOwner } from '../mock_data_structure';
import { FormatService } from '@/service/format.service';

@Component({
  selector: 'app-sensitive-file-by-owner-more',
  templateUrl: './sensitive-file-by-owner-more.component.html',
  styleUrls: ['./sensitive-file-by-owner-more.component.css'],
})
export class SensitiveFileByOwnerMoreComponent implements OnInit {
  baseData: any[] = [];
  @Input() data: ISensitiveFileOwner[];

  constructor(
    public globalService: AppGlobalService,
    private formatService: FormatService
  ) {}

  ngOnInit(): void {
    let maxCount = 0;
    this.baseData = this.data.map((item) => {
      if (item.files > maxCount) {
        maxCount = item.files;
      }
      return {
        owner: item.user_name,
        name: item.storage.name,
        type: item.storage.type,
        files: item.files,
        real_percent: 0,
        icon: this.formatService.getStorageType(item.storage.type).icon,
      };
    });

    this.baseData.forEach((item) => {
      item.real_percent = maxCount ? (item.files / maxCount) * 100 : 0;
    });
  }

  truncateLabel(label: string) {
    return this.formatService.truncateLabel(
      label,
      this.globalService.isPrinting ? 25 : 15
    );
  }

  getStorageLabel(type) {
    return this.formatService.getStorageType(type).text;
  }
}
