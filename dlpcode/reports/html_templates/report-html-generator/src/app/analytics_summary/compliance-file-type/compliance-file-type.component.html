<div class="title">
  {{ "sensitive_data.sensitive_files_by_file_type_5" | translate }}:
  {{ "sensitive_data.current_totals" | translate }}
</div>

<div class="body">
  <div class="content">
    <table class="info-table">
      <tr>
        <td class="bold">
          {{ "sensitive_data.file_types" | translate }}
        </td>
        <td class="align-right bold">
          {{ "sensitive_data.files" | translate }}
        </td>
        <td class="align-right">%</td>
        <td class="bold">
          of {{ data.total_scan_count }}
          {{ "sensitive_data.scanned_files" | translate }}
        </td>
      </tr>

      <tr *ngFor="let type of file_type_summary">
        <td>
          {{ type.type }}
        </td>
        <td class="align-right">
          {{ type.count.toLocaleString() }}
        </td>
        <td class="align-right">{{ type.percentage.toFixed(2) }}%</td>
        <td class="percent-cell">
          <div
            class="percent-bar"
            [ngStyle]="{ width: type.percentage + '%' }"
          ></div>
        </td>
      </tr>

      <tr>
        <td class="bold">
          {{ "sensitive_data.top_5_totals" | translate }}
        </td>
        <td class="align-right">
          {{ this.data.top5_total.count.toLocaleString() }}
        </td>
        <td class="align-right">
          {{ this.data.top5_total.percentage.toLocaleString() }}%
        </td>
        <td></td>
      </tr>

      <tr>
        <td colspan="3" class="align-right">
          <span class="link" (click)="show_all()"
            >{{ "show_more" | translate }} ></span
          >
        </td>
        <td></td>
      </tr>
    </table>
  </div>
</div>
