import { Component, Input, OnInit } from '@angular/core';
import { ISensitiveFile, ISensitiveFileData } from '../mock_data_structure';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ShowMoreDialogComponent } from './show-more-dialog/show-more-dialog.component';
import { flatten, reverse, sortBy } from 'lodash';

@Component({
  selector: 'app-compliance-file-type',
  templateUrl: './compliance-file-type.component.html',
  styleUrls: ['../base.scss', './compliance-file-type.component.css'],
})
export class ComplianceFileTypeComponent implements OnInit {
  @Input() data: ISensitiveFile;
  file_type_summary: ISensitiveFileData[] = [];

  constructor(private dialog: MatDialog) {}

  ngOnInit(): void {
    this.file_type_summary.push(
      ...reverse(
        sortBy(flatten(this.data.data.map((v) => v.sub_types)), 'count')
      ).slice(0, 5)
    );
  }

  show_all = () => {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.restoreFocus = false;

    dialogConfig.data = {
      title: 'sensitive_data.sensitive_files_by_file_type',
      data: this.data,
    };

    dialogConfig.width = '50vw';
    dialogConfig.height = '650px';

    const dialogRef = this.dialog.open(ShowMoreDialogComponent, dialogConfig);
  };
}
