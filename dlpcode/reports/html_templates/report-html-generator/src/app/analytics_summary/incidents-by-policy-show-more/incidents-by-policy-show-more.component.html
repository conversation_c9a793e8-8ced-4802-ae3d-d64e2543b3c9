<div class="main-table">
  <table class="info-table">
    <tr class="bold">
      <td>{{ "sensitive_data.discovery_policy_name" | translate }}</td>
      <td class="align-right">
        {{ "sensitive_data.incidents" | translate }}
      </td>
      <td class="align-right">%</td>
      <td class="percent-cell">
        of {{ total_count.toLocaleString() }}
        {{ "sensitive_data.scan_incidents" | translate }}
      </td>
      <td class="align-center svg-cell">
        {{ "sensitive_data.trend" | translate }}
      </td>
    </tr>

    <tr *ngFor="let summary of summary_data; let i = index" [attr.index]="i">
      <td>
        {{ summary.name }}
      </td>
      <td class="align-right">
        {{ summary.count.toLocaleString() }}
      </td>
      <td class="align-right">{{ summary.percentage.toFixed(2) }}%</td>
      <td class="percent-cell">
        <div
          class="percent-bar"
          [ngStyle]="{ width: summary.real_percent + '%' }"
        ></div>
      </td>
      <td class="svg-cell">
        <app-chart-to-svg [data]="summary.trendData" [width]="40" [height]="20">
        </app-chart-to-svg>
      </td>
    </tr>

    <tr class="bold">
      <td>
        {{ "sensitive_data.top10_total" | translate }}
      </td>
      <td class="align-right">
        {{ top10_count.toLocaleString() }}
      </td>
      <td class="align-right">{{ top10_percentage.toFixed(2) }}%</td>
      <td></td>
      <td></td>
    </tr>

    <tr [attr.index]="-1">
      <td>
        {{ "sensitive_data.all_other_policies" | translate }}
      </td>
      <td class="align-right">
        {{ otherData.count.toLocaleString() }}
      </td>
      <td class="align-right">{{ otherData.real_percent.toFixed(2) }}%</td>
      <td></td>
      <td class="svg-cell">
        <app-chart-to-svg
          [data]="otherData.trendData"
          [height]="20"
          [width]="40"
          *ngIf="otherData.trendData.length"
        >
        </app-chart-to-svg>
      </td>
    </tr>
  </table>
</div>
