import { AppGlobalService } from '@/analytics_summary/app-global.service';
import { IScanIncidentsTopn } from '@/analytics_summary/mock_data_structure';
import { Component, Input, OnInit } from '@angular/core';
import { defaultTo, reverse, sortBy } from 'lodash';

interface ISummaryData {
  name: string;
  count: number;
  percentage: number;
  trendData: { time: number; count: number }[];
  real_percent: number;
}

@Component({
  selector: 'app-incidents-by-policy-show-more',
  templateUrl: './incidents-by-policy-show-more.component.html',
  styleUrls: ['../base.scss', './incidents-by-policy-show-more.component.css'],
})
export class IncidentsByPolicyShowMoreComponent implements OnInit {
  @Input() data: IScanIncidentsTopn;
  total_count: number = 0;
  top10_count: number = 0;
  top10_percentage: number = 0;
  other_count: number = 0;
  other_percentage: number = 0;
  other_svg = '';

  other_trend = [];
  summary_data: ISummaryData[] = [];
  otherData: ISummaryData;

  constructor(public globalService: AppGlobalService) {}

  ngOnInit() {
    this.total_count = 0;
    this.top10_count = 0;
    this.top10_percentage = 0;
    this.other_count = 0;
    this.other_percentage = 0;
    this.summary_data = [];

    this.total_count = defaultTo(this.data.total_count.count, 0);
    const top10Data = reverse(sortBy(this.data.topn, 'count')).slice(0, 10);

    top10Data.forEach((item) => {
      this.top10_count += item.count;
      this.top10_percentage += item.percentage;
      this.summary_data.push({
        name: defaultTo(item.discovery_policy, ''),
        count: defaultTo(item.count, 0),
        percentage: defaultTo(item.percentage, 0),
        trendData: item.chartData,
        real_percent: defaultTo(item.percentage, 0),
      });
    });
    this.otherData = {
      name: 'All Others',
      count: this.data.top10_others.count,
      percentage: this.data.top10_others.percentage,
      real_percent: this.data.top10_others.percentage,
      trendData: this.data.top10_others.chartData,
    };
  }
}
