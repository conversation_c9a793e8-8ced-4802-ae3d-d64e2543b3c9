import { Component } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { ISummaryData } from './mock_data_structure';
import { mockData } from './mock_data';
import { AppGlobalService } from './app-global.service';
import * as Highcharts from 'highcharts';
import { TranslateService } from '@/common/translate/translate.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
})
export class AppComponent {
  reportData: ISummaryData = 'DOLLAR_UPPER_MOCK_DATA' as any;

  constructor(
    private iconRegistry: MatIconRegistry,
    private translateService: TranslateService,
    private sanitizer: DomSanitizer,
    public globalService: AppGlobalService
  ) {
    this.reportData = mockData; // Mock data import

    globalService.setTimezone(this.reportData.report_info.timezone);
    globalService.isPrinting = this.reportData.is_printing || false;

    this.registerMatIcons();
    Highcharts.setOptions({
      noData: {
        style: {
          fontWeight: 'bold',
          fontSize: '24px',
        },
      },
      lang: {
        noData: translateService.lookup('no_data'),
      },
      chart: {
        type: 'spline',
        style: {
          fontFamily: 'Inter, sans-serif',
        },
      },
    });
  }

  registerMatIcons = () => {
    const awsSvg = `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_20878_419063)"> <g clip-path="url(#clip1_20878_419063)"> <path d="M4.22446 5.63187C4.22446 5.83517 4.24418 6.00549 4.27868 6.12637C4.31812 6.24725 4.36741 6.38462 4.43642 6.52747C4.46107 6.57143 4.47093 6.61538 4.47093 6.65385C4.47093 6.70879 4.44135 6.76374 4.37727 6.81868L4.06672 7.05495C4.02236 7.08791 3.97799 7.1044 3.93856 7.1044C3.88927 7.1044 3.83997 7.07692 3.79068 7.02747C3.72167 6.94506 3.66251 6.85714 3.61322 6.76374C3.56393 6.67033 3.51463 6.56593 3.46041 6.43407C3.07099 6.94506 2.58299 7.2033 1.99147 7.2033C1.57247 7.2033 1.23728 7.07143 0.995739 6.8022C0.749271 6.53297 0.626038 6.18132 0.626038 5.73626C0.626038 5.26374 0.773918 4.87912 1.07954 4.59341C1.38516 4.30769 1.78936 4.15934 2.30202 4.15934C2.46961 4.15934 2.64707 4.17582 2.82946 4.2033C3.01184 4.23077 3.20409 4.27473 3.40126 4.32418V3.91758C3.40126 3.49451 3.32239 3.1978 3.16465 3.02747C3.00691 2.85714 2.7358 2.76923 2.35131 2.76923C2.17878 2.76923 1.9964 2.79121 1.81401 2.84066C1.63162 2.89011 1.44924 2.95055 1.27671 3.02747C1.19784 3.06593 1.13869 3.08791 1.10418 3.0989C1.06968 3.10989 1.04503 3.11538 1.02531 3.11538C0.956304 3.11538 0.921798 3.06044 0.921798 2.94506V2.67033C0.921798 2.58242 0.931657 2.51648 0.956304 2.47802C0.980951 2.43956 1.02531 2.4011 1.09433 2.36264C1.26685 2.26374 1.47881 2.18132 1.72528 2.10989C1.97175 2.03846 2.22808 2.00549 2.50412 2.00549C3.09564 2.00549 3.52942 2.15385 3.8104 2.45604C4.08644 2.75824 4.22446 3.21429 4.22446 3.82418V5.62637V5.63187ZM2.20343 6.47802C2.3661 6.47802 2.53862 6.44506 2.71608 6.37912C2.89354 6.31319 3.05621 6.19231 3.1893 6.02198C3.26817 5.91758 3.32732 5.8022 3.3569 5.66484C3.38647 5.53297 3.40619 5.36813 3.40619 5.18132V4.94506C3.26324 4.90659 3.1055 4.87363 2.94776 4.85165C2.79002 4.82967 2.63228 4.81868 2.47947 4.81868C2.14428 4.81868 1.90274 4.89011 1.73514 5.03846C1.57247 5.18681 1.48867 5.4011 1.48867 5.67582C1.48867 5.93956 1.54783 6.13187 1.67106 6.26374C1.79922 6.41209 1.97175 6.47802 2.20343 6.47802ZM6.20606 7.07692C6.11733 7.07692 6.05818 7.06044 6.01874 7.02198C5.97931 6.98901 5.9448 6.91209 5.91523 6.80769L4.74204 2.50549C4.71247 2.3956 4.69768 2.32418 4.69768 2.28571C4.69768 2.1978 4.73711 2.14835 4.81598 2.14835H5.30399C5.39764 2.14835 5.46173 2.16484 5.49623 2.2033C5.53567 2.23626 5.56524 2.31319 5.59482 2.41758L6.43281 6.0989L7.21164 2.41758C7.23629 2.30769 7.26587 2.23626 7.3053 2.2033C7.34474 2.17033 7.41375 2.14835 7.50248 2.14835H7.90175C7.99541 2.14835 8.05949 2.16484 8.09893 2.2033C8.13836 2.23626 8.17287 2.31319 8.19258 2.41758L8.98128 6.14286L9.84391 2.41758C9.87349 2.30769 9.908 2.23626 9.9425 2.2033C9.98194 2.17033 10.046 2.14835 10.1347 2.14835H10.5981C10.677 2.14835 10.7213 2.19231 10.7213 2.28571C10.7213 2.31319 10.7164 2.34066 10.7115 2.37363C10.7066 2.40659 10.6967 2.45055 10.677 2.51099L9.47421 6.80769C9.44464 6.91758 9.41013 6.98901 9.3707 7.02198C9.33126 7.05495 9.26718 7.07692 9.18338 7.07692H8.75453C8.66087 7.07692 8.59679 7.06044 8.55736 7.02198C8.51792 6.98352 8.48342 6.91209 8.4637 6.8022L7.68979 3.21429L6.92081 6.7967C6.89617 6.90659 6.86659 6.97802 6.82716 7.01648C6.78772 7.05495 6.71871 7.07143 6.62998 7.07143H6.20606V7.07692ZM12.6191 7.22528C12.3579 7.22528 12.1016 7.19231 11.8502 7.12637C11.5988 7.06044 11.4065 6.98901 11.2784 6.90659C11.1995 6.85714 11.1453 6.8022 11.1255 6.75275C11.1058 6.7033 11.096 6.64835 11.096 6.5989V6.31319C11.096 6.1978 11.1354 6.14286 11.2093 6.14286C11.2389 6.14286 11.2685 6.14835 11.2981 6.15934C11.3276 6.17033 11.372 6.19231 11.4213 6.21429C11.5889 6.2967 11.7762 6.36264 11.9685 6.40659C12.1656 6.45055 12.3628 6.47253 12.56 6.47253C12.8755 6.47253 13.117 6.41209 13.2895 6.29121C13.4571 6.17033 13.5508 5.98901 13.5508 5.76374C13.5508 5.60989 13.5064 5.47802 13.4177 5.37363C13.329 5.26923 13.1564 5.17582 12.9149 5.08242L12.1903 4.82967C11.8255 4.7033 11.5544 4.51099 11.3917 4.26374C11.2291 4.01648 11.1403 3.74725 11.1403 3.45604C11.1403 3.21978 11.1847 3.01648 11.2734 2.84066C11.3622 2.66484 11.4854 2.50549 11.6333 2.38462C11.7811 2.25824 11.9537 2.16484 12.1508 2.09341C12.348 2.02747 12.56 2 12.7818 2C12.8903 2 13.0086 2.00549 13.117 2.02198C13.2304 2.03846 13.3388 2.06044 13.4423 2.08242C13.5409 2.10989 13.6346 2.13736 13.7282 2.17033C13.817 2.2033 13.886 2.23626 13.9402 2.26923C14.0092 2.31319 14.0585 2.35714 14.0881 2.40659C14.1177 2.45055 14.1324 2.51099 14.1324 2.58791V2.85165C14.1324 2.96703 14.093 3.02747 14.0191 3.02747C13.9796 3.02747 13.9156 3.00549 13.8318 2.96154C13.5459 2.81868 13.2304 2.74725 12.8755 2.74725C12.5896 2.74725 12.3677 2.7967 12.21 2.9011C12.0572 3.00549 11.9734 3.17033 11.9734 3.3956C11.9734 3.54945 12.0227 3.68681 12.1213 3.79121C12.2199 3.8956 12.4072 4 12.6684 4.0989L13.3783 4.35165C13.7381 4.47802 13.9944 4.65934 14.1522 4.88462C14.305 5.10989 14.3838 5.37363 14.3838 5.66484C14.3838 5.90659 14.3395 6.12088 14.2557 6.30769C14.167 6.49451 14.0437 6.66484 13.8909 6.7967C13.7381 6.93407 13.5508 7.03846 13.3388 7.10989C13.1071 7.18681 12.8705 7.22528 12.6191 7.22528Z" fill="#262F3E"/> <path d="M13.5606 9.9286C11.9192 11.2802 9.53335 11.9945 7.48274 11.9945C4.60893 12 2.02102 10.8132 0.0640728 8.84618C-0.0887369 8.69234 0.0492848 8.47805 0.231671 8.59893C2.34636 9.96706 4.95398 10.7967 7.65527 10.7967C9.47419 10.7967 11.4755 10.3737 13.3191 9.50552C13.5951 9.36816 13.8317 9.70882 13.5606 9.9286ZM14.2458 9.06047C14.0338 8.75827 12.8607 8.91761 12.3234 8.98904C12.1656 9.01102 12.141 8.85717 12.2839 8.73629C13.2205 8.00003 14.7634 8.21432 14.9408 8.45607C15.1183 8.70882 14.8915 10.4231 14.0141 11.2473C13.881 11.3737 13.7479 11.3077 13.812 11.1429C14.0092 10.5989 14.4528 9.35717 14.2458 9.06047Z" fill="#F8991D"/> </g> </g> <defs> <clipPath id="clip0_20878_419063"> <rect width="14" height="14" fill="white"/> </clipPath> <clipPath id="clip1_20878_419063"> <rect width="15" height="10" fill="white" transform="translate(0 2)"/> </clipPath> </defs> </svg> `;
    const closeSvg = `<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#1f1f1f"><path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/></svg>`;
    this.iconRegistry.addSvgIconLiteral(
      'close',
      this.sanitizer.bypassSecurityTrustHtml(closeSvg)
    );
    const fortidataSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"> <path d="M0 0 C3.3 0 6.6 0 10 0 C10 1.98 10 3.96 10 6 C6.7 6 3.4 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(11,21)" /> <path d="M0 0 C3.3 0 6.6 0 10 0 C10 1.98 10 3.96 10 6 C6.7 6 3.4 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(11,5)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,13)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(0,13)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C8.125 4.875 8.125 4.875 7 6 C4.667 6.041 2.333 6.042 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,21)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C5.625 6.125 5.625 6.125 2 6 C1.34 5.34 0.68 4.68 0 4 C0 2.68 0 1.36 0 0 Z" fill="#EE3124" transform="translate(0,21)" /> <path d="M0 0 C3.375 -0.125 3.375 -0.125 7 0 C9 2 9 2 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,5)" /> <path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.063 C3.438 2.043 3.438 4.023 3.438 6.063 C0.468 6.063 -2.503 6.063 -5.563 6.063 C-4.488 0.079 -4.488 0.079 0 0 Z" fill="#EE3124" transform="translate(5.5625,4.9375)" /> </svg>`;
    this.iconRegistry.addSvgIconLiteral(
      'fortidata',
      this.sanitizer.bypassSecurityTrustHtml(fortidataSvg)
    );
    this.iconRegistry.addSvgIconLiteral(
      'aws',
      this.sanitizer.bypassSecurityTrustHtml(awsSvg)
    );
    const cloudSvg = `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_20878_419051)"> <path d="M7.16278 8C9.32054 8 11.0698 6.20914 11.0698 4C11.0698 1.79086 9.32054 0 7.16278 0C5.00501 0 3.2558 1.79086 3.2558 4C3.2558 6.20914 5.00501 8 7.16278 8Z" fill="#036C70"/> <path d="M10.4186 11.3333C12.3966 11.3333 14 9.69171 14 7.66667C14 5.64162 12.3966 4 10.4186 4C8.44067 4 6.83722 5.64162 6.83722 7.66667C6.83722 9.69171 8.44067 11.3333 10.4186 11.3333Z" fill="#1A9BA1"/> <path d="M7.65117 14C9.17958 14 10.4186 12.7315 10.4186 11.1667C10.4186 9.6019 9.17958 8.33337 7.65117 8.33337C6.12275 8.33337 4.88373 9.6019 4.88373 11.1667C4.88373 12.7315 6.12275 14 7.65117 14Z" fill="#37C6D0"/> <path opacity="0.1" d="M7.81396 3.61V10.7233C7.81235 10.9705 7.66605 11.1926 7.4428 11.2867C7.37172 11.3174 7.29533 11.3333 7.21815 11.3333H4.88699C4.88373 11.2767 4.88373 11.2233 4.88373 11.1667C4.88265 11.111 4.88482 11.0554 4.89025 11C4.94983 9.93448 5.59098 8.99437 6.54746 8.56999V7.94999C4.41877 7.60463 2.96659 5.55794 3.30391 3.37858C3.30625 3.36348 3.30867 3.3484 3.31118 3.33333C3.32738 3.22091 3.35022 3.1096 3.37955 3H7.21816C7.54669 3.00128 7.81271 3.27364 7.81396 3.61Z" fill="black"/> <path opacity="0.2" d="M6.89255 3.33337H3.31115C2.94936 5.50881 4.3786 7.57263 6.50344 7.94303C6.56778 7.95424 6.63239 7.96381 6.6972 7.97171C5.6879 8.46171 4.95241 9.85337 4.8899 11C4.88448 11.0554 4.8823 11.1111 4.88338 11.1667C4.88338 11.2234 4.88338 11.2767 4.88663 11.3334C4.89251 11.4454 4.90666 11.5568 4.92896 11.6667H6.89221C7.13364 11.6651 7.35052 11.5153 7.44244 11.2867C7.47251 11.2139 7.48801 11.1357 7.48803 11.0567V3.94337C7.48679 3.60714 7.22096 3.33484 6.89255 3.33337Z" fill="black"/> <path opacity="0.2" d="M6.89255 3.33337H3.31116C2.94944 5.50901 4.3789 7.57292 6.50394 7.94325C6.54741 7.95083 6.591 7.95765 6.63469 7.96371C5.65795 8.48904 4.95144 9.87738 4.89023 11H6.89255C7.22058 10.9975 7.48589 10.7259 7.48837 10.39V3.94337C7.48712 3.60701 7.22109 3.33465 6.89255 3.33337Z" fill="black"/> <path opacity="0.2" d="M6.56698 3.33337H3.31117C2.96962 5.3872 4.22663 7.36506 6.19908 7.87737C5.45221 8.75101 4.99413 9.84394 4.89024 11H6.56699C6.89553 10.9988 7.16155 10.7264 7.1628 10.39V3.94337C7.16262 3.60656 6.89597 3.33356 6.56698 3.33337Z" fill="black"/> <path d="M0.596791 3.33337H6.566C6.8956 3.33337 7.16279 3.60693 7.16279 3.94437V10.0557C7.16279 10.3932 6.8956 10.6667 6.566 10.6667H0.596791C0.267192 10.6667 0 10.3932 0 10.0557V3.94437C0 3.60693 0.267192 3.33337 0.596791 3.33337Z" fill="url(#paint0_linear_20878_419051)"/> <path d="M2.66553 6.92535C2.52567 6.83037 2.40885 6.70393 2.32399 6.55568C2.24177 6.4007 2.20079 6.22632 2.20515 6.05001C2.19783 5.8113 2.27651 5.57817 2.42622 5.39501C2.58354 5.21163 2.78688 5.07583 3.01422 5.00234C3.27331 4.91503 3.54468 4.87201 3.81742 4.87501C4.1761 4.86159 4.53428 4.91294 4.87556 5.02668V5.79335C4.72727 5.70138 4.56576 5.63388 4.39696 5.59335C4.2138 5.54736 4.02586 5.52431 3.83728 5.52468C3.63842 5.51721 3.44083 5.56003 3.26198 5.64935C3.1239 5.71032 3.03436 5.8494 3.03408 6.00335C3.03351 6.09674 3.06856 6.18667 3.13175 6.25402C3.20639 6.33342 3.29468 6.39806 3.39222 6.44469C3.50074 6.50002 3.66353 6.57358 3.88059 6.66536C3.90449 6.67309 3.92778 6.68267 3.95026 6.69403C4.16389 6.7795 4.36997 6.88355 4.56626 7.00503C4.71491 7.09886 4.83967 7.2275 4.93027 7.38036C5.02315 7.55363 5.06817 7.74953 5.0605 7.94703C5.0711 8.19211 4.99787 8.43329 4.85343 8.62903C4.70946 8.80897 4.51698 8.94159 4.29994 9.01036C4.04465 9.09228 3.77822 9.13188 3.51073 9.12769C3.27073 9.1288 3.03108 9.10873 2.79445 9.06769C2.59465 9.03421 2.39966 8.97555 2.21394 8.89302V8.08469C2.39147 8.21449 2.5898 8.31152 2.79999 8.37136C3.00946 8.43818 3.22721 8.47388 3.44659 8.47736C3.64964 8.49052 3.85232 8.44649 4.03264 8.35003C4.15895 8.27705 4.23578 8.13882 4.23255 7.99036C4.23339 7.88705 4.19348 7.78778 4.12185 7.71503C4.03278 7.6255 3.92965 7.55193 3.81679 7.49736C3.68656 7.43069 3.49478 7.3428 3.24148 7.23369C3.03997 7.15075 2.84703 7.04746 2.66553 6.92535Z" fill="white"/> </g> <defs> <linearGradient id="paint0_linear_20878_419051" x1="1.24432" y1="2.85595" x2="6.08538" y2="11.0455" gradientUnits="userSpaceOnUse"> <stop stop-color="#058F92"/> <stop offset="0.5" stop-color="#038489"/> <stop offset="1" stop-color="#026D71"/> </linearGradient> <clipPath id="clip0_20878_419051"> <rect width="14" height="14" fill="white"/> </clipPath> </defs> </svg> `;
    this.iconRegistry.addSvgIconLiteral(
      'sharepoint_cloud',
      this.sanitizer.bypassSecurityTrustHtml(cloudSvg)
    );
    const premSvg = `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_20878_419057)"> <path d="M8.12875 1.94377e-05C8.10797 -0.00107431 8.08828 0.00439444 8.0675 0.00876944L0.2275 1.48752C0.0951563 1.51268 0 1.63299 0 1.76752V12.2063C0 12.3408 0.0951563 12.4611 0.2275 12.4863L8.0675 13.9913C8.085 13.9945 8.1025 14 8.12 14C8.18453 14 8.24469 13.9803 8.295 13.9388C8.35953 13.8852 8.4 13.8042 8.4 13.72V0.280019C8.4 0.196894 8.35953 0.114863 8.295 0.0612694C8.24687 0.0218944 8.19 0.00220694 8.12875 1.94377e-05ZM8.96 2.26627V5.57377C9.60531 5.46986 10.1139 5.00721 10.29 4.39252C10.8281 4.61674 11.2777 4.99736 11.5938 5.48627C11.0305 5.75752 10.64 6.33393 10.64 7.00002C10.64 7.66174 11.0283 8.23158 11.585 8.50502C11.2678 8.98736 10.8172 9.35924 10.2812 9.58127C10.0975 8.97971 9.59656 8.52908 8.96 8.42627V11.7338C9.74094 11.608 10.3184 10.9616 10.3512 10.15C11.1059 9.87549 11.7294 9.36142 12.1363 8.67127C12.1953 8.67783 12.2587 8.68002 12.32 8.68002C13.2464 8.68002 14 7.92642 14 7.00002C14 6.07361 13.2464 5.32002 12.32 5.32002C12.2653 5.32002 12.2073 5.3233 12.1538 5.32877C11.7469 4.62549 11.1169 4.10158 10.3512 3.82377C10.3064 3.02424 9.73219 2.39096 8.96 2.26627ZM4.08625 4.31377C4.36625 4.31377 4.59047 4.33783 4.78625 4.36627C4.98203 4.39471 5.17781 4.45049 5.34625 4.50627V5.60002C5.26203 5.54424 5.17781 5.48955 5.06625 5.43377C4.95469 5.37799 4.87156 5.34846 4.76 5.32002C4.64844 5.29158 4.53797 5.26752 4.45375 5.26752C4.34219 5.23908 4.25906 5.23252 4.1475 5.23252C4.0075 5.23252 3.89156 5.23908 3.78 5.26752C3.66844 5.29596 3.58422 5.31674 3.5 5.37252C3.41578 5.4283 3.36219 5.49174 3.33375 5.54752C3.30531 5.6033 3.2725 5.68205 3.2725 5.76627C3.2725 5.85049 3.30531 5.93799 3.33375 5.99377C3.38953 6.04955 3.44203 6.13049 3.52625 6.18627C3.61047 6.24205 3.72094 6.29674 3.8325 6.35252C3.94406 6.4083 4.08625 6.47174 4.22625 6.52752C4.42203 6.61174 4.61781 6.69596 4.78625 6.80752C4.95469 6.89174 5.09469 7.00002 5.20625 7.14002C5.31781 7.25158 5.40203 7.39158 5.48625 7.56002C5.54203 7.72846 5.57375 7.89799 5.57375 8.09377C5.54531 8.40221 5.49172 8.62424 5.4075 8.82002C5.29594 9.0158 5.14828 9.18096 4.9525 9.29252C4.75672 9.40408 4.56531 9.49049 4.31375 9.54627C4.06219 9.60205 3.80625 9.63377 3.52625 9.63377C3.24625 9.63377 2.96406 9.60205 2.7125 9.54627C2.46094 9.51783 2.24219 9.43799 2.07375 9.35377V8.20752C2.26953 8.37596 2.49594 8.5083 2.7475 8.59252C2.99906 8.67674 3.22219 8.73252 3.47375 8.73252C3.61375 8.73252 3.75594 8.73471 3.8675 8.70627C3.97906 8.67783 4.06328 8.6483 4.1475 8.59252C4.23172 8.53674 4.28531 8.48205 4.31375 8.42627C4.34219 8.37049 4.36625 8.28955 4.36625 8.23377C4.36625 8.12221 4.34328 8.03799 4.2875 7.95377C4.23172 7.86955 4.14422 7.81049 4.06 7.72627C3.94844 7.67049 3.8325 7.58955 3.6925 7.53377C3.5525 7.47799 3.41469 7.41674 3.24625 7.33252C2.85469 7.16408 2.54953 6.97158 2.35375 6.72002C2.15797 6.46846 2.0475 6.18955 2.0475 5.85377C2.0475 5.57377 2.10219 5.34955 2.21375 5.15377C2.32531 4.95799 2.46422 4.81252 2.66 4.67252C2.85578 4.56096 3.05594 4.4483 3.3075 4.39252C3.55906 4.33674 3.80625 4.31377 4.08625 4.31377Z" fill="#017EE0"/> </g> <defs> <clipPath id="clip0_20878_419057"> <rect width="14" height="14" fill="white"/> </clipPath> </defs> </svg> `;
    this.iconRegistry.addSvgIconLiteral(
      'sharepoint_onprem',
      this.sanitizer.bypassSecurityTrustHtml(premSvg)
    );
    const smbSvg = `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_20878_419069)"> <path d="M13.2222 2.11114V3.44447C13.2222 4.67225 10.4361 5.66669 6.99999 5.66669C3.56388 5.66669 0.777771 4.67225 0.777771 3.44447V2.11114C0.777771 0.88336 3.56388 -0.111084 6.99999 -0.111084C10.4361 -0.111084 13.2222 0.88336 13.2222 2.11114ZM11.7 5.8528C12.2778 5.64725 12.8083 5.38336 13.2222 5.05836V7.88892C13.2222 9.11669 10.4361 10.1111 6.99999 10.1111C3.56388 10.1111 0.777771 9.11669 0.777771 7.88892V5.05836C1.19166 5.38614 1.72222 5.64725 2.29999 5.8528C3.54722 6.29725 5.20833 6.55558 6.99999 6.55558C8.79166 6.55558 10.4528 6.29725 11.7 5.8528ZM0.777771 9.5028C1.19166 9.83058 1.72222 10.0917 2.29999 10.2972C3.54722 10.7417 5.20833 11 6.99999 11C8.79166 11 10.4528 10.7417 11.7 10.2972C12.2778 10.0917 12.8083 9.8278 13.2222 9.5028V11.8889C13.2222 13.1167 10.4361 14.1111 6.99999 14.1111C3.56388 14.1111 0.777771 13.1167 0.777771 11.8889V9.5028Z" fill="#5FA4E4"/> </g> <defs> <clipPath id="clip0_20878_419069"> <rect width="14" height="14" fill="white"/> </clipPath> </defs> </svg> `;
    this.iconRegistry.addSvgIconLiteral(
      'smb',
      this.sanitizer.bypassSecurityTrustHtml(smbSvg)
    );
    const gDriveSvg = `<svg width="200" height="180" viewBox="0 0 200 180" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_19442_435414)"> <path d="M15.1203 153.912L23.9404 169.173C25.7732 172.386 28.4078 174.91 31.5006 176.746L63.0011 122.128H0C0 125.685 0.91638 129.242 2.74914 132.455L15.1203 153.912Z" fill="#0066DA"/> <path d="M100 57.8718L68.4994 3.25385C65.4066 5.08974 62.7721 7.6141 60.9393 10.8269L2.74914 111.801C0.950087 114.945 0.00239533 118.505 0 122.128H63.0011L100 57.8718Z" fill="#00AC47"/> <path d="M168.499 176.746C171.592 174.91 174.227 172.386 176.06 169.173L179.725 162.862L197.251 132.455C199.084 129.242 200 125.685 200 122.128H136.994L150.401 148.519L168.499 176.746Z" fill="#EA4335"/> <path d="M100 57.8718L131.501 3.25385C128.408 1.41795 124.857 0.5 121.191 0.5H78.8087C75.1432 0.5 71.5922 1.53269 68.4995 3.25385L100 57.8718Z" fill="#00832D"/> <path d="M136.999 122.128H63.0011L31.5005 176.746C34.5933 178.582 38.1443 179.5 41.8098 179.5H158.19C161.856 179.5 165.407 178.467 168.499 176.746L136.999 122.128Z" fill="#2684FC"/> <path d="M168.156 61.3141L139.061 10.8269C137.228 7.6141 134.593 5.08974 131.501 3.25385L100 57.8718L136.999 122.128H199.885C199.885 118.571 198.969 115.014 197.136 111.801L168.156 61.3141Z" fill="#FFBA00"/> </g> <defs> <clipPath id="clip0_19442_435414"> <rect width="200" height="179" fill="white" transform="translate(0 0.5)"/> </clipPath> </defs> </svg> `;
    this.iconRegistry.addSvgIconLiteral(
      'gdrive',
      this.sanitizer.bypassSecurityTrustHtml(gDriveSvg)
    );
  };
}
