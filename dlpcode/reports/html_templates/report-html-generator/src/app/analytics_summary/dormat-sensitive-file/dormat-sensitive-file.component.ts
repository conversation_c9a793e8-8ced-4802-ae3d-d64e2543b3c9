import { FormatService } from '@/service/format.service';
import { Component, Input, OnInit } from '@angular/core';
import { DormatShowMoreDialogComponent } from './show-more-dialog/show-more-dialog.component';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { TranslateService } from '@/common/translate/translate.service';
import { IDormantSensitiveFile } from '../mock_data_structure';
import { max } from 'lodash';

@Component({
  selector: 'app-dormat-sensitive-file',
  templateUrl: './dormat-sensitive-file.component.html',
  styleUrls: ['./dormat-sensitive-file.component.css'],
})
export class DormatSensitiveFileComponent implements OnInit {
  @Input() data: IDormantSensitiveFile[] = [];
  baseData: any[] = [];

  constructor(
    public formatService: FormatService,
    private translateService: TranslateService,
    private dialog: MatDialog
  ) {}

  show_all() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.restoreFocus = false;

    dialogConfig.data = {
      data: this.data,
      title: this.translateService.lookup(
        'sensitive_data.top100_dormant_sensitive_files'
      ),
    };
    dialogConfig.width = '40vw';
    dialogConfig.height = '650px';

    this.dialog.open(DormatShowMoreDialogComponent, dialogConfig);
  }

  ngOnInit(): void {
    let maxDays = 0;
    this.baseData = this.data.slice(0, 10).map((item) => {
      maxDays = Math.max(maxDays, item.idle_days);
      return {
        file_name: item.file_name,
        owner: item.owner,
        name: item.storage.name,
        days_idle: item.idle_days,
        last_access_time: item.last_accessed,
        icon: this.formatService.getStorageType(item.storage.type).icon,
        type: item.storage.type,
      };
    });

    this.baseData.forEach((item) => {
      item.real_percent = maxDays ? (item.days_idle / maxDays) * 100 : 0;
    });
  }
}
