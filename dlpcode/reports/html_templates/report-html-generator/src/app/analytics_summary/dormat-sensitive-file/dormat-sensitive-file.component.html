<div class="title">{{ "sensitive_data.top10_dormant_sensitive_files" | translate }} *</div>

<div class="body">
    <div class="content">
        <table class="info-table">
            <tr class="bold">
                <td class="percent-cell-20">
                    {{ 'sensitive_data.detail_file_name' | translate }}
                </td>
                <td class="percent-cell-15">
                    {{ 'sensitive_data.owner' | translate }}
                </td>
                <td class="percent-cell-15">
                    {{ 'sensitive_data.storage' | translate }}
                </td>
                <td class="align-right">
                    {{ 'sensitive_data.last_accessed' | translate }}
                </td>
                <td class="align-right">
                    {{ 'sensitive_data.days_idle' | translate }}
                </td>
                <td class="percent-cell-25">
                </td>
            </tr>

            <tr *ngFor="let data of baseData">
                <td class="">
                    <span [title]="data.file_name">
                        {{ formatService.truncateLabel(data.file_name, 25) }}
                    </span>
                </td>
                <td>
                    <span [title]="data.owner.join(',\r\n')">
                        {{ data.owner.length ? formatService.truncateLabel(data.owner[0], 16) : '' }}
                    </span>
                    <span *ngIf="data.owner.length > 1">
                        +{{ data.owner.length-1 }}
                    </span>
                </td>
                <td>
                    <div class="flex-cell">
                        <mat-icon [svgIcon]="data.icon"></mat-icon>
                        <span [title]="data.name">
                            {{ formatService.truncateLabel(data.name, 16) }}
                        </span>
                    </div>
                </td>
                <td class="align-right">
                    {{ data.last_access_time }}
                </td>
                <td class="align-right">
                    {{ data.days_idle.toLocaleString() }}
                </td>
                <td class="percent-cell-25">
                    <div class="percent-bar" [ngStyle]="{'width': data.real_percent + '%'}"></div>
                </td>
            </tr>

            <tr class="last-row">
                <td colspan="5">
                    <div class="flex-cell-space">
                        <div class="flex-cell">
                            <span>*</span>
                            <mat-icon svgIcon="sharepoint_cloud"></mat-icon>
                            <span>{{ 'scans.storage_type.cloud' | translate }}</span>
                            <span>,</span>
                            <mat-icon svgIcon="gdrive"></mat-icon>
                            <span>{{ 'scans.storage_type.google_drive'| translate }}</span>
                            <span>,</span>
                            <mat-icon svgIcon="aws"></mat-icon>
                            <span>{{ 'scans.storage_type.aws' | translate }}</span>
                            <span>&</span>
                            <mat-icon svgIcon="smb"></mat-icon>
                            <span>{{ 'scans.storage_type.smb' | translate }}</span>
                        </div>

                    </div>
                </td>
                <td>
                    <span class="link" (click)="show_all()">{{ 'show_more' | translate}} ></span>
                </td>
            </tr>

        </table>
    </div>
</div>