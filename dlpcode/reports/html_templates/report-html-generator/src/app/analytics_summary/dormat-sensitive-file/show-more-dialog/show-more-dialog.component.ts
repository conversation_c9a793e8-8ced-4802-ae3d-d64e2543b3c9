import { IDormantSensitiveFile } from '@/analytics_summary/mock_data_structure';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-dormat-show-more-dialog',
  templateUrl: './show-more-dialog.component.html',
  styleUrls: ['./show-more-dialog.component.css'],
})
export class DormatShowMoreDialogComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { title: string; data: IDormantSensitiveFile[] }
  ) {}
}
