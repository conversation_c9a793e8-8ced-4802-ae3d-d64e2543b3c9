<div class="report-title">
  <mat-icon svgIcon="fortidata"></mat-icon>
  <div class="title">FortiData Report for Analytics Summary</div>
</div>

<div class="report-info">
  <div class="item column-6">
    <label>Report Name:</label>
    <span>{{ reportData.report_info.name }}</span>
  </div>
  <div class="item column-6">
    <label>Period:</label>
    <span>{{ reportData.report_info.period }}</span>
  </div>
  <div class="item column-6">
    <label>Timestamp:</label>
    <span>{{ reportData.report_info.generated_on }}</span>
  </div>
  <div class="item column-6">
    <label>Schedule:</label>
    <span>{{ reportData.report_info.schedule }}</span>
  </div>
  <div class="item column-6">
    <label>Scan Name:</label>
    <span>{{ reportData.report_info.scan_name }}</span>
  </div>
  <div class="item column-6">
    <label>Storage:</label>
    <span>{{ reportData.report_info.scan_storage }}</span>
  </div>
  <div class="item column-6">
    <label>Created by:</label>
    <span>{{ reportData.report_info.created_by }}</span>
  </div>
  <div class="item column-12">
    <label>Notes:</label>
    <span>{{ reportData.report_info.notes }}</span>
  </div>
  <div class="item column-12">
    <label>Firmware Version:</label>
    <span>{{ reportData.report_info.firmware_version }}</span>
  </div>
</div>

<div [class]="{widgets: true, a4: globalService.isPrinting}">
  <div [class]="{
      widget: true,
      'no-border': globalService.isPrinting,
      'column-12': globalService.isPrinting,
      'column-6': !globalService.isPrinting
    }">
    <app-incidents-by-severity [data]="reportData.scanincidentsbyseverity"></app-incidents-by-severity>
  </div>
  <div [class]="{
      widget: true,
      'no-border': globalService.isPrinting,
      'column-12': globalService.isPrinting,
      'column-6': !globalService.isPrinting
    }">
    <app-files-by-type [data]="reportData.compliances"></app-files-by-type>
  </div>
  <div class="column-6 widget" *ngIf="!globalService.isPrinting">
    <app-compliance-file-type [data]="reportData.sensitive_files"></app-compliance-file-type>
  </div>
  <div class="column-6 widget" *ngIf="!globalService.isPrinting">
    <app-ai-category [data]="reportData.ai_categories"></app-ai-category>
  </div>

  <div class="column-12 widget-box" [ngClass]="{ 'no-border': globalService.isPrinting }"
    *ngIf="globalService.isPrinting">
    <div class="title">
      {{ "sensitive_data.sensitive_files_by_file_type" | translate }}
    </div>
    <app-compliance-file-type-show-more [data]="reportData.sensitive_files"></app-compliance-file-type-show-more>
  </div>
  <div class="column-12 widget-box" [ngClass]="{ 'no-border': globalService.isPrinting }"
    *ngIf="globalService.isPrinting">
    <div class="title">
      {{ "sensitive_data.ai_file_categories_and_sub_categories" | translate }}
    </div>
    <app-ai-category-show-more [data]="reportData.ai_categories"></app-ai-category-show-more>
  </div>

  <div class="column-6 widget" *ngIf="!globalService.isPrinting">
    <app-sensitive-file-by-owner [data]="reportData.sensitive_file_owners"></app-sensitive-file-by-owner>
  </div>
  <div class="column-12 widget" style="border: none" *ngIf="globalService.isPrinting">
    <div class="title">
      {{ "sensitive_data.top100_sensitive_file_owners" | translate }}
    </div>
    <app-sensitive-file-by-owner-more [data]="reportData.sensitive_file_owners"></app-sensitive-file-by-owner-more>
  </div>

  <div class="column-6 widget" *ngIf="!globalService.isPrinting">
    <app-shared-sensitive-file-by-owner
      [data]="reportData.shared_sensitive_file_owners"></app-shared-sensitive-file-by-owner>
  </div>
  <div class="column-12 widget" style="border: none; display: flex; flex-direction: column;"
    *ngIf="globalService.isPrinting">
    <div class="title">
      {{ "sensitive_data.top100_shared_sensitive_file_owners" | translate }}
    </div>
    <app-shared-sensitive-file-by-owner-more type="all"
      [data]="reportData.shared_sensitive_file_owners"></app-shared-sensitive-file-by-owner-more>
    <app-shared-sensitive-file-by-owner-more type="public"
      [data]="reportData.shared_sensitive_file_owners"></app-shared-sensitive-file-by-owner-more>
    <app-shared-sensitive-file-by-owner-more type="external"
      [data]="reportData.shared_sensitive_file_owners"></app-shared-sensitive-file-by-owner-more>
    <app-shared-sensitive-file-by-owner-more type="internal"
      [data]="reportData.shared_sensitive_file_owners"></app-shared-sensitive-file-by-owner-more>
  </div>

  <div class="column-12 widget" *ngIf="!globalService.isPrinting">
    <app-dormat-sensitive-file [data]="reportData.dormant_sensitive_files"></app-dormat-sensitive-file>
  </div>
  <div class="column-12 widget" style="border: none" *ngIf="globalService.isPrinting">
    <div class="title">
      {{ "sensitive_data.top100_dormant_sensitive_files" | translate }}
    </div>
    <app-dormat-sensitive-file-more [data]="reportData.dormant_sensitive_files"></app-dormat-sensitive-file-more>
  </div>

  <div class="column-12 widget" [ngClass]="{ 'no-border': globalService.isPrinting }">
    <app-incidents-by-policy [data]="reportData.scan_incident"></app-incidents-by-policy>
  </div>
</div>