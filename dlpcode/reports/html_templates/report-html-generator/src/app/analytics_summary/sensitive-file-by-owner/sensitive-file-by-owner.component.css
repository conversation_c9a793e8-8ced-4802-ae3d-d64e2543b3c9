:host {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #fff;
  position: relative;
  border-radius: 4px;
}

.info-table {
  border-collapse: true;
  font-size: 14px;
  table-layout: fixed;
  width: 100%;

  td {
    padding-right: 5px;
    white-space: nowrap;
  }

  tr {
    height: 22px;
  }

  .last-row {
    height: 26px;
  }

  .flex-cell {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .flex-cell-space {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .link {
    cursor: pointer;
    color: #2564bf;
  }

  .percent-cell {
    width: 65%;
  }

  .percent-cell-35 {
    width: 35%;
  }

  .percent-cell-30 {
    width: 30%;
  }

  .percent-cell-25 {
    width: 25%;
  }

  .percent-cell-22 {
    width: 22%;
  }

  .percent-cell-20 {
    width: 20%;
  }

  .percent-cell-15 {
    width: 15%;
  }

  .percent-cell-short {
    width: 45%;
  }

  .percent-bar {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: #aedff8;
    height: 10px;
  }

  .mat-icon {
    width: 16px;
    height: 16px;
    font-size: 16px;
  }

  span {
    white-space: nowrap;
  }
}

.title {
  font-size: 16px;
  font-weight: 700;
  color: #222222;
  display: flex;
  justify-content: space-between;
  height: 20px;
}

.small-title {
  font-size: 14px;
  font-weight: 700;
  color: #222222;
}

.body {
  position: relative;
  min-height: 100px;
}

.loading-panel {
  background-color: rgb(255, 255, 255) !important;
}

.pointer {
  cursor: pointer;
}

.circle {
  display: inline-block;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.align-right {
  text-align: right;
}

.align-center {
  text-align: center;
}

.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;

  .chart-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 200px;
    width: 100%;
  }
}

.circle-icon {
  display: inline-block;
  height: 8px;
  width: 8px;
  border-radius: 50px;
}

.footer {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-left: 25px;

  .small-group {
    display: flex;
    align-items: center;
    font-size: 12px;
    gap: 8px;
  }

  mat-icon {
    margin: 0 15px 0 auto;
  }
}

@media (max-width: 1110px) {
  :host {
    min-width: 800px;
  }
}

.bold {
  font-weight: bold;
}
