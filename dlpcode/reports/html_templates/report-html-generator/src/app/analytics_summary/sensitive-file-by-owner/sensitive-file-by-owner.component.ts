import { TranslateService } from '@/common/translate/translate.service';
import { FormatService } from '@/service/format.service';
import { Component, Input, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { SensitiveFileShowMoreDialogComponent } from './show-more-dialog/show-more-dialog.component';
import { ISensitiveFileOwner } from '../mock_data_structure';

@Component({
  selector: 'app-sensitive-file-by-owner',
  templateUrl: './sensitive-file-by-owner.component.html',
  styleUrls: ['./sensitive-file-by-owner.component.css'],
})
export class SensitiveFileByOwnerComponent implements OnInit {
  @Input() data: ISensitiveFileOwner[];
  baseData: any[] = [];

  constructor(
    private formatService: FormatService,
    private dialog: MatDialog,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.baseData = [];
    let maxFiles = 0;
    this.baseData = this.data.slice(0, 10).map((item) => {
      if (item.files > maxFiles) {
        maxFiles = item.files;
      }
      return {
        owner: item.user_name,
        name: item.storage.name,
        type: item.storage.type,
        files: item.files,
        real_percent: 0,
        icon: this.formatService.getStorageType(item.storage.type).icon,
      };
    });

    this.baseData.forEach((item) => {
      item.real_percent = maxFiles ? (item.files / maxFiles) * 100 : 0;
    });
  }

  truncateLabel(label: string) {
    return this.formatService.truncateLabel(label, 15);
  }

  show_all() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.restoreFocus = false;

    dialogConfig.data = {
      data: this.data,
      title: this.translateService.lookup(
        'sensitive_data.top100_sensitive_file_owners'
      ),
    };
    dialogConfig.width = '40vw';
    dialogConfig.height = '650px';

    this.dialog.open(SensitiveFileShowMoreDialogComponent, dialogConfig);
  }

  getStorageLabel(type: any) {
    return this.formatService.getStorageType(type).text;
  }
}
