interface ILineChartPoint {
  count: number;
  time: number;
}

export enum EStorageType {
  aws = 1,
  cloud = 2,
  prem = 3,
  smb = 4,
  gdrive = 6,
}

export enum EPeriod {
  Hour24 = 0,
  Day7 = 1,
  Day14 = 2,
}

export interface IReportInfo {
  name: string;
  /** When the report generated */
  generated_on: string;
  schedule: string;
  period: string;
  created_by: string;
  notes: string;
  scan_name: string;
  scan_storage: string;
  /** IANA format */
  timezone: string;
  firmware_version: string;
}

export interface IScanIncidentsBySeverity {
  period: EPeriod;
  critical: ILineChartPoint[];
  high: ILineChartPoint[];
  medium: ILineChartPoint[];
  low: ILineChartPoint[];
}

export interface IComplianceFileData {
  type: string;
  chartData: ILineChartPoint[];
}
export interface IComplianceFile {
  period: EPeriod;
  data: IComplianceFileData[];
}

export interface IScanIncidentData {
  discovery_policy: string;
  count: number;
  percentage: number;
  chartData: ILineChartPoint[];
}

export interface IScanIncidentCount {
  count: number;
  percentage: number;
}

export interface IScanIncidentsTopn {
  period: EPeriod;
  total_discovery_policy_count: number;
  topn: IScanIncidentData[];
  top5_others: IScanIncidentData;
  top10_others: IScanIncidentData;
  total_count: IScanIncidentCount;
}

export interface ISensitiveFileData {
  type: string;
  count: number;
  percentage: number;
  desc?: string;
  sub_types?: ISensitiveFileData[];
}
export interface ISensitiveFile {
  data: ISensitiveFileData[];
  top5_total: ISensitiveFileData;
  total: ISensitiveFileData;
  total_scan_count: number;
}

export interface IAiCategoryData {
  category: string;
  count: number;
  percentage: number;
  sub_categories?: IAiCategoryData[];
}
export interface IAiCategory {
  data: IAiCategoryData[];
  total: IAiCategoryData;
  total_scan_count: number;
}

export interface IStorage {
  type: EStorageType;
  name: string;
}

export interface ISensitiveFileOwner {
  user_name: string;
  storage: IStorage;
  files: number;
}

export interface ISharedSensitiveFileOwner {
  user_name: string;
  storage: IStorage;
  files: number;
}

export interface IDormantSensitiveFile {
  file_name: string;
  storage: IStorage;
  owner: string[];
  last_accessed: string;
  idle_days: number;
}

export interface ISharedSensitiveFileOwnerAll {
  all: ISharedSensitiveFileOwner[];
  public: ISharedSensitiveFileOwner[];
  internal: ISharedSensitiveFileOwner[];
  external: ISharedSensitiveFileOwner[];
}

export interface ISummaryData {
  /** for Scan Incidents by Severity */
  scanincidentsbyseverity: IScanIncidentsBySeverity;
  /** for Compliance file Types */
  compliances: IComplianceFile;
  /** for discovery policy scan incidents */
  scan_incident: IScanIncidentsTopn;
  /** for Sensitive File Extensions */
  sensitive_files: ISensitiveFile;
  /** for Ai Category */
  ai_categories: IAiCategory;
  /** report info */
  report_info: IReportInfo;
  /** sensitive file owners */
  sensitive_file_owners: ISensitiveFileOwner[];
  /** shared sensitive file owners */
  shared_sensitive_file_owners: ISharedSensitiveFileOwnerAll;
  /** dormant sensitive files */
  dormant_sensitive_files: IDormantSensitiveFile[];
  /** used to generate PDF format report */
  is_printing: boolean;
}
