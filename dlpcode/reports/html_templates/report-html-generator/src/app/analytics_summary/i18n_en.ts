export const TRANSLATIONS_EN = {
  show_more: 'Show More',
  dashboard: {
    critical: 'Critical',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
  },
  scans: {
    storage_type: {
      smb: 'SMB',
      sharepoint: 'SharePoint',
      google_drive: 'Google Drive',
      cloud: 'SharePoint Cloud',
      prem: 'SharePoint OnPrem',
      aws: 'AWS',
    },
  },
  sensitive_data: {
    top10_sensitive_file_owners: 'Top 10 Sensitive File Owners',
    top10_shared_sensitive_file_owners: 'Top 10 Shared Sensitive File Owners',
    top10_dormant_sensitive_files: 'Top 10 Dormant Sensitive Files',
    top100_sensitive_file_owners: 'Top 100 Sensitive File Owners',
    top100_shared_sensitive_file_owners: 'Top 100 Shared Sensitive File Owners',
    top100_dormant_sensitive_files: 'Top 100 Dormant Sensitive Files',
    share_types: 'Share Types',
    username: 'Username',
    storage: 'Storage',
    detail_file_name: 'File Name',
    owner: 'Owner',
    last_accessed: 'Last Accessed',
    days_idle: 'Days Idle',
    scan_incidents_by_severity: 'Scan Incidents by Risk',
    top5_compliance_file_types: 'Top 5 Compliance File Type',
    scan_incidents: 'Scan Incidents',
    files: 'Files',
    top5_discovery_policies: 'Top 5 Discovery Policies',
    policy_name: 'Policy Name',
    trend: 'Trend',
    all_scan_incidents: 'All Scan Incidents',
    discovery_policies: 'Discovery Policies',
    incidents: 'Incidents',
    of_all_incidents: 'of All Incidents',
    top10_discovery_policies_trends: 'Top 10 Discovery Policies & Trends',
    discovery_policy_name: 'Discovery Policy Name',
    top10_total: 'Top 10 Total',
    all_other_policies: 'All Other Policies',
    sensitive_files_by_file_type_5: 'Top 5 Sensitive File Extensions',
    sensitive_files_by_file_type: 'Sensitive Files by File Extension',
    current_totals: 'Current Totals',
    file_types: 'File Extension',
    total_sensitive_files: 'Total Sensitive Files',
    top_5_totals: 'Top 5 Totals',
    scanned_files: 'Scanned Files',
    ai_file_categories_and_sub_categories:
      'ML File Categories and Sub-categories',
    category_and_sub_category: 'Category and Sub-category',
    total_files_categoried: 'Total Files Categoried',
    total_categorized_files: 'Total Categorized Files',
    ai_file_categories: 'ML File Categories',
    category: 'Category',
  },
};
