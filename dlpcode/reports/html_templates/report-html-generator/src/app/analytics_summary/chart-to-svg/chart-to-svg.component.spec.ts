import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ChartToSvgComponent } from './chart-to-svg.component';

describe('ChartToSvgComponent', () => {
  let component: ChartToSvgComponent;
  let fixture: ComponentFixture<ChartToSvgComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ChartToSvgComponent]
    });
    fixture = TestBed.createComponent(ChartToSvgComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
