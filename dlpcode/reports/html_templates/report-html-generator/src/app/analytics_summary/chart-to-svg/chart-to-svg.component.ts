import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-chart-to-svg',
  templateUrl: './chart-to-svg.component.html',
  styleUrls: ['./chart-to-svg.component.scss'],
})
export class ChartToSvgComponent implements OnInit {
  @Input() data: { count: number; time: number }[] = [];
  @Input() width = 400;
  @Input() height = 100;

  pathD = '';

  ngOnInit(): void {
    this.generatePath();
  }

  private generatePath() {
    if (!this.data || this.data.length < 2) return;

    const maxCount = Math.max(...this.data.map((d) => d.count));
    const minCount = Math.min(...this.data.map((d) => d.count));
    const stepX = this.width / (this.data.length - 1);

    const scaleY = (count: number): number => {
      const normalized = (count - minCount) / (maxCount - minCount);
      return this.height - normalized * this.height;
    };

    const points = this.data.map((d, i) => ({
      x: i * stepX,
      y: scaleY(d.count),
    }));

    const path = [`M ${points[0].x},${points[0].y}`];

    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const cx = (prev.x + curr.x) / 2;
      path.push(`Q ${prev.x},${prev.y} ${cx},${(prev.y + curr.y) / 2}`);
    }

    const last = points[points.length - 1];
    path.push(`T ${last.x},${last.y}`);

    this.pathD = path.join(' ');
  }
}
