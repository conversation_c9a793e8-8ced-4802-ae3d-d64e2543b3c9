import { Component, Inject, Input } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { IComplianceFilesShowMore } from '@/dlp_dashboard/mock_data_structure';

@Component({
  selector: 'app-dialog',
  templateUrl: './dialog.component.html',
  styleUrls: ['./dialog.component.css'],
})
export class DialogComponent {
  @Input() title: string
  allData: IComplianceFilesShowMore;

  constructor(
    public dialogRef: MatDialogRef<DialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IComplianceFilesShowMore
  ) {
    this.allData = data;
  }

  close() {
    this.dialogRef.close();
  }
}
