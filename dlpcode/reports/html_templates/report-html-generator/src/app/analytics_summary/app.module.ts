import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppComponent } from './app.component';

import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { Observable, of } from 'rxjs';
import { TRANSLATIONS_EN } from './i18n_en';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateService } from '@/common/translate/translate.service';
import { MatIconModule } from '@angular/material/icon';
import { IncidentsBySeverityComponent } from './incidents-by-severity/incidents-by-severity.component';
import { ComplianceFileTypeComponent } from './compliance-file-type/compliance-file-type.component';
import { IncidentsByPolicyComponent } from './incidents-by-policy/incidents-by-policy.component';
import { FilesByTypeComponent } from './files-by-type/files-by-type.component';
import { AiCategoryComponent } from './ai-category/ai-category.component';
import { IncidentsByPolicyShowMoreComponent } from './incidents-by-policy-show-more/incidents-by-policy-show-more.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { ChartToSvgComponent } from './chart-to-svg/chart-to-svg.component';
import { MatDialogModule } from '@angular/material/dialog';
import { DialogComponent } from './dialog/dialog.component';
import { IncidentsByPolicyDialogComponent } from './incidents-by-policy/incidents-by-policy-dialog/incidents-by-policy-dialog.component';
import { ComplianceFileTypeShowMoreComponent } from './compliance-file-type-show-more/compliance-file-type-show-more.component';
import { ShowMoreDialogComponent } from './compliance-file-type/show-more-dialog/show-more-dialog.component';
import { AiCategoryShowMoreComponent } from './ai-category-show-more/ai-category-show-more.component';
import { AiCategoryShowMoreDialogComponent } from './ai-category/ai-category-show-more-dialog/ai-category-show-more-dialog.component';
import { SensitiveFileByOwnerComponent } from './sensitive-file-by-owner/sensitive-file-by-owner.component';
import { SensitiveFileByOwnerMoreComponent } from './sensitive-file-by-owner-more/sensitive-file-by-owner-more.component';
import { SharedSensitiveFileByOwnerComponent } from './shared-sensitive-file-by-owner/shared-sensitive-file-by-owner.component';
import { SharedSensitiveFileByOwnerMoreComponent } from './shared-sensitive-file-by-owner-more/shared-sensitive-file-by-owner-more.component';
import { DormatSensitiveFileComponent } from './dormat-sensitive-file/dormat-sensitive-file.component';
import { DormatSensitiveFileMoreComponent } from './dormat-sensitive-file-more/dormat-sensitive-file-more.component';
import { MatSelectModule } from '@angular/material/select';
import { SensitiveFileShowMoreDialogComponent } from './sensitive-file-by-owner/show-more-dialog/show-more-dialog.component';
import { SharedSensitiveShowMoreDialogComponent } from './shared-sensitive-file-by-owner/show-more-dialog/show-more-dialog.component';
import { DormatShowMoreDialogComponent } from './dormat-sensitive-file/show-more-dialog/show-more-dialog.component';

class InlineTranslateLoader implements TranslateLoader {
  getTranslation(lang: string): Observable<any> {
    switch (lang) {
      case 'en':
        return of(TRANSLATIONS_EN);
      default:
        return of({});
    }
  }
}

function appInitializerFactory(translateService: TranslateService) {
  return () => translateService.switchLanguage('en');
}

@NgModule({
  declarations: [
    AppComponent,
    IncidentsBySeverityComponent,
    ComplianceFileTypeComponent,
    IncidentsByPolicyComponent,
    FilesByTypeComponent,
    AiCategoryComponent,
    IncidentsByPolicyShowMoreComponent,
    DialogComponent,
    ChartToSvgComponent,
    IncidentsByPolicyDialogComponent,
    ComplianceFileTypeShowMoreComponent,
    ShowMoreDialogComponent,
    AiCategoryShowMoreComponent,
    AiCategoryShowMoreDialogComponent,
    SensitiveFileByOwnerComponent,
    SensitiveFileByOwnerMoreComponent,
    SharedSensitiveFileByOwnerComponent,
    SharedSensitiveFileByOwnerMoreComponent,
    DormatSensitiveFileComponent,
    DormatSensitiveFileMoreComponent,
    SensitiveFileShowMoreDialogComponent,
    SharedSensitiveShowMoreDialogComponent,
    DormatShowMoreDialogComponent
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    MatIconModule,
    HighchartsChartModule,
    MatCheckboxModule,
    MatDialogModule,
    MatIconModule,
    MatSelectModule,
    MatDividerModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useClass: InlineTranslateLoader,
      },
    }),
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true,
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
