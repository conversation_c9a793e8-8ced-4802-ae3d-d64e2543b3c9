import { IFile } from './mock_data_structure';

export const mockData: IFile = {
  is_printing: false,
  report_info: {
    name: 'Analytics_files_test_report',
    schedule: 'Weekly Monday, at 11:20 AM PDT',
    period: 'Last 7 days',
    created_by: 'Test',
    generated_on: '2023-10-01 12:00 PM',
    notes: 'This is a test report for analytics files.',
    scan_name: 'Test Scan',
    scan_storage: '10.255.255.176',
    storage_type: 4,
    timezone: 'America/Vancouver',
    firmware_version: 'FortiData-KVM 2.1.0 build0001 20250807'
  },
  table_data: [
    {
      collaborators: [],
      copy_status: 0,
      custom_match_count: 0,
      endpoint: '',
      file_attributes: {
        drive_id: 'N/A',
        file_cat: 'text files',
        file_display_path: 'public/Emily/email.txt',
        file_encryption: null,
        file_ext: 'txt',
        file_location: 'UNKNOWN',
        file_size: 0.03125,
        file_user_email: 'UNKNOWN',
        file_user_id: 'UNKNOWN',
        file_user_name: 'UNKNOWN',
        last_modified: '2025-05-21 23:07:38',
        owner: {
          email: 'UNKNOWN',
          name: 'UNKNOWN'
        },
        site_id: 'N/A'
      },
      file_tag: {
        custom: [],
        predefine: []
      },
      file_uuid: 'af4a6d2e-36e4-4f26-9d8f-03febd8cebfb',
      filename: 'email.txt',
      full_path: '/Emily/email.txt',
      id: 'af4a6d2e-36e4-4f26-9d8f-03febd8cebfb',
      last_scanned_time: 1754536450,
      links: { public: ['test1'] },
      main_class_confidence: 60,
      main_class_id: '60000',
      owner: [],
      quarantine_status: 0,
      scan_id: 'da443d9a-9917-4490-bbe2-6bf163713ec6',
      standard_match_count: 0,
      storage_id: '2c0cbbe5-05e7-4e9b-9eac-403389fce1ff',
      storage_type: 4,
      sub_class_confidence: 60,
      sub_class_id: '',
      update_time: 1754536450
    },
    {
      collaborators: [],
      copy_status: 0,
      custom_match_count: 0,
      endpoint: '',
      file_attributes: {
        drive_id: 'N/A',
        file_cat: 'office documents',
        file_display_path: 'public/Emily/SSN-US.docx',
        file_encryption: null,
        file_ext: 'docx',
        file_location: 'UNKNOWN',
        file_size: 12.4404296875,
        file_user_email: 'UNKNOWN',
        file_user_id: 'UNKNOWN',
        file_user_name: 'UNKNOWN',
        last_modified: '2025-05-23 18:00:07',
        owner: {
          email: 'UNKNOWN',
          name: 'UNKNOWN'
        },
        site_id: 'N/A'
      },
      file_tag: {
        custom: [],
        predefine: []
      },
      file_uuid: '085552a6-2872-4d70-896e-29187d56e582',
      filename: 'SSN-US.docx',
      full_path: '/Emily/SSN-US.docx',
      id: '085552a6-2872-4d70-896e-29187d56e582',
      last_scanned_time: 1754536450,
      links: {},
      main_class_confidence: 60,
      main_class_id: '60000',
      owner: [],
      quarantine_status: 0,
      scan_id: 'da443d9a-9917-4490-bbe2-6bf163713ec6',
      standard_match_count: 0,
      storage_id: '2c0cbbe5-05e7-4e9b-9eac-403389fce1ff',
      storage_type: 4,
      sub_class_confidence: 60,
      sub_class_id: '',
      update_time: 1754536450
    },
    {
      collaborators: [],
      copy_status: 0,
      custom_match_count: 0,
      endpoint: '',
      file_attributes: {
        drive_id: 'N/A',
        file_cat: 'text files',
        file_display_path: 'public/Emily/temp/test1.txt',
        file_encryption: null,
        file_ext: 'txt',
        file_location: 'UNKNOWN',
        file_size: 0.009765625,
        file_user_email: 'UNKNOWN',
        file_user_id: 'UNKNOWN',
        file_user_name: 'UNKNOWN',
        last_modified: '2025-08-01 23:44:56',
        owner: {
          email: 'UNKNOWN',
          name: 'UNKNOWN'
        },
        site_id: 'N/A'
      },
      file_tag: {
        custom: [],
        predefine: []
      },
      file_uuid: '163ea95d-fc34-4990-892c-20589a1a6701',
      filename: 'test1.txt',
      full_path: '/Emily/temp/test1.txt',
      id: '163ea95d-fc34-4990-892c-20589a1a6701',
      last_scanned_time: 1754536450,
      links: {},
      main_class_confidence: 60,
      main_class_id: '60000',
      owner: [],
      quarantine_status: 0,
      scan_id: 'da443d9a-9917-4490-bbe2-6bf163713ec6',
      standard_match_count: 0,
      storage_id: '2c0cbbe5-05e7-4e9b-9eac-403389fce1ff',
      storage_type: 4,
      sub_class_confidence: 60,
      sub_class_id: '',
      update_time: 1754536450
    },
    {
      collaborators: [],
      copy_status: 0,
      custom_match_count: 0,
      endpoint: '',
      file_attributes: {
        drive_id: 'N/A',
        file_cat: 'text files',
        file_display_path: 'public/Emily/temp/test2.txt',
        file_encryption: null,
        file_ext: 'txt',
        file_location: 'UNKNOWN',
        file_size: 0.005859375,
        file_user_email: 'UNKNOWN',
        file_user_id: 'UNKNOWN',
        file_user_name: 'UNKNOWN',
        last_modified: '2025-08-01 23:45:05',
        owner: {
          email: 'UNKNOWN',
          name: 'UNKNOWN'
        },
        site_id: 'N/A'
      },
      file_tag: {
        custom: [],
        predefine: []
      },
      file_uuid: '4e616062-3359-4619-b9a6-d8aaa0e3ab36',
      filename: 'test2.txt',
      full_path: '/Emily/temp/test2.txt',
      id: '4e616062-3359-4619-b9a6-d8aaa0e3ab36',
      last_scanned_time: 1754536450,
      links: {},
      main_class_confidence: 60,
      main_class_id: '60000',
      owner: [],
      quarantine_status: 0,
      scan_id: 'da443d9a-9917-4490-bbe2-6bf163713ec6',
      standard_match_count: 0,
      storage_id: '2c0cbbe5-05e7-4e9b-9eac-403389fce1ff',
      storage_type: 4,
      sub_class_confidence: 60,
      sub_class_id: '',
      update_time: 1754536450
    },
    {
      collaborators: [],
      copy_status: 0,
      custom_match_count: 0,
      endpoint: '',
      file_attributes: {
        drive_id: 'N/A',
        file_cat: 'text files',
        file_display_path:
          'public/Emily/10.255.255.22_admin_59_GETOBJECTURLGROUPBAIDUGOOFORITPOSTSETTINGLIST--Response.json',
        file_encryption: null,
        file_ext: 'json',
        file_location: 'UNKNOWN',
        file_size: 60.7734375,
        file_user_email: 'UNKNOWN',
        file_user_id: 'UNKNOWN',
        file_user_name: 'UNKNOWN',
        last_modified: '2025-05-13 23:07:08',
        owner: {
          email: 'UNKNOWN',
          name: 'UNKNOWN'
        },
        site_id: 'N/A'
      },
      file_tag: {
        custom: [],
        predefine: []
      },
      file_uuid: '0449b4f6-7721-48b1-8404-a370276f5082',
      filename:
        '10.255.255.22_admin_59_GETOBJECTURLGROUPBAIDUGOOFORITPOSTSETTINGLIST--Response.json',
      full_path:
        '/Emily/10.255.255.22_admin_59_GETOBJECTURLGROUPBAIDUGOOFORITPOSTSETTINGLIST--Response.json',
      id: '0449b4f6-7721-48b1-8404-a370276f5082',
      last_scanned_time: 1754536450,
      links: {},
      main_class_confidence: 93,
      main_class_id: '90000',
      owner: [],
      quarantine_status: 0,
      scan_id: 'da443d9a-9917-4490-bbe2-6bf163713ec6',
      standard_match_count: 0,
      storage_id: '2c0cbbe5-05e7-4e9b-9eac-403389fce1ff',
      storage_type: 4,
      sub_class_confidence: 93,
      sub_class_id: '90008',
      update_time: 1754536450
    }
  ],
  supported_type_categories: {
    type_category: [
      {
        description:
          'Personal data refers to information that can directly or indirectly identify an individual. This data can be used to recognize a specific person and may include contact information, identity identifiers, online identifiers, and more.',
        id: '20001',
        name: 'Personal Data'
      },
      {
        description:
          'Financial data includes information related to an individual\u2019s or a business\u2019s financial transactions, accounts, and activities.',
        id: '20002',
        name: 'Financial Data'
      },
      {
        description:
          'Business data refers to information related to the operation, management, and strategy of a business.',
        id: '20004',
        name: 'Business Data'
      },
      {
        description:
          'Credential data refers to authentication details used to verify the identity of users, systems, or services. These typically include usernames and passwords but can also involve tokens, API keys, digital certificates, or multi-factor authentication methods.',
        id: '20005',
        name: 'Credential Data'
      },
      {
        description:
          'PII refers to any information that can be used to identify a specific individual. This can include direct identifiers, such as a name or social security number, as well as indirect identifiers, which, when combined with other information, can reveal the identity of an individual.',
        id: '10001',
        name: 'PII'
      },
      {
        description:
          'PCI refers to any information related to payment cards (credit cards, debit cards, etc.) that is used in financial transactions. PCI DSS (Payment Card Industry Data Security Standard) is a set of security standards designed to ensure that all companies that accept, process, store, or transmit credit card information maintain a secure environment.',
        id: '10002',
        name: 'PCI'
      },
      {
        description:
          'PHI refers to any information in a medical context that can identify an individual and relates to their health status, provision of healthcare, or payment for healthcare services. PHI is protected under the Health Insurance Portability and Accountability Act (HIPAA) in the United States.',
        id: '10003',
        name: 'PHI'
      }
    ]
  },
  standard_data_type: [
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'ASP.NET Machine Key',
      description:
        'The ASP.NET Machine Key is a configuration element used to specify the encryption and validation keys for securing data such as authentication tokens, view state, and cookies in ASP.NET applications.',
      entity: 'MK_ASPNET',
      id: '492',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'AWS Access Key',
      description:
        'An AWS Access Key is a unique set of credentials consisting of an Access Key ID and a Secret Access Key, used to authenticate and authorize programmatic access to Amazon Web Services (AWS) resources and services.',
      entity: 'AWS_ACCESS_KEY',
      id: '336',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726787325.0
    },
    {
      continent: ['ALL'],
      create_time: 1726787441.0,
      data_type: 'AWS Secret Access Key',
      description:
        'An AWS Secret Access Key is a confidential part of AWS credentials, paired with an Access Key ID, used to securely sign API requests and authenticate programmatic access to AWS services; it must be kept private to protect your AWS resources.',
      entity: 'AWS_SECRET_ACCESS_KEY',
      id: '337',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726787487.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Age',
      description: 'The age of a person.',
      entity: 'PERSONAL_AGE',
      id: '3',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1733349705.0
    },
    {
      continent: ['ALL'],
      create_time: 1736203644.0,
      data_type: 'Alibaba Access Key ID',
      description:
        'The Alibaba Access Key ID is a unique identifier used alongside the Access Key Secret to authenticate API requests and enable secure access to Alibaba Cloud services.',
      entity: 'ALIBABA_AK',
      id: '407',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736203750.0
    },
    {
      continent: ['ALL'],
      create_time: 1736203845.0,
      data_type: 'Alibaba Secret Key',
      description:
        'The Alibaba Secret Key, also known as the Access Key Secret, is a confidential credential paired with the Access Key ID to securely authenticate and authorize API requests to Alibaba Cloud services.',
      entity: 'ALIBABA_SK',
      id: '408',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'American Bank Association Routing Number',
      description:
        'An American Bank Association (ABA) Routing Number is a unique 9-digit code assigned to banks and financial institutions in the United States to identify them in the processing of checks, electronic funds transfers, and other transactions.',
      entity: 'FI_ABA',
      id: '244',
      language: ['en'],
      region: ['US'],
      type: 'Regex + Validation Algorithms',
      type_category: ['20002'],
      update_time: **********.0
    },
    {
      continent: ['NA', 'NA'],
      create_time: **********.0,
      data_type: 'American Bankers CUSIP ID',
      description:
        'An American Bankers CUSIP ID is a unique 9-character alphanumeric code assigned by the Committee on Uniform Securities Identification Procedures (CUSIP) to identify U.S. and Canadian registered stocks, bonds, and other securities for the purpose of facilitating clearing and settlement.',
      entity: 'FINANCIAL_CUSIP',
      id: '240',
      language: ['en'],
      region: ['US', 'CA'],
      type: 'Regex',
      type_category: ['20002'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: "Argentina Driver's License Number",
      description:
        "An Argentina driver's license number is a unique alphanumeric identifier assigned to an individual's driver's license issued by the Argentine government.",
      entity: 'DL_ARGENTINA',
      id: '140',
      language: ['en'],
      region: ['AR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Argentina National Identity (DNI) Number',
      description:
        'A unique identifier assigned to individuals in Argentina upon issuance of their Documento Nacional de Identidad (DNI).',
      entity: 'NATIONAL_ID_ARGENTINA',
      id: '25',
      language: ['en'],
      region: ['AR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Argentina Passport Number',
      description:
        "An Argentina passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the Argentine government.",
      entity: 'PASSPORT_ARGENTINA',
      id: '136',
      language: ['en'],
      region: ['AR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721856740.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Argentina Unique Tax Identification',
      description:
        'In Argentina, the Unique Tax Identification Number (CUIT, C\u00f3digo \u00danico de Identificaci\u00f3n Tributaria) is a unique number assigned to individuals and entities for tax purposes.',
      entity: 'TAX_ID_AR',
      id: '200',
      language: ['en'],
      region: ['AR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1728423435.0
    },
    {
      continent: ['ALL'],
      create_time: 1737508328.0,
      data_type: 'Asana Client ID',
      description:
        'The Asana Client ID is a unique identifier used to authenticate and integrate third-party applications with the Asana API, enabling secure access to project and task data.',
      entity: 'ASANA_CI',
      id: '432',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1737508352.0
    },
    {
      continent: ['ALL'],
      create_time: 1737509534.0,
      data_type: 'Asana Client Secret',
      description:
        'The Asana Client Secret is a confidential key used alongside the Client ID to securely authenticate third-party applications with the Asana API, ensuring authorized access to protected resources.',
      entity: 'ASANA_CS',
      id: '433',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'Australia Bank Account Number',
      description:
        "A unique identifier assigned to an individual's or entity's bank account in Australia.",
      entity: 'BANK_ACCOUNT_NUMBER_AU',
      id: '185',
      language: ['en'],
      region: ['AU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'Australia Bank-State-Branch (BSB) Number',
      description:
        'An Australia Bank-State-Branch (BSB) Number is a 6-digit numeric code used to identify banks and branches in Australia for domestic electronic funds transfers (EFT), direct debits, and BPAY payments.',
      entity: 'BSB_AUSTRALIA',
      id: '485',
      language: ['en'],
      region: ['AU'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'Australian Business Number(ABN)',
      description:
        'An Australian Business Number (ABN) is an 11-digit unique identifier issued by the Australian Business Register (ABR) to businesses and organizations for tax and business purposes, facilitating interactions with government agencies and other businesses.',
      entity: 'BUSINESS_ID_AU_ABN',
      id: '241',
      language: ['en'],
      region: ['AU'],
      type: 'Regex + Validation Algorithms',
      type_category: ['20004'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'Australian Company Number',
      description:
        'An Australian Company Number (ACN) is a unique nine-digit identifier issued by the Australian Securities and Investments Commission (ASIC) to every registered company in Australia for legal and regulatory purposes.',
      entity: 'BUSINESS_ID_AU_ACN',
      id: '242',
      language: ['en'],
      region: ['AU'],
      type: 'Regex + Validation Algorithms',
      type_category: ['20004'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: "Australian Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Australia upon issuance of their driver's license.",
      entity: 'DL_AUSTRALIA',
      id: '66',
      language: ['en'],
      region: ['AU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'Australian Medicare card number',
      description:
        "An Australian Medicare card number is a unique identifier assigned to individuals enrolled in Australia's Medicare program, which provides access to healthcare services.",
      entity: 'HEALTH_ID_AUSTRALIAN_MEDICARE',
      id: '213',
      language: ['en'],
      region: ['AU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'Australian Passport Number',
      description:
        'A unique identifier assigned to individuals in Australia upon issuance of their passport',
      entity: 'PASSPORT_AUSTRALIA',
      id: '117',
      language: ['en'],
      region: ['AU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'Australian Tax File Number',
      description:
        'A unique identifier issued by the Australian Taxation Office (ATO) to individuals and organizations for tax purposes.',
      entity: 'TAX_ID_AUSTRALIA',
      id: '178',
      language: ['en'],
      region: ['AU'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1721428314.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Austria Identity Card',
      description:
        'An Austria Identity Card, known as the "Personalausweis," is a government-issued document used to verify an individual\'s identity within Austria.',
      entity: 'NATIONAL_ID_AUSTRIA',
      id: '146',
      language: ['en'],
      region: ['AT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1728077598.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Austria Social Security Number',
      description:
        'An Austria Social Security Number (Sozialversicherungsnummer) is a unique identifier assigned to individuals for social security purposes in Austria.',
      entity: 'SSN_AUSTRIA',
      id: '147',
      language: ['en'],
      region: ['AT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721434713.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Austria Tax Identification Number',
      description:
        'An Austria Tax Identification Number (TIN), known as the "Steuerliche Identifikationsnummer" (StNr or TIN), is a unique identifier assigned to individuals and entities for tax purposes by the Austrian tax authorities.',
      entity: 'TAX_ID_AUSTRIA',
      id: '191',
      language: ['en'],
      region: ['AT'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Austrian Bank Account Number',
      description:
        'An Austrian bank account number is a unique identifier assigned to a specific bank account in Austria.',
      entity: 'BANK_ACCOUNT_NUMBER_AT',
      id: '247',
      language: ['en'],
      region: ['AT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Austrian Driver's License Number",
      description:
        "An Austrian driver's license number is a unique alphanumeric identifier assigned to an individual's driver's license issued by the Austrian government.",
      entity: 'DL_AUSTRIA',
      id: '145',
      language: ['en'],
      region: ['AT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Austrian Passport Number',
      description:
        "An Austrian passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the Austrian government.",
      entity: 'PASSPORT_AUSTRIA',
      id: '144',
      language: ['en'],
      region: ['AT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Austrian VAT Identification Number',
      description:
        'An Austrian VAT Identification Number (UID) is a unique alphanumeric code assigned to businesses in Austria for the purpose of identifying them in transactions subject to Value Added Tax (VAT), facilitating VAT reporting, and ensuring compliance with European Union tax regulations.',
      entity: 'VAT_AUSTRIA',
      id: '248',
      language: ['en'],
      region: ['AT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721431303.0
    },
    {
      continent: ['ALL'],
      create_time: 1727131277.0,
      data_type: 'Azure Credential',
      description:
        'An Azure credential is a secure authentication token or key, used to grant access to Azure services, resources, and APIs, ensuring authorized and managed access within cloud environments.',
      entity: 'AZURE_CREDENTIAL',
      id: '357',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1727301500.0
    },
    {
      continent: ['ALL'],
      create_time: 1739235019.0,
      data_type: 'Beamer API Token',
      description:
        "The Beamer API Token is a secure authentication key that enables seamless integration and access to Beamer's notification and communication services for developers and applications.",
      entity: 'BEAMER_AT',
      id: '446',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Belgium Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Belgium upon issuance of their driver's license.",
      entity: 'DL_BELGIUM',
      id: '67',
      language: ['en'],
      region: ['BE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Belgium National Number',
      description:
        'A unique identifier assigned to individuals in Belgium for identification and administrative purposes.',
      entity: 'NATIONAL_ID_BELGIUM',
      id: '48',
      language: ['en'],
      region: ['BE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Belgium Passport Number',
      description:
        'A unique identifier assigned to individuals in Belgium upon issuance of their passport',
      entity: 'PASSPORT_BELGIUM',
      id: '116',
      language: ['en'],
      region: ['BE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721845563.0
    },
    {
      continent: ['EU'],
      create_time: 1740704594.0,
      data_type: 'Belgium Tax Identification Number',
      description:
        'The Belgium Tax Identification Number (TIN) is a unique 10-digit identifier assigned to Belgian citizens and residents for tax, social security, and administrative purposes.',
      entity: 'TIN_BELGIUM',
      id: '451',
      language: ['en'],
      region: ['BE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1740704632.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Belgium VAT Identification Number',
      description:
        'A Belgian VAT Identification Number (UID) is a unique alphanumeric code assigned to businesses in Belgium for the purpose of identifying them in transactions subject to Value Added Tax (VAT), facilitating VAT reporting, and ensuring compliance with European Union tax regulations.',
      entity: 'VAT_BELGIUM',
      id: '249',
      language: ['en'],
      region: ['BE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Bing Maps Key',
      description:
        'A Bing Maps Key is an API key required to authenticate requests to Bing Maps Services, including geocoding, routing, and map rendering.',
      entity: 'MK_BING',
      id: '493',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Bitbucket Client ID',
      description:
        "A Bitbucket Client ID is a unique identifier used to authenticate and authorize third-party applications to access a user's Bitbucket account securely.",
      entity: 'BITBUCKET_CI',
      id: '440',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Bitbucket Client Secret',
      description:
        "A Bitbucket Client Secret is a confidential key used in conjunction with the Client ID to securely authenticate and authorize third-party applications to access a user's Bitbucket account.",
      entity: 'BITBUCKET_CS',
      id: '441',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Bosnia and Herzegovina Unique Master Citizen Number',
      description:
        'The Unique Master Citizen Number (JMBG) is a 13-digit personal identification number used in Bosnia and Herzegovina and some other former Yugoslav republics to uniquely identify individuals.',
      entity: 'JMBG_BA',
      id: '452',
      language: ['en', 'bs', 'hr', 'sr'],
      region: ['BA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Brazil Bank Account Number',
      description:
        'he Brazil Bank Account Number is a unique identifier used by financial institutions in Brazil to identify individual bank accounts, typically consisting of a branch code, account number, and sometimes a check digit to ensure accuracy.',
      entity: 'BR_BAN',
      id: '299',
      language: ['en', 'pt'],
      region: ['BR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Brazil CNPJ (National Registry of Legal Entities)',
      description:
        'The Brazil CNPJ (Cadastro Nacional da Pessoa Jur\u00eddica) is a unique 14-digit identifier assigned to legal entities operating in Brazil.',
      entity: 'TAX_ID_BRAZIL_CNPJ',
      id: '189',
      language: ['en'],
      region: ['BR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Brazil Election Identification Number',
      description:
        'A Brazil Election Identification Number, known as the "N\u00famero de T\u00edtulo de Eleitor," is a unique identifier assigned to individuals eligible to vote in Brazilian elections.',
      entity: 'BR_ELECTION_ID',
      id: '250',
      language: ['en'],
      region: ['BR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721669276.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Brazil National Identification Card (RG) Number',
      description:
        'A unique identifier assigned to individuals in Brazil upon issuance of their Registro Geral (RG), which is the national identification card.',
      entity: 'NATIONAL_ID_BRAZIL_RG',
      id: '22',
      language: ['en'],
      region: ['BR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720204076.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Brazil Natural Person Registry Number (CPF)',
      description:
        'A unique identifier assigned to Brazilian citizens and resident aliens for tax and financial purposes.',
      entity: 'NATIONAL_ID_BRAZIL_CPF',
      id: '26',
      language: ['en'],
      region: ['BR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Bulgaria Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Bulgaria upon issuance of their driver's license.",
      entity: 'DL_BULGARIA',
      id: '68',
      language: ['en'],
      region: ['BG'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Bulgaria Passport Number',
      description:
        'A unique identifier assigned to individuals in Bulgaria upon issuance of their passport',
      entity: 'PASSPORT_BULGARIA',
      id: '115',
      language: ['en'],
      region: ['BG'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Bulgaria Uniform Civil Number (EGN)',
      description:
        'A unique identifier assigned to individuals in Bulgaria for identification and administrative purposes.',
      entity: 'NATIONAL_ID_BULGARIA_EGN',
      id: '47',
      language: ['en'],
      region: ['BG'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Canada Bank Account Number',
      description:
        "A unique identifier assigned to an individual's or entity's bank account in Canada.",
      entity: 'BANK_ACCOUNT_NUMBER_CA',
      id: '184',
      language: ['en'],
      region: ['CA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: "Canada Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Canada upon issuance of their driver's license.",
      entity: 'DL_CA',
      id: '64',
      language: ['en'],
      region: ['CA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Canada Health Service Number',
      description:
        'The Canada Health Service Number (HSN) is a unique personal identification number assigned to residents by their provincial or territorial health insurance plans for accessing publicly funded healthcare services.',
      entity: 'HSN_CANADA',
      id: '477',
      language: ['en', 'fr'],
      region: ['CA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Canada Passport Number',
      description:
        'A unique identifier assigned to individuals in Canada upon issuance of their passport',
      entity: 'PASSPORT_CANADA',
      id: '126',
      language: ['en'],
      region: ['CA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Canada Permanent Resident Number',
      description:
        'A Canada Permanent Resident Number is a unique identifier assigned to individuals who have been granted permanent residency status in Canada.',
      entity: 'NATIONAL_ID_CANADA_PRN',
      id: '152',
      language: ['en'],
      region: ['CA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Canada Routing Number',
      description:
        'A Canada Routing Number is an 8-digit numeric code used to identify banks and branches in Canada for electronic funds transfers (EFT), direct deposits, bill payments, and wire transfers.',
      entity: 'RN_CANADA',
      id: '486',
      language: ['en'],
      region: ['CA'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Canada Social Insurance Number (SIN)',
      description:
        'A number issued by the Government of Canada to Canadian citizens, permanent residents, and temporary residents who are eligible to work in Canada.',
      entity: 'SSN_CANADA_SIN',
      id: '16',
      language: ['en'],
      region: ['CA'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Canada Zip Code',
      description:
        'An alphanumeric code used by Canada Post to identify specific geographic locations.',
      entity: 'ZIP_CANADA',
      id: '11',
      language: ['en'],
      region: ['CA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1729891399.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Chile Identity Card Number',
      description:
        'A unique identifier assigned to individuals in Chile upon issuance of their national identity card (C\u00e9dula de Identidad).',
      entity: 'NATIONAL_ID_CHILE',
      id: '27',
      language: ['en'],
      region: ['CL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1728334730.0
    },
    {
      continent: ['AS'],
      create_time: 1741814763.0,
      data_type: 'China Business Registration Number',
      description:
        'The China Business Registration Number is a 18-character Unified Social Credit Code (USCC, \u7edf\u4e00\u793e\u4f1a\u4fe1\u7528\u4ee3\u7801) assigned to businesses and organizations in China for legal identification, taxation, and regulatory compliance.',
      entity: 'BRN_CHINA',
      id: '476',
      language: ['en'],
      region: ['CN'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741814901.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'China Passport Number',
      description:
        'A unique identifier assigned to individuals in China upon issuance of their passport',
      entity: 'PASSPORT_CHINA',
      id: '123',
      language: ['en'],
      region: ['CN'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720462347.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'China Resident Identity Card (PRC) Number',
      description:
        'An identifier assigned to Chinese citizens upon issuance of their Resident Identity Card (\u8eab\u4efd\u8bc1)',
      entity: 'NATIONAL_ID_CHINA',
      id: '23',
      language: ['en'],
      region: ['CN'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1722380663.0
    },
    {
      continent: ['ALL'],
      create_time: 1739236744.0,
      data_type: 'Clojars API Token',
      description:
        "The Clojars API Token is a secure authentication key that allows developers to publish, manage, and interact with Clojars' repository of Clojure libraries programmatically.",
      entity: 'CLOJARS_AT',
      id: '447',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1740766513.0
    },
    {
      continent: ['ALL'],
      create_time: 1739323615.0,
      data_type: 'Codecov Access Token',
      description:
        "The Codecov Access Token is a secure authentication key that allows developers to integrate and interact with Codecov's code coverage services, enabling automated reporting and analysis of test coverage data.",
      entity: 'CODECOV_AT',
      id: '448',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Coinbase API Token',
      description:
        "The Coinbase API Token is a secure credential used to authenticate requests and enable applications to interact with Coinbase's API for accessing cryptocurrency account data, transactions, and other services.",
      entity: 'COINBASE_AT',
      id: '406',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Colombia Citizen Identification Number',
      description:
        'The Colombia Citizen Identification Number is a unique numeric identifier assigned to Colombian citizens, officially known as the C\u00e9dula de Ciudadan\u00eda. It is the primary national identification number for legal, financial, and administrative purposes.',
      entity: 'CIN_COLOMBIA',
      id: '453',
      language: ['en', 'es'],
      region: ['CO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Colombia Foreigner Identification Number',
      description:
        'The Colombia Foreigner Identification Number is a unique personal identifier assigned to foreign residents in Colombia, officially known as the C\u00e9dula de Extranjer\u00eda. It serves as the primary identification document for non-citizens living in the country.',
      entity: 'FIN_COLOMBIA',
      id: '454',
      language: ['en', 'es'],
      region: ['CO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741223224.0
    },
    {
      continent: ['SA'],
      create_time: 1741225946.0,
      data_type: 'Colombia Minor\u2019s Identification Number',
      description:
        'The Colombia Minor\u2019s Identification Number (Tarjeta de Identidad) is a unique identification number assigned to Colombian minors aged 7 to 17, issued by the National Civil Registry (Registradur\u00eda Nacional del Estado Civil).',
      entity: 'MIN_COLOMBIA',
      id: '455',
      language: ['en', 'es'],
      region: ['CO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741225984.0
    },
    {
      continent: ['SA'],
      create_time: 1741287241.0,
      data_type: 'Colombia NUIP',
      description:
        'The NUIP (N\u00famero \u00danico de Identificaci\u00f3n Personal) is a unique identification number assigned to Colombian citizens from birth, used throughout their life for official identification.',
      entity: 'NUIP_COLOMBIA',
      id: '456',
      language: ['en', 'es'],
      region: ['CO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741287269.0
    },
    {
      continent: ['SA'],
      create_time: 1741299998.0,
      data_type: 'Colombia Tax Identification Number',
      description:
        'The Colombia NIT (N\u00famero de Identificaci\u00f3n Tributaria) is a unique tax identification number assigned to individuals and legal entities by the DIAN (Direcci\u00f3n de Impuestos y Aduanas Nacionales) for tax purposes.',
      entity: 'TIN_COLOMBIA',
      id: '457',
      language: ['en', 'es'],
      region: ['CO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741300027.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Compensation',
      description: 'Compensation',
      entity: 'FINANCIAL_COMPENSATION',
      id: '237',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20002'],
      update_time: 1725908486.0
    },
    {
      continent: ['NA'],
      create_time: 1741304445.0,
      data_type: 'Costa Rica Entity Legal Identification Number',
      description:
        'The Costa Rica Entity Legal Identification Number, also known as the C\u00e9dula Jur\u00eddica, is a unique identification number assigned to businesses, organizations, and legal entities in Costa Rica for tax, legal, and regulatory purposes.',
      entity: 'ELIN_COSTARICA',
      id: '458',
      language: ['en', 'es'],
      region: ['CR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741304471.0
    },
    {
      continent: ['NA'],
      create_time: 1741312100.0,
      data_type: 'Costa Rica Personal Identification Number',
      description:
        'The Costa Rica Personal Identification Number (C\u00e9dula de Identidad) is a unique numeric identifier assigned to Costa Rican citizens and residents for official identification.',
      entity: 'PIN_COSTARICA',
      id: '459',
      language: ['en', 'es'],
      region: ['CR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741312127.0
    },
    {
      continent: ['ALL'],
      create_time: 1741890978.0,
      data_type: 'Credit Card Expiry Date',
      description:
        'A Credit Card Expiry Date is a 4-digit numeric date (MM/YY) printed on a payment card, indicating the month and year when the card becomes invalid for transactions.',
      entity: 'ED_CC',
      id: '483',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Number Of American Express',
      description:
        'A Credit Card Number of American Express is a unique 15-digit identifier assigned to an American Express credit card, typically starting with the digits "34" or "37," used to facilitate transactions and account management.',
      entity: 'CREDIT_CARD_AE',
      id: '218',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Number Of China UnionPay',
      description:
        'A Credit Card Number of China UnionPay is a unique identifier assigned to a UnionPay credit card, typically consisting of 16 to 19 digits and starting with the digits "62," used for processing transactions and account management.',
      entity: 'CREDIT_CARD_UNION',
      id: '219',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Number Of Diners Club',
      description:
        'The Credit Card Number of Diners Club refers to the unique identification number associated with a Diners Club International credit card, offering cardholders access to a wide range of global travel, dining, and financial benefits.',
      entity: 'CREDIT_CARD_DC',
      id: '444',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Number Of Discover',
      description:
        'A Credit Card Number of Discover is a unique 16-digit identifier assigned to a Discover credit card, typically starting with the digits "6011," "622," "644," or "65," used for facilitating transactions and account management.',
      entity: 'CREDIT_CARD_DISCOVER',
      id: '220',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Number Of JCB',
      description:
        'A Credit Card Number of JCB (Japan Credit Bureau) is a unique identifier assigned to a JCB credit card, typically consisting of 16 digits and starting with the digits "3528" to "3589," used for processing transactions and account management.',
      entity: 'CREDIT_CARD_JCB',
      id: '221',
      language: ['en', 'jp'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Number Of Maestro',
      description:
        'A Credit Card Number of Maestro is a unique identifier assigned to a Maestro debit card, typically consisting of 12 to 19 digits and starting with the digits "50," "56," "57," "58," "6," or "67," used for processing transactions and account management.',
      entity: 'CREDIT_CARD_MAESTRO',
      id: '224',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Number Of Mastercard',
      description:
        'A Credit Card Number of Mastercard is a unique identifier assigned to a Mastercard credit card, typically consisting of 16 digits and starting with the digits "51" through "55" or "2221" through "2720," used for facilitating transactions and account management.',
      entity: 'CREDIT_CARD_MASTER',
      id: '222',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Number Of Visa',
      description:
        'A Credit Card Number of Visa is a unique identifier assigned to a Visa credit card, typically consisting of 16 digits and starting with the digit "4," used for facilitating transactions and account management.',
      entity: 'CREDIT_CARD_VISA',
      id: '223',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Credit Card Verification Value',
      description:
        'A Credit Card CVV (Card Verification Value) is a 3- or 4-digit security code used to authenticate online and card-not-present transactions, enhancing payment security.',
      entity: 'CVV',
      id: '482',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Croatia Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Croatia upon issuance of their driver's license.",
      entity: 'DL_CROATIA',
      id: '69',
      language: ['en'],
      region: ['HR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Croatia Identity Card Number',
      description:
        'A unique identifier assigned to individuals in Croatia upon issuance of their identity card (osobna iskaznica).',
      entity: 'NATIONAL_ID_CROATIA',
      id: '28',
      language: ['en'],
      region: ['HR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Croatia Passport Number',
      description:
        'A unique identifier assigned to individuals in Croatia upon issuance of their passport',
      entity: 'PASSPORT_CROATIA',
      id: '114',
      language: ['en'],
      region: ['HR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722468482.0
    },
    {
      continent: ['EU'],
      create_time: 1741385297.0,
      data_type: 'Croatia Unique Master Citizen Number',
      description:
        'The Croatia Unique Master Citizen Number, known as Osobni identifikacijski broj (OIB), is a unique personal identification number assigned to Croatian citizens, residents, and legal entities for taxation, social security, and identification purposes.',
      entity: 'UMCN_CROATIA',
      id: '460',
      language: ['en', 'hr'],
      region: ['HR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: "Cyprus Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Cyprus upon issuance of their driver's license.",
      entity: 'DL_CYPRUS',
      id: '70',
      language: ['en'],
      region: ['CY'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Cyprus Identity Card',
      description:
        'A unique identifier assigned to individuals in Cyprus upon issuance of their identity card (\u0391\u03c3\u03c4\u03c5\u03bd\u03bf\u03bc\u03b9\u03ba\u03ae \u03a4\u03b1\u03c5\u03c4\u03cc\u03c4\u03b7\u03c4\u03b1, Astynomiki Tautotita).',
      entity: 'NATIONAL_ID_CYPRUS',
      id: '49',
      language: ['en'],
      region: ['CY'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Cyprus Passport Number',
      description:
        'A unique identifier assigned to individuals in Cyprus upon issuance of their passport',
      entity: 'PASSPORT_CYPRUS',
      id: '113',
      language: ['en'],
      region: ['CY'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720462122.0
    },
    {
      continent: ['AS'],
      create_time: 1713769200.0,
      data_type: 'Cyprus Tax Identification Number',
      description:
        'A Cyprus Tax Identification Number (TIN), known as the "\u0391\u03c1\u03b9\u03b8\u03bc\u03cc\u03c2 \u03a6\u03bf\u03c1\u03bf\u03bb\u03bf\u03b3\u03b9\u03ba\u03bf\u03cd \u039c\u03b7\u03c4\u03c1\u03ce\u03bf\u03c5" (\u0391\u03a6\u039c or AFM), is a unique identifier assigned to individuals and entities for tax purposes by the Cypriot tax authorities.',
      entity: 'TAX_ID_CYPRUS',
      id: '194',
      language: ['en'],
      region: ['CY'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1721434862.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Cyprus Value Added Tax (VAT) Number',
      description:
        'A Cyprus Value Added Tax (VAT) Number is a unique identifier assigned to businesses and entities registered for VAT in Cyprus.',
      entity: 'VAT_CYPRUS',
      id: '251',
      language: ['en'],
      region: ['CY'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Czech Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Czech upon issuance of their driver's license.",
      entity: 'DL_CZECH',
      id: '71',
      language: ['en'],
      region: ['CZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Czech Passport Number',
      description:
        'A unique identifier assigned to individuals in Czech upon issuance of their passport',
      entity: 'PASSPORT_CZECH',
      id: '112',
      language: ['en'],
      region: ['CZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Czech Personal Identity Number',
      description:
        'A unique identifier assigned to individuals in the Czech Republic for identification purposes.',
      entity: 'NATIONAL_ID_CZECH',
      id: '29',
      language: ['en'],
      region: ['CZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720204344.0
    },
    {
      continent: ['EU'],
      create_time: 1721681780.0,
      data_type: 'Czech Tax Identification Number',
      description:
        'A Czech Tax Identification Number (TIN), known as the "Da\u0148ov\u00e9 Identifika\u010dn\u00ed \u010c\u00edslo" (DI\u010c), is a unique identifier assigned to individuals and entities for tax purposes in the Czech Republic.',
      entity: 'TAX_ID_CZECH',
      id: '252',
      language: ['en'],
      region: ['CZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721681780.0
    },
    {
      continent: ['EU'],
      create_time: 1741391733.0,
      data_type: 'Czech Unique Personal Identification Number',
      description:
        'The Rodn\u00e9 \u010d\u00edslo (R\u010c) is a unique personal identification number assigned to Czech citizens and certain residents for identification, social security, and tax purposes in the Czech Republic.',
      entity: 'UPIN_CZECH',
      id: '461',
      language: ['en', 'cs'],
      region: ['CZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741391766.0
    },
    {
      continent: ['EU'],
      create_time: 1721683079.0,
      data_type: 'Czech VAT Identification Number',
      description:
        'A Czech VAT Identification Number, known as the "Da\u0148ov\u00e9 Identifika\u010dn\u00ed \u010c\u00edslo" (DI\u010c), is a unique identifier assigned to businesses and entities registered for Value Added Tax (VAT) in the Czech Republic.',
      entity: 'VAT_CZECH',
      id: '253',
      language: ['en'],
      region: ['CZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721683079.0
    },
    {
      continent: ['ALL'],
      create_time: 1738609172.0,
      data_type: 'Dankort Card Number',
      description:
        'The Dankord Card Number refers to the unique 16-digit card number assigned to a Dankort, Denmark\u2019s national debit card, used for secure electronic transactions and payments.',
      entity: 'CREDIT_CARD_DANKORT',
      id: '445',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: 1738955469.0
    },
    {
      continent: ['ALL'],
      create_time: 1737511508.0,
      data_type: 'Databricks API Token',
      description:
        'The Databricks API Token is a secure, personal access key used to authenticate API requests and enable programmatic interaction with Databricks resources and services.',
      entity: 'DATABRICKS_AT',
      id: '434',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1737511593.0
    },
    {
      continent: ['ALL'],
      create_time: 1736210728.0,
      data_type: 'Datadog Access Token',
      description:
        'The Datadog Access Token is a secure credential used to authenticate and enable data submission and integration with Datadog\u2019s monitoring and analytics platform.',
      entity: 'DATADOG_AT',
      id: '409',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736210750.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Date of Birth',
      description:
        'The exact date when an individual was born, comprising the day, month, and year.',
      entity: 'PERSONAL_DOB',
      id: '4',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Denmark Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Denmark upon issuance of their driver's license.",
      entity: 'DL_DENMARK',
      id: '72',
      language: ['en'],
      region: ['DK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Denmark Passport Number',
      description:
        'A unique identifier assigned to individuals in Denmark upon issuance of their passport',
      entity: 'PASSPORT_DENMARK',
      id: '111',
      language: ['en'],
      region: ['DK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Denmark Personal Identification Number',
      description:
        'A unique identifier assigned to individuals in Denmark for identification and administrative purposes.',
      entity: 'NATIONAL_ID_DENMARK',
      id: '30',
      language: ['en'],
      region: ['DK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720204367.0
    },
    {
      continent: ['EU'],
      create_time: 1721683892.0,
      data_type: 'Denmark Tax Identification Number',
      description:
        'A Denmark Tax Identification Number (TIN), known as the "CPR-nummer" for individuals or the "CVR-nummer" for entities, is a unique identifier assigned for tax purposes by the Danish tax authorities.',
      entity: 'TAX_ID_DENMARK',
      id: '254',
      language: ['en'],
      region: ['DK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721683892.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Denmark Value Added Tax (VAT) Number',
      description:
        'A Denmark Value Added Tax (VAT) Number, known as the "Momsnummer" or "CVR-nummer," is a unique identifier assigned to businesses and entities registered for VAT in Denmark.',
      entity: 'VAT_DENMARK',
      id: '255',
      language: ['en'],
      region: ['DK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'DigitalOcean Access Token',
      description:
        'The DigitalOcean Access Token is a secure, personal authentication key used to authorize API requests and manage resources such as droplets, domains, and databases within a DigitalOcean account.',
      entity: 'DIGITALOCEAN_AT',
      id: '437',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'DigitalOcean Personal Access Token',
      description:
        "The DigitalOcean Personal Access Token is a secure, user-generated key that allows programmatic access to DigitalOcean's API for managing account resources and performing automated tasks.",
      entity: 'DIGITALOCEAN_PAT',
      id: '438',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'DigitalOcean Refresh Token',
      description:
        'The DigitalOcean Refresh Token is a secure token used to obtain a new access token once the current one expires, ensuring uninterrupted authentication for API access and integrations.',
      entity: 'DIGITALOCEAN_RT',
      id: '439',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Discord API Token',
      description:
        "The Discord API Token is a unique and confidential credential used to authenticate bots or applications, enabling them to interact with Discord's API for managing servers, users, and other features.",
      entity: 'DISCORD_AT',
      id: '400',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1735598778.0
    },
    {
      continent: ['ALL'],
      create_time: 1735598949.0,
      data_type: 'Discord Client ID',
      description:
        'The Discord Client ID is a unique identifier assigned to your Discord application, used to identify the app in interactions such as OAuth2 authentication and API integrations.',
      entity: 'DISCORD_CI',
      id: '401',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1735598973.0
    },
    {
      continent: ['ALL'],
      create_time: 1735599128.0,
      data_type: 'Discord Client Secret',
      description:
        "The Discord Client Secret is a confidential credential used in conjunction with the Client ID to authenticate your Discord application during OAuth2 authorization and secure interactions with Discord's API.",
      entity: 'DISCORD_CS',
      id: '402',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Dropbox API Token',
      description:
        "The Dropbox API Token is a secure, unique credential used to authenticate requests and enable applications to interact with Dropbox's API for managing files, folders, and other account resources.",
      entity: 'DROPBOX_AT',
      id: '405',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'EC Private Key',
      description:
        'An EC (Elliptic Curve) Private Key is a cryptographic key used in elliptic curve cryptography (ECC) for decrypting data or generating digital signatures, offering a high level of security with smaller key sizes compared to RSA; it must be kept confidential to ensure secure communication and authentication.',
      entity: 'EC_PRIVATE_KEY',
      id: '334',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: [
        'AS',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU',
        'EU'
      ],
      create_time: **********.0,
      data_type: 'EU Health Insurance Card Number (EHIC)',
      description:
        'The EU Health Insurance Card (EHIC) Number is a unique identifier found on the European Health Insurance Card, which entitles the holder to receive medical treatment in another member state of the European Economic Area (EEA) or Switzerland under the same conditions and at the same cost as residents of that country.',
      entity: 'HEALTH_ID_EU_EHIC',
      id: '211',
      language: ['en'],
      region: [
        'AT',
        'BE',
        'BG',
        'HR',
        'CY',
        'CZ',
        'DK',
        'EE',
        'FI',
        'FR',
        'DE',
        'GR',
        'HU',
        'IE',
        'IT',
        'LV',
        'LT',
        'LU',
        'MT',
        'NL',
        'PL',
        'PT',
        'RO',
        'SK',
        'SI',
        'ES',
        'SE',
        'IS',
        'LI',
        'NO',
        'CH'
      ],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'EasyPost API Token',
      description:
        'The EasyPost API Token is a unique authentication key used to securely access and interact with the EasyPost API for shipping and tracking services.',
      entity: 'EASYPOST_AT',
      id: '435',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1737589243.0
    },
    {
      continent: ['ALL'],
      create_time: 1737591308.0,
      data_type: 'EasyPost Test API Token',
      description:
        'The EasyPost Test API Token is a special authentication key used to securely access the EasyPost API in a sandbox environment for testing and development purposes without processing real shipments.',
      entity: 'EASYPOST_TAT',
      id: '436',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1737591892.0
    },
    {
      continent: ['SA'],
      create_time: 1741396165.0,
      data_type: 'Ecuador Identification Number',
      description:
        'The Ecuadorian ID Number, known as the C\u00e9dula de Identidad, is a unique 10-digit identification number assigned to Ecuadorian citizens and residents for legal, administrative, and taxation purposes.',
      entity: 'ID_ECUADOR',
      id: '462',
      language: ['en', 'es'],
      region: ['EC'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Email Address',
      description: 'The unique identifier for an email account.',
      entity: 'EMAIL',
      id: '5',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Estonia Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Estonia upon issuance of their driver's license.",
      entity: 'DL_ESTONIA',
      id: '73',
      language: ['en'],
      region: ['EE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Estonia Passport Number',
      description:
        'A unique identifier assigned to individuals in Estonia upon issuance of their passport',
      entity: 'PASSPORT_ESTONIA',
      id: '110',
      language: ['en'],
      region: ['EE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Estonia Personal Identification Code',
      description:
        'A unique identifier assigned to individuals in Estonia for identification and administrative purposes.',
      entity: 'NATIONAL_ID_ESTONIA',
      id: '50',
      language: ['en'],
      region: ['EE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720204909.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Ethnicity',
      description:
        'Ethnicity refers to a group of people who share common cultural, linguistic, ancestral, or historical traits.',
      entity: 'PERSONAL_ETHNICITY',
      id: '94',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1733350169.0
    },
    {
      continent: ['ALL'],
      create_time: 1738110469.0,
      data_type: 'Finicity API Token',
      description:
        "The Finicity API Token is a secure credential used to authenticate and authorize access to Finicity's financial data aggregation and credit decisioning APIs.",
      entity: 'FINICITY_AT',
      id: '442',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1740766501.0
    },
    {
      continent: ['ALL'],
      create_time: 1738110610.0,
      data_type: 'Finicity Client Secret',
      description:
        'The Finicity Client Secret is a confidential credential used alongside the Client ID to securely authenticate and authorize API requests to Finicity\u2019s financial data services.',
      entity: 'FINICITY_CS',
      id: '443',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Finland Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Finland upon issuance of their driver's license.",
      entity: 'DL_FINLAND',
      id: '74',
      language: ['en'],
      region: ['FI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Finland Health Insurance Card Number',
      description:
        'A unique identifier assigned to individuals in Finland upon issuance of their health insurance card (Kela card).',
      entity: 'HEALTH_ID_FINLAND',
      id: '209',
      language: ['en'],
      region: ['FI'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Finland National ID',
      description:
        'A unique identifier assigned to individuals in Finland upon issuance of their national identity card (Henkil\u00f6kortti).',
      entity: 'NATIONAL_ID_FINLAND',
      id: '31',
      language: ['en'],
      region: ['FI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Finland Passport Number',
      description:
        'A unique identifier assigned to individuals in Finland upon issuance of their passport',
      entity: 'PASSPORT_FINLAND',
      id: '109',
      language: ['en'],
      region: ['FI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722460347.0
    },
    {
      continent: ['EU'],
      create_time: 1741398373.0,
      data_type: 'Finland Social Security Number',
      description:
        'The Finland Social Security Number, known as the Henkil\u00f6tunnus (HETU), is a unique 11-character personal identification number assigned to Finnish citizens and residents for social security, taxation, and official identification purposes.',
      entity: 'SSN_FINLAND',
      id: '463',
      language: ['en', 'fi'],
      region: ['FI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741398404.0
    },
    {
      continent: ['EU'],
      create_time: 1721686734.0,
      data_type: 'Finland Tax Identification Number',
      description:
        'A Finland Tax Identification Number (TIN), known as the "Henkil\u00f6tunnus" for individuals or "Y-tunnus" for entities, is a unique identifier assigned for tax purposes by the Finnish tax authorities.',
      entity: 'TAX_ID_FINLAND',
      id: '257',
      language: ['en'],
      region: ['FI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721686797.0
    },
    {
      continent: ['EU'],
      create_time: 1721686888.0,
      data_type: 'Finland Value Added Tax (VAT) Number',
      description:
        'A Finland Value Added Tax (VAT) Number, known as the "Arvonlis\u00e4verotunniste" (ALV-numero) in Finnish, is a unique identifier assigned to businesses and entities registered for VAT in Finland.',
      entity: 'VAT_FINLAND',
      id: '258',
      language: ['en'],
      region: ['FI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721686888.0
    },
    {
      continent: ['ALL'],
      create_time: 1737073199.0,
      data_type: 'Flutterwave Encryption Key',
      description:
        'The Flutterwave Encryption Key is a secure credential used to encrypt sensitive data, such as payment details, ensuring secure communication and transactions within the Flutterwave payment platform.',
      entity: 'FLUTTERWAVE_EK',
      id: '426',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1737073335.0
    },
    {
      continent: ['ALL'],
      create_time: 1737073451.0,
      data_type: 'Flutterwave Public Key',
      description:
        'The Flutterwave Public Key is a unique identifier used to authenticate your application and initiate secure transactions through Flutterwave\u2019s payment platform.',
      entity: 'FLUTTERWAVE_PK',
      id: '427',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1737073542.0
    },
    {
      continent: ['ALL'],
      create_time: 1737075334.0,
      data_type: 'Flutterwave Secret Key',
      description:
        "The Flutterwave Secret Key is a confidential credential used alongside the Public Key to securely authenticate and authorize transactions and API requests on Flutterwave's payment platform.",
      entity: 'FLUTTERWAVE_SK',
      id: '428',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'France Bank Account Number',
      description:
        'A France Bank Account Number, known as a Compte Bancaire, is identified by a unique 23-character code, which includes the IBAN (International Bank Account Number) system for facilitating both domestic and international transactions.',
      entity: 'BANK_ACCOUNT_NUMBER_FR',
      id: '205',
      language: ['en'],
      region: ['FR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "France Driver's License Number",
      description:
        "A unique identifier assigned to individuals in France upon issuance of their driver's license.",
      entity: 'DL_FRANCE',
      id: '75',
      language: ['en'],
      region: ['FR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'France National ID Card (CNI) Number',
      description:
        "A unique identifier assigned to individuals in France upon issuance of their national identity card (Carte Nationale d'Identit\u00e9 or CNI).",
      entity: 'NATIONAL_ID_FRANCE_CNI',
      id: '19',
      language: ['en'],
      region: ['FR'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'France Passport Number',
      description:
        'A unique identifier assigned to individuals in France upon issuance of their passport',
      entity: 'PASSPORT_FRANCE',
      id: '125',
      language: ['en'],
      region: ['FR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'France Routing Number',
      description:
        'A France Routing Number, also known as Bank Code (Code Banque) or Branch Code (Code Guichet), is used to identify financial institutions and their branches in France for domestic and international transactions.',
      entity: 'RN_FRANCE',
      id: '487',
      language: ['en', 'fr'],
      region: ['FR'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'France Social Security Number (INSEE) (or France Health Insurance Number(HIN))',
      description:
        'A France Social Security Number (INSEE) or Health Insurance Number (HIN) is a unique identifier assigned to individuals in France for social security and health insurance purposes.',
      entity: 'SSN_FRANCE_INSEE',
      id: '134',
      language: ['en'],
      region: ['FR'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'France Tax Identification Number',
      description:
        'A France Tax Identification Number (TIN), known as the "Num\u00e9ro d\'Identification Fiscale" (NIF), is a unique numeric identifier assigned to individuals and entities for tax purposes by the French tax authorities.',
      entity: 'TAX_ID_FRANCE',
      id: '190',
      language: ['en'],
      region: ['FR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'France Value Added Tax (VAT) Number',
      description:
        'A France Value Added Tax (VAT) Number, known as the "Num\u00e9ro d\'Identification \u00e0 la Taxe sur la Valeur Ajout\u00e9e" (TVA), is a unique identifier assigned to businesses and entities registered for VAT in France.',
      entity: 'VAT_FRANCE',
      id: '259',
      language: ['en', 'fr'],
      region: ['FR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'FreshBooks Access Token',
      description:
        'A FreshBooks Access Token is a secure, unique key that allows third-party applications to connect and interact with a FreshBooks account, enabling seamless data exchange and integration.',
      entity: 'FRESHBOOKS_AT',
      id: '449',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'GCP API Key',
      description:
        'The GCP API Key is a unique credential used to authenticate and authorize requests to Google Cloud Platform services, enabling secure access to APIs and resources.',
      entity: 'GCP_AK',
      id: '422',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Gender',
      description: 'Gender',
      entity: 'PERSONAL_GENDER',
      id: '95',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Germany Bank Account Number',
      description:
        'A Germany Bank Account Number, known as a Kontonummer, is a unique identifier for individual bank accounts in Germany, typically consisting of up to 10 digits, and is used alongside the IBAN (International Bank Account Number) system for both domestic and international transactions.',
      entity: 'BANK_ACCOUNT_NUMBER_DE',
      id: '204',
      language: ['en'],
      region: ['DE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Germany Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Germany upon issuance of their driver's license.",
      entity: 'DL_GERMANY',
      id: '77',
      language: ['en'],
      region: ['DE'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Germany Identity Card Number',
      description:
        'A unique identifier assigned to individuals in Germany upon issuance of their identity card (Personalausweis).',
      entity: 'NATIONAL_ID_GERMANY',
      id: '51',
      language: ['en'],
      region: ['DE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Germany Passport Number',
      description:
        'A unique identifier assigned to individuals in Germany upon issuance of their passport',
      entity: 'PASSPORT_GERMANY',
      id: '121',
      language: ['en'],
      region: ['DE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Germany Routing Number',
      description:
        'A Germany Routing Number, known as the Bankleitzahl (BLZ), is an 8-digit numeric code used to identify banks and their branches within the German banking system.',
      entity: 'RN_GERMANY',
      id: '488',
      language: ['en', 'de'],
      region: ['DE'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Germany Tax Identification Number',
      description:
        'A Germany Tax Identification Number (TIN), known as the "Steuerliche Identifikationsnummer" or "IdNr," is a unique 11-digit identifier assigned to individuals for tax purposes by the German Federal Central Tax Office.',
      entity: 'TAX_ID_GERMANY',
      id: '197',
      language: ['en'],
      region: ['DE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Germany Value Added Tax (VAT) Number',
      description:
        'A Germany Value Added Tax (VAT) Number, known as the "Umsatzsteuer-Identifikationsnummer" (USt-IdNr.), is a unique identifier assigned to businesses and entities registered for VAT in Germany.',
      entity: 'VAT_GERMANY',
      id: '260',
      language: ['en', 'de'],
      region: ['DE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: 1726869294.0,
      data_type: 'GitHub App Refresh Token',
      description:
        'A GitHub App Refresh Token is a long-lived token used to request a new GitHub App access token after the previous one expires, ensuring continuous authentication and authorization for the app without requiring user intervention.',
      entity: 'GITHUB_APP_RT',
      id: '343',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726869319.0
    },
    {
      continent: ['ALL'],
      create_time: 1726868732.0,
      data_type: 'GitHub App User Access Token',
      description:
        "A GitHub App User Access Token is a short-lived, user-specific token generated by a GitHub App to authenticate and perform actions on behalf of a user with scoped permissions, allowing secure and granular access to the user's GitHub resources.",
      entity: 'GITHUB_APP_UAT',
      id: '341',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726869352.0
    },
    {
      continent: ['ALL'],
      create_time: 1726868914.0,
      data_type: 'Github App Installation Access Token',
      description:
        "A GitHub App Installation Access Token is a short-lived, scoped token generated by a GitHub App to authenticate and perform actions on behalf of an app installation, granting access to specific repositories or resources as defined by the app's permissions.",
      entity: 'GITHUB_APP_IAT',
      id: '342',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Github Fine-grained Personal Access Token',
      description:
        'A GitHub Fine-grained Personal Access Token (PAT) is a more secure, customizable key that allows for precise, granular control over repository and resource access, limiting permissions to specific actions and areas within your GitHub account.',
      entity: 'GITHUB_FPAT',
      id: '339',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Github OAuth Access Token',
      description:
        'A GitHub OAuth Access Token is a secure, temporary key generated through the OAuth protocol, enabling third-party applications to access your GitHub account with specific permissions, without exposing your username or password.',
      entity: 'GITHUB_OAT',
      id: '340',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Github Personal Access Token',
      description:
        'A GitHub Personal Access Token (PAT) is a secure, unique key used to authenticate and authorize access to your GitHub account, enabling actions such as cloning repositories, making commits, or interacting with the GitHub API without using your password.',
      entity: 'GITHUB_PAT',
      id: '338',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Gitlab Deploy Token',
      description:
        'A GitLab Deploy Token is a secure, limited-access token used to authenticate and access repositories for read-only or write operations, specifically designed for use in automation processes such as continuous deployment or integrating with external systems without needing user credentials.',
      entity: 'GITLAB_DT',
      id: '346',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Gitlab OAuth Application Secret',
      description:
        "A GitLab OAuth Application Secret is a confidential key used by an OAuth application to securely authenticate with GitLab's OAuth server, enabling the application to request access tokens on behalf of users and interact with GitLab resources according to the granted permissions.",
      entity: 'GITLAB_OAS',
      id: '345',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Gitlab Personal Access Token',
      description:
        'A GitLab Personal Access Token is a secure, unique key used to authenticate and authorize access to your GitLab account, allowing you to perform actions such as cloning repositories, pushing code, or interacting with the GitLab API without using your password.',
      entity: 'GITLAB_PAT',
      id: '344',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Gitlab Runner Authentication Token',
      description:
        'A GitLab Runner Authentication Token is a unique, secure token used to register and authenticate a GitLab Runner with a GitLab instance, allowing the runner to receive and execute CI/CD pipeline jobs for a specific project or group.',
      entity: 'GITLAB_RAT',
      id: '347',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Gitlab Trigger Token',
      description:
        'A GitLab Trigger Token is a secure token used to manually trigger and execute specific GitLab CI/CD pipeline jobs via the GitLab API, enabling automation of tasks or pipeline execution outside of the standard GitLab workflow.',
      entity: 'GITLAB_TT',
      id: '348',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726871354.0
    },
    {
      continent: ['ALL'],
      create_time: 1726704298.0,
      data_type: 'Google API Key',
      description:
        "The Google API Key is a unique identifier that provides secure access to Google's services and APIs, allowing applications to interact with various Google features like Maps, YouTube, and cloud services.",
      entity: 'GOOGLE_API_KEY',
      id: '329',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Greece Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Greece upon issuance of their driver's license.",
      entity: 'DL_GREECE',
      id: '78',
      language: ['en'],
      region: ['GR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Greece National ID Card Number',
      description:
        'A unique identifier assigned to individuals in Greece upon issuance of their national identity card (\u0391\u03c3\u03c4\u03c5\u03bd\u03bf\u03bc\u03b9\u03ba\u03ae \u03a4\u03b1\u03c5\u03c4\u03cc\u03c4\u03b7\u03c4\u03b1, Astynomiki Tautotita).',
      entity: 'NATIONAL_ID_GREECE',
      id: '32',
      language: ['en'],
      region: ['GR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Greece Passport Number',
      description:
        'A unique identifier assigned to individuals in Greece upon issuance of their passport',
      entity: 'PASSPORT_GREECE',
      id: '108',
      language: ['en'],
      region: ['GR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720462075.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Greece Social Security Number',
      description:
        'A Greece Social Security Number, known as the "\u0391\u03c1\u03b9\u03b8\u03bc\u03cc\u03c2 \u039c\u03b7\u03c4\u03c1\u03ce\u03bf\u03c5 \u039a\u03bf\u03b9\u03bd\u03c9\u03bd\u03b9\u03ba\u03ae\u03c2 \u0391\u03c3\u03c6\u03ac\u03bb\u03b9\u03c3\u03b7\u03c2" (\u0391\u039c\u039a\u0391 or AMKA), is a unique identifier assigned to individuals for social security purposes in Greece.',
      entity: 'SSN_GREECE',
      id: '151',
      language: ['en'],
      region: ['GR'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1721434814.0
    },
    {
      continent: ['EU'],
      create_time: 1713769200.0,
      data_type: 'Greece Tax Identification Number',
      description:
        'A Greece Tax Identification Number (TIN), known as the "\u0391\u03c1\u03b9\u03b8\u03bc\u03cc\u03c2 \u03a6\u03bf\u03c1\u03bf\u03bb\u03bf\u03b3\u03b9\u03ba\u03bf\u03cd \u039c\u03b7\u03c4\u03c1\u03ce\u03bf\u03c5" (\u0391\u03a6\u039c or AFM), is a unique identifier assigned to individuals and entities for tax purposes by the Greek tax authorities.',
      entity: 'TAX_ID_GREECE',
      id: '196',
      language: ['en'],
      region: ['GR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721434910.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Greece Value Added Tax (VAT) Number',
      description:
        'A Greece Value Added Tax (VAT) Number, known as the "\u0391\u03c1\u03b9\u03b8\u03bc\u03cc\u03c2 \u03a6\u03bf\u03c1\u03bf\u03bb\u03bf\u03b3\u03b9\u03ba\u03bf\u03cd \u039c\u03b7\u03c4\u03c1\u03ce\u03bf\u03c5" (\u0391\u03a6\u039c) or VAT Identification Number, is a unique identifier assigned to businesses and entities registered for VAT in Greece.',
      entity: 'VAT_GREECE',
      id: '261',
      language: ['en', 'el'],
      region: ['GR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Health Problem',
      description:
        'A clinical pathology or condition, characterized by abnormal physiological or psychological states requiring professional healthcare intervention.',
      entity: 'SPACY_PROBLEM',
      id: '227',
      language: ['en'],
      region: ['ALL'],
      type: 'NLP Model',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Health Treatment',
      description:
        "Medical treatment refers to the systematic application of therapeutic interventions, including medications, procedures, and therapies, to alleviate or cure a patient's clinical condition or disease.",
      entity: 'SPACY_TREATMENT',
      id: '228',
      language: ['en'],
      region: ['ALL'],
      type: 'NLP Model',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Hong Kong Identity Card (HKID) Number',
      description:
        'A unique identifier assigned to residents of Hong Kong upon issuance of their Hong Kong Identity Card (HKID).',
      entity: 'NATIONAL_ID_HK_HKID',
      id: '33',
      language: ['en'],
      region: ['HK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Hospital',
      description:
        'A hospital is a healthcare institution providing comprehensive medical, surgical, and therapeutic services.',
      entity: 'SPACY_HOSPITAL',
      id: '226',
      language: ['en'],
      region: ['ALL'],
      type: 'NLP Model',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Hugging Face Access Token',
      description:
        "A Hugging Face access token is a secure, unique identifier used to authenticate and authorize access to Hugging Face's API and services.",
      entity: 'HF_AT',
      id: '387',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Hugging Face Organization API Token',
      description:
        "A Hugging Face Organization API Token is a secure credential that grants authenticated access to an organization's resources and services on the Hugging Face platform.",
      entity: 'HF_OAT',
      id: '388',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Hungary Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Hungary upon issuance of their driver's license.",
      entity: 'DL_HUNGARY',
      id: '79',
      language: ['en', 'hu'],
      region: ['HU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Hungary Passport Number',
      description:
        'A unique identifier assigned to individuals in Hungary upon issuance of their passport',
      entity: 'PASSPORT_HUNGARY',
      id: '107',
      language: ['en', 'hu'],
      region: ['HU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Hungary Personal Identification Number',
      description:
        'A unique identifier assigned to individuals in Hungary for identification and administrative purposes.',
      entity: 'NATIONAL_ID_HUNGARY',
      id: '52',
      language: ['en', 'hu'],
      region: ['HU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721943177.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Hungary Social Security Number',
      description:
        'A Hungary Social Security Number, known as the "T\u00e1rsadalombiztos\u00edt\u00e1si Azonos\u00edt\u00f3 Jel" (TAJ), is a unique identifier assigned to individuals for social security purposes in Hungary.',
      entity: 'SSN_HUNGARY',
      id: '150',
      language: ['en', 'hu'],
      region: ['HU'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1725407135.0
    },
    {
      continent: ['EU'],
      create_time: 1721944384.0,
      data_type: 'Hungary Tax Identification Number',
      description:
        'A Hungary Tax Identification Number (TIN), known as the "Ad\u00f3azonos\u00edt\u00f3 jel," is a unique identifier assigned to individuals and entities for tax purposes by the Hungarian tax authorities.',
      entity: 'TAX_ID_HUNGARY',
      id: '262',
      language: ['en', 'hu'],
      region: ['HU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721944384.0
    },
    {
      continent: ['EU'],
      create_time: 1721945276.0,
      data_type: 'Hungary VAT Number',
      description:
        'A Hungary VAT Number, known as the "Ad\u00f3sz\u00e1m," is a unique identifier assigned to businesses and entities registered for Value Added Tax (VAT) in Hungary.',
      entity: 'VAT_HUNGARY',
      id: '263',
      language: ['en', 'hu'],
      region: ['HU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721945276.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'IP Address',
      description:
        'The IP address (Internet Protocol address) is a unique numerical label assigned to each device connected to a computer network that uses the Internet Protocol for communication.',
      entity: 'IP_ADDRESS',
      id: '215',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1728683883.0
    },
    {
      continent: ['EU'],
      create_time: 1741632078.0,
      data_type: 'Iceland Personal Identification Number',
      description:
        'The Kennitala (KT) is a unique 10-digit personal identification number assigned to Icelandic citizens and residents for identification, taxation, and administrative purposes.',
      entity: 'KT_ICELAND',
      id: '464',
      language: ['en', 'is'],
      region: ['IS'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741632109.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'India Aadhaar Number',
      description:
        'A unique identification number issued by the Unique Identification Authority of India (UIDAI) to residents of India',
      entity: 'NATIONAL_ID_INDIA',
      id: '6',
      language: ['en'],
      region: ['IN'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: "India Driver's License Number",
      description:
        "An India driver's license number is a unique alphanumeric identifier assigned to an individual's driver's license issued by the Indian government.",
      entity: 'DL_INDIA',
      id: '142',
      language: ['en'],
      region: ['IN'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'India Passport Number',
      description:
        "An India passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the Indian government.",
      entity: 'PASSPORT_INDIA',
      id: '137',
      language: ['en'],
      region: ['IN'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'India Permanent Account Number (PAN)',
      description:
        'A unique alphanumeric identifier issued by the Income Tax Department of India to individuals, companies, and entities.',
      entity: 'TAX_ID_INDIA_PAN',
      id: '179',
      language: ['en'],
      region: ['IN'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: "Individual's Name",
      description:
        'Refers to the unique identifier assigned to a person, typically comprising a given name and a surname.',
      entity: 'SPACY_PERSON',
      id: '1',
      language: ['en'],
      region: ['ALL'],
      type: 'NLP Model',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'International Banking Account Number',
      description:
        'An International Bank Account Number (IBAN) is a standardized alphanumeric code used globally to uniquely identify individual bank accounts, ensuring accurate and efficient processing of cross-border transactions.',
      entity: 'FI_IBAN',
      id: '245',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20002'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'International Classification of Diseases (ICD) code',
      description:
        'An International Classification of Diseases (ICD) code is a standardized alphanumeric code used globally to classify and report diseases, health conditions, and medical procedures, facilitating consistent documentation and analysis in healthcare and medical research.',
      entity: 'HEALTHCARE_ID_ICD',
      id: '231',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'International Mobile Equipment Identity (IMEI)',
      description: 'A unique identifier assigned to mobile phones and other cellular devices.',
      entity: 'IMEI',
      id: '217',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'International Securities Identification Number',
      description:
        'An International Securities Identifying Number (ISIN) is a unique 12-character alphanumeric code used to identify specific securities, such as stocks, bonds, and other financial instruments, across international markets.',
      entity: 'ISIN',
      id: '264',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20002'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Ireland Bank Account Number',
      description:
        'An Ireland Bank Account Number consists of an 8-digit account number paired with a 6-digit National Sort Code (NSC) and is used for domestic and international banking transactions.',
      entity: 'BAN_IRELAND',
      id: '475',
      language: ['en'],
      region: ['IE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Ireland Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Ireland upon issuance of their driver's license.",
      entity: 'DL_IRELAND',
      id: '80',
      language: ['en', 'ga'],
      region: ['IE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Ireland National Sort Code (NSC)',
      description:
        'The Ireland National Sort Code (NSC) is a 6-digit code used to identify banks and their branches for domestic banking transactions in Ireland.',
      entity: 'NSC_IRELAND',
      id: '489',
      language: ['en', 'ga'],
      region: ['IE'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Ireland Passport Number',
      description:
        'A unique identifier assigned to individuals in Ireland upon issuance of their passport',
      entity: 'PASSPORT_IRELAND',
      id: '106',
      language: ['en', 'ga'],
      region: ['IE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Ireland Personal Public Service (PPS) Number',
      description:
        'A unique identifier assigned to individuals in Ireland for social welfare and taxation purposes.',
      entity: 'NATIONAL_ID_IRELAND_PPS',
      id: '34',
      language: ['en', 'ga'],
      region: ['IE'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1728065901.0
    },
    {
      continent: ['EU'],
      create_time: 1722016801.0,
      data_type: 'Ireland Tax Identification Number',
      description:
        'An Ireland Tax Identification Number (TIN) is a unique identifier used for tax purposes in Ireland.',
      entity: 'TAX_ID_IRELAND',
      id: '265',
      language: ['en', 'ga'],
      region: ['IE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722016801.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Ireland Value Added Tax (VAT) Number',
      description:
        'An Ireland Value Added Tax (VAT) Number is a unique identifier assigned to businesses and entities registered for VAT in Ireland.',
      entity: 'VAT_IRELAND',
      id: '266',
      language: ['en', 'ga'],
      region: ['IE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Israel Bank Account Number',
      description:
        "A unique identifier assigned to an individual's or entity's bank account in Israel.",
      entity: 'BANK_ACCOUNT_NUMBER_IL',
      id: '188',
      language: ['en'],
      region: ['IL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Israel Bank and Branch Code',
      description:
        'The Israel Bank and Branch Code is a 7-digit numeric identifier used to distinguish banks and their branches within the Israeli financial system.',
      entity: 'BBC_ISRAEL',
      id: '490',
      language: ['en'],
      region: ['IL'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Israel National Identification Number',
      description:
        'A unique identifier assigned to individuals in Israel for identification and administrative purposes.',
      entity: 'NATIONAL_ID_ISRAEL',
      id: '35',
      language: ['en', 'he', 'ar'],
      region: ['IL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Italy Bank Account Number',
      description:
        'An Italy Bank Account Number, also known as an IBAN (International Bank Account Number), is a unique alphanumeric code used to identify individual bank accounts in Italy and facilitate international and domestic transactions.',
      entity: 'BANK_ACCOUNT_NUMBER_IT',
      id: '202',
      language: ['en'],
      region: ['IT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Italy Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Italy upon issuance of their driver's license.",
      entity: 'DL_ITALY',
      id: '93',
      language: ['en', 'it'],
      region: ['IT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Italy Health Insurance Number',
      description:
        'The Italy Health Insurance Number (Codice Fiscale) is a unique alphanumeric identifier assigned to individuals in Italy for tax and health insurance purposes, derived from personal information such as name, date, and place of birth.',
      entity: 'HIN_ITALY',
      id: '300',
      language: ['en', 'it'],
      region: ['IT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Italy Passport Number',
      description:
        'A unique identifier assigned to individuals in Italy upon issuance of their passport',
      entity: 'PASSPORT_ITALY',
      id: '105',
      language: ['en', 'it'],
      region: ['IT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Italy Tax ID/SSN (Codice Fiscale)',
      description:
        'A unique alphanumeric identifier assigned to individuals in Italy for tax and administrative purposes.',
      entity: 'NATIONAL_ID_ITALY_CF',
      id: '53',
      language: ['en'],
      region: ['IT'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Italy Value Added Tax(VAT) Number',
      description:
        'An Italy Health Insurance Number, known as the "Codice Fiscale," is a unique identifier assigned to individuals for tax and health insurance purposes in Italy.',
      entity: 'VAT_ITALY',
      id: '267',
      language: ['en', 'it'],
      region: ['IT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'JFrog API Key',
      description:
        'A JFrog API key is a secure, alphanumeric credential used to authenticate and authorize requests to JFrog\u2019s platform services.',
      entity: 'JFROG_AK',
      id: '389',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'JFrog Identity Token',
      description:
        'A JFrog identity token is a time-bound credential tied to a user\u2019s account, granting secure, authenticated access to JFrog platform resources and services.',
      entity: 'JFROG_IT',
      id: '390',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Jamaica Passport Number',
      description:
        'The Jamaica Passport Number is a unique alphanumeric identifier assigned to each Jamaican passport holder, used for identity verification and travel documentation.',
      entity: 'PASSPORT_JAMAICA',
      id: '362',
      language: ['en'],
      region: ['JM'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Japan Bank Account Number',
      description:
        "A unique identifier assigned to an individual's or entity's bank account in Japan.",
      entity: 'BANK_ACCOUNT_NUMBER_JP',
      id: '187',
      language: ['en'],
      region: ['JP'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Japan Corporate Number',
      description:
        'The Japan Corporate Number (\u6cd5\u4eba\u756a\u53f7, H\u014djin Bang\u014d) is a unique 13-digit identification number assigned to businesses and legal entities in Japan for taxation, legal, and administrative purposes.',
      entity: 'CN_JAPAN',
      id: '465',
      language: ['en', 'ja'],
      region: ['JP'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: "Japan Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Japan upon issuance of their driver's license.",
      entity: 'DL_JAPAN',
      id: '92',
      language: ['en', 'jp'],
      region: ['JP'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Japan My Number Code',
      description:
        'An identifier assigned to residents of Japan for social security and tax purposes.',
      entity: 'NATIONAL_ID_JAPAN_MY',
      id: '20',
      language: ['en', 'jp'],
      region: ['JP'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Japan Passport Number',
      description:
        'A unique identifier assigned to individuals in Japan upon issuance of their passport',
      entity: 'PASSPORT_JAPAN',
      id: '124',
      language: ['en', 'jp'],
      region: ['JP'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722034628.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Japan Residence Card Number',
      description:
        'An identifier issued to foreign residents in Japan who hold a residence card (\u5728\u7559\u30ab\u30fc\u30c9, zairyu card).',
      entity: 'NATIONAL_ID_JAPAN_RESIDENCE',
      id: '21',
      language: ['en'],
      region: ['JP'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720204042.0
    },
    {
      continent: ['ALL'],
      create_time: 1727134247.0,
      data_type: 'Json Web Token',
      description:
        'A JSON Web Token (JWT) is a compact, URL-safe token format used for securely transmitting claims between parties as a JSON object, commonly used for authentication and authorization in web applications.',
      entity: 'JWT',
      id: '359',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1727134420.0
    },
    {
      continent: ['AS'],
      create_time: 1731543856.0,
      data_type: 'Kazakhstan Passport Number',
      description:
        'The Kazakhstan Passport Number is a unique alphanumeric code assigned to each Kazakhstani passport holder, used to identify and authenticate the individual for international travel and legal documentation.',
      entity: 'PASSPORT_KAZAKHSTAN',
      id: '363',
      language: ['en', 'kk', 'ru'],
      region: ['KZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1731543905.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Korea Residence Registration Number',
      description:
        'A Korea Residence Registration Number, known as the "\uc8fc\ubbfc\ub4f1\ub85d\ubc88\ud638" (Jumin Deungrok Beonho), is a unique 13-digit identifier assigned to South Korean citizens and residents.',
      entity: 'RRN_KOREA',
      id: '268',
      language: ['en', 'kr'],
      region: ['KR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Kucoin API Key',
      description:
        'A Kucoin API Key provides secure, programmatic access to your Kucoin account, enabling automated trading, data retrieval, and integration with external applications.',
      entity: 'KC_AK',
      id: '392',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Kucoin API Secret',
      description:
        'A Kucoin API Secret is the private authentication key that, when paired with your API Key, securely verifies and authorizes trading activities, data requests, and integrations on the Kucoin exchange.',
      entity: 'KC_AS',
      id: '393',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Latvia Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Latvia upon issuance of their driver's license.",
      entity: 'DL_LATVIA',
      id: '81',
      language: ['en', 'lv'],
      region: ['LV'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Latvia Passport Number',
      description:
        'A unique identifier assigned to individuals in Latvia upon issuance of their passport',
      entity: 'PASSPORT_LATVIA',
      id: '104',
      language: ['en', 'lv'],
      region: ['LV'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Latvia Personal Code',
      description:
        'A unique identifier assigned to individuals in Latvia for identification and administrative purposes.',
      entity: 'NATIONAL_ID_LATVIA',
      id: '54',
      language: ['en', 'lv'],
      region: ['LV'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1728065917.0
    },
    {
      continent: ['EU'],
      create_time: 1722273600.0,
      data_type: 'Latvia Value Added Tax (VAT) Number',
      description:
        'A Latvia Value Added Tax (VAT) Number is a unique identifier assigned to businesses and entities registered for VAT in Latvia.',
      entity: 'VAT_LATVIA',
      id: '269',
      language: ['en', 'lv'],
      region: ['LV'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722273600.0
    },
    {
      continent: ['EU'],
      create_time: 1722277180.0,
      data_type: 'Liechtenstein Passport Number',
      description:
        "A Liechtenstein passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the government of Liechtenstein.",
      entity: 'PASSPORT_LIECHTENTEIN',
      id: '270',
      language: ['en', 'de'],
      region: ['LI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722277180.0
    },
    {
      continent: ['ALL'],
      create_time: 1736373948.0,
      data_type: 'Linear API Key',
      description:
        "The Linear API Key is a unique authentication token that grants secure access to the Linear API, enabling developers to interact programmatically with Linear's project management platform.",
      entity: 'LINEAR_AK',
      id: '412',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736375505.0
    },
    {
      continent: ['ALL'],
      create_time: 1736375241.0,
      data_type: 'Linear Client Secret',
      description:
        'The Linear Client Secret is a confidential key used alongside the API key to securely authenticate and authorize applications when interacting with the Linear API, ensuring safe access to user data and actions.',
      entity: 'LINEAR_CS',
      id: '413',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736375271.0
    },
    {
      continent: ['ALL'],
      create_time: 1734658037.0,
      data_type: 'Linkedin Client ID',
      description:
        'The LinkedIn Client ID is a unique identifier assigned to your application that enables secure access and integration with the LinkedIn API.',
      entity: 'LINKEDIN_CI',
      id: '394',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1734658139.0
    },
    {
      continent: ['ALL'],
      create_time: 1734658616.0,
      data_type: 'Linkedin Client Secret',
      description:
        'The LinkedIn Client Secret is a confidential key associated with your application that, when combined with the Client ID, authenticates requests and grants authorized access to LinkedIn\u2019s protected resources.',
      entity: 'LINKEDIN_CS',
      id: '395',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Lithuania Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Lithuania upon issuance of their driver's license.",
      entity: 'DL_LITHUANIA',
      id: '82',
      language: ['en', 'lt'],
      region: ['LT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Lithuania Passport Number',
      description:
        'A unique identifier assigned to individuals in Lithuania upon issuance of their passport',
      entity: 'PASSPORT_LITHUANIA',
      id: '103',
      language: ['en', 'lt'],
      region: ['LT'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Lithuania Personal Code',
      description:
        'A unique identifier assigned to individuals in Lithuania for identification and administrative purposes.',
      entity: 'NATIONAL_ID_LITHUANIA',
      id: '55',
      language: ['en', 'lt'],
      region: ['LT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722278164.0
    },
    {
      continent: ['EU'],
      create_time: 1722288863.0,
      data_type: 'Lithuania Tax Identification Number',
      description:
        'A Lithuania Tax Identification Number (TIN), known as the "Asmens kodas" for individuals or "Juridinio asmens kodas" for entities, is a unique identifier assigned by the Lithuanian tax authorities for tax purposes.',
      entity: 'TAX_ID_LITHUANIA',
      id: '271',
      language: ['en', 'lt'],
      region: ['LT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722288863.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Lithuania Value Added Tax (VAT) Number',
      description:
        'A Lithuania Value Added Tax (VAT) Number is a unique identifier assigned to businesses and entities registered for VAT in Lithuania.',
      entity: 'VAT_LITHUANIA',
      id: '272',
      language: ['en', 'lt'],
      region: ['LT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Luxembourg Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Luxembourg upon issuance of their driver's license.",
      entity: 'DL_LUXEMBOURG',
      id: '83',
      language: ['en', 'fr', 'de'],
      region: ['LU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Luxembourg National Identification Number',
      description:
        'A unique identifier assigned to individuals in Luxembourg for identification and administrative purposes.',
      entity: 'NATIONAL_ID_LUXEMBOURG',
      id: '56',
      language: ['en', 'fr', 'de'],
      region: ['LU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Luxembourg Passport Number',
      description:
        "A Luxembourg passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the Luxembourg government.",
      entity: 'PASSPORT_LUXEMBURG',
      id: '135',
      language: ['en', 'fr', 'de'],
      region: ['LU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1726074114.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Luxembourg Social Security Number',
      description:
        'A Luxembourg Social Security Number (Matricule de la S\u00e9curit\u00e9 Sociale) is a unique identifier assigned to individuals for social security purposes in Luxembourg.',
      entity: 'SSN_LUXEMBOURG',
      id: '149',
      language: ['en', 'fr', 'de'],
      region: ['LU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722290540.0
    },
    {
      continent: ['EU'],
      create_time: 1722290790.0,
      data_type: 'Luxembourg Tax Identification Number',
      description:
        'A Luxembourg Tax Identification Number (TIN) is a unique identifier assigned to individuals and entities for tax purposes by the Luxembourg tax authorities.',
      entity: 'TAX_ID_LUXEMBOURG',
      id: '273',
      language: ['en', 'fr', 'de'],
      region: ['LU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722290790.0
    },
    {
      continent: ['EU'],
      create_time: 1722291040.0,
      data_type: 'Luxembourg Value Added Tax (VAT) Number',
      description:
        'A Luxembourg Value Added Tax (VAT) Number is a unique identifier assigned to businesses and entities registered for VAT in Luxembourg.',
      entity: 'VAT_LUXEMBOURG',
      id: '274',
      language: ['en', 'fr', 'de'],
      region: ['LU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722291040.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'MAC Address',
      description:
        'The MAC address (Media Access Control address) is a unique identifier assigned to a network interface card (NIC) or network adapter.',
      entity: 'MAC_ADDRESS',
      id: '216',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721432672.0
    },
    {
      continent: ['EU'],
      create_time: 1741651916.0,
      data_type: 'Macedonia Unique Master Citizen Number',
      description:
        'The Macedonia Unique Master Citizen Number, known as the \u0415\u0434\u0438\u043d\u0441\u0442\u0432\u0435\u043d \u041c\u0430\u0442\u0438\u0447\u0435\u043d \u0411\u0440\u043e\u0458 \u043d\u0430 \u0413\u0440\u0430\u0453\u0430\u043d\u0438\u043d (\u0415\u041c\u0411\u0413), is a 13-digit personal identification number assigned to North Macedonian citizens for identification, taxation, and administrative purposes.',
      entity: 'UMCN_MACEDONIA',
      id: '466',
      language: ['en', 'mk'],
      region: ['MK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Mailchimp API Key',
      description:
        'The Mailchimp API Key is a unique identifier used to authenticate and grant secure access to your Mailchimp account, enabling seamless integration with third-party applications for managing email campaigns and audience data.',
      entity: 'MAILCHIMP_API_KEY',
      id: '328',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Mailgun API Key',
      description:
        "A Mailgun API Key is a secure, alphanumeric string used to authenticate and authorize access to Mailgun\u2019s email-sending services, enabling applications to send, receive, and track emails through Mailgun's API.",
      entity: 'MAILGUN_API_KEY',
      id: '335',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Malaysia Identification Card Number',
      description:
        'A unique identifier assigned to individuals in Malaysia upon issuance of their MyKad (Malaysian Identity Card).',
      entity: 'NATIONAL_ID_MALAYSIA',
      id: '36',
      language: ['en', 'ms'],
      region: ['MY'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722291789.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Malaysia Passport Number',
      description:
        "A Malaysia passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the Malaysian government.",
      entity: 'PASSPORT_MALAYSIA',
      id: '275',
      language: ['en', 'ms'],
      region: ['MY'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Malta Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Malta upon issuance of their driver's license.",
      entity: 'DL_MALTA',
      id: '84',
      language: ['en', 'mt'],
      region: ['MT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Malta Identity Card Number',
      description:
        'A unique identifier assigned to individuals in Malta upon issuance of their identity card (Identity Malta Card).',
      entity: 'NATIONAL_ID_MALTA',
      id: '57',
      language: ['en', 'mt'],
      region: ['MT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Malta Passport Number',
      description:
        'A unique identifier assigned to individuals in Malta upon issuance of their passport',
      entity: 'PASSPORT_MALTA',
      id: '102',
      language: ['en', 'mt'],
      region: ['MT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722296805.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Malta Tax Identification Number',
      description:
        'A Malta Tax Identification Number (TIN), known as the "Taxpayer Reference Number" (TRN), is a unique identifier assigned to individuals and entities for tax purposes by the Maltese tax authorities.',
      entity: 'TAX_ID_MALTA',
      id: '198',
      language: ['en', 'mt'],
      region: ['MT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1723670860.0
    },
    {
      continent: ['EU'],
      create_time: 1722297168.0,
      data_type: 'Malta Value Added Tax (VAT) Number',
      description:
        'A Malta Value Added Tax (VAT) Number is a unique identifier assigned to businesses and entities registered for VAT in Malta.',
      entity: 'VAT_MALTA',
      id: '276',
      language: ['en', 'mt'],
      region: ['MT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722297168.0
    },
    {
      continent: ['ALL'],
      create_time: 1739581792.0,
      data_type: 'Mattermost Access Token',
      description:
        'A Mattermost Access Token is a secure, unique string that allows users or third-party applications to authenticate and interact with the Mattermost platform, enabling actions such as posting messages or managing channels.',
      entity: 'MATTERMOST_AT',
      id: '450',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Medical Record Number',
      description:
        "A Medical Record Number (MRN) is a unique identifier assigned to a patient's medical records by a healthcare provider or institution, used to accurately track and manage an individual's medical history and healthcare information.",
      entity: 'MEDICAL_RECORD_NUMBER',
      id: '229',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Messagebird API Token',
      description:
        "The MessageBird API Token is a secure credential used to authenticate requests and enable access to MessageBird's communication APIs for sending messages, managing contacts, and other related services.",
      entity: 'Messagebird_AT',
      id: '411',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Messagebird Client ID',
      description:
        'The MessageBird Client ID is a unique identifier assigned to your MessageBird application, used to authenticate and integrate with MessageBird\u2019s communication APIs for sending messages, managing contacts, and other services.',
      entity: 'Messagebird_CI',
      id: '410',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Mexico CLABE Number',
      description:
        'The Mexico CLABE Number (Clave Bancaria Estandarizada) is an 18-digit standardized bank account number used for electronic funds transfers within Mexico, consisting of a bank code, branch code, account number, and a check digit for validation.',
      entity: 'CLABE_MEXICO',
      id: '301',
      language: ['en', 'es'],
      region: ['MX'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Mexico Driver License Number',
      description:
        "The Mexico Driver License Number is a unique alphanumeric code assigned to individuals who have been issued a driver's license in Mexico, typically including a combination of letters and numbers that may reflect personal information such as the individual's initials, birth date, and a sequential number specific to the issuing state or authority. This number is used to identify the license holder and track their driving record across the country.",
      entity: 'DL_MEXICO',
      id: '306',
      language: ['en', 'es'],
      region: ['MX'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Mexico Passport Number',
      description:
        'The Mexico Passport Number is a unique alphanumeric code assigned to each Mexican passport, typically consisting of nine characters, which include both letters and numbers, used to identify and authenticate the passport holder during international travel.',
      entity: 'PASSPORT_MEXICO',
      id: '303',
      language: ['en', 'es'],
      region: ['MX'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Mexico Personal Registration and Identification Number',
      description:
        'The Mexico Personal Registration and Identification Number (Clave de Registro e Identificaci\u00f3n Personal, CRIP) is a unique identification number assigned by the Mexican government to individuals for civil registration purposes. It is used primarily in official documents to track personal records, such as birth certificates and other civil registry documents, ensuring accurate identification and record-keeping across various government services.',
      entity: 'CRIP_MEXICO',
      id: '305',
      language: ['en', 'es'],
      region: ['MX'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Mexico Social Security Number',
      description:
        'The Mexico Social Security Number (N\u00famero de Seguridad Social, NSS) is an 11-digit unique identifier assigned by the Mexican Social Security Institute (IMSS) to each individual, used to track employment history, social security benefits, and healthcare services.',
      entity: 'NSS_MEXICO',
      id: '364',
      language: ['en', 'es'],
      region: ['MX'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Mexico Tax Identification Number',
      description:
        "The Mexico Tax Identification Number (Registro Federal de Contribuyentes, RFC) is a unique alphanumeric code assigned to individuals and entities in Mexico for tax purposes, composed of a combination of the individual's name, date of birth, or the company's name and registration date, followed by a verification code.",
      entity: 'TIN_MEXICO',
      id: '302',
      language: ['en', 'es'],
      region: ['MX'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'Mexico Unique Population Registry Code',
      description:
        'The Mexico Unique Population Registry Code (Clave \u00danica de Registro de Poblaci\u00f3n, CURP) is an 18-character alphanumeric code assigned to every Mexican citizen and resident, used as a national identification number for various administrative purposes, derived from personal information such as name, date of birth, and place of birth.',
      entity: 'CURP_MEXICO',
      id: '304',
      language: ['en', 'es'],
      region: ['MX'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: *********3.0,
      data_type: 'Montenegro Unique Master Citizen Number',
      description:
        'The Montenegro Unique Master Citizen Number, known as the Jedinstveni Mati\u010dni Broj Gra\u0111ana (JMBG), is a 13-digit personal identification number assigned to Montenegrin citizens and residents for legal, taxation, and administrative purposes.',
      entity: 'UMCN_MONTENEGRO',
      id: '467',
      language: ['en', 'me'],
      region: ['ME'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741653473.0
    },
    {
      continent: ['ALL'],
      create_time: 1736807082.0,
      data_type: 'NPM Access Token',
      description:
        'The NPM Access Token is a secure credential used to authenticate with the npm registry, enabling users or applications to publish, install, and manage packages securely.',
      entity: 'NPM_AT',
      id: '418',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Netherlands Bank Account Number',
      description:
        "A Netherlands bank account number is a unique identifier assigned to an individual's or entity's bank account within the Netherlands' banking system.",
      entity: 'BANK_ACCOUNT_NUMBER_NETHERLANDS',
      id: '256',
      language: ['en', 'nl'],
      region: ['NL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Netherlands Citizens Service (BSN) Number',
      description:
        'A unique personal identification number assigned to residents of the Netherlands for administrative purposes.',
      entity: 'NATIONAL_ID_NETHERLANDS_BSN',
      id: '37',
      language: ['en', 'nl'],
      region: ['NL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Netherlands Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Netherlands upon issuance of their driver's license.",
      entity: 'DL_NETHERLANDS',
      id: '85',
      language: ['en', 'nl'],
      region: ['NL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Netherlands Passport Number',
      description:
        'A unique identifier assigned to individuals in Netherlands upon issuance of their passport',
      entity: 'PASSPORT_NETHERLANDS',
      id: '120',
      language: ['en', 'nl'],
      region: ['NL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Netherlands Tax Identification Number',
      description:
        'The Netherlands Tax Identification Number (TIN), known as the Burgerservicenummer (BSN) for individuals and Rechtspersonen en Samenwerkingsverbanden Informatienummer (RSIN) for businesses, is a unique number assigned for tax, social security, and administrative purposes.',
      entity: 'TIN_NETHERLANDS',
      id: '468',
      language: ['en'],
      region: ['NL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741714637.0
    },
    {
      continent: ['EU'],
      create_time: 1722376384.0,
      data_type: 'Netherlands Value Added Tax (VAT) Number',
      description:
        'A Netherlands Value Added Tax (VAT) Number is a unique identifier assigned to businesses and entities registered for VAT in the Netherlands.',
      entity: 'VAT_NETHERLANDS',
      id: '277',
      language: ['en', 'nl'],
      region: ['NL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722376384.0
    },
    {
      continent: ['ALL'],
      create_time: 1736813140.0,
      data_type: 'Netlify Access Token',
      description:
        "The Netlify Access Token is a secure credential used to authenticate API requests, enabling access to Netlify's services for managing deployments, sites, and other resources.",
      entity: 'NETLIFY_AT',
      id: '420',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736813167.0
    },
    {
      continent: ['ALL'],
      create_time: 1736469460.0,
      data_type: 'New Relic Browser API Token',
      description:
        'The New Relic Browser API Token is a secure credential used to authenticate and integrate your web application with New Relic\u2019s Browser monitoring services, enabling detailed tracking and performance analysis of front-end user interactions.',
      entity: 'NEWRELIC_BAT',
      id: '414',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736469553.0
    },
    {
      continent: ['ALL'],
      create_time: 1736469775.0,
      data_type: 'New Relic Insert Key',
      description:
        'The New Relic Insert Key is a secure credential used to send custom event and log data to New Relic\u2019s platform, enabling real-time monitoring and analytics for applications and systems.',
      entity: 'NEWRELIC_IK',
      id: '415',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'New Relic User API ID',
      description:
        'The New Relic User API ID is a unique identifier associated with a user account, used to authenticate and interact with New Relic\u2019s API for managing account-specific configurations and accessing data.',
      entity: 'NEWRELIC_UAI',
      id: '416',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'New Relic User API Key',
      description:
        "The New Relic User API Key is a secure credential tied to a specific user account, used to authenticate API requests and manage account settings or retrieve data from New Relic's platform.",
      entity: 'NEWRELIC_UAK',
      id: '417',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'New Zealand Bank Account Number',
      description:
        'In New Zealand, a Bank Account Number is a unique identifier assigned to each individual bank account for the purpose of managing and processing transactions.',
      entity: 'BANK_ACCOUNT_NUMBER_NZ',
      id: '201',
      language: ['en'],
      region: ['NZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: "New Zealand Driver's License Number",
      description:
        "A New Zealand driver's license number is a unique alphanumeric identifier assigned to an individual's driver's license issued by the New Zealand government.",
      entity: 'DL_NEW_ZEALAND',
      id: '141',
      language: ['en', 'mi', 'nzsl'],
      region: ['NZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'New Zealand National Health Index Number',
      description:
        'The New Zealand National Health Index (NHI) Number is a unique identifier assigned to individuals for the purpose of managing their health records and ensuring continuity of care within the New Zealand healthcare system.',
      entity: 'NHI_NEW_ZEALAND',
      id: '278',
      language: ['en', 'mi', 'nzsl'],
      region: ['NZ'],
      type: 'Regex',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'New Zealand Passport Number',
      description:
        "A New Zealand passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the New Zealand government.",
      entity: 'PASSPORT_NEW_ZEALAND',
      id: '138',
      language: ['en', 'mi', 'nzsl'],
      region: ['NZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['OC'],
      create_time: **********.0,
      data_type: 'New Zealand Social Welfare Number',
      description:
        'The New Zealand Social Welfare Number refers to the Client Number issued by the Ministry of Social Development (MSD) for individuals receiving government benefits, pensions, or social support.',
      entity: 'SWN_NEWZEALAND',
      id: '479',
      language: ['en'],
      region: ['NZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741822992.0
    },
    {
      continent: ['OC'],
      create_time: 1722379981.0,
      data_type: 'New Zealand Tax Identification Number',
      description:
        'A New Zealand Tax Identification Number (TIN), also known as an Inland Revenue Department (IRD) number, is a unique identifier assigned to individuals and entities for tax purposes by the New Zealand Inland Revenue Department.',
      entity: 'TAX_ID_NEW_ZEALAND',
      id: '280',
      language: ['en', 'mi', 'nzsl'],
      region: ['NZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722379981.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Norway Driver's Licence Number",
      description:
        "A Norway Driver's Licence Number is a unique identifier assigned to an individual's driver's licence issued by the Norwegian authorities.",
      entity: 'DL_NORWAY',
      id: '279',
      language: ['en', 'nb', 'nn'],
      region: ['NO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Norway Health Insurance Card Number',
      description:
        'The Norway Health Insurance Card Number is a unique identifier assigned to individuals to verify their entitlement to healthcare services within Norway and the European Economic Area (EEA).',
      entity: 'NICN_NORWAY',
      id: '307',
      language: ['en', 'nb', 'nn'],
      region: ['NO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Norway Identification Number',
      description:
        'A unique identifier assigned to individuals in Norway for identification and administrative purposes.',
      entity: 'NATIONAL_ID_NORWAY',
      id: '38',
      language: ['en', 'nb', 'nn'],
      region: ['NO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Okta Access Token',
      description:
        "The Okta Access Token is a secure, time-bound credential used to authenticate and authorize API requests, enabling access to Okta's identity and access management services.",
      entity: 'OKTA_AT',
      id: '419',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736809901.0
    },
    {
      continent: ['ALL'],
      create_time: 1735585206.0,
      data_type: 'OpenAI API Key',
      description:
        "The OpenAI API Key is a unique, private credential used to authenticate requests and access OpenAI's suite of AI-powered services and models.",
      entity: 'OPENAI_AK',
      id: '399',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1735585551.0
    },
    {
      continent: ['ALL'],
      create_time: 1726769703.0,
      data_type: 'PGP Private Key',
      description:
        'A PGP Private Key is a cryptographic key used in Pretty Good Privacy (PGP) encryption for decrypting messages or signing data, ensuring confidentiality and authenticity; it must be kept secret and is paired with a publicly shared key for secure communication.',
      entity: 'PGP_PRIVATE_KEY',
      id: '332',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726769738.0
    },
    {
      continent: ['ALL'],
      create_time: 1734130046.0,
      data_type: 'PYPI Upload Token',
      description:
        'A secure, single-purpose authentication token enabling seamless, authorized uploads of Python packages to the PyPI repository.',
      entity: 'PYPI_TOKEN',
      id: '391',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Password',
      description:
        'A password is a confidential string of characters used to authenticate and grant access to an account, system, or resource, ensuring security and protecting against unauthorized access.',
      entity: 'PASSWORD',
      id: '238',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'PayPal Braintree Access Token',
      description:
        'The PayPal Braintree Access Token is a secure credential that allows your application to authenticate and interact with the Braintree payment gateway, enabling transactions and managing payment methods within the PayPal ecosystem.',
      entity: 'PAYPAL_BRAINTREE_ACCESS_TOKEN',
      id: '330',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Personal Identification Number',
      description:
        "A Personal Identification Number (PIN) is a unique numeric or alphanumeric code used to verify an individual's identity and provide secure access to various systems, accounts, or services.",
      entity: 'PIN',
      id: '225',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Philippines Passport Number',
      description:
        "A Philippines passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the Philippine government.",
      entity: 'PASSPORT_PHILIPPINES',
      id: '281',
      language: ['en', 'fil'],
      region: ['PH'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Philippines Unified Multi-purpose Identification Number',
      description:
        'A unique identifier issued by the Philippine government to Filipino citizens and residents.',
      entity: 'NATIONAL_ID_PHILIPPINES',
      id: '39',
      language: ['en'],
      region: ['PH'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720204598.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Phone Number',
      description:
        'The phone number is a sequence of digits assigned to a telephone line or device.',
      entity: 'PHONE_NUMBER',
      id: '7',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1721432618.0
    },
    {
      continent: ['ALL'],
      create_time: 1735342573.0,
      data_type: 'Plaid API Token',
      description:
        'The Plaid API Token is a secure, unique identifier used to authenticate and facilitate communication between your application and Plaid\u2019s financial data integration services.',
      entity: 'PLAID_AT',
      id: '396',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Plaid Client ID',
      description:
        'The Plaid Client ID is a unique identifier assigned to your Plaid account, used to authenticate API requests and link your application to Plaid\u2019s financial data services.',
      entity: 'PLAID_CI',
      id: '397',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Plaid Secret Key',
      description:
        "The Plaid Secret Key is a confidential credential used alongside the Client ID to securely authenticate API requests and access Plaid's financial data services.",
      entity: 'PLAID_SK',
      id: '398',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Poland Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Poland upon issuance of their driver's license.",
      entity: 'DL_POLAND',
      id: '86',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Poland European Health Insurance Number',
      description:
        'A Poland European Health Insurance Card (EHIC) Number is a unique identifier assigned to individuals who hold a European Health Insurance Card issued by Poland.',
      entity: 'EHI_POLAND',
      id: '282',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Poland Identity Card Number',
      description:
        'A unique identifier assigned to individuals in Poland upon issuance of their identity card (Dow\u00f3d osobisty).',
      entity: 'NATIONAL_ID_POLAND_IC',
      id: '40',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Poland National Identification Number (PESEL)',
      description:
        'A unique identifier assigned to individuals in Poland for identification and administrative purposes.',
      entity: 'NATIONAL_ID_POLAND_PESEL',
      id: '41',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1722382418.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Poland Passport Number',
      description:
        'A unique identifier assigned to individuals in Poland upon issuance of their passport',
      entity: 'PASSPORT_POLAND',
      id: '118',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722383480.0
    },
    {
      continent: ['EU'],
      create_time: 1741824075.0,
      data_type: 'Poland REGON Number',
      description:
        'The Poland REGON Number is a 9- or 14-digit unique business identifier issued by the Central Statistical Office of Poland (GUS) to companies, organizations, and entrepreneurs for statistical and administrative purposes.',
      entity: 'REGON_POLAND',
      id: '480',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741824118.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Poland Tax Identification Number (NIP)',
      description:
        'An identifier assigned to businesses and other entities in Poland for tax purposes.',
      entity: 'TAX_ID_POLAND_NIP',
      id: '182',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722382562.0
    },
    {
      continent: ['EU'],
      create_time: 1722384407.0,
      data_type: 'Poland Value Added Tax (VAT) Number',
      description:
        'A Poland Value Added Tax (VAT) Number, known as the "Numer Identyfikacji Podatkowej" (NIP), is a unique identifier assigned to businesses and entities registered for VAT in Poland.',
      entity: 'VAT_POLAND',
      id: '283',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722384407.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Portugal Citizen Card Number',
      description:
        'A unique identifier assigned to individuals in Portugal upon issuance of their citizen card (Cart\u00e3o de Cidad\u00e3o).',
      entity: 'NATIONAL_ID_PORTUGAL',
      id: '58',
      language: ['en', 'pt'],
      region: ['PT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Portugal Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Portugal upon issuance of their driver's license.",
      entity: 'DL_PORTUGAL',
      id: '87',
      language: ['en', 'pt'],
      region: ['PT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Portugal Passport Number',
      description:
        'A unique identifier assigned to individuals in Portugal upon issuance of their passport',
      entity: 'PASSPORT_PORTUGAL',
      id: '101',
      language: ['en', 'pt'],
      region: ['PT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Portugal Social Security Number',
      description:
        'A Portugal Social Security Number (N\u00famero de Identifica\u00e7\u00e3o da Seguran\u00e7a Social, NISS) is a unique identifier assigned to individuals for social security purposes in Portugal.',
      entity: 'SSN_PORTUGAL',
      id: '148',
      language: ['en', 'pt'],
      region: ['PT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722385591.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Portugal Tax Identification Number',
      description:
        'The Portugal Tax Identification Number (N\u00famero de Identifica\u00e7\u00e3o Fiscal, NIF) is a unique 9-digit identifier assigned to individuals and entities for tax purposes in Portugal.',
      entity: 'TAX_ID_PORTUGAL',
      id: '193',
      language: ['en', 'pt'],
      region: ['PT'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1728410755.0
    },
    {
      continent: ['EU'],
      create_time: 1722385776.0,
      data_type: 'Portugal Value Added Tax (VAT) Number',
      description:
        'A Portugal Value Added Tax (VAT) Number, known as the "N\u00famero de Identifica\u00e7\u00e3o Fiscal" (NIF) for individuals and entities, is a unique identifier assigned to those registered for VAT in Portugal.',
      entity: 'VAT_PORTUGAL',
      id: '284',
      language: ['en', 'pt'],
      region: ['PT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722385776.0
    },
    {
      continent: ['ALL'],
      create_time: 1736900374.0,
      data_type: 'Postman API Token',
      description:
        'The Postman API Token is a secure credential used to authenticate and enable access to Postman\u2019s API, allowing users to manage collections, environments, workspaces, and other Postman resources programmatically.',
      entity: 'POSTMAN_AT',
      id: '421',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736900475.0
    },
    {
      continent: ['ALL'],
      create_time: 1726769943.0,
      data_type: 'RSA Private Key',
      description:
        'An RSA Private Key is a cryptographic key used in the RSA encryption algorithm to decrypt data that has been encrypted with the corresponding public key, or to digitally sign data to verify authenticity; it must be kept secure to ensure the integrity and confidentiality of encrypted communications.',
      entity: 'RSA_PRIVATE_KEY',
      id: '333',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Romania Driver's License Number",
      description:
        "A Romania driver's license number is a unique alphanumeric identifier assigned to an individual's driver's license issued by the Romanian government.",
      entity: 'DL_ROMANIA',
      id: '139',
      language: ['en', 'ro'],
      region: ['RO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Romania Passport Number',
      description:
        'A unique identifier assigned to individuals in Romania upon issuance of their passport',
      entity: 'PASSPORT_ROMANIA',
      id: '100',
      language: ['en', 'ro'],
      region: ['RO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Romania Personal Numeric Code (CNP)',
      description:
        'A unique identifier assigned to individuals in Romania for identification and administrative purposes.',
      entity: 'NATIONAL_ID_ROMANIA_CNP',
      id: '60',
      language: ['en', 'ro'],
      region: ['RO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722445024.0
    },
    {
      continent: ['EU'],
      create_time: 1722445524.0,
      data_type: 'Romania Value Added Tax (VAT) Number',
      description:
        'A Romania Value Added Tax (VAT) Number, known as the "Cod de Identificare Fiscal\u0103" (CIF) or "Num\u0103r de Identificare Fiscal\u0103" (NIF), is a unique identifier assigned to businesses and entities registered for VAT in Romania.',
      entity: 'VAT_ROMANIA',
      id: '285',
      language: ['en', 'ro'],
      region: ['RO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722445524.0
    },
    {
      continent: ['ALL'],
      create_time: 1737420698.0,
      data_type: 'RubyGems API Token',
      description:
        'A RubyGems API Token is a secure authentication token used to interact with the RubyGems.org API, allowing users to publish, manage, and access Ruby gems programmatically.',
      entity: 'RUBYGEMS_AT',
      id: '431',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Russia Insurance Account Number (SNILS)',
      description:
        'The Russia Insurance Account Number, known as SNILS (\u0421\u0442\u0440\u0430\u0445\u043e\u0432\u043e\u0439 \u043d\u043e\u043c\u0435\u0440 \u0438\u043d\u0434\u0438\u0432\u0438\u0434\u0443\u0430\u043b\u044c\u043d\u043e\u0433\u043e \u043b\u0438\u0446\u0435\u0432\u043e\u0433\u043e \u0441\u0447\u0451\u0442\u0430), is a unique personal identifier issued to Russian citizens and residents for social security, pension, and healthcare services, represented by an 11-digit code on a green card.',
      entity: 'SNILS_RUSSIA',
      id: '367',
      language: ['en', 'ru'],
      region: ['RU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Russia Taxpayer Identification Number',
      description:
        'The Russian Taxpayer Identification Number (\u0418\u041d\u041d, or INN) is a unique numeric identifier assigned by the Federal Tax Service to individuals and legal entities in Russia for tax reporting and identification purposes.',
      entity: 'TIN_RUSSIA',
      id: '366',
      language: ['en', 'ru'],
      region: ['RU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'SEPA Creditor Identifier Number for Austria',
      description:
        'The SEPA Creditor Identifier (CI) Number for Austria is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, facilitating the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_AT',
      id: '318',
      language: ['en', 'de'],
      region: ['AT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: 1724799258.0,
      data_type: 'SEPA Creditor Identifier Number for Belgium',
      description:
        'The SEPA Creditor Identifier (CI) Number for Belgium is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, allowing for the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_BE',
      id: '320',
      language: ['en', 'fr', 'de', 'nl'],
      region: ['BE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724799258.0
    },
    {
      continent: ['EU'],
      create_time: 1724801272.0,
      data_type: 'SEPA Creditor Identifier Number for Bulgaria',
      description:
        'The SEPA Creditor Identifier (CI) Number for Bulgaria is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, allowing for the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_BG',
      id: '327',
      language: ['en', 'bg'],
      region: ['BG'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1728600370.0
    },
    {
      continent: ['EU'],
      create_time: 1724801007.0,
      data_type: 'SEPA Creditor Identifier Number for Czech',
      description:
        'The SEPA Creditor Identifier (CI) Number for the Czech Republic is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, allowing for the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_CZ',
      id: '325',
      language: ['en', 'cs'],
      region: ['CZ'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724801007.0
    },
    {
      continent: ['EU'],
      create_time: 1724696514.0,
      data_type: 'SEPA Creditor Identifier Number for Denmark',
      description:
        'The SEPA Creditor Identifier (CI) Number for Denmark is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, facilitating the identification of the creditor across SEPA member countries.',
      entity: 'SEPA_ID_DK',
      id: '312',
      language: ['en', 'da'],
      region: ['DK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724696514.0
    },
    {
      continent: ['EU'],
      create_time: 1724696369.0,
      data_type: 'SEPA Creditor Identifier Number for Finland',
      description:
        'The SEPA Creditor Identifier (CI) Number for Finland is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, ensuring the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_FI',
      id: '311',
      language: ['en', 'fi', 'sv'],
      region: ['FI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724696369.0
    },
    {
      continent: ['EU'],
      create_time: 1724799360.0,
      data_type: 'SEPA Creditor Identifier Number for France',
      description:
        'The SEPA Creditor Identifier (CI) Number for France is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, enabling the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_FR',
      id: '321',
      language: ['en', 'fr'],
      region: ['FR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724799360.0
    },
    {
      continent: ['EU'],
      create_time: 1724797359.0,
      data_type: 'SEPA Creditor Identifier Number for Germany',
      description:
        'The SEPA Creditor Identifier (CI) Number for Germany is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, enabling the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_DE',
      id: '316',
      language: ['en', 'de'],
      region: ['DE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724797359.0
    },
    {
      continent: ['EU'],
      create_time: 1724800592.0,
      data_type: 'SEPA Creditor Identifier Number for Hungary',
      description:
        'The SEPA Creditor Identifier (CI) Number for Hungary is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, enabling the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_HU',
      id: '324',
      language: ['en', 'hu'],
      region: ['HU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724800592.0
    },
    {
      continent: ['EU'],
      create_time: 1724696058.0,
      data_type: 'SEPA Creditor Identifier Number for Ireland',
      description:
        'The SEPA Creditor Identifier (CI) Number for Ireland is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, enabling the identification of the creditor across SEPA member states.',
      entity: 'SEPA_ID_IE',
      id: '310',
      language: ['en', 'ga'],
      region: ['IE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724696058.0
    },
    {
      continent: ['EU'],
      create_time: 1724707251.0,
      data_type: 'SEPA Creditor Identifier Number for Italy',
      description:
        'The SEPA Creditor Identifier (CI) Number for Italy is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, enabling the identification of the creditor across SEPA member states.',
      entity: 'SEPA_ID_IT',
      id: '314',
      language: ['en', 'it'],
      region: ['IT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724707251.0
    },
    {
      continent: ['EU'],
      create_time: 1724797772.0,
      data_type: 'SEPA Creditor Identifier Number for Luxembourg',
      description:
        'The SEPA Creditor Identifier (CI) Number for Luxembourg is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, enabling the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_LU',
      id: '319',
      language: ['en', 'fr', 'de'],
      region: ['LU'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724797772.0
    },
    {
      continent: ['EU'],
      create_time: 1724797497.0,
      data_type: 'SEPA Creditor Identifier Number for Netherlands',
      description:
        'The SEPA Creditor Identifier (CI) Number for the Netherlands is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, allowing for the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_NL',
      id: '317',
      language: ['en', 'nl'],
      region: ['NL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724797497.0
    },
    {
      continent: ['EU'],
      create_time: 1724800263.0,
      data_type: 'SEPA Creditor Identifier Number for Poland',
      description:
        'The SEPA Creditor Identifier (CI) Number for Poland is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, allowing for the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_PL',
      id: '322',
      language: ['en', 'pl'],
      region: ['PL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724800263.0
    },
    {
      continent: ['EU'],
      create_time: 1724707089.0,
      data_type: 'SEPA Creditor Identifier Number for Portugal',
      description:
        'The SEPA Creditor Identifier (CI) Number for Portugal is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, allowing for the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_PT',
      id: '313',
      language: ['en', 'pt'],
      region: ['PT'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724707089.0
    },
    {
      continent: ['EU'],
      create_time: 1724800427.0,
      data_type: 'SEPA Creditor Identifier Number for Romania',
      description:
        'The SEPA Creditor Identifier (CI) Number for Romania is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, facilitating the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_RO',
      id: '323',
      language: ['en', 'ro'],
      region: ['RO'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724800427.0
    },
    {
      continent: ['EU'],
      create_time: 1724801147.0,
      data_type: 'SEPA Creditor Identifier Number for Slovakia',
      description:
        'The SEPA Creditor Identifier (CI) Number for Slovakia is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, enabling the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_SK',
      id: '326',
      language: ['en', 'sk'],
      region: ['SK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724801147.0
    },
    {
      continent: ['EU'],
      create_time: 1724707374.0,
      data_type: 'SEPA Creditor Identifier Number for Spain',
      description:
        'The SEPA Creditor Identifier (CI) Number for Spain is a unique alphanumeric code assigned to organizations authorized to collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, allowing for the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_ES',
      id: '315',
      language: ['en', 'es'],
      region: ['ES'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724707374.0
    },
    {
      continent: ['EU'],
      create_time: 1724695895.0,
      data_type: 'SEPA Creditor Identifier Number for Sweden',
      description:
        'The SEPA Creditor Identifier (CI) Number for Sweden is a unique alphanumeric code assigned to organizations authorized to collect payments via the Single Euro Payments Area (SEPA) Direct Debit system, allowing for the identification of the creditor across all SEPA member countries.',
      entity: 'SEPA_ID_SE',
      id: '309',
      language: ['en', 'sv'],
      region: ['SE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724695895.0
    },
    {
      continent: ['EU'],
      create_time: 1724695445.0,
      data_type: 'SEPA Creditor Identifier Number for the United Kingdom',
      description:
        'The SEPA Creditor Identifier (CI) Number for the United Kingdom is a unique alphanumeric code assigned to organizations that collect payments through the Single Euro Payments Area (SEPA) Direct Debit system, enabling the identification of the creditor across SEPA member states.',
      entity: 'SEPA_ID_UK',
      id: '308',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1724695445.0
    },
    {
      continent: ['ALL'],
      create_time: 1742942826.0,
      data_type: 'SQL Server Connection String',
      description:
        'An SQL Server connection string is a formatted string containing authentication details and parameters that allow applications to connect to a Microsoft SQL Server database.',
      entity: 'CS_SQLSERVER',
      id: '494',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1742942946.0
    },
    {
      continent: ['ALL'],
      create_time: 1727135111.0,
      data_type: 'SSH Private Key',
      description:
        'An SSH private key is a cryptographic key used for secure authentication in SSH (Secure Shell) connections, enabling access to remote systems by proving the identity of the user without transmitting the key over the network.',
      entity: 'SSH_PRIVATE_KEY',
      id: '360',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1727135136.0
    },
    {
      continent: ['ALL'],
      create_time: 1727135202.0,
      data_type: 'SSH Public Key',
      description:
        'An SSH public key is a cryptographic key paired with a private key, used to authenticate a user on a remote system by allowing the system to verify the identity of the user without exposing sensitive credentials during an SSH connection.',
      entity: 'SSH_PUBLIC_KEY',
      id: '361',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'SWIFT Code',
      description:
        'A SWIFT Code (also known as BIC \u2013 Bank Identifier Code) is an internationally recognized code used to identify a specific bank or financial institution in international transactions.',
      entity: 'SWIFT',
      id: '491',
      language: ['en', 'fr', 'es', 'de'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10002'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Saudi Arabia National ID',
      description:
        'A unique identifier assigned to individuals in Saudi Arabia upon issuance of their national identity card (\u0627\u0644\u0647\u0648\u064a\u0629 \u0627\u0644\u0648\u0637\u0646\u064a\u0629, Al-Hawiyya Al-Wataniyya).',
      entity: 'NATIONAL_ID_SAUDI',
      id: '42',
      language: ['en'],
      region: ['SA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'SendGrid API Token',
      description:
        'The SendGrid API Token is a secure authentication key that allows applications to interact with the SendGrid email service, enabling users to send emails, manage contacts, and access other email-related features programmatically.',
      entity: 'SENDGRID_AT',
      id: '423',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736979066.0
    },
    {
      continent: ['ALL'],
      create_time: 1735694064.0,
      data_type: 'Sendbird Access ID',
      description:
        'The Sendbird Access ID is a unique identifier for your application on the Sendbird platform, used to authenticate and enable access to Sendbird\u2019s communication APIs and services.',
      entity: 'SENDBIRD_AI',
      id: '403',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1735694131.0
    },
    {
      continent: ['ALL'],
      create_time: 1735694197.0,
      data_type: 'Sendbird Access Token',
      description:
        'The Sendbird Access Token is a secure credential used to authenticate users or applications, enabling authorized access to Sendbird\u2019s chat and communication APIs.',
      entity: 'SENDBIRD_AT',
      id: '404',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1735694246.0
    },
    {
      continent: ['EU'],
      create_time: 1731978956.0,
      data_type: 'Serbia Unique Master Citizen Number',
      description:
        'The Serbia Unique Master Citizen Number (Jedinstveni mati\u010dni broj gra\u0111ana - JMBG) is a unique 13-digit identifier assigned to each Serbian citizen for personal identification in official records and administrative systems.',
      entity: 'UMCN_SERBIA',
      id: '368',
      language: ['en', 'sr'],
      region: ['RS'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1731979303.0
    },
    {
      continent: ['EU'],
      create_time: 1731979853.0,
      data_type: 'Serbia Value Added Tax (VAT) Number',
      description:
        'The Serbia Value Added Tax (VAT) Number is a unique identifier assigned to businesses registered for VAT in Serbia, consisting of a prefix "RS" followed by 9 digits, used for tax reporting and transactions.',
      entity: 'VAT_SERBIA',
      id: '369',
      language: ['en', 'sr'],
      region: ['RS'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1731979901.0
    },
    {
      continent: ['ALL'],
      create_time: 1733186888.0,
      data_type: 'Shopify Access Token',
      description:
        "A Shopify Access Token is a secure key used to authenticate and authorize third-party apps or custom integrations to interact with a Shopify store's API, enabling access to store data and resources.",
      entity: 'SHOPIFY_AT',
      id: '383',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1733188931.0
    },
    {
      continent: ['ALL'],
      create_time: 1733188677.0,
      data_type: 'Shopify Custom Access Token',
      description:
        "A Shopify Custom Access Token is a unique key used to authenticate private apps or custom integrations, providing secure, direct access to a specific Shopify store's API for tailored functionalities.",
      entity: 'SHOPIFY_CAT',
      id: '384',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1733188916.0
    },
    {
      continent: ['AS'],
      create_time: 1722446381.0,
      data_type: 'Singapore Driving Licence Number',
      description:
        "A Singapore Driving Licence Number is a unique alphanumeric identifier assigned to an individual's driving licence issued by the Singapore Traffic Police.",
      entity: 'DL_SINGAPORE',
      id: '286',
      language: ['en', 'ms', 'zh', 'ta'],
      region: ['SG'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722446381.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Singapore National Registration Identity Card (NRIC) Number',
      description:
        'An identifier assigned to Singaporean citizens and permanent residents upon issuance of their National Registration Identity Card (NRIC).',
      entity: 'NATIONAL_ID_SINGAPORE_NRIC',
      id: '24',
      language: ['en', 'ms', 'zh', 'ta'],
      region: ['SG'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1728065925.0
    },
    {
      continent: ['AS'],
      create_time: 1722446693.0,
      data_type: 'Singapore Passport Number',
      description:
        "A Singapore passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the Singapore government.",
      entity: 'PASSPORT_SINGAPORE',
      id: '287',
      language: ['en', 'ms', 'zh', 'ta'],
      region: ['SG'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722446693.0
    },
    {
      continent: ['AS'],
      create_time: 1722447143.0,
      data_type: 'Singapore Unique Entity Number (UEN)',
      description:
        'A Singapore Unique Entity Number (UEN) is a unique identifier assigned to entities such as businesses, societies, and public organizations registered in Singapore.',
      entity: 'UEN_SINGAPORE',
      id: '289',
      language: ['en', 'ms', 'zh', 'ta'],
      region: ['SG'],
      type: 'Regex',
      type_category: ['20004'],
      update_time: 1722447143.0
    },
    {
      continent: ['ALL'],
      create_time: 1726872928.0,
      data_type: 'Slack App-level Token',
      description:
        'A Slack App-level Token is a secure, unique token that authenticates and authorizes the Slack app itself, rather than a user, allowing the app to perform tasks like subscribing to events or using Socket Mode for real-time communication, with access limited by app-level scopes and permissions.',
      entity: 'SLACK_AT',
      id: '351',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726873534.0
    },
    {
      continent: ['ALL'],
      create_time: 1726872351.0,
      data_type: 'Slack Bot Token',
      description:
        "A Slack Bot Token is a secure, unique token used to authenticate and authorize a bot in Slack, allowing it to interact with channels, send messages, and perform actions on behalf of the bot according to the permissions and scopes assigned during the bot's setup.",
      entity: 'SLACK_BT',
      id: '349',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726872385.0
    },
    {
      continent: ['ALL'],
      create_time: 1726874004.0,
      data_type: 'Slack Configuration Access Token',
      description:
        "A Slack Configuration Access Token is a secure token that grants authorization to access and modify the configuration settings of a Slack app or workspace, enabling actions such as managing features, permissions, and integrations tied to the app's setup.",
      entity: 'SLACK_CAT',
      id: '352',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726874029.0
    },
    {
      continent: ['ALL'],
      create_time: 1726874103.0,
      data_type: 'Slack Configuration Refresh Token',
      description:
        "A Slack Configuration Refresh Token is a long-lived token used to obtain a new access token when the current configuration access token expires, ensuring continuous access to modify and manage a Slack app or workspace's configuration without user intervention.",
      entity: 'SLACK_CRT',
      id: '353',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726874306.0
    },
    {
      continent: ['ALL'],
      create_time: 1733186092.0,
      data_type: 'Slack Legacy Token',
      description:
        "A Slack Legacy Token is an authentication token that grants access to Slack's API for individual or workspace-level operations.",
      entity: 'SLACK_LT',
      id: '382',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1733186133.0
    },
    {
      continent: ['ALL'],
      create_time: 1733185095.0,
      data_type: 'Slack Legacy Workspace Token',
      description:
        "A Slack Legacy Workspace Token is a type of authentication token that grants access to a Slack workspace's APIs.",
      entity: 'SLACK_LWT',
      id: '381',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Slack User Token',
      description:
        "A Slack User Token is a secure, unique token that provides authentication and authorization for an individual user's account in Slack, enabling third-party apps or integrations to perform actions on behalf of the user, such as sending messages or accessing user-specific data, based on the permissions granted.",
      entity: 'SLACK_UT',
      id: '350',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Slovakia Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Slovakia upon issuance of their driver's license.",
      entity: 'DL_SLOVAKIA',
      id: '88',
      language: ['en', 'sk'],
      region: ['SK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Slovakia Passport Number',
      description:
        'A unique identifier assigned to individuals in Slovakia upon issuance of their passport',
      entity: 'PASSPORT_SLOVAKIA',
      id: '99',
      language: ['en', 'sk'],
      region: ['SK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Slovakia Personal Number',
      description:
        'A unique identifier assigned to individuals in Slovakia for identification and administrative purposes.',
      entity: 'NATIONAL_ID_SLOVAKIA',
      id: '61',
      language: ['en', 'sk'],
      region: ['SK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722448067.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Slovakia Value Added Tax (VAT) Number',
      description:
        'A Slovakia Value Added Tax (VAT) Number, known as the "Identifika\u010dn\u00e9 \u010d\u00edslo pre da\u0148" (I\u010c DPH), is a unique identifier assigned to businesses and entities registered for VAT in Slovakia.',
      entity: 'VAT_SLOVAKIA',
      id: '290',
      language: ['en', 'sk'],
      region: ['SK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Slovenia Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Slovenia upon issuance of their driver's license.",
      entity: 'DL_SLOVENIA',
      id: '89',
      language: ['en'],
      region: ['SI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Slovenia Passport Number',
      description:
        'A unique identifier assigned to individuals in Slovenia upon issuance of their passport',
      entity: 'PASSPORT_SLOVENIA',
      id: '98',
      language: ['en', 'sl'],
      region: ['SI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Slovenia Tax Identification Number',
      description:
        'A Slovenia Tax Identification Number (TIN), known as the "Dav\u010dna \u0161tevilka," is a unique identifier assigned to individuals and entities for tax purposes by the Slovenian tax authorities.',
      entity: 'TAX_ID_SLOVENIA',
      id: '192',
      language: ['en', 'sl'],
      region: ['SI'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1722449145.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Slovenia Unique Master Citizen Number',
      description:
        'A unique identifier assigned to individuals in Slovenia for identification and administrative purposes.',
      entity: 'NATIONAL_ID_SLOVENIA',
      id: '62',
      language: ['en', 'sl'],
      region: ['SI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722448864.0
    },
    {
      continent: ['EU'],
      create_time: 1741718767.0,
      data_type: 'Slovenia Unique Master Citizen Number',
      description:
        'The Slovenia Unique Master Citizen Number, known as the Enotna mati\u010dna \u0161tevilka ob\u010dana (EM\u0160O), is a 13-digit personal identification number assigned to Slovenian citizens and residents for identification, taxation, and administrative purposes.',
      entity: 'UMCN_SLOVENIA',
      id: '469',
      language: ['en', 'sl'],
      region: ['SI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741718822.0
    },
    {
      continent: ['EU'],
      create_time: 1722449254.0,
      data_type: 'Slovenia Value Added Tax (VAT) Number',
      description:
        'A Slovenia Value Added Tax (VAT) Number, known as the "Dav\u010dna \u0161tevilka" (DDV), is a unique identifier assigned to businesses and entities registered for VAT in Slovenia.',
      entity: 'VAT_SLOVENIA',
      id: '291',
      language: ['en', 'sl'],
      region: ['SI'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722449254.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Social Media Link',
      description:
        'A web address or URL that directs users to a specific profile or page on a social media platform.',
      entity: 'SOCIAL_MEDIA_LINK',
      id: '15',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1733509649.0
    },
    {
      continent: ['AF'],
      create_time: **********.0,
      data_type: 'South Africa Identification Number',
      description:
        'A unique identifier assigned to individuals in South Africa known as the South African ID number',
      entity: 'NATIONAL_ID_SOUTH_AFRICA',
      id: '43',
      language: ['en'],
      region: ['ZA'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: "South Korea Driver's License Number",
      description:
        "A South Korea driver's license number is a unique alphanumeric identifier assigned to an individual's driver's license issued by the South Korean government.",
      entity: 'DL_SOUTH_KOREA',
      id: '143',
      language: ['en', 'kr'],
      region: ['KR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'South Korea Passport Number',
      description:
        'A unique identifier assigned to individuals in South Korea upon issuance of their passport',
      entity: 'PASSPORT_SOUTH_KOREA',
      id: '122',
      language: ['en', 'kr'],
      region: ['KR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'South Korea Resident Registration Number',
      description:
        'An identifier assigned to residents of South Korea for administrative and identification purposes.',
      entity: 'NATIONAL_ID_SK_RRN',
      id: '44',
      language: ['en'],
      region: ['KR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720204761.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'South Korea Tax ID',
      description:
        'A South Korea Tax ID, known as a Resident Registration Number (RRN) for individuals or a Business Registration Number (BRN) for companies, is a unique identifier issued by the National Tax Service (NTS) to facilitate tax administration and compliance.',
      entity: 'TAX_ID_SK',
      id: '207',
      language: ['en'],
      region: ['SK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Spain Bank Account Number',
      description:
        'A Spain Bank Account Number, referred to as a Cuenta Bancaria, consists of 20 digits used to identify individual bank accounts for domestic transactions, and it is part of the International Bank Account Number (IBAN) system for international transactions.',
      entity: 'BANK_ACCOUNT_NUMBER_ES',
      id: '203',
      language: ['en'],
      region: ['ES'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Spain Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Spain upon issuance of their driver's license.",
      entity: 'DL_SPAIN',
      id: '90',
      language: ['en', 'es'],
      region: ['ES'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Spain Foreigner's Identification Number",
      description:
        "The Spain Foreigner's Identification Number (N\u00famero de Identidad de Extranjero, NIE) is a unique personal identification number assigned to non-Spanish residents for legal, tax, and administrative purposes in Spain.",
      entity: 'FIN_SPAIN',
      id: '470',
      language: ['en', 'es'],
      region: ['ES'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Spain National Identity Document (DNI)',
      description:
        'A unique identifier assigned to individuals in Spain upon issuance of their national identity card (Documento Nacional de Identidad, DNI).',
      entity: 'NATIONAL_ID_SPAIN_DNI',
      id: '59',
      language: ['en', 'es'],
      region: ['ES'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722450163.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Spain Passport Number',
      description:
        'A unique identifier assigned to individuals in Spain upon issuance of their passport',
      entity: 'PASSPORT_SPAIN',
      id: '119',
      language: ['en', 'es'],
      region: ['ES'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722450296.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Spain Social Security Number (SSN)',
      description:
        'A unique identifier assigned to individuals in Spain for social security and taxation purposes.',
      entity: 'SSN_SPAIN',
      id: '45',
      language: ['en', 'es'],
      region: ['ES'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722450062.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Spain Tax Identification Number',
      description:
        'A Spain Tax Identification Number (TIN), known as the "N\u00famero de Identificaci\u00f3n Fiscal" (NIF), is a unique identifier assigned to individuals and entities for tax purposes by the Spanish tax authorities.',
      entity: 'TAX_ID_SPAIN',
      id: '199',
      language: ['en', 'es'],
      region: ['ES'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1722450397.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Spain Value Added Tax (VAT) Number',
      description:
        'A Spain Value Added Tax (VAT) Number, known as the "N\u00famero de Identificaci\u00f3n Fiscal" (NIF) or "N\u00famero de Identificaci\u00f3n a Efectos del IVA" (IVA Number), is a unique identifier assigned to businesses and entities registered for VAT in Spain.',
      entity: 'VAT_SPAIN',
      id: '292',
      language: ['en', 'es'],
      region: ['ES'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Square Access Token',
      description:
        "A Square Access Token is a secure, alphanumeric string used to authenticate API requests and grant authorized access to a Square account's resources, enabling interactions with Square's ecosystem for payments and business management.",
      entity: 'SQUARE_ACCESS_TOKEN',
      id: '331',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Street Address',
      description:
        'The specific location of a building or residence, typically including the street name and number.',
      entity: 'SPACY_STREET_ADDRESS',
      id: '2',
      language: ['en'],
      region: ['ALL'],
      type: 'NLP Model',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Stripe API Key',
      description:
        "A Stripe API Key is a secure, unique key used to authenticate and authorize access to Stripe's API, enabling applications to interact with Stripe services such as processing payments, managing subscriptions, and handling customer data.",
      entity: 'STRIPE_API_KEY',
      id: '356',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1726876092.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Student GPA',
      description:
        "A Student GPA (Grade Point Average) is a numerical representation of a student's academic performance, calculated by averaging the grade points earned in all completed courses over a specific period, typically on a scale of 0 to 4.0 or 0 to 5.0.",
      entity: 'EDUCATIONAL_INFO_STUDENT_GPA',
      id: '239',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20001'],
      update_time: 1721778605.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Student ID',
      description: 'A unique identifier assigned to students by educational institutions.',
      entity: 'EDUCATIONAL_STUDENT_ID',
      id: '14',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1720203669.0
    },
    {
      continent: ['ALL'],
      create_time: 1736982626.0,
      data_type: 'Sumologic Access ID',
      description:
        "The Sumo Logic Access ID is a unique identifier used to authenticate and authorize users or applications to access Sumo Logic's cloud-based log management and analytics platform.",
      entity: 'SUMOLOGIC_AI',
      id: '424',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1736984109.0
    },
    {
      continent: ['ALL'],
      create_time: 1736984293.0,
      data_type: 'Sumologic Access Token',
      description:
        'The Sumo Logic Access Token is a secure authentication token used to authorize API requests and enable access to Sumo Logic\u2019s platform for log management, monitoring, and data analytics.',
      entity: 'SUMOLOGIC_AT',
      id: '425',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Sweden Bank Account Number',
      description:
        'A Sweden Bank Account Number, known as a Bankkontonummer, is a unique identifier for individual bank accounts in Sweden, typically consisting of up to 15 digits, and is part of the IBAN (International Bank Account Number) system for both domestic and international transactions.',
      entity: 'BANK_ACCOUNT_NUMBER_SE',
      id: '206',
      language: ['en'],
      region: ['SE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "Sweden Driver's License Number",
      description:
        "A unique identifier assigned to individuals in Sweden upon issuance of their driver's license.",
      entity: 'DL_SWEDEN',
      id: '91',
      language: ['en', 'sv'],
      region: ['SE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Sweden National ID',
      description:
        'A unique identifier assigned to individuals in Sweden upon issuance of their national identity card (Nationellt ID-kort).',
      entity: 'NATIONAL_ID_SWEDEN',
      id: '46',
      language: ['en', 'sv'],
      region: ['SE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Sweden Passport Number',
      description:
        'A unique identifier assigned to individuals in Sweden upon issuance of their passport',
      entity: 'PASSPORT_SWEDEN',
      id: '97',
      language: ['en', 'sv'],
      region: ['SE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1722451693.0
    },
    {
      continent: ['EU'],
      create_time: 1713769200.0,
      data_type: 'Sweden Tax Identification Number',
      description:
        'A Sweden Tax Identification Number (TIN), known as the "Personnummer" for individuals or "Organisationsnummer" for entities, is a unique identifier assigned by the Swedish Tax Agency.',
      entity: 'TAX_ID_SWEDEN',
      id: '195',
      language: ['en', 'sv'],
      region: ['SE'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1722451818.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Sweden Value Added Tax (VAT) Number',
      description:
        'A Sweden Value Added Tax (VAT) Number, known as the "Momsregistreringsnummer" or "VAT-nummer," is a unique identifier assigned to businesses and entities registered for VAT in Sweden.',
      entity: 'VAT_SWEDEN',
      id: '293',
      language: ['en', 'sv'],
      region: ['SE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Switzerland Health Insurance Card Number',
      description:
        'A Switzerland Health Insurance Card Number is a unique identifier assigned to individuals enrolled in the Swiss health insurance system.',
      entity: 'HIC_SWITZERLAND',
      id: '295',
      language: ['en', 'de', 'fr', 'it', 'rm'],
      region: ['CH'],
      type: 'Regex',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Switzerland Passport Number',
      description:
        "A Switzerland passport number is a unique alphanumeric identifier assigned to an individual's passport issued by the Swiss government.",
      entity: 'PASSPORT_SWITZERLAND',
      id: '296',
      language: ['en', 'de', 'fr', 'it', 'rm'],
      region: ['CH'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Switzerland Social Security Number (AHV)',
      description:
        'A Swiss Social Security Number, known as the "AHV-Nummer" (Alters- und Hinterlassenenversicherung) or "AVS-Num\u00e9ro" (Assurance-vieillesse et survivants), is a unique identifier assigned to individuals for social security purposes in Switzerland.',
      entity: 'AHV_SWITZERLAND',
      id: '294',
      language: ['en', 'de', 'fr', 'it', 'rm'],
      region: ['CH'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Switzerland Value Added Tax (VAT) Number',
      description:
        'A Switzerland Value Added Tax (VAT) Number, known as the "Unternehmens-Identifikationsnummer" (UID) or "Num\u00e9ro d\'identification des entreprises" (IDE), is a unique identifier assigned to businesses and entities registered for VAT in Switzerland.',
      entity: 'VAT_SWITZERLAND',
      id: '297',
      language: ['en', 'de', 'fr', 'it', 'rm'],
      region: ['CH'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: "Taiwan Driver's License Number",
      description:
        "The Taiwan Driver's License Number is a 10-character alphanumeric identifier issued by the Ministry of Transportation and Communications (MOTC) to individuals who hold a valid driver's license in Taiwan.",
      entity: 'DL_TAIWAN',
      id: '484',
      language: ['en'],
      region: ['TW'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Taiwan Identification Number',
      description:
        'A unique identifier assigned to residents of Taiwan for identification and administrative purposes.',
      entity: 'NATIONAL_ID_TAIWAN',
      id: '63',
      language: ['en'],
      region: ['TW'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['AS'],
      create_time: **********.0,
      data_type: 'Taiwan Passport Number',
      description:
        'The Taiwan Passport Number is a 9-character alphanumeric identifier issued by the Ministry of Foreign Affairs (MOFA) of Taiwan, used for international travel and identification of Taiwanese citizens.',
      entity: 'PN_TAIWAN',
      id: '471',
      language: ['en'],
      region: ['TW'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741729220.0
    },
    {
      continent: ['AS'],
      create_time: 1741827147.0,
      data_type: 'Taiwan Resident Certificate Number',
      description:
        'The Taiwan Resident Certificate Number is a 10-character alphanumeric identifier issued to foreign residents (ARC) and Taiwanese expatriates (TARC) for legal residency, employment, and government services in Taiwan.',
      entity: 'RCN_TAIWAN',
      id: '481',
      language: ['en'],
      region: ['TW'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1741827191.0
    },
    {
      continent: ['AS'],
      create_time: 1732062602.0,
      data_type: 'Thailand Passport Number',
      description:
        'The Thailand Passport Number is a unique alphanumeric identifier assigned to Thai passports, typically consisting of 9 characters (letters and numbers), used to identify the passport holder and facilitate international travel.',
      entity: 'PASSPORT_THAILAND',
      id: '371',
      language: ['en', 'th'],
      region: ['TH'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1732062828.0
    },
    {
      continent: ['AS'],
      create_time: 1732062056.0,
      data_type: 'Thailand Personal Identification Number',
      description:
        'The Thailand Personal Identification Number (PIN) is a 13-digit unique identifier assigned to Thai citizens and residents, used for official documentation, government services, and identification in administrative systems.',
      entity: 'PIN_THAILAND',
      id: '370',
      language: ['en', 'th'],
      region: ['TH'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1732062197.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Title',
      description:
        "A formal designation or name given to a person's position, role, or honorific status within an organization, profession, or social context.",
      entity: 'PERSONAL_TITLE',
      id: '13',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1733350310.0
    },
    {
      continent: ['AS'],
      create_time: 1732064616.0,
      data_type: 'Turkey Identification Number',
      description:
        'The Turkey Identification Number (T.C. Kimlik Numaras\u0131) is an 11-digit unique identifier assigned to Turkish citizens and residents for official documentation, government services, and administrative processes.',
      entity: 'ID_TURKEY',
      id: '372',
      language: ['en', 'tr'],
      region: ['TR'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: 1732064748.0
    },
    {
      continent: ['AS'],
      create_time: 1732129448.0,
      data_type: 'Turkey Passport Number',
      description:
        'The Turkey Passport Number is a unique alphanumeric identifier, typically consisting of 9 characters, used to identify Turkish passport holders and facilitate international travel.',
      entity: 'PASSPORT_TURKEY',
      id: '374',
      language: ['en', 'tr'],
      region: ['TR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1732129503.0
    },
    {
      continent: ['AS'],
      create_time: 1732129729.0,
      data_type: 'Turkey Tax Identification Number',
      description:
        'The Turkey Tax Identification Number (Vergi Kimlik Numaras\u0131) is a 10-digit unique identifier assigned to individuals and legal entities for tax-related purposes and official financial transactions in Turkey.',
      entity: 'TIN_TURKEY',
      id: '375',
      language: ['en', 'tr'],
      region: ['TR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1732129784.0
    },
    {
      continent: ['AS'],
      create_time: 1732129988.0,
      data_type: 'Turkey Value Added Tax (VAT) Number',
      description:
        'The Turkey Value Added Tax (VAT) Number is a unique 10-digit identifier assigned to businesses for VAT registration, reporting, and compliance with tax regulations in Turkey.',
      entity: 'VAT_TURKEY',
      id: '376',
      language: ['en', 'tr'],
      region: ['TR'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Twilio API Key',
      description:
        "A Twilio API Key is a secure, unique identifier used to authenticate and authorize access to Twilio's APIs, enabling applications to send messages, make calls, and interact with Twilio's services without exposing account credentials.",
      entity: 'TWILIO_API_KEY',
      id: '354',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Twilio API Secret',
      description:
        "A Twilio API Secret is a confidential key paired with a Twilio API Key, used to securely authenticate requests made to Twilio's API, ensuring that only authorized applications or users can access Twilio's services.",
      entity: 'TWILIO_API_SECRET',
      id: '355',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'U.K. Bank Account Number',
      description:
        "A unique identifier assigned to an individual's or entity's bank account in the United Kindom.",
      entity: 'BANK_ACCOUNT_NUMBER_UK',
      id: '186',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: "U.K. Driver's License Number",
      description:
        "A unique identifier assigned to individuals in U.K. upon issuance of their driver's license.",
      entity: 'DL_UK',
      id: '76',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'U.K. Electoral Roll Number',
      description:
        'A unique identifier assigned to individuals registered to vote in elections within the United Kingdom.',
      entity: 'NATIONAL_ID_ELECTORAL_ROLL_NUMBER',
      id: '17',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'U.K. National Health Service Number',
      description:
        'The U.K. National Health Service (NHS) Number is a unique 10-digit identifier assigned to individuals registered with the National Health Service in the United Kingdom.',
      entity: 'HEALTH_ID_UK_NHS',
      id: '212',
      language: ['en'],
      region: ['UK'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'U.K. National Insurance Number (NINO)',
      description:
        'A unique identifier assigned to individuals in the United Kingdom for purposes related to the National Insurance system.',
      entity: 'SSN_UK_NINO',
      id: '18',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'U.K. Passport Number',
      description:
        'A unique identifier assigned to individuals in the United Kingdom upon issuance of their passport',
      entity: 'PASSPORT_UK',
      id: '96',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1723828185.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'U.K. Unique Taxpayer Reference Number (UTR)',
      description:
        'An alphanumeric identifier issued by HM Revenue and Customs (HMRC) to individuals and entities in the United Kingdom for tax purposes.',
      entity: 'TAX_ID_UK_UTR',
      id: '181',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1729900554.0
    },
    {
      continent: ['EU'],
      create_time: 1741730634.0,
      data_type: 'UK Postal Code',
      description:
        'The UK Postal Code, also known as the Postcode, is an alphanumeric code used by Royal Mail to identify specific geographic locations in the United Kingdom, aiding in mail delivery and address verification.',
      entity: 'PC_UK',
      id: '472',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'UK Sort Code',
      description:
        "The UK Sort Code is a 6-digit bank identifier used to route domestic payments within the United Kingdom's banking system, uniquely identifying banks and their branches.",
      entity: 'SC_UK',
      id: '473',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'UK Value Added Tax (VAT) Number',
      description:
        'A UK Value Added Tax (VAT) Number is a unique identifier assigned to businesses and entities registered for VAT in the United Kingdom.',
      entity: 'VAT_UK',
      id: '298',
      language: ['en'],
      region: ['UK'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'URL',
      description:
        'A URL pattern that matches http or https URLs containing valid domain names and is contextually related to personal information.',
      entity: 'URL',
      id: '214',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Bank Account Number',
      description:
        "A unique identifier assigned to an individual's or entity's bank account in the United States.",
      entity: 'BANK_ACCOUNT_NUMBER_US',
      id: '183',
      language: ['en'],
      region: ['US'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: "US Driver's License Number",
      description:
        "A unique identifier assigned to individuals in the United States upon issuance of their driver's license.",
      entity: 'DL_US',
      id: '65',
      language: ['en'],
      region: ['US'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Drug Enforcement Agency Registration Number(DEA)',
      description:
        'A US Drug Enforcement Administration (DEA) Registration Number is a unique identifier assigned to healthcare providers, such as physicians and pharmacists, authorized to prescribe or handle controlled substances, ensuring compliance with regulations governing their use and distribution.',
      entity: 'HEALTHCARE_ID_US_DEA',
      id: '233',
      language: ['en'],
      region: ['US'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Health Insurance Claim Number (HICN)',
      description:
        "The US Health Insurance Claim Number (HICN) is a unique identifier previously assigned to individuals eligible for Medicare benefits, typically based on the individual's Social Security Number (SSN) followed by an identifying suffix.",
      entity: 'HEALTH_ID_US_HICN',
      id: '210',
      language: ['en'],
      region: ['US'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Healthcare Common Procedure Coding System(HCPCS)',
      description:
        'The Healthcare Common Procedure Coding System (HCPCS) is a coding system used in the United States to standardize the identification of medical procedures, services, and supplies.',
      entity: 'HEALTHCARE_ID_US_HCPCS',
      id: '235',
      language: ['en'],
      region: ['US'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Illinois State ID Number',
      description:
        "The Illinois State ID Number is a unique alphanumeric identifier issued by the Illinois Secretary of State (SOS) to residents for identification purposes, separate from a driver's license or Social Security Number.",
      entity: 'ISIDN_US',
      id: '474',
      language: ['en'],
      region: ['US'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Individual Taxpayer Identification Number (ITIN)',
      description: 'A number issued by the Internal Revenue Service (IRS) to individuals.',
      entity: 'TAX_ID_US_ITIN',
      id: '180',
      language: ['en'],
      region: ['US'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Medicare Beneficiary Identifier(MBI)',
      description:
        'A US Medicare Beneficiary Identifier (MBI) is a unique, randomly generated alphanumeric code assigned to individuals eligible for Medicare benefits, replacing the previous Social Security-based Health Insurance Claim Number (HICN).',
      entity: 'HEALTHCARE_ID_US_MBI',
      id: '208',
      language: ['en'],
      region: ['US'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US National Drug Code (NDC)',
      description:
        'A US National Drug Code (NDC) is a unique 10- or 11-digit identifier assigned by the Food and Drug Administration (FDA) to each drug product marketed in the United States, used for identification, tracking, and regulatory purposes.',
      entity: 'HEALTHCARE_ID_US_NDC',
      id: '232',
      language: ['en'],
      region: ['US'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US National Provider Identifier (NPI)',
      description:
        'A US National Provider Identifier (NPI) is a unique 10-digit identification number assigned to healthcare providers and organizations by the Centers for Medicare & Medicaid Services (CMS) for use in administrative and financial transactions under HIPAA.',
      entity: 'HEALTHCARE_ID_NPI',
      id: '230',
      language: ['en'],
      region: ['US'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Passport Number',
      description: 'A unique identifier assigned to a United States passport.',
      entity: 'PASSPORT_US',
      id: '8',
      language: ['en'],
      region: ['US'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Social Security number (SSN)',
      description:
        'A identifier issued by the Social Security Administration (SSA) to U.S. citizens, permanent residents, and temporary residents with work authorization.',
      entity: 'SSN_US',
      id: '9',
      language: ['en'],
      region: ['US'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Unique device Identifier(UDI)',
      description:
        'The Unique Device Identifier (UDI) is a system used in the United States to assign a unique identification code to medical devices.',
      entity: 'HEALTHCARE_ID_US_UDI',
      id: '234',
      language: ['en'],
      region: ['US'],
      type: 'Regex + Validation Algorithms',
      type_category: ['10003'],
      update_time: **********.0
    },
    {
      continent: ['NA'],
      create_time: **********.0,
      data_type: 'US Zip Code',
      description:
        'A numeric code used by the United States Postal Service (USPS) to identify specific geographic regions.',
      entity: 'ZIP_US',
      id: '10',
      language: ['en'],
      region: ['US'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['EU'],
      create_time: **********.0,
      data_type: 'Ukraine Identity Card',
      description:
        'The Ukraine Identity Card is an official biometric document issued to Ukrainian citizens, serving as a primary form of identification and containing personal information such as a unique ID number, name, date of birth, and photo.',
      entity: 'ID_UKRAINE',
      id: '377',
      language: ['en', 'uk'],
      region: ['UA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1732143317.0
    },
    {
      continent: ['EU'],
      create_time: 1732143803.0,
      data_type: 'Ukraine Passport Number',
      description:
        'The Ukraine Passport Number is a unique alphanumeric identifier assigned to Ukrainian passports, typically consisting of two letters followed by six digits, used for international travel and official identification.',
      entity: 'PASSPORT_UKRAINE',
      id: '378',
      language: ['en', 'uk'],
      region: ['UA'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1732143834.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'Username',
      description:
        'A chosen name or identifier used to access computer systems, websites, or online services',
      entity: 'USERNAME',
      id: '12',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: 1738953516.0
    },
    {
      continent: ['ALL'],
      create_time: 1737420321.0,
      data_type: 'Vault Batch Token',
      description:
        'Vault Batch Token is a secure and efficient solution for managing and processing multiple tokens or assets in a single batch transaction, optimizing performance and reducing costs.',
      entity: 'VAULT_BT',
      id: '429',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1737420344.0
    },
    {
      continent: ['ALL'],
      create_time: 1737420435.0,
      data_type: 'Vault Service Token',
      description:
        'Vault Service Token is a secure authentication token used to access and interact with Vault services, ensuring authorized and encrypted communication between users and the system.',
      entity: 'VAULT_ST',
      id: '430',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Venezuela Driving License Number',
      description:
        'The Venezuela Driving License Number is a unique alphanumeric identifier assigned to individuals holding a valid driving license in Venezuela, used for personal identification and verification of driving privileges.',
      entity: 'DL_VENEZUELA',
      id: '380',
      language: ['en', 'es'],
      region: ['VE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['SA'],
      create_time: **********.0,
      data_type: 'Venezuela National Identification Number',
      description:
        'The Venezuela National Identification Number (C\u00e9dula de Identidad) is a unique numeric identifier assigned to Venezuelan citizens and residents, used for official identification and administrative purposes, typically consisting of a letter followed by 8 digits.',
      entity: 'ID_VENEZUELA',
      id: '379',
      language: ['en', 'es'],
      region: ['VE'],
      type: 'Regex',
      type_category: ['10001'],
      update_time: **********.0
    },
    {
      continent: ['ALL'],
      create_time: **********.0,
      data_type: 'X.509 Certificate',
      description:
        'An X.509 certificate is a digital certificate that uses the X.509 public key infrastructure (PKI) standard to verify the identity of entities, enabling secure communication and authentication over networks through public and private key pairs.',
      entity: 'X509_CERTIFICATE',
      id: '358',
      language: ['en'],
      region: ['ALL'],
      type: 'Regex',
      type_category: ['20005'],
      update_time: 1727133269.0
    }
  ],
  custom_data_type: [
    {
      created_at: **********.021863,
      definition: {
        keywords: [
          {
            keyword: 'ttt'
          }
        ],
        patterns: [
          {
            regex: '1'
          }
        ]
      },
      description: 'test',
      discover_policy_cnt: 0,
      dlp_policy_cnt: 0,
      edm_cnt: 0,
      group_uuid: '8c3c4476-6e36-4f15-8ae9-e982defb57d9',
      id: 'c63beea4-9ea4-4027-80e4-d0201344f78f',
      name: 'test',
      updated_at: **********.021863
    }
  ],
  main_categories: [
    {
      discarded: false,
      main_class_id: '10000',
      main_class_name: 'Finance'
    },
    {
      discarded: false,
      main_class_id: '20000',
      main_class_name: 'Healthcare'
    },
    {
      discarded: false,
      main_class_id: '30000',
      main_class_name: 'Information Technology'
    },
    {
      discarded: true,
      main_class_id: '40000',
      main_class_name: 'Education (Discarded)'
    },
    {
      discarded: true,
      main_class_id: '50000',
      main_class_name: 'Manufacture (Discarded)'
    },
    {
      discarded: false,
      main_class_id: '60000',
      main_class_name: 'Other'
    },
    {
      discarded: false,
      main_class_id: '70000',
      main_class_name: 'Legal'
    },
    {
      discarded: false,
      main_class_id: '80000',
      main_class_name: 'HR'
    },
    {
      discarded: false,
      main_class_id: '90000',
      main_class_name: 'Source Code'
    }
  ],
  sub_categories: {
    'education_(discarded)': {
      discarded: true,
      main_class_id: '40000',
      main_class_name: 'Education (Discarded)',
      sub_class_list: {
        'education_(discarded)': {
          description: 'Education_(discarded) document',
          discarded: true,
          sub_class_id: 0,
          sub_class_name: 'Education_(Discarded)'
        }
      }
    },
    finance: {
      discarded: false,
      main_class_id: '10000',
      main_class_name: 'Finance',
      sub_class_list: {
        'finance-audit_and_assurance_documents': {
          description:
            'Documents generated during internal or external audit processes to assess financial accuracy, internal controls, and regulatory compliance. These reports provide independent assurance and are critical for corporate governance and risk management.',
          discarded: false,
          sub_class_id: '10010',
          sub_class_name: 'Audit and Assurance Documents'
        },
        'finance-credit_and_risk_management': {
          description:
            'Documents involved in assessing and managing credit risk, market risk, etc., such as credit reports, risk assessment reports, and risk management strategies. These documents are important for financial institutions to develop risk controls and mitigation measures.',
          discarded: false,
          sub_class_id: '10003',
          sub_class_name: 'Credit and Risk Management'
        },
        'finance-expense_and_reimbursement_records': {
          description:
            'Documents that support expense tracking and reimbursement processes. These typically include supporting evidence for employee expenses, management approvals, and payment confirmation. They are frequently used in both internal audits and operational controls.',
          discarded: false,
          sub_class_id: '10008',
          sub_class_name: 'Expense and Reimbursement Records'
        },
        'finance-financial_report': {
          description:
            'Used to display the financial status of an individual or organization, including balance sheets, income statements, cash flow statements, and shareholder equity statements. They are critical for investors, management and regulators to understand and assess financial health.',
          discarded: false,
          sub_class_id: '10002',
          sub_class_name: 'Financial Report'
        },
        'finance-financial_strategy_and_research': {
          description:
            'Including market research reports, investment strategy documents and industry analysis, etc., used to guide investment decisions and financial product development. This type of document provides financial institutions with market insights based on in-depth analysis and forecasts.',
          discarded: false,
          sub_class_id: '10004',
          sub_class_name: 'Financial Strategy and Research'
        },
        'finance-loan_and_financing_agreements': {
          description:
            'Documents outlining the terms and structure of loans, credit facilities, or other financial arrangements. These define obligations between borrowers and lenders, repayment conditions, interest rates, and covenants, and are central to debt and capital management.',
          discarded: false,
          sub_class_id: '10009',
          sub_class_name: 'Loan and Financing Agreements'
        },
        'finance-other_(discarded)': {
          description:
            'Other financial documents or records that do not fall into the predefined categories.',
          discarded: true,
          sub_class_id: '10006',
          sub_class_name: 'Other (Discarded)'
        },
        'finance-tax_and_regulatory_documents': {
          description:
            'Documents prepared for tax reporting, compliance with government regulations, and financial oversight. These are critical for ensuring legal compliance and are often submitted to tax authorities or regulatory agencies.',
          discarded: false,
          sub_class_id: '10007',
          sub_class_name: 'Tax and Regulatory Documents'
        },
        'finance-transaction_record': {
          description:
            'Includes all documents recording details of financial transactions, such as transaction confirmations, receipts, transfer instructions, and statements. These documents are used to prove the existence, condition, and completion of a transaction.',
          discarded: false,
          sub_class_id: '10001',
          sub_class_name: 'Transaction Record'
        }
      }
    },
    healthcare: {
      discarded: false,
      main_class_id: '20000',
      main_class_name: 'Healthcare',
      sub_class_list: {
        'healthcare-clinical_records': {
          description:
            'Documents that record patient health information and treatment processes, including medical records, disease course records, surgical records, discharge summaries, examination and test reports, etc. These documents are the basis for medical care decisions and are critical to patient diagnosis, treatment, and follow-up.',
          discarded: false,
          sub_class_id: '20001',
          sub_class_name: 'Clinical Records'
        },
        'healthcare-clinical_studies': {
          description:
            'Clinical research report: records the design, implementation process and result analysis of the clinical trial.',
          discarded: false,
          sub_class_id: '20005',
          sub_class_name: 'Clinical Studies'
        },
        'healthcare-compliance_and_risk_management': {
          description:
            'Patient Consent: A document in which a patient gives explicit consent to a treatment plan, surgery, or other medical procedure. Privacy Policy: Policies and measures describing how a medical institution protects patients\u2019 personal information.',
          discarded: false,
          sub_class_id: '20004',
          sub_class_name: 'Compliance and Risk Management'
        },
        'healthcare-insurance_and_settlement': {
          description:
            'Documents related to medical expense reimbursement and settlement, such as insurance claim forms, expense statements, and payment records. These documents are critical to processing medical bills and ensuring the financial benefit of providers and patients.',
          discarded: false,
          sub_class_id: '20003',
          sub_class_name: 'Insurance and Settlement'
        },
        'healthcare-medication_management': {
          description:
            'Documentation involving drug prescriptions, medication instructions, and drug monitoring. This includes prescription orders, medication records and adverse drug reaction reports to ensure patients are using their medications safely and effectively.',
          discarded: false,
          sub_class_id: '20002',
          sub_class_name: 'Medication Management'
        },
        'healthcare-other_(discarded)': {
          description:
            'Other healthcare documents or records that do not fall into the predefined categories.',
          discarded: true,
          sub_class_id: '20006',
          sub_class_name: 'Other (Discarded)'
        }
      }
    },
    hr: {
      discarded: false,
      main_class_id: '80000',
      main_class_name: 'HR',
      sub_class_list: {
        'hr-cv': {
          description:
            'Candidate CVs submitted during the recruitment process. It provides quick access to applicants\u2019 professional backgrounds, qualifications, and work experience, supporting informed hiring decisions and streamlined candidate evaluation.',
          discarded: false,
          sub_class_id: '80004',
          sub_class_name: 'CV'
        },
        'hr-employee_record': {
          description:
            'Comprehensive records maintained for each employee throughout their lifecycle within the organization. Includes personal information, employment contracts, benefits enrollment, disciplinary records, training history, and separation documentation. These records ensure compliance with labor regulations and support HR operations, audits, and dispute resolution.',
          discarded: false,
          sub_class_id: '80005',
          sub_class_name: 'Employee Record'
        },
        'hr-performance_review': {
          description:
            'Documents related to employee performance evaluations. It helps track individual progress, identify areas for development, and support decisions related to promotions, training, or role adjustments. Consistent performance reviews contribute to employee growth and organizational success.',
          discarded: false,
          sub_class_id: '80002',
          sub_class_name: 'Performance Review'
        },
        'hr-recruitment': {
          description:
            'Documents related to the hiring process, including job requisitions, job descriptions, candidate applications, interview records, background checks, and offer letters. These documents are used to track recruitment activities, evaluate candidate qualifications, and ensure compliance with hiring policies. Proper management of recruitment documentation supports transparency, consistency, and alignment with organizational hiring standards.',
          discarded: false,
          sub_class_id: '80003',
          sub_class_name: 'Recruitment'
        },
        'hr-timesheet': {
          description:
            'Timesheet is used to record and track employee working hours, attendance, and time allocation across projects or tasks. Accurate timesheet documentation supports payroll processing, project management, and compliance with labor regulations.',
          discarded: false,
          sub_class_id: '80001',
          sub_class_name: 'Timesheet'
        }
      }
    },
    information_technology: {
      discarded: false,
      main_class_id: '30000',
      main_class_name: 'Information Technology',
      sub_class_list: {
        'information_technology-configuration_files': {
          description:
            'Configuration files containing structured information used to define system settings, application behaviors, or operational parameters. Includes JSON, XML, or YAML configuration files, environment variable definitions, and similar resources designed to facilitate system customization and operation.',
          discarded: false,
          sub_class_id: '30007',
          sub_class_name: 'Configuration Files'
        },
        'information_technology-development_documentation': {
          description:
            'For software developers and project team members, it provides information such as product design, architecture, development specifications, and code samples. Development documentation supports the software development process and promotes effective communication within the team.',
          discarded: false,
          sub_class_id: '30002',
          sub_class_name: 'Development Documentation'
        },
        'information_technology-log': {
          description:
            'Log is dedicated to recording detailed, time-stamped entries related to IT activities, system events, and operational workflows. It provides a chronological history of actions taken, system behavior, or incidents encountered, which is essential for auditing, troubleshooting, and maintaining system integrity.',
          discarded: false,
          sub_class_id: '30009',
          sub_class_name: 'Log'
        },
        'information_technology-operational_documentation': {
          description:
            'Provides the guidance and processes system administrators and IT professionals need to manage and maintain IT systems. Includes system administration manuals, backup and recovery guides, security protocols and maintenance plans.',
          discarded: false,
          sub_class_id: '30004',
          sub_class_name: 'Operational Documentation'
        },
        'information_technology-other_(discarded)': {
          description:
            'Other information technology related documents or records that do not fall into the predefined categories.',
          discarded: true,
          sub_class_id: '30008',
          sub_class_name: 'Other (Discarded)'
        },
        'information_technology-source_code_(discarded)': {
          description:
            'Source code is written in a programming language, which can be a high-level language (such as Python, Java, C++) or a low-level language that is closer to machine language (such as assembly language). It exists in text form, so it can be created and edited using a text editor.',
          discarded: true,
          sub_class_id: '30005',
          sub_class_name: 'Source Code (Discarded)'
        },
        'information_technology-test_documentation': {
          description:
            'Documentation that records software test plans, test cases, test results, and defect reports. Test documentation is critical to ensuring product quality and identifying and fixing defects.',
          discarded: false,
          sub_class_id: '30003',
          sub_class_name: 'Test Documentation'
        },
        'information_technology-training_materials': {
          description:
            'Training course materials for users, developers, or system administrators designed to improve technical skills and product knowledge. Includes training manuals, online courses and instructional videos.',
          discarded: false,
          sub_class_id: '30006',
          sub_class_name: 'Training Materials'
        },
        'information_technology-user_documentation': {
          description:
            'For end users, it provides detailed guidance on how to use a product or system. Includes user manual, quick start guide, FAQ (Frequently Asked Questions) and use cases. User documentation helps users understand product functionality and use the product effectively.',
          discarded: false,
          sub_class_id: '30001',
          sub_class_name: 'User Documentation'
        }
      }
    },
    legal: {
      discarded: false,
      main_class_id: '70000',
      main_class_name: 'Legal',
      sub_class_list: {
        'legal-contracts_and_agreements': {
          description:
            "Formal documents that define the legally binding terms and conditions between two or more parties involved in a transaction or service relationship. This includes agreements such as loan contracts, investment management agreements, service contracts, and insurance policies. These documents specify each party's rights, obligations, responsibilities, and remedies in case of breach.",
          discarded: false,
          sub_class_id: '10005',
          sub_class_name: 'Contracts and Agreements'
        },
        'legal-license_agreement': {
          description:
            "Legally enforceable contract that allows one party (the licensee) to use certain intellectual property owned by another party (the licensor), such as software, trademarks, patents, or copyrighted content. The agreement specifies terms such as usage scope, payment or royalties, duration, restrictions, and termination clauses, ensuring both parties' rights and responsibilities are clearly defined.",
          discarded: false,
          sub_class_id: '70004',
          sub_class_name: 'License Agreement'
        },
        'legal-nda_form': {
          description:
            'Legal document in which one or more parties agree to maintain the confidentiality of proprietary or sensitive information disclosed during a business or professional relationship. NDAs are designed to protect trade secrets, business plans, technical data, or personal information, and typically outline the scope of confidentiality, duration, and consequences of breach.',
          discarded: false,
          sub_class_id: '70002',
          sub_class_name: 'NDA Form'
        },
        'legal-patent': {
          description:
            'Official legal document granted by a governmental authority that confers exclusive rights to an inventor or applicant to make, use, sell, or license a specific invention for a fixed period (usually 20 years). A patent includes technical descriptions, claims, and drawings that define the scope of legal protection. It serves as a key instrument for protecting intellectual property and promoting innovation.',
          discarded: false,
          sub_class_id: '70003',
          sub_class_name: 'Patent'
        }
      }
    },
    'manufacture_(discarded)': {
      discarded: true,
      main_class_id: '50000',
      main_class_name: 'Manufacture (Discarded)',
      sub_class_list: {
        'manufacture_(discarded)': {
          description: 'Manufacture_(discarded) document',
          discarded: true,
          sub_class_id: 0,
          sub_class_name: 'Manufacture_(Discarded)'
        }
      }
    },
    other: {
      discarded: false,
      main_class_id: '60000',
      main_class_name: 'Other',
      sub_class_list: {
        other: {
          description: 'Other document',
          discarded: false,
          sub_class_id: 0,
          sub_class_name: 'Other'
        }
      }
    },
    source_code: {
      discarded: false,
      main_class_id: '90000',
      main_class_name: 'Source Code',
      sub_class_list: {
        'source_code-c#': {
          description:
            'C# source files (.cs) define classes, methods, and application logic. They are compiled by the .NET runtime and are widely used in Windows applications, games (Unity), and enterprise systems.',
          discarded: false,
          sub_class_id: '90009',
          sub_class_name: 'C#'
        },
        'source_code-c_language': {
          description:
            "The 'C Language' here refers collectively to both C and C++ source files. C provides low-level programming capabilities, while C++ builds on C with object-oriented features. Their source files typically use extensions such as .c, .cpp, and .h, and are compiled into machine code for high-performance applications.",
          discarded: false,
          sub_class_id: '90001',
          sub_class_name: 'C Language'
        },
        'source_code-golang': {
          description:
            'Golang source files use the .go extension and start with a package declaration. They are compiled and used to build efficient, concurrent systems such as web servers, APIs, and CLI tools.',
          discarded: false,
          sub_class_id: '90002',
          sub_class_name: 'Golang'
        },
        'source_code-java': {
          description:
            'Java source files use the .java extension, typically containing a public class matching the filename. They are compiled into bytecode and run on the Java Virtual Machine (JVM) for platform independence.',
          discarded: false,
          sub_class_id: '90003',
          sub_class_name: 'Java'
        },
        'source_code-javascript': {
          description:
            'JavaScript files (.js, .mjs) contain code for browser or server environments. They define variables, functions, and modules, and are widely used for building interactive web applications.',
          discarded: false,
          sub_class_id: '90004',
          sub_class_name: 'JavaScript'
        },
        'source_code-kotlin': {
          description:
            'Kotlin source files (.kt, .kts) define classes, functions, and scripts. Interoperable with Java, Kotlin is widely used in Android and backend development for its concise syntax and modern features.',
          discarded: false,
          sub_class_id: '90005',
          sub_class_name: 'Kotlin'
        },
        'source_code-php': {
          description:
            'PHP files (.php) embed code within HTML and are executed server-side. They handle logic, database access, and dynamic content generation for websites and web applications.',
          discarded: false,
          sub_class_id: '90006',
          sub_class_name: 'PHP'
        },
        'source_code-python': {
          description:
            'Python source files (.py) contain readable, indented code used in scripting, data science, web development, and automation. They are interpreted by the Python runtime environment.',
          discarded: false,
          sub_class_id: '90007',
          sub_class_name: 'Python'
        },
        'source_code-shell': {
          description:
            'Shell scripts (.sh, .bash) contain command-line instructions, variables, and control structures. They automate tasks in Unix/Linux environments and are interpreted by shell programs like bash or zsh.',
          discarded: false,
          sub_class_id: '90008',
          sub_class_name: 'Shell'
        }
      }
    }
  },
  storage_profiles_summary: [
    {
      id: 'a036e631-8fd0-4ba8-8047-1cbd3bfc5e9b',
      name: 'spc',
      notes: '',
      type: 2
    },
    {
      id: '2c0cbbe5-05e7-4e9b-9eac-403389fce1ff',
      name: 'smb',
      notes: '',
      type: 4
    }
  ],
  identity_list: [
    {
      identifier: 'a7b097c0-911a-496d-9911-db5db2633ba1:3',
      name: 'emily_test Owners',
      sid: 'b02038f6-8336-4212-b379-1ee7f3d5408c',
      type: 1
    },
    {
      identifier: 'a7b097c0-911a-496d-9911-db5db2633ba1:4',
      name: 'emily_test Visitors',
      sid: 'b02038f6-8336-4212-b379-1ee7f3d5408c',
      type: 1
    },
    {
      identifier: 'a7b097c0-911a-496d-9911-db5db2633ba1:5',
      name: 'emily_test Members',
      sid: 'b02038f6-8336-4212-b379-1ee7f3d5408c',
      type: 1
    },
    {
      identifier: '51437253-5ea0-4820-bb28-84fe9b30cb3b',
      name: 'emily_test',
      sid: 'b02038f6-8336-4212-b379-1ee7f3d5408c',
      type: 1
    }
  ],
  // TODO. confirm the details format with backend
  sensitive_data_details: {
    '1f69132c-9302-4988-a1ac-15b9426822f2': {
      copy_failed_message: '',
      copy_status: 0,
      copy_targets: [],
      custom_list: [],
      edm_match_info: {},
      file_attributes: {
        drive_id: 'N/A',
        file_cat: 'source code',
        file_display_path: 'root/Baojun/ftsperf721/tools/get_all_test_types.py',
        file_ext: 'py',
        file_size: 1.541015625,
        file_user_email: 'UNKNOWN',
        file_user_id: 'UNKNOWN',
        file_user_name: 'UNKNOWN',
        last_modified: '2022-10-11 11:10:06',
        owner: {
          email: 'UNKNOWN',
          name: 'UNKNOWN'
        },
        site_id: 'N/A'
      },
      file_hash: '50a3a0985b636e7645efbf19d914ef9663c71a38',
      idm_match_info: {},
      labels: {
        custom: [],
        predefine: []
      },
      quarantine_failed_message: '',
      quarantine_status: 0,
      quarantine_targets: [],
      standard_list: []
    },
    '05258c09-ec05-4409-abe5-878f139ca73d': {
      copy_failed_message: '',
      copy_status: 0,
      copy_targets: [],
      custom_list: [],
      edm_match_info: {},
      file_attributes: {
        drive_id: 'N/A',
        file_cat: 'source code',
        file_display_path: 'root/Baojun/ftsperf721/lib/network/create_network.py',
        file_ext: 'py',
        file_size: 28.2568359375,
        file_user_email: 'UNKNOWN',
        file_user_id: 'UNKNOWN',
        file_user_name: 'UNKNOWN',
        last_modified: '2024-04-17 15:58:35',
        owner: {
          email: 'UNKNOWN',
          name: 'UNKNOWN'
        },
        site_id: 'N/A'
      },
      file_hash: '691a4540608d05698a3e5512847b7e571e6ab9be',
      idm_match_info: {},
      labels: {
        custom: [],
        predefine: [
          'SOX',
          'CCPA',
          'GLBA',
          'Highly Confidential',
          'LGPD',
          'CSL',
          'APPs',
          'APPI',
          'DPA',
          'Restricted',
          'FISMA',
          'HIPAA',
          'NIST 800-53 and NIST 800-171',
          'GDPR',
          'FERPA',
          'PCI_DSS',
          'PIPEDA'
        ]
      },
      quarantine_failed_message: '',
      quarantine_status: 0,
      quarantine_targets: [],
      standard_list: [
        {
          '#': 1,
          category: ['10001'],
          count: 1,
          data: [
            {
              confidence: 95,
              text: '0.<#wVxJ8341#>6<#/wVxJ8341#>'
            }
          ],
          max_confidence: 95,
          region: ['ALL'],
          sub_category: 'IP Address'
        }
      ]
    }
  },
  column_names: {
    filename: 'File Name',
    standard_match_count: 'Standard Data Types',
    custom_match_count: 'Custom Data Types',
    main_class_id: 'ML Category',
    sub_class_id: 'ML Subcategory',
    main_class_confidence: 'Category Confidence',
    file_tag: 'Labels',
    quarantine_status: 'Quarantined',
    copy_status: 'Copied',
    file_ext: 'Extension',
    file_size: 'Size',
    owner: 'Owner',
    storage_type: 'Storage Type',
    scan_storage: 'Storage',
    file_display_path: 'File Path',
    update_time: 'Updated',
    collaborators: 'Collaborators',
    links: 'Shareable Links'
  }
};
