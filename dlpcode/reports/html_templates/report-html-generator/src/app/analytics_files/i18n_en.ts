export const TRANSLATIONS_EN = {
  search_query: 'Search',
  multiplePlaceholder: 'value1,value2,...etc.',
  pleaseSelectValue: 'Please select the value',
  value: 'Value',
  search: 'Search',
  clearAllConditions: 'Clear all conditions',
  copy_success: 'Successfully copied!',
  errorMessage: {
    required: 'This field is required',
    invalidExportType: 'Invalid export type',
    invalidTableQueryValue: '{{column}} can only be {{values}}',
    notAllowComma: 'Comma is not allowed',
    notAllowBackslash: 'Backslash is not allowed',
    noRowsToExport: 'First select the rows you want to export',
    noRowsToDelete: 'First select the rows you want to delete',
    noRowsToAck: 'First select the rows you want to acknowledge',
    noExistingQueries: 'There is no existing query available',
    noQuery: 'Please add a query first',
    load_file_failed: 'Could not load this file',
    min: 'The minimum value is {{min}}',
    max: 'The maximum value is {{max}}',
    range: 'Out of range: {{min}} - {{max}}',
    pattern: 'Invalid input',
    email: 'Invalid email format',
    url: 'Invalid URL',
    invalid_file_type: 'Invalid file type',
    invalid: 'This value is invalid',
    file_size: 'Maximum file size is {{max}} {{unit}}',
    cannot_read_upload_file:
      'The file cannot be uploaded because its content might be changed. Please select the file again.',
    scans_file_size_limit: 'File size limit should not exceed 50 MB',
    scans_file_size_invalid: 'Invalid file size',
    scans_data_regions_empty: 'No Standard Data Type Regions selected',
    scans_data_category_empty: 'No Standard Data Type Categories selected',
    pass_not_match: 'Passwords do not match',
    no_filter_available:
      'Filters cannot be duplicated, and all are currently being applied. Please edit or remove them first.',
    end_with_slash: 'Cannot end with a backslash (\\) or a forward slash (/).',
    end_with_forward_slash: 'Cannot end with a forward slash (/).',
    start_with_forward_slash: 'Input must start with a forward slash (/)',
    posiive_integer: 'Value must be a positive integer',
    invalidIPAddress: 'Invalid IP Address',
    invalidIPAddressDomain: 'Invalid IP Address or domain',
    text_length_limit: 'Input length limit: {{length}}',
    any: '{{message}}',
    maxlength: 'The maximum length is {{requiredLength}}',
    illegalChar: 'Invalid characters',
    'password-not-match': 'Passwords do not match',
    key_length: '8 characters long',
    lower_word: 'one lowercase letter',
    upper_word: 'one uppercase letter',
    number_word: 'one number',
    special_word: 'one sepcial character',
    invalidCIDR: 'Invalid CIDR format',
    non_negative_integer: 'Value must be a non-negative integer',
    scans_profiles_confirm_error:
      'Please check the box to confirm that you understand the potential issues',
    number_letter_underscore: 'The value can contain only numbers, letters, and underscores',
    number_letter_underscore_hyphen:
      'The value can contain only numbers, letters, underscores, and hyphens',
    number_letter_underscore_space:
      'The value can contain only numbers, letters, underscores, and spaces',
    number_letter_underscore_hyphen_space:
      'The value can contain only numbers, letters, underscores, hyphens, and spaces',
    trailing_spaces_error: 'Trailing spaces are not allowed. Spaces between words are fine.',
    trailingSpace: 'Trailing spaces are not allowed. Spaces between words are fine.',
    end_time_must_after_start_time: 'End time must be after start time',
    start_time_should_not_before: 'Start time must not be before {{time}}'
  },
  tableFilter: {
    equal: '=',
    like: 'Like',
    range: 'Range',
    greater: '>',
    greater_equal: '≥',
    less: '<',
    less_equal: '≤'
  },
  analytics_files: {
    id: 'ID',
    details: 'Details',
    file_name: 'File Name',
    standard_sensitivities: 'Standard Data Types',
    custom_sensitivities: 'Custom Data Types',
    ai_category: 'ML Category',
    ai_subcategory: 'ML Subcategory',
    category_confidence: 'Category Confidence',
    subcategory_confidence: 'Subcategory Confidence',
    labels: 'Labels',
    extension: 'Extension',
    size: 'Size',
    owner: 'Owner',
    storage: 'Storage',
    storage_type: 'Storage Type',
    path: 'Path',
    scanned_files: 'Scanned Files',
    scan_name: 'Scan Name',
    period: 'Period',
    add_report: 'Add Report',
    notes: 'Notes',
    report_dialog_title: 'Add Files Report',
    report_name: 'Report Name',
    report_name_hint: 'Auto-timestamped',
    run_report: 'Run Report',
    days_of_month: 'Days of Month',
    days: 'Days',
    hour: 'Hour',
    minutes_after_the_hour: 'Minutes after the hour',
    start_time: 'Start Time',
    start_time_hint: 'Current browser time',
    query_name: 'Query Name',
    query: 'Query',
    format: 'Format',
    email_to: 'Email To',
    email_to_placeholder: 'Separate multiple mailboxes with comma',
    report_notes: 'Report Notes',
    preview: 'Preview',
    preview_hint: 'Click to preview the configured report.',
    report_end_time_message: '{{frequency}} Report {{end}}',
    files: 'Files',
    folders: 'Folders',
    file_condition_placeholder: 'Search / Query by file conditions',
    folder_condition_placeholder: 'Search / Query by folder conditions',
    sensitive_data_collections: 'Sensitive Data Collections',
    file_path: 'File Path',
    folder_path: 'Folder',
    labeled: 'Labeled',
    updated: 'Updated',
    quarantine_file: 'Quarantine File',
    restore_quarantined_file: 'Restore Quarantined File',
    quarantine_files: 'Quarantine Files',
    restore_quarantined_files: 'Restore Quarantined Files',
    quarantine_status: 'Quarantined',
    copy_status: 'Copied',
    time_zone: 'Time Zone',
    failed_to_restore: 'Failed to restore the following quarantined files: {{files}}',
    failed_to_quarantine: 'Failed to quarantine the following files: {{files}}',
    view_details: 'View Details',
    file_scan_period: 'File Scan Results Period',
    license_warning:
      'Your trial license allows viewing only the latest 1,000 files. Please purchase a product license.',
    file_hash: 'File Hash',
    standard_data_types: 'Standard Data Types',
    custom_data_types: 'Custom Data Types',
    idm_matched: 'IDM Matched',
    edm_matched: 'EDM Matched',
    collaborators: 'Collaborators',
    shareable_links: 'Shareable Links',
    links_count: '{{count}} Links'
  },
  scans: {
    storage_type: {
      prem: 'SharePoint On Prem',
      smb: 'SMB',
      cloud: 'SharePoint Cloud',
      aws: 'AWS Bucket',
      google_drive: 'Google Drive',
      gdrive: 'Google Drive',
      unknown: 'Unknown'
    }
  },
  sensitive_data: {
    scan_name: 'Scan Name',
    storage: 'Storage',
    file_name: 'File',
    detail_file_name: 'File Name',
    file: 'File',
    details: 'Details',
    standard_instances: 'Standard Instances',
    custom_instances: 'Custom Instances',
    ml_doc_type: 'ML Document Classification',
    file_path: 'File Path',
    folder_path: 'Folder',
    updated: 'Updated',
    select_scan: 'Select a Scan Name',
    sensitive_data_type_detail: 'File Details',
    standard_values: 'Standard Data Type Categories',
    custom_values: 'Custom Data Types',
    collaborators_values: 'File Collaborators',
    count: 'Count',
    region: 'Region',
    category: 'Category',
    sub_category: 'Data Type',
    custom_data_type_group: 'Data Type Group',
    custom_data_type: 'Data Type',
    main_class_name: 'Main Category',
    sub_class_name: 'Sub Category',
    total: 'Total',
    files: 'Files',
    type: 'Type',
    sensitive_files: 'Sensitive Files',
    scanned_files: 'Scanned Files',
    sensitive_data_type_files: 'Sensitive Data Type Categories',
    machine_learning_document_types: 'Machine Learning Classification Categories',
    cat_subcat: 'Category / Sub-category',
    percentage: 'Percentage',
    sensitive_data_collections: 'Sensitive data collections',
    folder: 'Folder',
    assigned_labels: 'Assigned Labels',
    label: 'Label',
    labels: 'Labels',
    folders: 'Folders',
    file_condition_placeholder: 'Search / Query by file conditions',
    folder_condition_placeholder: 'Search / Query by folder conditions',
    max_confidence: 'Max Confidence',
    class_confidence: 'Category Confidence',
    main_class_confidence: 'Main Classification Confidence',
    sub_class_confidence: 'Sub Classification Confidence',
    confidence_detail: 'Confidence Detail',
    data_type: 'Data Type',
    data_preview: 'Data Preview',
    confidence_score: 'Confidence Score',
    label_predefined: 'Standard Labels',
    label_custom: 'Custom Labels',
    label_ml: 'Machine Learning Labels',
    endpoint: 'Endpoint',
    period: 'Period',
    storage_type: 'Storage Type',
    scan_notes: 'Scan Notes',
    total_sensitive_files: 'Total Sensitive Files',
    sensitive_files_by_file_type: 'Sensitive Files by File Extension',
    current_totals: 'Current Totals',
    file_types: 'File Types',
    ai_file_categories: 'ML File Categories',
    total_categorized_files: 'Total Categorized Files',
    all_scans: 'All Scans',
    scan_incidents_by_severity: 'Scan Incidents by Risk',
    scan_incidents: 'Scan Incidents',
    top5_discovery_policies: 'Top 5 Discovery Policies',
    all_scan_incidents: 'All Scan Incidents',
    of_all_incidents: 'of All Incidents',
    incidents: 'Incidents',
    discovery_policies: 'Discovery Policies',
    policy_name: 'Policy Name',
    trend: 'Trend',
    as_of: 'as of',
    email: 'Email',
    permission: 'Permission',
    name: 'Name',
    top5_compliance_file_types: 'Top 5 Compliance File Type',
    ai_file_categories_and_sub_categories: 'ML File Categories and Sub-categories',
    category_and_sub_category: 'Category and Sub-category',
    total_files_categoried: 'Total Files Categorized',
    top10_discovery_policies_trends: 'Top 10 Discovery Policies & Trends',
    discovery_policy_name: 'Discovery Policy Name',
    top10_total: 'Top 10 Total',
    all_other_policies: 'All Other Policies',
    add_report: 'Add Report',
    copied: 'Copied',
    copied_file_path: 'Copied File Path',
    quarantined: 'Quarantined',
    quarantined_file_path: 'Quarantined File Path',
    action: 'Action',
    destination_path: 'Destination Path',
    failed_detail_info: 'Failed Detail Info',
    remediation_actions: 'Remediation Actions',
    idm_match_info: 'IDM Match Info',
    edm_match_info: 'EDM Match Info',
    top5_sensitive_file_extensions: 'Top 5 Sensitive File Extensions',
    file_extension: 'File Extension',
    top5_totals: 'Top 5 Totals',
    file_hash: 'File Hash',
    template_name: 'Dataset Name',
    index_name: 'Index Name',
    rule_name: 'Rule Name',
    similarity: 'Similarity'
  }
};
