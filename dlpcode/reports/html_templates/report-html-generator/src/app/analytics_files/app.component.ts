import { elem } from '@/common/table/element';
import { FilterOperation, TableSettings } from '@/common/table/table';
import { TableComponent } from '@/common/table/table/table.component';
import { EStorageType } from '@/dlp_dashboard/mock_data_structure';
import { DateUtilService } from '@/service/date-util.service';
import { FormatService } from '@/service/format.service';
import { Component, ElementRef, OnInit, Renderer2, ViewChild } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { isNil, keys, last, pick, reduce } from 'lodash';
import { AppGlobalService } from './app-global.service';
import { DataTypeService } from './data_type.service';
import { ICONS } from './icons';
import { mockData } from './mock_data';
import { IAnalyticsFileEntry, IFile } from './mock_data_structure';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  reportData: IFile = 'DOLLAR_UPPER_MOCK_DATA' as any;

  tableSettings: TableSettings;

  selectedScan;

  @ViewChild(TableComponent)
  tableComponent!: TableComponent;

  tableData: any[];

  detailData: any;

  allDetails: any[];

  storageIdentities = [];

  get isPrinting() {
    return this.globalService.isPrinting;
  }

  private _subMapping;

  constructor(
    public globalService: AppGlobalService,
    private iconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
    private el: ElementRef,
    private renderer: Renderer2,
    private dateUtilService: DateUtilService,
    private formatService: FormatService,
    private dataTypeService: DataTypeService
  ) {
    this.reportData = mockData; // Mock data import

    globalService.setTimezone(this.reportData.report_info.timezone);
    globalService.isPrinting = this.reportData.is_printing || false;

    if (this.reportData.is_printing) {
      this.renderer.setStyle(this.el.nativeElement, 'max-width', '210mm');
    }

    this.selectedScan = pick(
      this.reportData.report_info,
      'scan_name',
      'scan_storage',
      'storage_type'
    );
    this.registerMatIcons();
  }

  ngOnInit(): void {
    this.dataTypeService.initData(this.reportData);
    this.tableData = this.reportData.table_data;
    this.storageIdentities = this.reportData.identity_list ?? [];

    this.initTableSettings();

    if (this.isPrinting) {
      this.initAllDetails();
    }
  }

  initTableSettings() {
    // manual add 'Custom' in searchMapping
    let mapping = this.dataTypeService.getTypeCategoryMapping();
    mapping['Custom'] = '30001';

    this._subMapping = this.dataTypeService.getSubSearchMapping();
    for (let key in this._subMapping) {
      // if the value is 0, means not a useful sub-class category
      if (this._subMapping[key] == 0) delete this._subMapping[key];
    }

    const standardDataTypeMappings = {};
    this.reportData.standard_data_type.forEach((x) => {
      standardDataTypeMappings[x.data_type] = x.id;
    });

    const customDataTypeMappings = {};
    this.reportData.custom_data_type.forEach((x) => {
      customDataTypeMappings[x.name] = x.id;
    });

    const storageIdentitiesMappings = {};
    this.storageIdentities.forEach((x) => (storageIdentitiesMappings[x.name] = x.identifier));

    this.tableSettings = {
      tableId: 'analytics_files_table',
      hasSelect: false,
      saveToStorage: false,
      hasMenuButtons: false,
      hasHideShowCols: false,
      isClientSidePagination: true,
      forceToRefresh: true,
      hasMenuBar: !this.isPrinting,
      timeZone: this.globalService.timezone,
      columns: [
        {
          id: 'filename',
          langKey: 'analytics_files.file_name',
          breakLines: true,
          filterSettings: {
            operations: [FilterOperation.Equal],
            usePartialMatch: true
          }
        },
        {
          id: 'storage_type',
          langKey: 'analytics_files.storage',
          sorting: true,
          filterSettings: {
            displayedKey: 'analytics_files.storage_type',
            operations: [FilterOperation.Equal],
            searchMapping: Object.assign(
              {},
              ...Object.values(EStorageType)
                .filter((k) => typeof k !== 'string')
                .map((k) => ({
                  [this.formatService.getStorageType(k as EStorageType).text]: k
                }))
            )
          },
          cellFormatter: (row: IAnalyticsFileEntry) => {
            const storage = this.reportData.storage_profiles_summary.find(
              (x) => x.id === row.storage_id
            );
            if (!storage) {
              return '';
            }

            const storageInfo = this.formatService.getStorageType(row.storage_type);
            return elem('fd-n-icon', {
              svgIcon: storageInfo.icon,
              text: storage.name,
              tooltip: storageInfo.text
            });
          }
        },
        {
          id: 'links',
          langKey: 'analytics_files.shareable_links',
          cellFormatter: (entry: IAnalyticsFileEntry) => {
            return elem('fd-n-shareable-links', { title: entry.filename, value: entry.links });
          }
        },
        {
          id: 'collaborators',
          langKey: 'analytics_files.collaborators',
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMapping: storageIdentitiesMappings,
            filterGroupBy: ['analytics_files.files'],
            allowDuplicate: true,
            searchReplacedKey: 'access'
          },
          cellFormatter: (entry: IAnalyticsFileEntry) => {
            entry.collaborators = [
              '88c8e0ee-1aae-4159-b5ba-3db9007fd10d',
              '49121b09-ab5d-4484-920a-0f0db1da0cd1'
            ];
            const identities = this.storageIdentities.filter((x) =>
              entry.collaborators.includes(x.identifier)
            );

            const value = { internal: [], external: [] };
            identities.forEach((identity) => {
              value[identity.type === 2 ? 'external' : 'internal'].push(identity);
            });

            return elem('fd-n-collaborators', {
              title: entry.filename,
              value
            });
          }
        },
        {
          id: 'owner',
          langKey: 'analytics_files.owner',
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMapping: storageIdentitiesMappings,
            allowDuplicate: true
          },
          cellFormatter: (row: IAnalyticsFileEntry) => {
            row.owner = [
              '88c8e0ee-1aae-4159-b5ba-3db9007fd10d',
              '49121b09-ab5d-4484-920a-0f0db1da0cd1'
            ];
            if (!row.owner?.length) {
              const name = row.file_attributes?.owner?.name;
              return !name || name.toLowerCase() === 'unknown' ? '' : name;
            }

            const names = [];
            row.owner.forEach((x) => {
              const identity = (this.storageIdentities ?? []).find((i) => i.identifier === x);
              if (identity) {
                names.push(identity.name);
              }
            });

            if (names.length === 0) {
              return '';
            }

            if (names.length === 1) {
              return names[0];
            }

            return elem('fd-n-multiple-items-popover', {
              displayedItem: names[0],
              triggeredItem: `+${names.length - 1}`,
              popoverContent: this.formatService.getPopoverContentHtml(names),
              clickable: false
            });
          }
        },
        {
          id: 'standard_match_count',
          langKey: 'analytics_files.standard_sensitivities'
        },
        {
          id: 'custom_match_count',
          langKey: 'analytics_files.custom_sensitivities'
        },
        {
          id: 'main_class_id',
          langKey: 'analytics_files.ai_category',
          cellFormatter: (row: IAnalyticsFileEntry) => {
            let id = row?.main_class_id || '';
            return this.dataTypeService.getMainCategoryLabel(id);
          },
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMapping: this.getMainSearchMapping()
            //filterGroupBy: ['analytics_files.files']
          }
        },
        {
          id: 'sub_class_id',
          langKey: 'analytics_files.ai_subcategory',
          cellFormatter: (row: IAnalyticsFileEntry) => {
            let id = row?.sub_class_id || '';
            return this.dataTypeService.getSubCategoryLabel(id);
          },
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMapping: this._subMapping
            //filterGroupBy: ['analytics_files.files']
          }
        },
        {
          id: 'main_class_confidence',
          langKey: 'analytics_files.category_confidence'
        },
        // {
        //   id: 'folder_path',
        //   langKey: 'analytics_files.folder_path',
        //   invisibleOnShowHidePanel: true,
        //   hideOnReset: true,
        //   filterSettings: {
        //     filterGroupBy: ['analytics_files.folders'],
        //     operations: [FilterOperation.Equal]
        //   }
        // },
        {
          id: 'label',
          langKey: 'analytics_files.labels',
          // filterSettings: {
          //   operations: [FilterOperation.Equal],
          //   //filterGroupBy: ['analytics_files.files'],
          //   searchMapping: {
          //     loading: 'loading'
          //   },
          //   searchMappingOrder: 'asc',
          //   displayItem: ['loading'],
          //   filterSearchMappings: true
          // },
          cellFormatter: (entry: IAnalyticsFileEntry) => {
            const labels = entry.file_tag;
            if (isNil(labels)) {
              return '';
            }
            const labelCounts = reduce(
              keys(labels),
              (res, current) => {
                const r = res + labels[current].length;
                return r;
              },
              0
            );
            if (labelCounts === 0) {
              return '';
            }

            return elem('fd-n-labels', { labels: labels });
          }
        },
        {
          id: 'quarantine_status',
          langKey: 'analytics_files.quarantine_status',
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMappingOrder: { key: 'desc' },
            searchMapping: {
              Yes: 1,
              No: 0,
              Failed: 2
            }
            //filterGroupBy: ['analytics_files.files']
          },
          cellFormatter: (entry: IAnalyticsFileEntry) => {
            const setting = this.formatService.getCopyQuarantineStatus(entry.quarantine_status);
            return setting === '' ? setting : elem('fd-n-icon', setting);
          }
        },
        {
          id: 'copy_status',
          langKey: 'analytics_files.copy_status',
          filterSettings: {
            operations: [FilterOperation.Equal],
            searchMappingOrder: { key: 'desc' },
            searchMapping: {
              Yes: 1,
              No: 0,
              Failed: 2
            }
            //filterGroupBy: ['analytics_files.files']
          },
          cellFormatter: (entry: IAnalyticsFileEntry) => {
            const setting: any = this.formatService.getCopyQuarantineStatus(entry.copy_status);
            return setting === '' ? setting : elem('fd-n-icon', setting);
          }
        },
        {
          id: 'file_ext',
          langKey: 'analytics_files.extension',
          cellFormatter: (row: IAnalyticsFileEntry) => last(row.filename.split('.'))
        },
        {
          id: 'file_size',
          langKey: 'analytics_files.size',
          cellFormatter: (row: IAnalyticsFileEntry) =>
            this.formatService.formatBytes(row.file_attributes.file_size * 1024)
        },
        {
          id: 'file_owner',
          langKey: 'analytics_files.owner',
          cellFormatter: (row: IAnalyticsFileEntry) => {
            const name = row.file_attributes?.owner?.name;
            return !name || name.toLowerCase() === 'unknown' ? '' : name;
          }
        },
        {
          id: 'full_path',
          langKey: 'analytics_files.file_path',
          breakLines: true,
          cellFormatter: (row: IAnalyticsFileEntry) => {
            let value = row?.full_path || '';
            if (row.storage_type == 1) {
              return `s3:/${value}`;
            }
            if (
              !isNil(row?.file_attributes?.file_display_path) &&
              row?.file_attributes.file_display_path !== 'N/A'
            ) {
              return row.file_attributes?.file_display_path;
            }

            return value;
          }
        },
        {
          id: 'update_time',
          langKey: 'analytics_files.updated',
          sorting: true,
          cellFormatter: (row: any) => {
            const update_time = row?.update_time;
            return !update_time
              ? ''
              : this.dateUtilService.convertToGMTTimestamp(
                  update_time,
                  this.globalService.timezone
                );
          }
        }
      ]
      // customActions: this.isPrinting
      //   ? null
      //   : [
      //       {
      //         text: 'analytics_files.view_details',
      //         matIcon: 'vertical_split',
      //         iconSize: 16,
      //         width: 'auto',
      //         click: (row: any) => {
      //           this.detailData = {
      //             ...row,
      //             storage: this.getStorageLabel(),
      //             storage_type: this.selectedScan.storage_type,
      //             scan_policy_id: this.selectedScan.id
      //           };
      //           //this.selectedChanged(row);
      //         }
      //       }
      //     ]
    };
  }

  getStorageType() {
    const scan = this.selectedScan;
    if (!scan) return '';
    if ([-1 || 'All'].includes(scan?.storage_type)) return 'All';
    return this.formatService.getScanStoreType(scan.storage_type);
  }

  getStorageLabel(): string {
    const scan = this.selectedScan;
    if (!scan) return '';
    if ([-1 || 'All'].includes(scan?.storage_type)) return 'All';
    return scan.target ?? scan.scan_storage;
  }

  getMainSearchMapping() {
    let ret = {};

    this.reportData.main_categories.forEach((m) => {
      ret[m.main_class_name] = m.main_class_id;
    });

    return ret;
  }

  registerMatIcons() {
    for (const [key, value] of Object.entries(ICONS)) {
      this.iconRegistry.addSvgIconLiteral(key, this.sanitizer.bypassSecurityTrustHtml(value));
    }
  }

  closeDetail() {
    this.detailData = null;
  }

  initAllDetails() {
    if (!this.tableData?.length) {
      return [];
    }

    if (this.allDetails) {
      return this.allDetails;
    }

    const ret = [];
    const extraInfo = {
      storage: this.getStorageLabel(),
      storage_type: this.selectedScan.storage_type,
      scan_policy_id: this.selectedScan.id
    };

    this.tableData.forEach((x) => {
      // TODO. confirm the details format with backend
      const detail = this.reportData.sensitive_data_details[x.file_uuid];
      if (detail && ret.length < 3) {
        ret.push({ ...x, ...detail, ...extraInfo });
      }
    });
    this.allDetails = ret;
    return this.allDetails;
  }
}
