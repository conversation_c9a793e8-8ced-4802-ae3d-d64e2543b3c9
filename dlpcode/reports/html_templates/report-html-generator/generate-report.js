const fs = require("fs");

const args = process.argv.slice(1);
const newReportName = args[1];
const baseTemplate = args[2];

const packageJsonPath = "./package.json";
const angularJsonPath = "./angular.json";

const baseSampleName = baseTemplate || "base_sample";
const reportsFolderPath = "./src/app";
const baseReportTemplatePath = `${reportsFolderPath}/${baseSampleName}`;
const projectName = "report-html-generator";

if (newReportName === undefined) {
  throw "no report name";
}

// modify package.json
fs.readFile(packageJsonPath, "utf8", (err, data) => {
  if (err) {
    throw `Error reading file: ${err}`;
  }
  const json = JSON.parse(data);
  const newStartStr = `start:${newReportName}`;
  const newBuildStr = `build:inline:${newReportName}`;

  if (
    json.scripts[newStartStr] !== undefined ||
    json.scripts[newBuildStr] !== undefined
  ) {
    throw `${newReportName} exists`;
  }
  json.scripts[newStartStr] = `ng serve --configuration=${newReportName} --port 8200`;
  json.scripts[
    newBuildStr
  ] = `cp src/app/${newReportName}/app.component.ts src/app/${newReportName}/app.component.backup && sed -i '/mockData/ s/^/\\/\\//' src/app/${newReportName}/app.component.ts && ng build --configuration=${newReportName} && gulp inline --name=${newReportName} && mv src/app/${newReportName}/app.component.backup src/app/${newReportName}/app.component.ts && sed -i 's/\"DOLLAR_UPPER_MOCK_DATA\"/DOLLAR_UPPER_MOCK_DATA/g' dist/single-file/${newReportName}.html && mkdir ../templates/${newReportName}/ -p && mv dist/single-file/${newReportName}.html ../templates/${newReportName}/${newReportName}.html && node ./generate-files.js ${newReportName}`;

  fs.writeFile(packageJsonPath, JSON.stringify(json, null, 2), (err) => {
    if (err) {
      throw `Error writing file: ${err}`;
    }
    console.log("package.json successfully updated!");
  });
});

// modify angular.json
fs.readFile(angularJsonPath, "utf8", (err, data) => {
  if (err) {
    throw `Error reading file: ${err}`;
  }
  const json = JSON.parse(data);
  const buildCfg = json.projects[projectName].architect.build.configurations;
  const newReportDev = `${newReportName}_dev`;
  const baseSampleDev = `${baseSampleName}_dev`;

  if (buildCfg[newReportName] !== undefined) {
    throw `${newReportName} exists`;
  }
  buildCfg[newReportName] = JSON.parse(
    JSON.stringify(buildCfg[baseSampleName])
  );
  buildCfg[
    newReportName
  ].index = `${reportsFolderPath}/${newReportName}/index.html`;
  buildCfg[
    newReportName
  ].main = `${reportsFolderPath}/${newReportName}/main.ts`;

  buildCfg[newReportDev] = JSON.parse(JSON.stringify(buildCfg[baseSampleDev]));
  buildCfg[
    newReportDev
  ].index = `${reportsFolderPath}/${newReportName}/index.html`;
  buildCfg[newReportDev].main = `${reportsFolderPath}/${newReportName}/main.ts`;

  const serveCfg = json.projects[projectName].architect.serve.configurations;
  if (serveCfg[newReportName] !== undefined) {
    throw `${newReportName} exists`;
  }
  serveCfg[newReportName] = JSON.parse(
    JSON.stringify(serveCfg[baseSampleName])
  );
  serveCfg[
    newReportName
  ].browserTarget = `${projectName}:build:${newReportName}_dev`;

  fs.writeFile(angularJsonPath, JSON.stringify(json, null, 2), (err) => {
    if (err) {
      throw `Error writing file: ${err}`;
    }
    console.log("Angular.json successfully updated!");
  });
});

const path = require("path");
async function copyFolder(src, dest) {
  try {
    await fs.promises.mkdir(dest, { recursive: true });

    const files = await fs.promises.readdir(src);

    for (const file of files) {
      const srcPath = path.join(src, file);
      const destPath = path.join(dest, file);

      const stat = await fs.promises.stat(srcPath);

      if (stat.isDirectory()) {
        await copyFolder(srcPath, destPath);
      } else {
        await fs.promises.copyFile(srcPath, destPath);
      }
    }

    console.log(`Successfully copied folder from ${src} to ${dest}`);
  } catch (err) {
    console.error("Error copying folder:", err);
  }
}

const destFolder = `${reportsFolderPath}/${newReportName}`;

copyFolder(baseReportTemplatePath, destFolder);
