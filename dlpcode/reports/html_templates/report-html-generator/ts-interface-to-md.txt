import * as fs from 'fs';
import * as ts from 'typescript';

const processed = new Set<string>();

function parseFile(filePath: string): string {
  const sourceCode = fs.readFileSync(filePath, 'utf-8');
  const sourceFile = ts.createSourceFile(
    filePath,
    sourceCode,
    ts.ScriptTarget.Latest,
    true
  );

  const interfaceMap = new Map<string, ts.InterfaceDeclaration>();
  const enumMap = new Map<string, ts.EnumDeclaration>();

  // collect all interfaces and enums
  ts.forEachChild(sourceFile, (node) => {
    if (ts.isInterfaceDeclaration(node)) {
      interfaceMap.set(node.name.text, node);
    } else if (ts.isEnumDeclaration(node)) {
      enumMap.set(node.name.text, node);
    }
  });

  const mdSections: string[] = [];

  enumMap.forEach((node, name) => {
    const section = generateMarkdownForEnum(name, node);
    if (section) mdSections.push(section);
  });

  interfaceMap.forEach((node, name) => {
    const section = generateMarkdownForInterface(name, node, interfaceMap);
    if (section) mdSections.push(section);
  });

  return mdSections.join('\n\n');
}

function generateMarkdownForInterface(
  name: string,
  node: ts.InterfaceDeclaration,
  interfaceMap: Map<string, ts.InterfaceDeclaration>
): string {
  if (processed.has(name)) return '';
  processed.add(name);

  const header = `### Interface \`${name}\`\n\n| name | type | must | description |\n|--------|------|------|------|`;
  const rows: string[] = [];

  for (const member of node.members) {
    if (ts.isPropertySignature(member) && member.name) {
      const propName = (member.name as ts.Identifier).text;
      const propType = member.type?.getText() || 'any';
      const isOptional = member.questionToken ? 'No' : 'Yes';

      // get JSDoc comment
      const jsDoc = ts.getJSDocCommentsAndTags(member);
      const comment =
        jsDoc
          .map((doc) => (ts.isJSDoc(doc) ? doc.comment : ''))
          .join('')
          .trim() || '-';

      rows.push(`| ${propName} | ${propType} | ${isOptional} | ${comment} |`);

      if (interfaceMap.has(propType)) {
        const subNode = interfaceMap.get(propType)!;
        const subMd = generateMarkdownForInterface(
          propType,
          subNode,
          interfaceMap
        );
        if (subMd) rows.push('\n' + subMd);
      }
    }
  }

  return `${header}\n${rows.join('\n')}`;
}

function generateMarkdownForEnum(
  name: string,
  node: ts.EnumDeclaration
): string {
  const header = `### Enum \`${name}\`\n\n| Enum | Value |\n|--------|-----|`;
  const rows: string[] = [];

  node.members.forEach((member) => {
    const enumName = member.name.getText();
    const enumValue = member.initializer
      ? member.initializer.getText()
      : enumName;
    rows.push(`| ${enumName} | ${enumValue} |`);
  });

  return `${header}\n${rows.join('\n')}`;
}

const result = parseFile('./src/app/_DOLLAR_MAGIC/mock_data_structure.ts');
fs.writeFileSync('data_structure.md', result);
console.log('✅ Generate structure Markdown Table successfully!');

import { mockData } from './src/app/_DOLLAR_MAGIC/mock_data';
fs.writeFileSync('data_sample.json', JSON.stringify(mockData, null, 2));
console.log('✅ Generate Sample json file successfully！');
