### Enum `EScanStorageType`

| Enum | Value |
|--------|-----|
| aws | 1 |
| cloud | 2 |
| prem | 3 |
| smb | 4 |
| gdrive | 6 |

### Enum `CopyQuarantineStatus`

| Enum | Value |
|--------|-----|
| Init | Init |
| Success | Success |
| Failed | Failed |
| InProgress | InProgress |

### Interface `IReportInfo`

| name | type | must | description |
|--------|------|------|------|
| name | string | Yes | - |
| generated_on | string | Yes | When the report generated |
| schedule | string | Yes | - |
| period | string | Yes | - |
| created_by | string | Yes | - |
| notes | string | Yes | - |
| scan_name | string | Yes | - |
| scan_storage | string | Yes | - |
| storage_type | number | Yes | - |
| timezone | string | Yes | IANA format |
| firmware_version | string | Yes | - |

### Interface `IMainCategory`

| name | type | must | description |
|--------|------|------|------|
| main_class_id | string | Yes | - |
| main_class_name | string | Yes | - |
| discarded | boolean | No | - |

### Interface `ISubClass`

| name | type | must | description |
|--------|------|------|------|
| main_class_name | string | No | - |
| sub_class_id | number | string | Yes | - |
| sub_class_name | string | Yes | - |
| discarded | boolean | No | - |
| description | string | Yes | - |

### Interface `ISubCategory`

| name | type | must | description |
|--------|------|------|------|


### Interface `IScan`

| name | type | must | description |
|--------|------|------|------|
| name | string | Yes | - |
| id | string | Yes | - |
| target | string | Yes | - |
| description | string | Yes | - |
| storage_type | number | Yes | - |
| copy | boolean | Yes | - |
| quarantine | boolean | Yes | - |

### Interface `ITypeCategory`

| name | type | must | description |
|--------|------|------|------|
| id | string | Yes | - |
| name | string | Yes | - |
| description | string | Yes | - |

### Interface `IStandardDataType`

| name | type | must | description |
|--------|------|------|------|
| continent | string[] | Yes | - |
| create_time | number | Yes | - |
| data_type | string | Yes | - |
| description | string | Yes | - |
| entity | string | Yes | - |
| id | string | Yes | - |
| language | string[] | Yes | - |
| region | string[] | Yes | - |
| type | string | Yes | - |
| type_category | string[] | Yes | - |
| update_time | number | Yes | - |

### Interface `ICustomDataType`

| name | type | must | description |
|--------|------|------|------|
| created_at | number | Yes | - |
| definition | {
    keywords: [
      {
        keyword: string;
      }
    ];
    patterns: [
      {
        regex: string;
      }
    ];
  } | Yes | - |
| description | string | Yes | - |
| discover_policy_cnt | number | Yes | - |
| dlp_policy_cnt | number | Yes | - |
| edm_cnt | number | Yes | - |
| group_uuid | string | Yes | - |
| id | string | Yes | - |
| name | string | Yes | - |
| updated_at | number | Yes | - |

### Interface `IFile`

| name | type | must | description |
|--------|------|------|------|
| table_data | any[] | Yes | - |
| report_info | IReportInfo | Yes | report info |
| supported_type_categories | { [key: string]: ITypeCategory[] } | Yes | supported standard type categories |
| standard_data_type | IStandardDataType[] | Yes | standard data types |
| custom_data_type | ICustomDataType[] | Yes | custom data types |
| main_categories | IMainCategory[] | Yes | Main ML categories |
| sub_categories | ISubCategory | Yes | Sub ML categories |
| is_printing | boolean | Yes | used to generate PDF format report |
| sensitive_data_details | any | Yes | Sensitive data details |
| column_names | any | Yes | For CSV column mapping |
| storage_profiles_summary | any[] | Yes | Storages summary |
| identity_list | any[] | Yes | identities list |

### Interface `IAnalyticsFileEntry`

| name | type | must | description |
|--------|------|------|------|
| custom_match_count | number | Yes | - |
| endpoint | string | Yes | - |
| file_attributes | any | Yes | - |
| file_tag | { custom: any[]; ml: any[]; predefine: any[] } | Yes | - |
| file_uuid | string | Yes | - |
| filename | string | Yes | - |
| full_path | string | Yes | - |
| id | string | Yes | - |
| main_class_confidence | number | Yes | - |
| main_class_id | string | Yes | - |
| standard_match_count | number | Yes | - |
| sub_class_confidence | number | Yes | - |
| sub_class_id | string | Yes | - |
| storage_type | number | Yes | - |
| storage | string | Yes | - |
| storage_id | string | Yes | - |
| update_time | number | Yes | - |
| quarantine_status | CopyQuarantineStatus | Yes | - |
| copy_status | CopyQuarantineStatus | Yes | - |
| scan_id | string | Yes | - |
| collaborators | string[] | Yes | - |
| owner | string[] | Yes | - |
| links | {
    public?: string[];
    internal?: string[];
    external?: string[];
  } | Yes | - |