# trackers.py
import os, json, time
import redis
import logging
from typing import Optional, List, Tuple
from util.db import get_redis_client

logger = logging.getLogger("ddr_download")

def _now() -> int:
    return int(time.time())


class DownloadLeaseTracker:
    """
    Deal with download task leases
    - acquire(backlog_hash, file_info, params, session_key, lease_secs)
    - renew(lease_id, extra_secs)
    - release(lease_id, error)
    - list_stalled(older_than)
    - fetch_payload(lease_id)
    """
    H_PREFIX = "ddr:lease"         # Hash: ddr:lease:{task_uuid}:{backlog_hash}
    Z_KEY = "ddr:lease_idx"        # ZSET: member=task_uuid|backlog_hash, score=expire_ts
    O_PREFIX = "ddr:lease_owner"   # SET:  ddr:lease_owner:{pid}

    def __init__(self, task_uuid: str):
        self.task_uuid = task_uuid
        self.pid = str(os.getpid())
        self.r = get_redis_client()

    def _hkey(self, backlog_hash: str) -> str:
        return f"{self.H_PREFIX}:{self.task_uuid}:{backlog_hash}"

    def _owner_key(self) -> str:
        return f"{self.O_PREFIX}:{self.pid}"

    def _member(self, backlog_hash: str) -> str:
        return f"{self.task_uuid}|{backlog_hash}"

    def acquire(self, backlog_hash: str, file_info: dict, params: dict, session_key: str, lease_secs: int = 600) -> str:
        """
        Atomically acquire: record necessary params in payload (JSON).
        """
        hkey = self._hkey(backlog_hash)
        member = self._member(backlog_hash)
        now = _now()
        exp = now + lease_secs

        payload = {
            "task_uuid": self.task_uuid,
            "backlog_hash": backlog_hash,
            "file_info": file_info,
            "params": params,
            "session_key": session_key,
            "owner_pid": self.pid,
            "lease_start": now,
            "lease_expire": exp,
        }
        pipe = self.r.pipeline()
        pipe.hset(hkey, mapping={"payload": json.dumps(payload)})
        pipe.zadd(self.Z_KEY, {member: exp})
        pipe.sadd(self._owner_key(), hkey)
        pipe.execute()
        return hkey  # lease_id

    def renew(self, lease_id: str, extra_secs: int = 60) -> bool:
        try:
            raw = self.r.hget(lease_id, "payload")
            if not raw:
                return False
            payload = json.loads(raw)
            exp = _now() + extra_secs
            payload["lease_expire"] = exp
            backlog_hash = payload["backlog_hash"]
            member = self._member(backlog_hash)

            pipe = self.r.pipeline()
            pipe.hset(lease_id, "payload", json.dumps(payload))
            pipe.zadd(self.Z_KEY, {member: exp})
            pipe.execute()
            return True
        except Exception:
            logger.exception("[lease] renew failed")
            return False

    def get_lease_id(self, backlog_hash: str) -> Optional[str]:
        return self._hkey(backlog_hash)

    def release(self, lease_id: str, error: bool = False) -> None:
        try:
            raw = self.r.hget(lease_id, "payload")
            if not raw:
                return
            payload = json.loads(raw)
            backlog_hash = payload.get("backlog_hash", "")
            member = self._member(backlog_hash) if backlog_hash else ""

            pipe = self.r.pipeline()
            pipe.delete(lease_id)
            if member:
                pipe.zrem(self.Z_KEY, member)
            pipe.srem(self._owner_key(), lease_id)
            pipe.execute()
        except Exception:
            logger.exception("[lease] release failed")

    def release_all(self) -> None:
        try:
            keys = list(self.r.smembers(self._owner_key()) or [])
            pipe = self.r.pipeline()
            for lease_id in keys:
                raw = self.r.hget(lease_id, "payload")
                if not raw:
                    pipe.srem(self._owner_key(), lease_id)
                    continue
                payload = json.loads(raw)
                backlog_hash = payload.get("backlog_hash", "")
                member = self._member(backlog_hash) if backlog_hash else ""
                pipe.delete(lease_id)
                if member:
                    pipe.zrem(self.Z_KEY, member)
                pipe.srem(self._owner_key(), lease_id)
            pipe.execute()
        except Exception:
            logger.exception("[lease] release_all failed")

    def lease_heartbeat(self, lease_id: str):
        try:
            self.lease.renew(lease_id)
        except Exception:
            logger.debug("lease renew failed", exc_info=True)

    @classmethod
    def list_stalled(cls, older_than: int = 0) -> List[Tuple[str, str]]:
        """
        List stalled leases: return list of (task_uuid, backlog_hash) tuples.
        older_than: grace period in seconds to avoid false positives.
        """
        r = get_redis_client()
        now = _now()
        cutoff = now - max(older_than, 0)
        members = r.zrangebyscore(cls.Z_KEY, min=0, max=cutoff)
        res = []
        for m in members:
            try:
                task_uuid, backlog_hash = m.split("|", 1)
                res.append((task_uuid, backlog_hash))
            except ValueError:
                r.zrem(cls.Z_KEY, m)
        return res

    @classmethod
    def fetch_payload(cls, task_uuid: str, backlog_hash: str) -> Optional[dict]:
        r = get_redis_client()
        hkey = f"{cls.H_PREFIX}:{task_uuid}:{backlog_hash}"
        raw = r.hget(hkey, "payload")
        if not raw:
            return None
        try:
            return json.loads(raw)
        except Exception:
            return None

    @classmethod
    def delete_lease(cls, task_uuid: str, backlog_hash: str) -> None:
        r = get_redis_client()
        hkey = f"{cls.H_PREFIX}:{task_uuid}:{backlog_hash}"
        member = f"{task_uuid}|{backlog_hash}"
        pipe = r.pipeline()
        pipe.delete(hkey)
        pipe.zrem(cls.Z_KEY, member)
        pipe.execute()

