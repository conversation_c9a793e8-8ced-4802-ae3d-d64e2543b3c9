import uuid
from exts import  Base
from sqlalchemy import Column,  DateTime
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from datetime import timezone


# DDR incidents
class DDRIncident(Base):
    __tablename__ = "ddr_incidents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    ctime = Column(DateTime, server_default=func.now())
    utime = Column(DateTime, server_default=func.now(), onupdate=func.now())
    attributes = Column(JSONB)
    attributes_ext = Column(JSONB)
    pol_details = Column(JSONB)
    data_source = Column(JSONB)
    file_info = Column(JSONB)
    activity = Column(JSONB)


    def to_dict(self):
        return {
            'id': str(self.id),
            'ctime': self.ctime.replace(tzinfo=timezone.utc).timestamp() if self.ctime.tzinfo is None else self.ctime.timestamp(),
            'utime': self.utime.replace(tzinfo=timezone.utc).timestamp() if self.utime.tzinfo is None else self.utime.timestamp(), 
            'attributes': self.attributes,
            'attributes_ext': self.attributes_ext,
            'pol_details': self.pol_details,
            'data_source': self.data_source,
            'file_info': self.file_info,
            'activity': self.activity
        }

