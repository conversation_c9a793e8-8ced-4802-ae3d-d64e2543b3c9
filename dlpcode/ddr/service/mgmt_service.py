from __future__ import annotations

import uuid
import time
import threading
from cachetools import TTLCache
from exts import get_logger
from domain_model.tracker.doing_download_task_tracker import DoingDownloadTaskTracker as _DoingDownloadTracker
from domain_model.tracker.analyze_worker_tracker import AnalyzeWorkerTracker as _AnalyzeTracker
from domain_model.tracker.task_tracker import TaskTracker as _TaskTracker
from ddr.model.task import get_ddr_task
from ddr.model.connector_builder import DDRConnectorBuilder


logger = get_logger("ddr")

_CONNECTOR_CACHE = TTLCache(maxsize=512, ttl=30 * 60)
_CONNECTOR_LOCK = threading.Lock()

def _connector_cache_key(task_id: str, session_key: str) -> str:
    # Each thread has its own copy to avoid non-thread-safe clients
    return f"{task_id}:{session_key}:{threading.get_ident()}"


class DDRTaskMgmtService:
    """
    DDR Task Management Service
    - Event-driven "download -> analyze" two-stage pipeline
    - Unified status management and tracking
    """
    def __init__(self, ddr_task_id: str, session_key: str = ""):
        self.ddr_task_id = ddr_task_id
        # Cache task info to avoid repeated DB queries
        self._task_info = None
        self.session_key = session_key

    def _get_task_info(self):
        """Get cached task info, query DB only once"""
        if self._task_info is None:
            self._task_info = get_ddr_task(id=self.ddr_task_id)
        return self._task_info

    def is_scanning(self) -> bool:
        try:
            task = self._get_task_info()
            return bool(task and task.enabled)
        except Exception:
            logger.exception("is_scanning failed")
            return False

    def get_session_key(self) -> str:
        """
        Returns the current session key.
        Returns:
            str: The current session key.
        """
        return self.session_key

    def is_stopping(self) -> bool:
        try:
            task = self._get_task_info()
            return False if task is None else (not bool(task.enabled))
        except Exception:
            logger.exception("is_stopping failed")
            return False


    def get_connector(self):
        """
        Get (or reuse) Connector:
        - Hit cache directly return
        - Not hit or invalid -> build new instance and cache
        - Encounter typical errors -> clear cache and rebuild
        """
        key = _connector_cache_key(self.ddr_task_id, self.session_key)

        # 1) Hit cache
        with _CONNECTOR_LOCK:
            conn = _CONNECTOR_CACHE.get(key)

        if conn is not None:
            try:
                if hasattr(conn, "test_connection") and not conn.test_connection():
                    raise RuntimeError("connector unhealthy")
                return conn
            except Exception:
                # test check failed, remove cache
                with _CONNECTOR_LOCK:
                    _CONNECTOR_CACHE.pop(key, None)

        # 2) Not hit or removed -> build
        try:
            task = self._get_task_info()
            if not task:
                logger.error(f"DDR task {self.ddr_task_id} not found")
                return None
            new_conn = DDRConnectorBuilder(task).get_connector()
            if not new_conn:
                return None
            with _CONNECTOR_LOCK:
                _CONNECTOR_CACHE[key] = new_conn
            return new_conn
        except Exception:
            logger.exception("get_connector(build) failed")
            return None

    # -------- Rate Limit / Backoff --------
    def scan_backoff(self):
        try:
            time.sleep(self._get_scan_backoff_time())
        except Exception:
            logger.exception("scan_backoff failed")

    def _get_scan_backoff_time(self) -> float:
        try:
            task = self._get_task_info()
            if not task:
                logger.error(f"DDR task {self.ddr_task_id} not found")
                return 0.0
            # Keep the same as the production: 1->0.01s, 2->0.02s, 3->0.00s
            val = getattr(task, "scan_interval", None)
            interval = 0.0
            if val == 1:
                interval = 0.01
            elif val == 2:
                interval = 0.02
            elif val == 3:
                interval = 0.00
            return interval
        except Exception:
            logger.exception("_get_scan_backoff_time failed")
            return 0.0