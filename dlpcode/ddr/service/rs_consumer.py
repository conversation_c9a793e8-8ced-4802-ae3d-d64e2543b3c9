import redis
import json
import random
import time
import signal
from collections import defaultdict
from threading import Thread
from exts import get_logger, rs_client
from huey_worker.analyze_worker import analyze_worker, ddr_analyze_worker, huey as analyze_huey

logger = get_logger("rs_analyze")

# Redis Stream config
STREAM_NAME = "stream:analyze_tasks"
GROUP_NAME = "group:dispatcher"
CONSUMER_NAME = "rs_consumer-analyze"

# Retry config
MAX_RETRY_TIMES = 3
RETRY_COUNT_HASH_KEY = "analyze:retry_counts"

LAST_PROCESSED_ID_KEY = "stream:last_id"
RESUME_FROM_LAST = True

# Task type ratio
TASK_RATIOS = {
    "ddr": 0.7,
    "scan_policy": 0.3
}

# Internal states
should_exit = False
last_seen = defaultdict(lambda: 0)
last_dispatch_time = defaultdict(lambda: 0)
dispatch_count = defaultdict(lambda: 0)


def handle_exit(signum, frame):
    global should_exit
    should_exit = True
    logger.info("Received termination signal. Exiting dispatcher...")


def get_ratio_dynamic(source, active_interval=30):
    now = time.time()
    active = [s for s, t in last_seen.items() if now - t < active_interval]
    if not active or source not in active:
        return 0.0
    total = sum(TASK_RATIOS.get(s, 0) for s in active) or 1.0
    return TASK_RATIOS.get(source, 0) / total


def setup_stream():
    try:
        rs_client.xgroup_create(STREAM_NAME, GROUP_NAME, id='0', mkstream=True)
        logger.info(f"Created stream group '{GROUP_NAME}'")
    except redis.exceptions.ResponseError as e:
        if "BUSYGROUP" in str(e):
            logger.info("Stream group already exists")
        else:
            raise


def refresh_ratios_from_config():
    global TASK_RATIOS
    while not should_exit:
        try:
            raw = rs_client.get("config:task_ratios")
            if raw:
                ratios = json.loads(raw)
                if isinstance(ratios, dict):
                    total = sum(ratios.values()) or 1.0
                    TASK_RATIOS = {k: v / total for k, v in ratios.items()}
                    logger.info(f"[Config] Updated task ratios: {TASK_RATIOS}")
        except Exception as e:
            logger.warning(f"[Config] Failed to load task ratios: {e}")
        time.sleep(60)


def get_analyze_speed_limit():
    try:
        enabled = rs_client.get("config:analyze_speed_control_enabled")
        if enabled and enabled.lower() in {"false", "0", "disabled"}:
            return None
        raw = rs_client.get("config:analyze_speed_limit")
        return float(raw) if raw else None
    except Exception as e:
        logger.warning(f"[Config] Failed to get speed limit: {e}")
        return None


def process_message(msg_id, fields):
    # Decode fields keys (and optionally values) from bytes to str
    fields = {k.decode(): v.decode() if isinstance(v, bytes) else v for k, v in fields.items()}

    source = fields.get("source")
    payload_raw = fields.get("payload")

    if not payload_raw or source not in ("ddr", "scan_policy"):
        logger.warning(f"[Skip] Missing payload or invalid source: {fields}")
        rs_client.xack(STREAM_NAME, GROUP_NAME, msg_id)
        return

    try:
        payload = json.loads(payload_raw)
    except Exception as e:
        logger.warning(f"[Skip] Malformed payload: {payload_raw} ({e})")
        rs_client.xack(STREAM_NAME, GROUP_NAME, msg_id)
        return

    last_seen[source] = time.time()
    ratio = get_ratio_dynamic(source, active_interval=30)

    # Speed limit
    speed_limit = get_analyze_speed_limit()
    now = time.time()
    if now - last_dispatch_time[source] >= 1.0:
        dispatch_count[source] = 0
        last_dispatch_time[source] = now

    if speed_limit and dispatch_count[source] >= speed_limit * ratio:
        logger.info(f"[Throttle] {msg_id} from {source}, dispatch_count={dispatch_count[source]}, limit={speed_limit * ratio:.2f}")
        return

    dispatched = False
    if random.random() <= ratio:
        logger.info(f"[Accept] {msg_id} from {source}, dispatch_count={dispatch_count[source]}, ratio={ratio:.2f}")
        dispatch_count[source] += 1

        task_args = dict(
            local_file=payload.get("local_file"),
            remote_info=payload.get("remote_info"),
            file_uuid=payload.get("file_uuid"),
            scan_info=payload.get("scan_info"),
            task_uuid=payload.get("task_uuid"),
            session_key=payload.get("session_key"),
            backlog_hash=payload.get("backlog_hash"),
        )

        if source == "ddr":
            analyze_huey.enqueue(ddr_analyze_worker.s(**task_args))
            dispatched = True
        elif source == "scan_policy":
            analyze_huey.enqueue(analyze_worker.s(**task_args))
            dispatched = True
        else:
            logger.warning(f"[Drop] Unknown source: {source}")
    else:
        logger.info(f"[Skip] {msg_id} from {source}, dispatch_count={dispatch_count[source]}, ratio={ratio:.2f}")

    try:
        if dispatched:
            rs_client.xack(STREAM_NAME, GROUP_NAME, msg_id)
            rs_client.xdel(STREAM_NAME, msg_id)
    except Exception as e:
        logger.critical(f"[Ack Error] Failed to ack {msg_id}. This may cause duplicate processing! Error: {e}")


def process_old_and_stuck_messages():
    """
    Deal with stuck (pending) messages and old unread messages.
    """
    logger.info("[Recovery] Background message recovery thread started...")

    # Get last processed ID
    last_id = rs_client.get(LAST_PROCESSED_ID_KEY)
    if not last_id or not RESUME_FROM_LAST:
        last_id = '0'
    else:
        last_id = last_id.decode()  # bytes -> str

    while not should_exit:
        try:
            # Step 1: Find stuck (pending) messages and claim them back
            pending_info = rs_client.xpending(STREAM_NAME, GROUP_NAME)
            total_pending = pending_info["pending"]

            if total_pending > 0:
                min_id = pending_info["min"]
                max_id = pending_info["max"]

                messages = rs_client.xrange(STREAM_NAME, min=min_id, max=max_id, count=10)
                claimed = rs_client.xclaim(
                    STREAM_NAME,
                    GROUP_NAME,
                    CONSUMER_NAME,
                    min_idle_time=60000,  # ms
                    message_ids=[msg_id for msg_id, _ in messages]
                )

                logger.info(f"[Recovery] Claimed {len(claimed)} stuck messages")

                for msg_id, fields in claimed:
                    retry_count = rs_client.hincrby(RETRY_COUNT_HASH_KEY, msg_id, 1)
                    if retry_count > MAX_RETRY_TIMES:
                        logger.warning(f"[Recovery] Drop message {msg_id} (retry limit reached)")
                        rs_client.xack(STREAM_NAME, GROUP_NAME, msg_id)
                        continue
                    logger.info(f"[Recovery] Retry message: {msg_id} (attempt {retry_count})")
                    process_message(msg_id, fields)
                    rs_client.set(LAST_PROCESSED_ID_KEY, msg_id)

            # Step 2: Scan old unread messages
            old_messages = rs_client.xreadgroup(
                GROUP_NAME, CONSUMER_NAME, {STREAM_NAME: last_id},
                count=10, block=1000
            )

            if old_messages:
                for _, msgs in old_messages:
                    for msg_id, fields in msgs:
                        logger.info(f"[Recovery] Processing old message: {msg_id}")
                        try:
                            process_message(msg_id, fields)
                            rs_client.set(LAST_PROCESSED_ID_KEY, msg_id)
                        except Exception as e:
                            logger.error(f"[Recovery] Failed to process old msg {msg_id}: {e}")
        except Exception as e:
            logger.error(f"[Recovery] Error during recovery: {e}")

        time.sleep(5)


def dispatch():
    logger.info("[Dispatcher] Dispatcher thread started")
    while not should_exit:
        try:
            res = rs_client.xreadgroup(
                GROUP_NAME, CONSUMER_NAME, {STREAM_NAME: '>'},
                count=10, block=5000
            )
            if not res:
                continue

            for _, messages in res:
                for msg_id, fields in messages:
                    try:
                        process_message(msg_id, fields)
                    except Exception as e:
                        logger.error(f"[Dispatch] Error processing message {msg_id}: {e}")

        except redis.exceptions.ConnectionError as ce:
            logger.error(f"[Dispatch] Redis connection error: {ce}")
            time.sleep(5)
        except Exception as e:
            logger.error(f"[Dispatch] Unexpected error: {e}")
            time.sleep(3)


def main():
    signal.signal(signal.SIGTERM, handle_exit)
    signal.signal(signal.SIGINT, handle_exit)
    setup_stream()
    Thread(target=refresh_ratios_from_config, daemon=True).start()
    Thread(target=process_old_and_stuck_messages, daemon=True).start()
    dispatch()


if __name__ == "__main__":
    main()
