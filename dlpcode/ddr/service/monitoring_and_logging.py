"""
DDR系统监控和日志增强
主要功能：
1. 结构化日志记录
2. 性能指标收集
3. 健康检查
4. 告警机制
5. 链路追踪
"""

import time
import json
import uuid
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from contextlib import contextmanager
from enum import Enum
import logging
from logging.handlers import RotatingFileHandler

from util.common_log import get_logger
from util.config import configs

# 基础日志器
base_logger = get_logger("ddr_monitoring")


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class TraceContext:
    """链路追踪上下文"""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str] = None
    operation_name: str = ""
    start_time: float = 0
    tags: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}
        if self.start_time == 0:
            self.start_time = time.time()


@dataclass
class Metric:
    """指标数据"""
    name: str
    type: MetricType
    value: float
    timestamp: float
    tags: Dict[str, str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}


class StructuredLogger:
    """结构化日志器"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"ddr.{name}")
        self._setup_logger()
        
        # 线程本地存储用于追踪上下文
        self._local = threading.local()
    
    def _setup_logger(self):
        """设置日志器"""
        if self.logger.handlers:
            return
        
        # 设置日志级别
        log_level = configs.get("logging", {}).get("level", "INFO")
        self.logger.setLevel(getattr(logging, log_level))
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = configs.get("logging", {}).get("file", f"/tmp/ddr_{self.name}.log")
        file_handler = RotatingFileHandler(
            log_file, maxBytes=100*1024*1024, backupCount=5
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def set_trace_context(self, trace_context: TraceContext):
        """设置追踪上下文"""
        self._local.trace_context = trace_context
    
    def get_trace_context(self) -> Optional[TraceContext]:
        """获取追踪上下文"""
        return getattr(self._local, 'trace_context', None)
    
    def _build_log_entry(self, level: LogLevel, message: str, **kwargs) -> Dict[str, Any]:
        """构建日志条目"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "level": level.value,
            "logger": self.name,
            "message": message,
            "thread_id": threading.get_ident(),
        }
        
        # 添加追踪信息
        trace_context = self.get_trace_context()
        if trace_context:
            entry.update({
                "trace_id": trace_context.trace_id,
                "span_id": trace_context.span_id,
                "operation": trace_context.operation_name
            })
        
        # 添加额外字段
        if kwargs:
            entry["extra"] = kwargs
        
        return entry
    
    def log(self, level: LogLevel, message: str, **kwargs):
        """记录结构化日志"""
        entry = self._build_log_entry(level, message, **kwargs)
        log_message = json.dumps(entry, ensure_ascii=False)
        
        # 使用标准日志器输出
        getattr(self.logger, level.value.lower())(log_message)
    
    def debug(self, message: str, **kwargs):
        self.log(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        self.log(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self.log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self.log(LogLevel.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        self.log(LogLevel.CRITICAL, message, **kwargs)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self._metrics: List[Metric] = []
        self._metrics_lock = threading.Lock()
        self._max_metrics = 10000
        
        # 聚合指标
        self._counters: Dict[str, float] = {}
        self._gauges: Dict[str, float] = {}
        self._histograms: Dict[str, List[float]] = {}
    
    def record_metric(self, name: str, metric_type: MetricType, value: float, tags: Dict[str, str] = None):
        """记录指标"""
        metric = Metric(
            name=name,
            type=metric_type,
            value=value,
            timestamp=time.time(),
            tags=tags or {}
        )
        
        with self._metrics_lock:
            self._metrics.append(metric)
            
            # 保持指标数量限制
            if len(self._metrics) > self._max_metrics:
                self._metrics.pop(0)
            
            # 更新聚合指标
            self._update_aggregated_metrics(metric)
    
    def _update_aggregated_metrics(self, metric: Metric):
        """更新聚合指标"""
        key = f"{metric.name}:{':'.join(f'{k}={v}' for k, v in metric.tags.items())}"
        
        if metric.type == MetricType.COUNTER:
            self._counters[key] = self._counters.get(key, 0) + metric.value
        elif metric.type == MetricType.GAUGE:
            self._gauges[key] = metric.value
        elif metric.type == MetricType.HISTOGRAM:
            if key not in self._histograms:
                self._histograms[key] = []
            self._histograms[key].append(metric.value)
            # 保持直方图大小限制
            if len(self._histograms[key]) > 1000:
                self._histograms[key].pop(0)
    
    def increment_counter(self, name: str, value: float = 1.0, tags: Dict[str, str] = None):
        """递增计数器"""
        self.record_metric(name, MetricType.COUNTER, value, tags)
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """设置仪表盘值"""
        self.record_metric(name, MetricType.GAUGE, value, tags)
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录直方图值"""
        self.record_metric(name, MetricType.HISTOGRAM, value, tags)
    
    @contextmanager
    def timer(self, name: str, tags: Dict[str, str] = None):
        """计时器上下文管理器"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.record_metric(name, MetricType.TIMER, duration, tags)
    
    def get_metrics_summary(self, minutes: int = 60) -> Dict[str, Any]:
        """获取指标摘要"""
        cutoff_time = time.time() - (minutes * 60)
        
        with self._metrics_lock:
            recent_metrics = [m for m in self._metrics if m.timestamp > cutoff_time]
        
        summary = {
            "total_metrics": len(recent_metrics),
            "counters": dict(self._counters),
            "gauges": dict(self._gauges),
            "histograms": {}
        }
        
        # 计算直方图统计
        for key, values in self._histograms.items():
            if values:
                summary["histograms"][key] = {
                    "count": len(values),
                    "min": min(values),
                    "max": max(values),
                    "avg": sum(values) / len(values),
                    "p95": self._percentile(values, 0.95),
                    "p99": self._percentile(values, 0.99)
                }
        
        return summary
    
    def _percentile(self, values: List[float], percentile: float) -> float:
        """计算百分位数"""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile)
        return sorted_values[min(index, len(sorted_values) - 1)]


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self._checks: Dict[str, Callable[[], bool]] = {}
        self._check_results: Dict[str, Dict[str, Any]] = {}
        self._check_lock = threading.Lock()
    
    def register_check(self, name: str, check_func: Callable[[], bool]):
        """注册健康检查"""
        self._checks[name] = check_func
    
    def run_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {}
        overall_healthy = True
        
        for name, check_func in self._checks.items():
            try:
                start_time = time.time()
                is_healthy = check_func()
                duration = time.time() - start_time
                
                results[name] = {
                    "healthy": is_healthy,
                    "duration": duration,
                    "timestamp": datetime.now().isoformat()
                }
                
                if not is_healthy:
                    overall_healthy = False
                    
            except Exception as e:
                results[name] = {
                    "healthy": False,
                    "error": str(e),
                    "duration": time.time() - start_time,
                    "timestamp": datetime.now().isoformat()
                }
                overall_healthy = False
        
        with self._check_lock:
            self._check_results = results
        
        return {
            "overall_healthy": overall_healthy,
            "checks": results
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        with self._check_lock:
            return self._check_results.copy()


class DDRMonitor:
    """DDR监控器"""
    
    def __init__(self):
        self.logger = StructuredLogger("monitor")
        self.metrics = MetricsCollector()
        self.health_checker = HealthChecker()
        
        # 注册默认健康检查
        self._register_default_health_checks()
    
    def _register_default_health_checks(self):
        """注册默认健康检查"""
        
        def check_redis_connection():
            try:
                from exts import rs_client
                rs_client.ping()
                return True
            except:
                return False
        
        def check_database_connection():
            try:
                from ddr.service.optimized_db_operations import db_manager
                with db_manager.get_session(read_only=True) as session:
                    session.execute("SELECT 1")
                return True
            except:
                return False
        
        def check_queue_health():
            try:
                from ddr.service.smart_backoff import smart_backoff
                stats = smart_backoff.get_current_stats()
                # 检查队列是否过载
                return stats.get('grand_total', 0) < 50000
            except:
                return False
        
        self.health_checker.register_check("redis", check_redis_connection)
        self.health_checker.register_check("database", check_database_connection)
        self.health_checker.register_check("queues", check_queue_health)
    
    @contextmanager
    def trace_operation(self, operation_name: str, **tags):
        """操作追踪上下文管理器"""
        trace_context = TraceContext(
            trace_id=str(uuid.uuid4()),
            span_id=str(uuid.uuid4()),
            operation_name=operation_name,
            tags=tags
        )
        
        self.logger.set_trace_context(trace_context)
        
        try:
            self.logger.info(f"Starting operation: {operation_name}", **tags)
            self.metrics.increment_counter("ddr.operations.started", tags={"operation": operation_name})
            
            with self.metrics.timer("ddr.operations.duration", tags={"operation": operation_name}):
                yield trace_context
            
            self.logger.info(f"Completed operation: {operation_name}", **tags)
            self.metrics.increment_counter("ddr.operations.completed", tags={"operation": operation_name})
            
        except Exception as e:
            self.logger.error(f"Failed operation: {operation_name}", error=str(e), **tags)
            self.metrics.increment_counter("ddr.operations.failed", tags={"operation": operation_name})
            raise
        finally:
            self.logger.set_trace_context(None)
    
    def record_processing_metrics(self, task_name: str, processed: int, triggered: int, 
                                 skipped: int, duration: float, errors: int = 0):
        """记录处理指标"""
        tags = {"task": task_name}
        
        self.metrics.increment_counter("ddr.activities.processed", processed, tags)
        self.metrics.increment_counter("ddr.activities.triggered", triggered, tags)
        self.metrics.increment_counter("ddr.activities.skipped", skipped, tags)
        self.metrics.increment_counter("ddr.activities.errors", errors, tags)
        self.metrics.record_histogram("ddr.processing.duration", duration, tags)
        
        # 计算处理速率
        if duration > 0:
            throughput = processed / duration
            self.metrics.set_gauge("ddr.processing.throughput", throughput, tags)
    
    def record_cache_metrics(self, cache_type: str, hits: int, misses: int):
        """记录缓存指标"""
        tags = {"cache_type": cache_type}
        
        self.metrics.increment_counter("ddr.cache.hits", hits, tags)
        self.metrics.increment_counter("ddr.cache.misses", misses, tags)
        
        total = hits + misses
        if total > 0:
            hit_rate = hits / total
            self.metrics.set_gauge("ddr.cache.hit_rate", hit_rate, tags)
    
    def record_queue_metrics(self, queue_name: str, size: int, processing_time: float = None):
        """记录队列指标"""
        tags = {"queue": queue_name}
        
        self.metrics.set_gauge("ddr.queue.size", size, tags)
        
        if processing_time is not None:
            self.metrics.record_histogram("ddr.queue.processing_time", processing_time, tags)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        health_status = self.health_checker.run_checks()
        metrics_summary = self.metrics.get_metrics_summary()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "health": health_status,
            "metrics": metrics_summary,
            "version": "1.0.0"  # 可以从配置或环境变量获取
        }


# 全局监控器实例
ddr_monitor = DDRMonitor()


# 便捷函数
def trace_operation(operation_name: str, **tags):
    """操作追踪装饰器"""
    return ddr_monitor.trace_operation(operation_name, **tags)


def log_info(message: str, **kwargs):
    """记录信息日志"""
    ddr_monitor.logger.info(message, **kwargs)


def log_error(message: str, **kwargs):
    """记录错误日志"""
    ddr_monitor.logger.error(message, **kwargs)


def record_metric(name: str, value: float, metric_type: MetricType = MetricType.COUNTER, tags: Dict[str, str] = None):
    """记录指标"""
    ddr_monitor.metrics.record_metric(name, metric_type, value, tags)
