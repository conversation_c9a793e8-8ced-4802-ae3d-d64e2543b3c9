import uuid
import traceback
from sqlalchemy import and_, func
from exts import Session
from domain_model.data_classifier import DataClassifier
from domain_model.idm_template import get_file_fingerprint
from domain_model.edm_rule import EDMRule
from domain_model.edm_template import EDMTemplate
from ddr.model.policy import DDRPolicy
from ddr.model.task import DDRTask
from typing import List, Dict
from util.common_log import get_logger

logger = get_logger("ddr")


def get_ddr_task_ref(id:str) -> list:
    try:
        with Session() as session:
            records = session.query(DDRTask).filter(DDRTask.ddr_policy_ids.contains([id])).all()
            return [record.name for record in records]
    except Exception as e:
        logger.error(e)
        return None


def get_trigger_events_by_policy_ids(policy_ids: List[str]) -> List[str]:
    """
    depend on policy_ids search DDRPolicy, return all event_type
    Args:
        policy_ids: policy_id list
    Returns:
        return event_type list without duplicate
    """
    if not policy_ids:
        return []
    try:
        with Session() as session:
            policies = session.query(DDRPolicy).filter(DDRPolicy.id.in_(policy_ids)).all()

            unique_event_types = list(
                {policy.event_type for policy in policies if policy.event_type is not None}
            )
            return unique_event_types
    except Exception as e:
        logger.error(f"Error fetching event_types for policy_ids {policy_ids}: {e}")
        return []


def get_trigger_dict_by_policy_ids(policy_ids: List[str]) -> Dict[str, List[str]]:
    """
    depend on policy_ids search DDRPolicy, return event_type and merged trust_levels
    Args:
        policy_ids: policy_id list
    Returns:
        return dict with event_type as key and merged list of trust_levels as value
        {
            "Download File": [1, 2, 3],
            "Access File": [1]
        }
    """
    if not policy_ids:
        return {}
    try:
        with Session() as session:
            policies = session.query(DDRPolicy).filter(DDRPolicy.id.in_(policy_ids)).all()

            event_trust_mapping = {}
            for policy in policies:
                if policy.event_type is not None and policy.trust_level is not None:
                    trust_levels = policy.trust_level
                    if not isinstance(trust_levels, list):
                        trust_levels = [trust_levels] if trust_levels else []

                    if policy.event_type not in event_trust_mapping:
                        event_trust_mapping[policy.event_type] = set()

                    event_trust_mapping[policy.event_type].update(trust_levels)

            return {event: list(levels) for event, levels in event_trust_mapping.items()}
    except Exception as e:
        logger.error(f"Error fetching event_types and trust_levels for policy_ids {policy_ids}: {e}")
        return {}


def get_entity_by_policy(policy_ids:list):
    with Session() as session:
        data_classifier_ids = []
        res = session.query(DDRPolicy.data_classifier_ids).filter(DDRPolicy.id.in_(policy_ids)).all()
        for r in res:
            data_classifier_ids.extend(list(r[0]))
        classifiers = session.query(DataClassifier).filter(DataClassifier.id.in_(data_classifier_ids)).all()
        standard_entity_ids = []
        custom_entity_ids = []
        idm_mapping = dict()
        edm_ids = []
        idm_ids = []
        for classifier in classifiers:
            for _, value in classifier.match_condition.items():
                if 'entity_id' in value and value.get('type', "") == "data_type":
                    standard_entity_ids.append(value['entity_id'])
                if 'entity_id' in value and value.get('type', "") == "custom_data_type":
                    custom_entity_ids.append(value['entity_id'])
                if value.get('type', "") == "edm":
                    edm_ids.append(value['edm_id'])
                if value.get('type', "") == "idm":
                    idm_ids.append(value['idm_id'])
        
        result = session.query(
            EDMRule.match_criteria,
            EDMTemplate.data_field
        ).join(
            EDMTemplate, EDMRule.edm_template_id == EDMTemplate.id
        ).filter(
            EDMRule.id.in_(edm_ids)
        ).all()
        
        for v in result:
            match_criteria = dict(v[0])
            data_field = v[1]
            keys = match_criteria.get('primary_key', [])
            keys.extend(match_criteria.get('secondary_key', []))
            
            for column_name, field in data_field.items():
                if column_name in keys:
                    standard_entity_ids.extend(field.get("standard_datatype_ids", []))
                    custom_entity_ids.extend(field.get("custom_datatype_ids", []))

        idm_info = get_file_fingerprint(template_ids=idm_ids)
        for item in idm_info:
            idm_mapping[str(item[0])] = item[1]

    return list(set(standard_entity_ids)), list(set(custom_entity_ids)), idm_mapping