import threading
import time
from huey import <PERSON>is<PERSON>uey, PriorityRedisHuey
from util.config import configs
from util.common_log import get_logger

logger = get_logger("smart_backoff")

DEFAULT_CACHE_DURATION = configs.get("smart_backoff", {}).get("cache_duration", 0.1)
DDR_WEIGHT = configs.get("smart_backoff", {}).get("ddr", 350)
SCAN_POLICY_WEIGHT = configs.get("smart_backoff", {}).get("scan_policy", 150)
TOTAL_WEIGHT = configs.get("smart_backoff", {}).get("grand_total", 500)


class SmartBackoffManager:
    def __init__(self, cache_duration=DEFAULT_CACHE_DURATION):
        """
        Initialize the backoff manager
        Args:
            cache_duration: cache duration (seconds), default 100ms
        """
        self.cache_duration = cache_duration
        self.last_update = 0
        self.db_cache = {
            'ddr': True,
            'scan_policy': True
        }
        self.db_cache_duration = 300
        self.last_task_detect_time = 0

        self.cached_stats = {
            "ddr": 0,          # DDR total: ddr_download + analyze
            "scan_policy": 0,  # ScanPolicy total: download + analyze
        }
        self.thresholds = {
            "ddr": DDR_WEIGHT,
            "scan_policy": SCAN_POLICY_WEIGHT,
            "grand_total": TOTAL_WEIGHT
        }
        self._lock = threading.RLock()  # re-entrant lock, used to protect shared data
        self.call_count = 0             # call counter, used to dynamically adjust
        self.last_call_time = time.time()

        self.ddr_download_queue = RedisHuey("ddr_download_worker")
        self.download_queue = RedisHuey("doing-download-task")
        self.analyze_queue = PriorityRedisHuey("analyze_worker")


    def is_needed_to_back_off(self, task_uuid=None, scan_type=None):
        """
        Check if backoff is needed
        Args:
            task_uuid: task uuid
            scan_type: business type, 'ddr' or 'scan_policy', None means check total
        Returns:
            bool: True means backoff is needed, False means continue
        """
        current_time = time.time()
        self.call_count += 1

        # Dynamically adjust the cache time (adjust once every 100 calls)
        if self.call_count % 100 == 0:
            self._adjust_cache_duration()

        self._detect_enabled_tasks()

        if current_time - self.last_update <= self.cache_duration:
            if self._fast_threshold_check(scan_type):
                return True

        if task_uuid and self._check_file_count_limit(task_uuid, scan_type):
            return True

        return self._slow_path_with_lock(current_time, scan_type)


    def _detect_enabled_tasks(self):
        current_time = time.time()
        if current_time - self.last_task_detect_time < self.db_cache_duration:
            return self.db_cache
        try:
            from ddr.model.task import ddr_task_exists
            ddr_tasks = ddr_task_exists(enabled=True)
            self.db_cache['ddr'] = bool(ddr_tasks)
        except Exception:
            logger.exception("detect enabled ddr tasks failed")
        try:
            from domain_model.scan_policy import scan_policy_exists
            scan_policies = scan_policy_exists()
            self.db_cache['scan_policy'] = bool(scan_policies)
        except Exception:
            logger.exception("detect enabled scan policies failed")
        self.last_task_detect_time = current_time
        self._adjust_thresholds_based_on_tasks()
        return self.db_cache


    def _adjust_thresholds_based_on_tasks(self):
        ddr_enabled = self.db_cache["ddr"]
        sp_enabled = self.db_cache["scan_policy"]
        if ddr_enabled and sp_enabled:
            sp_weight = int(TOTAL_WEIGHT * 0.3)
            ddr_weight = int(TOTAL_WEIGHT * 0.7)
        elif ddr_enabled and not sp_enabled:
            ddr_weight = int(TOTAL_WEIGHT * 0.9)
            sp_weight = TOTAL_WEIGHT - ddr_weight
        elif not ddr_enabled and sp_enabled:
            sp_weight = int(TOTAL_WEIGHT * 0.9)
            ddr_weight = TOTAL_WEIGHT - sp_weight
        else:
            ddr_weight = TOTAL_WEIGHT // 2
            sp_weight = TOTAL_WEIGHT - ddr_weight

        self.set_thresholds(ddr=ddr_weight, scan_policy=sp_weight)

    def _check_file_count_limit(self, task_uuid, scan_type):
        from pathlib import Path
        file_count = 0
        if scan_type == "ddr":
            download_dir = Path(configs["download_queue"]["ddr_file_download_path"]) / task_uuid
            threshold = self.thresholds["ddr"]
        elif scan_type == "scan_policy":
            download_dir = Path(configs["download_queue"]["file_download_path"]) / task_uuid
            threshold = self.thresholds["scan_policy"]
        else:
            return False

        if download_dir.exists() and download_dir.is_dir():
            file_count = sum(1 for item in download_dir.glob('**/*') if item.is_file())

        return file_count > threshold


    def _fast_threshold_check(self, scan_type):
        """
        Fast threshold check without lock
        Args:
            scan_type: business type
        Returns:
            bool: whether exceeds threshold
        """
        if scan_type == "ddr":
            return self.cached_stats["ddr"] > self.thresholds["ddr"]
        elif scan_type == "scan_policy":
            return self.cached_stats["scan_policy"] > self.thresholds["scan_policy"]
        else:
            total = (self.ddr_download_queue.storage.queue_size() +
                     self.download_queue.storage.queue_size() +
                     self.analyze_queue.storage.queue_size())
            return total > self.thresholds["grand_total"]


    def _slow_path_with_lock(self, current_time, scan_type):
        """
        Slow path check with lock
        Args:
            current_time: current timestamp
            scan_type: business type
        Returns:
            bool: whether backoff is needed
        """
        with self._lock:
            if self._fast_threshold_check(scan_type):
                return True

            if current_time - self.last_update > self.cache_duration:
                # Cache expired, need to update real statistics
                self._update_real_stats()
                self.last_update = current_time
            else:
                self._increment_cached_stats(scan_type)

            return self._fast_threshold_check(scan_type)


    def _adjust_cache_duration(self):
        """
        Based on the call frequency, dynamically adjust the cache time
        The more frequently called, the shorter the cache time
        """
        current_time = time.time()
        time_span = current_time - self.last_call_time

        if time_span > 0:
            avg_interval = time_span / 100  # average call interval
            # Dynamically adjust the cache time, range from 50ms to 300ms
            new_duration = max(0.05, min(0.3, avg_interval * 2))

            if abs(new_duration - self.cache_duration) > 0.02:  # change more than 20ms to update
                self.cache_duration = new_duration

        self.last_call_time = current_time


    def _update_real_stats(self):
        ddr_download_size = self.ddr_download_queue.storage.queue_size()
        download_size = self.download_queue.storage.queue_size()
        analyze_total_size = self.analyze_queue.storage.queue_size()

        # Estimate the distribution of task types in the analysis queue
        ddr_analyze_size, analyze_size = self._estimate_analyze_task_types(analyze_total_size)

        # Summarize statistics by business type
        ddr_total = ddr_download_size + ddr_analyze_size
        scan_policy_total = download_size + analyze_size

        # Update cache statistics
        self.cached_stats.update({
            "ddr": ddr_total,
            "scan_policy": scan_policy_total
        })


    def _estimate_analyze_task_types(self, analyze_total_size):
        """
        Estimate the number of DDR and ScanPolicy tasks in the analysis queue
        """
        if analyze_total_size == 0:
            return 0, 0
        queue_key = self.analyze_queue.storage.queue_key
        all_tasks = self.analyze_queue.storage.redis.lrange(queue_key, 0, analyze_total_size - 1)
        ddr_count = 0
        for task_data in all_tasks:
            if b'ddr_analyze_worker' in task_data:
                ddr_count += 1

        return ddr_count, analyze_total_size - ddr_count


    def _increment_cached_stats(self, scan_type):
        """
        Predictive incremental update cache statistics
        """
        if scan_type == "ddr":
            self.cached_stats["ddr"] += 1
        else:
            self.cached_stats["scan_policy"] += 1


    def set_thresholds(self, ddr=None, scan_policy=None, grand_total=None):
        """
        Set the threshold for each business type
        """
        with self._lock:
            if ddr is not None:
                self.thresholds["ddr"] = ddr
            if scan_policy is not None:
                self.thresholds["scan_policy"] = scan_policy
            if grand_total is not None:
                self.thresholds["grand_total"] = grand_total


    def get_current_stats(self):
        """
        Get current statistics (thread-safe)
        Returns:
            dict: current statistics
        """
        with self._lock:
            return self.cached_stats.copy()


    def get_thresholds(self):
        """
        Get current threshold configuration
        Returns:
            dict: threshold configuration
        """
        return self.thresholds.copy()


    def reset_cache(self):
        """
        Reset cache, force next call to update statistics
        """
        with self._lock:
            self.last_update = 0


# Create a global singleton instance
smart_backoff = SmartBackoffManager()


# Example usage
if __name__ == "__main__":
    # Check if DDR business needs backoff
    if smart_backoff.is_needed_to_back_off(scan_type="ddr"):
        print("DDR business busy, need backoff")

    # Check if ScanPolicy business needs backoff
    if smart_backoff.is_needed_to_back_off(scan_type="scan_policy"):
        print("ScanPolicy business busy, need backoff")

    # Check if total needs backoff
    if smart_backoff.is_needed_to_back_off():
        print("Total queue busy, need backoff")

    # Get current statistics
    stats = smart_backoff.get_current_stats()
    print(f"Current statistics: DDR={stats['ddr']}, ScanPolicy={stats['scan_policy']}, Total={stats['grand_total']}")
