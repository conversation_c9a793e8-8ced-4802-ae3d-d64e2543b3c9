from util.enum_ import Apply<PERSON><PERSON>lMode, ConditionRelationType
from util.common_log import get_logger
from readerwriterlock import rwlock
from service.data_classifier_engine_service import DataClassifierEngine
from service.data_label_service import LabelFetcher
from ddr.model.policy import get_ddr_policy

logger = get_logger("ddr")


class DDRPolicyMatchEngine:
    def __init__(self, policy_id:str):
        policy = get_ddr_policy(id=policy_id)
        self.enabled = policy.enabled
        self.event_type = policy.event_type
        self.trust_level = policy.trust_level
        self.action = policy.action
        self.risk = policy.risk
        self.update_time = policy.updated_at
        self.id = policy.id
        self.name = policy.name
        self.notes = policy.description
        self.notification_id = policy.notification_id
        self.match_condition_relation = policy.match_condition_relation
        self.data_classifier_ids = list(policy.data_classifier_ids)

    def evaluate_logic(self, expression, file_info):
        stack = []
        tokens = expression.split()

        for token in tokens:
            if token == 'AND':
                a = stack.pop()
                b = stack.pop()
                stack.append(a and b)
            elif token == 'OR':
                a = stack.pop()
                b = stack.pop()
                stack.append(a or b)
            elif token == 'NOT':
                a = stack.pop()
                stack.append(not a)
            else:
                logger.error(f"EvaluateLogic condition id: {token} can not find.")
        return stack.pop()


    def infix_to_postfix(self, expression):
        output = []
        operators = []
        tokens = expression.split()

        # lower number means lower precedence
        precedence = {
            'NOT': 3,
            'AND': 2,
            'OR': 1
        }
        associativity = {
            'NOT': 'Right',
            'AND': 'Left',
            'OR': 'Left'
        }

        for token in tokens:
            #print("token:", token)
            if token in precedence:
                while (operators and operators[-1] != '(' and
                    (precedence[operators[-1]] > precedence[token] or
                    (precedence[operators[-1]] == precedence[token] and associativity[token] == 'Left'))):
                    output.append(operators.pop())
                operators.append(token)
            elif token == '(':
                operators.append(token)
            elif token == ')':
                while operators and operators[-1] != '(':
                    output.append(operators.pop())
                operators.pop()  # Pop '('
            else:
                # Assuming token is a variable or literal
                output.append(token)

        # Pop all the operators left in the stack
        while operators:
            output.append(operators.pop())

        return ' '.join(output)


    def match_condition(self, file_info:dict):
        if not self.match_condition_relation:
            return False

        if self.match_condition_relation['type'] == ConditionRelationType.ALL.value:
            for condition in self.conditions.values():
                if not condition.match(file_info):
                    return False
            return True
        elif self.match_condition_relation['type'] == ConditionRelationType.ONE.value:
            for condition in self.conditions.values():
                if condition.match(file_info):
                    return True
            return False
        elif self.match_condition_relation['type'] == ConditionRelationType.CUSTOM.value:
            custom_relations = self.match_condition_relation['custom_relations']
            custom_relations_postfix = self.infix_to_postfix(custom_relations)
            result = self.evaluate_logic(custom_relations_postfix, file_info)
            # logger.debug(f'custom_relations: {custom_relations}, custom_relations_postfix: {custom_relations_postfix}, result: {result}')
            return result
        else:
            logger.error(f"ConditionRelationType {self.match_condition_relation['type']} does not support.")
            return False

    def match_operation(self, operation:dict):
        if not operation:
            return False
        if operation["event_type"] != self.event_type:
            return False
        if operation["trust_level"] not in self.trust_level:
            return False
        return True

    def match(self, record):
        """
        Matches the record against the policy's data_classifier_engines and returns the match result.
        
        Args:
            record (dict): The record to match against the policy.
        Returns:
            tuple: A tuple containing:
                - bool: True if the record matches the policy, False otherwise. 
                - int: The sensitivity level of the matched record, or -1 if no match.
                - str: The name of the data classifier engine that matched the record, or None if no match.
        """
        matched_sensitivity = -1
        matched_result = False
        matched_classifier_name = None

        if not self.match_operation(record.get("operation", {})):
            return matched_result, matched_sensitivity, matched_classifier_name

        for classifier_id in self.data_classifier_ids:
            if classifier_id in record.get("classifier", {}):
                classifier_info = record.get("classifier", {}).get(classifier_id, {})
                matched_result = True
                if classifier_info.get("sensitivity", -1) > matched_sensitivity:
                    matched_sensitivity = classifier_info.get("sensitivity", -1)
                    matched_classifier_name = classifier_info.get("name", "")

        if not matched_result:
            return matched_result, matched_sensitivity, matched_classifier_name

        if not self.match_condition(record.get("file_info", {})):
            return matched_result, matched_sensitivity, matched_classifier_name

        return matched_result, matched_sensitivity, matched_classifier_name


    def update_needed(self) -> bool:
        policy = get_ddr_policy(id=self.id)
        if policy.enabled and policy.updated_at > self.update_time:
            return True
        return False

class DDRPolicyEngine:
    def __init__(self):
        self.policies = dict()
        self._lock = rwlock.RWLockFairD()
        self._lock_reader = self._lock.gen_rlock()
        self._lock_writer = self._lock.gen_wlock()

    def get_ids(self) -> list:
        self._lock_reader.acquire()
        try:
            return self.policies.keys()
        finally:
            self._lock_reader.release()

    def update_needed(self, discovery_ids:list):
        if set(self.get_ids()) != set(discovery_ids):
            return True

        self._lock_reader.acquire()
        try:
            for _, policy in self.policies.items():
                if policy.update_needed():
                    return True
            return False
        finally:
            self._lock_reader.release()

    def create_policy(self, id):
        self._lock_writer.acquire()
        try:
            self.policies[id] = DDRPolicyMatchEngine(policy_id=id)
        finally:
            self._lock_writer.release()

    def remove_policy(self, id):
        self._lock_writer.acquire()
        try:
            if id in self.policies.keys():
                del self.policies[id]
        finally:
            self._lock_writer.release()

    def match_all(self, record:dict):
        self._lock_writer.acquire()

        match_result = {"policies":[]}
        try:
            remediation_actions = set()
            sensitivity_level = -1
            for id, policy in self.policies.items():
                if not policy.enabled:
                    continue
                matched, sensitivity, data_classifier_name = policy.match(record)
                if matched:
                    sensitivity_label_mode = policy.action.get("label", {}).get("sensitivity_label_mode", 0)
                    if sensitivity_label_mode == ApplyLabelMode.ENABLE.value:
                        sensitivity_level = max(sensitivity, sensitivity_level)
                    action_list = []
                    for k, v in policy.action.get("remediation", {}).items():
                        if v:
                            action_list.append(k)
                            remediation_actions.add(k)
                    match_result["policies"].append({
                        "pid": id,
                        "pname": policy.name,
                        "pnotes": policy.notes,
                        "risk": policy.risk,
                        "action": action_list,
                        'notification_id': policy.notification_id,
                        "data_classifier_name": data_classifier_name,
                        "sensitivity": sensitivity if sensitivity != -1 else None,
                        "sensitivity_label_mode": policy.action.get("label", {}).get("sensitivity_label_mode", 0)
                    })
            match_result['remediation_actions'] = list(remediation_actions)
            match_result["sensitivity"] = sensitivity_level
            match_result["protection_framework"] = []
            return match_result
        finally:
            self._lock_writer.release()


def ddr_create_policy_engine(policy_ids) -> DDRPolicyEngine:
    ddr_engine = DDRPolicyEngine()
    for policy_id in policy_ids:
        try:
            ddr_engine.create_policy(policy_id)
            logger.info(f"Created policy engine for policy id: {policy_id}")
        except Exception as e:
            logger.error(f"Error processing policy id {policy_id}: {e}")
    return ddr_engine
