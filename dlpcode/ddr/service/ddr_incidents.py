import json
from exts import Session, Base
from sqlalchemy import Column, String, Integer, DateTime, text, desc, exists, select, literal_column, cast
from sqlalchemy.sql import func, exists, and_, update, select
from sqlalchemy import or_, bindparam
from psycopg2.errors import UniqueViolation
from ddr.model.incidents import DDRIncident
from service.log_server_service import get_log_servers_by_conditions
from service.incidents_common import ICDStatus
from util.common_log import get_logger

logger = get_logger("ddr")


def create_ddr_incident(attr=None, attr_ext=None, pol_details=None, data_source=None, file_info=None, activity=None, logger=logger):
    session = Session()
    try:
        incident = DDRIncident(
            attributes=attr or {},
            attributes_ext=attr_ext or {},
            pol_details=pol_details or {},
            data_source=data_source or {},
            file_info=file_info or {},
            activity=activity or {}
        )
        session.add(incident)
        session.commit()
        return incident.to_dict()
    except Exception as e:
        logger.error(e)
    finally:
        session.close()

def update_incident_column_field(ids, column, key, value, logger=logger):
    if not ids:
        logger.info(f"No ds incident ids provided for updating {column}.{key}")
        return 0

    try:
        session = Session()
        
        jsonb_column = getattr(DDRIncident, column)
        
        current_value_stmt = (
            select(DDRIncident.id, jsonb_column)
            .where(DDRIncident.id.in_(ids))
        )
        current_rows = session.execute(current_value_stmt).fetchall()

        ids_to_update = []
        for row in current_rows:
            row_id, current_jsonb = row
            if current_jsonb is None or current_jsonb.get(key) != value:
                ids_to_update.append(row_id)

        if not ids_to_update:
            return []

        stmt = (
            update(DDRIncident)
            .where(DDRIncident.id.in_(ids))
            .values({column: func.jsonb_set(jsonb_column, text("ARRAY[:key]"), func.to_jsonb(bindparam("val")))})
        )

        session.execute(stmt, {"key": key, "val": value})
        session.commit()
            
        return ids_to_update
    except Exception as e:
        logger.error(e)
    finally:
        session.close()


def update_ddr_incident_status(ids, value, logger=logger):
    return update_incident_column_field(ids, 'attributes', 'status', value, logger=logger)

def update_ddr_incident_scan_status(ids, value, logger=logger):
    return update_incident_column_field(ids, 'attributes_ext', 'scan_status', value, logger=logger)

def update_ddr_incident_severity(ids, value, logger=logger):
    return update_incident_column_field(ids, 'attributes', 'severity', value, logger=logger)

def update_ddr_incident_ignored(ids, value, logger=logger):
    updated_ids = update_incident_column_field(ids, 'attributes', 'ignored', value, logger=logger)
    if value:
        return update_incident_column_field(updated_ids, 'attributes', 'status', ICDStatus.CLOSED.value, logger=logger)
    else:
        return update_incident_column_field(updated_ids, 'attributes', 'status', ICDStatus.NEW.value, logger=logger)

def update_ddr_incident_false_positive(ids, value, logger=logger):
    updated_ids = update_incident_column_field(ids, 'attributes', 'false_positive', value, logger=logger)
    if value:
        return update_incident_column_field(updated_ids, 'attributes', 'status', ICDStatus.CLOSED.value, logger=logger)
    else:
        return update_incident_column_field(updated_ids, 'attributes', 'status', ICDStatus.NEW.value, logger=logger)

def update_ddr_incident_comments(ids, value, logger=logger):
    return update_incident_column_field(ids, 'attributes', 'comments', value, logger=logger)

def delete_ddr_incidents_by_ids(ids, logger=logger):
    if not ids:
        logger.info("No ds incident ids provided for deletion")
        return 0
    
    with Session() as session:
        try:
            affected_rows = session.query(DDRIncident).filter(DDRIncident.id.in_(ids)).delete()
            # commit the transaction
            session.commit()
            return affected_rows
        except Exception as e:
            logger.error(e)
            session.rollback()
            return 0
        finally:
            session.close()

# method: 
#     like:   pattern matching
#     ilike:  pattern matching, Case-insensitive
#     equal:  
#     iequal: Case-insensitive
#     gte: greater than or equal
#     lte, less than or equal
condition_fields = {
    "id":          None,  # custom match method
    "start_ctime":  {'method': "gte", "type": "int", "field":"ctime"},
    "end_ctime":    {'method': "lte", "type": "int", "field":"ctime"},
    
    # attributes
    "severity":     {'method': "equal", "type": "int", "field":"attributes.severity"},
    "status":       {'method': "equal", "type": "int", "field":"attributes.status"},
    "action":       {'method': "equal", "type": "int", "field":"attributes.action"},
    "ignored":      {'method': "equal", "type": "str", "field":"attributes.ignored"},
    "message":      {'method': "ilike", "type": "str", "field":"attributes.message"},
    "comments":     {'method': "ilike", "type": "str", "field":"attributes.comments"},
    "ignored":      {'method': "equal", "type": "bool", "field":"attributes.ignored"},
    "false_positive":{'method': "equal", "type": "bool", "field":"attributes.false_positive"},
    
    # data source
    "data_source_type":    {'method': "equal", "type": "str", "field":"data_source.data_source_type"}, 
    
    # file owner
    "fid":          {'method': "ilike", "type": "str", "field":"file_info.id"},
    "file_name":    {'method': "ilike", "type": "str", "field":"file_info.name"},
    "file_path":    {'method': "ilike", "type": "str", "field":"file_info.full_path"},
    "ftype":        {'method': "ilike", "type": "str", "field":"file_info.type"},
    "file_hash":    {'method': "ilike", "type": "str", "field": "file_info.hash"},
    
    #pol_details
    "sid":          {'method': "equal", "type": "str", "field":"pol_details.sid"},
    "pid":          {'method': "equal", "type": "str", "field":"pol_details.pid"},
    "rid":          {'method': "equal", "type": "str", "field":"pol_details.rid"},
    "sname":        {'method': "ilike", "type": "str", "field":"pol_details.sname"},
    "pname":        {'method': "ilike", "type": "str", "field":"pol_details.pname"},
    "rname":        {'method': "ilike", "type": "str", "field":"pol_details.rname"},

    # activity
    "actor":      {'method': "ilike", "type": "str", "field":"activity.actor"},
    "actor_ip":   {'method': "ilike", "type": "str", "field":"activity.actor_ip"},
    "operator":   {'method': "ilike", "type": "str", "field":"activity.operator"},

    # idm/edm
    "idm_matched": {"field": "idm_match_info"},
    "edm_matched": {"field": "edm_match_info"}
}

allow_sort_fields = {
    "ctime":    {"type": "str"},
    "utime":    {"type": "str"},
    "severity": {"type": "int", "field": "attributes.severity"},
    "status":   {"type": "int", "field": "attributes.status"}
}

def get_ddr_incident_scan_info(logger=logger):
    from ddr.model.task import get_ddr_tasks
    from storage.service.profiles import get_storage_profile_by_id
    try:
        result = {}
        existing_scans = get_ddr_tasks()
        for scan in existing_scans:
            sid = str(scan.id)
            storage = get_storage_profile_by_id(scan.scan_init_info['storage_id'])
            result[sid] = {
                'scan_name': scan.name,
                'created_at': scan.created_at.timestamp(),
                'exist': True,
                'storage_type': scan.storage_type,
                'storage_name': storage.get("name") if storage else None,
                'storage_id': scan.scan_init_info['storage_id'],
                'description': scan.description
            }

        session = Session()
        data = session.query(DDRIncident.pol_details, DDRIncident.attributes_ext, DDRIncident.data_source).distinct().all()
        # get deleted scans
        for d in data:
            pol_details = d[0]
            attributes_ext = d[1]
            data_source = d[2]
            sid = pol_details.get("sid")
            if sid in result:
                continue
            result[sid] = {
                "scan_name": pol_details.get("sname", ""), 
                "created_at": pol_details.get("sctime", 0), 
                "exist": attributes_ext.get("scan_status", False),
                "storage_type": pol_details.get("stype", ""),
                "storage_name": data_source.get("storage_name"),
                "storage_id": "",
                "description": pol_details.get("snotes", "")
            }

        return result

    except Exception as e:
        logger.error(e)
        return None
    finally:
        session.close()


def get_ddr_incidents_by_conditions(conditions, subquery_limit = -1, logger=logger):
    """
    Query DDRIncident records with flexible filters.

    Supported conditions, please refer to condition:
      - ids
      - start_ctime, end_ctime
      - severity, status, ignored, action, message, comments
      - sname, pname, rname
      - dsrc_type
      - fid, file_name, file_path, ftype
      - activity
      - sid, pid, rid
    """
    try:
        session = Session()
        subquery = None
        licensed = is_licensed()
        TableClass = DDRIncident  # default when licensed

        if not licensed:
            limit_n = configs.get('no_license_limitation', {}).get('max_scan_incidents', 1000)

            base_q = session.query(DDRIncident)
            sid = conditions.get("sid")
            if sid:
                base_q = base_q.filter(DDRIncident.pol_details['sid'].astext == sid)

            latest_sq = (
                base_q.order_by(DDRIncident.ctime.desc(), DDRIncident.id.desc())
                      .limit(limit_n)
                      .subquery("latest_incidents")
            )
            TableClass = aliased(DSIncident, latest_sq)

        params = []
        for key, value in conditions.items():
            if key not in condition_fields or value is None: # skip invalid entry 
                continue
            
            val_dict = condition_fields[key]
            if key == "id":
                if value:
                    if isinstance(value, list):
                        params.append(TableClass.id.in_(value))
                    else:
                        params.append(TableClass.id == value)
            elif key == "activity":
                jsonb_keys = {"operator", "actor", "actor_ip"}
                subconditions = []
                for jsonb_key in jsonb_keys:
                    subconditions.append(getattr(TableClass, "activity")[jsonb_key].op("@>")([value]))
                    
                params.append(or_(*subconditions))
            elif key in ("idm_matched", "edm_matched"):
                attributes_ext_column = getattr(TableClass, "attributes_ext")
                field_name = val_dict["field"]
                nested_column = attributes_ext_column[field_name]

                if value is True:
                    params.append(
                        exists(
                            select(literal_column("1")).select_from(
                                func.jsonb_each_text(nested_column)
                            )
                        )
                    )
                elif value is False:
                    params.append(
                        ~exists(
                            select(literal_column("1")).select_from(
                                func.jsonb_each_text(nested_column)
                            )
                        )
                    )

            elif val_dict:
                _field = val_dict.get("field", None)
                if _field and "." in _field:
                    jsonb_column, *json_keys = _field.split(".")
                    jsonb_path = tuple(json_keys)
                    if val_dict["type"] == 'str':
                        column = func.jsonb_extract_path_text(getattr(TableClass, jsonb_column), *jsonb_path)
                    elif val_dict["type"] == 'int':
                        column = func.jsonb_extract_path_text(getattr(TableClass, jsonb_column), *jsonb_path).cast(Integer)
                    elif val_dict["type"] == 'bool':
                        column = func.jsonb_extract_path_text(getattr(TableClass, jsonb_column), *jsonb_path) == 'true'
                    else:
                        logger.error(f"wrong query condition defined {key}, unsupported condition type {val_dict['type']}")
                        continue
                else:
                    column = getattr(TableClass, _field)
                    
                method = val_dict["method"]
                if method == "like":
                        value = f"%{value}%"
                        params.append(column.like(value))
                elif method == "ilike":
                        value = f"%{value}%"
                        params.append(column.ilike(value))
                elif method == "equal":
                    if isinstance(value, list):
                        params.append(column.in_(value))
                    else:
                        params.append(column == value)
                elif method == "iequal":
                    if val_dict["type"] == "str":
                        params.append(column.ilike(value))
                elif method == "gte":
                    params.append(column>=func.to_timestamp(value))
                elif method == "lte":
                    params.append(column<=func.to_timestamp(value))
                else:
                    logger.error(f"invalid match method {val_dict['method']}")
            else:
                logger.error(f"wrong query condition defined {key}")
            
        sort_field = conditions.get('sort_field') if conditions.get('sort_field') is not None  else 'ctime'
        sort_method = conditions.get('sort_method') if conditions.get('sort_method') is not None  else 'desc'
            
        if sort_field not in allow_sort_fields:
            logger.error(f"Sort field '{sort_field}' not in the allow list, so uses the default sort field 'ctime'")
            column = getattr(TableClass, 'ctime')
        else:
            val_dict = allow_sort_fields[sort_field]
            _field = val_dict.get("field")
            if _field and "." in _field:
                jsonb_column, *json_keys = _field.split(".")
                jsonb_path = tuple(json_keys)
                if val_dict["type"] == 'int':
                    column = func.jsonb_extract_path_text(getattr(TableClass, jsonb_column), *jsonb_path).cast(Integer)
                else:
                    column = func.jsonb_extract_path_text(getattr(TableClass, jsonb_column), *jsonb_path)
            else:
                column = getattr(TableClass, sort_field) 

        query = session.query(TableClass)
        if sort_method == 'desc':
            results = query.filter(*params).order_by(column.desc())
        else:
            results = query.filter(*params).order_by(column.asc())
        
        total = results.count()
        page = conditions.get('page')
        per_page = conditions.get('per_page', 10)
 
        if page is not None:
            page = int(page)
            per_page = int(per_page) if per_page is not None else 10
            slice_from = per_page * (page - 1)
            slice_to = min(per_page * page, total)
            if slice_from > slice_to:
                return [], total
            results = results.slice(slice_from, slice_to)

        incidents = results.all()
        if incidents:
            return [record.to_dict() for record in incidents], total
        else:
            return [], total
    except Exception as e:
        logger.error(e)
    finally:
        session.close()


def get_policy_counts_by_conditions(conditions, logger):
    session = Session()
    try:
        from system.dlp_license import is_licensed
        if is_licensed():
            src = DDRIncident
            pol_col = DDRIncident.pol_details
            attr_col = DDRIncident.attributes
            ctime_col = DDRIncident.ctime
        else:
            latest_1000 = (
                session.query(
                    DDRIncident.pol_details.label("pol_details"),
                    DDRIncident.attributes.label("attributes"),
                    DDRIncident.ctime.label("ctime"),
                    DDRIncident.id.label("id"),
                )
                .order_by(DDRIncident.ctime.desc(), DDRIncident.id.desc())
                .limit(1000)
                .subquery("latest_incidents")
            )
            src = latest_1000
            pol_col = latest_1000.c.pol_details
            attr_col = latest_1000.c.attributes
            ctime_col = latest_1000.c.ctime

        pid_expr    = pol_col['pid'].astext
        pname_expr  = pol_col['pname'].astext
        pnotes_expr = func.coalesce(pol_col['pnotes'].astext, '')
        sid_expr    = pol_col['sid'].astext

        risk_expr   = cast(attr_col['risk'].astext, Integer)

        count_expr  = func.count(literal_column("1"))
        pnotes_max  = func.max(pnotes_expr)
        risk_min    = func.min(risk_expr)

        q = (
            session.query(
                pid_expr.label('pid'),
                pname_expr.label('pname'),
                pnotes_max.label('pnotes'),
                risk_min.label('risk'),   # min risk per policy
                count_expr.label('count')
            )
            .select_from(src)
        )

        # WHERE filters
        if (pid_in := conditions.get('pid_in')):
            q = q.filter(pid_expr.in_(pid_in))

        if (sid_in := conditions.get('sid_in')):
            q = q.filter(sid_expr.in_(sid_in))

        if (pname_like := conditions.get('pname_like')):
            q = q.filter(pname_expr.ilike(f"%{pname_like}%"))

        if (pnotes_like := conditions.get('pnotes_like')):
            q = q.filter(pnotes_expr.ilike(f"%{pnotes_like}%"))

        if (ctime_from := conditions.get('ctime_from')):
            q = q.filter(ctime_col >= ctime_from)

        if (ctime_to := conditions.get('ctime_to')):
            q = q.filter(ctime_col <  ctime_to)

        # GROUP BY
        q = q.group_by(pid_expr, pname_expr)

        # HAVING on aggregates
        if (mr := conditions.get('min_risk')) is not None:
            q = q.having(risk_min >= int(mr))
        if (xr := conditions.get('max_risk')) is not None:
            q = q.having(risk_min <= int(xr))

        if (mc := conditions.get('min_count')) is not None:
            q = q.having(count_expr >= int(mc))
        if (xc := conditions.get('max_count')) is not None:
            q = q.having(count_expr <= int(xc))


        # Sorting
        sort_field  = conditions.get('sort_field', 'count')
        sort_method = conditions.get('sort_method', 'desc')

        order_cols = {
            "count" : count_expr,
            "pname" : pname_expr,
            "pid"   : pid_expr,
            "pnotes": pnotes_max,
            "risk"  : risk_min,             # allow sorting by min risk
        }
        order_col = order_cols.get(sort_field, count_expr)
        q = q.order_by(order_col.desc() if sort_method == 'desc' else order_col.asc())

        # Pagination: if no page, return all results, if no per_page, return 10 per page
        total    = q.count()
        page     = conditions.get('page')
        if page is not None:
            per_page = int(conditions.get('per_page', 10))
            q = q.offset(per_page * (page - 1)).limit(per_page)

        rows = q.all()

        return {
            "list": [
                {
                    "pid": r.pid,
                    "pname": r.pname,
                    "pnotes": r.pnotes or "",
                    "risk": (int(r.risk) if r.risk is not None else 2),  # default risk if all NULL
                    "count": int(r.count),
                }
                for r in rows
            ],
            "total": total,
            "page": page,
            "per_page": per_page
        }
    finally:
        session.close()


def build_incident_email_payload(incident: dict) -> dict:
    """
    Build the payload to send to send_email_by_template for an incident notification.
    Returns a dict: {...}
    """
    attrs = incident.get("attributes", {})
    attr_ext = incident.get("attributes_ext", {})
    pol_details = incident.get("pol_details", {})
    file_info = incident.get("file_info", {})
    activity = incident.get("activity", {})
    
    actions = attrs.get("action", "None")
    if isinstance(actions, list):
        actions = ", ".join(str(a) for a in actions)
    

    # Email notification variables for the "incident" template
    payload = {
        "policy_name": pol_details.get("pname", "Unknown"),
        "policy_id": pol_details.get("pid"),
        "ctime": incident.get("ctime"),
        "incident_time": incident.get("ctime"),
        "incident_id": incident.get("id", ""),
        "scan_id": pol_details.get("sid", ""),
        "classifier_name": pol_details.get("data_classifier_name", ""),
        "detected_at": pol_details.get("sctime", ""),
        "severity": attrs.get("risk", "Unknown"),
        "file_name": file_info.get("name", ""),
        "file_path": file_info.get("full_path", ""),
        "user": file_info.get("collaboration", "Unknown"),
        "activity": activity,
        "description": pol_details.get("snotes", ""),
        "actions": actions,
    }
    return payload

from service.incidents_common import (
    ICDStatus,
    DATA_SOURCE_TYPE_MAPPING,
    DATA_SOURCE_INFO_CRITERIA,
    INCIDENT_RECORDER,
    get_severity_level
)

def build_ddr_incident_payload(incident: dict) -> str:
    attrs = incident.get("attributes", {})
    attr_ext = incident.get("attributes_ext", {})
    remediation_info = attr_ext.get("action_details", {})
    pol_details = incident.get("pol_details", {})
    file_info = incident.get("file_info", {})
    data_source = incident.get("data_source", {})
    activity = incident.get("activity", {})

    payload = {
        "incident_id": incident.get("id"),
        "incident_time": incident.get("ctime"),
        "incident_type": "Data Scanning Incident",
        "incident_risk": attrs.get("risk"),
        "incident_status": ICDStatus(attrs.get("status")).name,
        "assigned_to": attrs.get("assigned_to"),
        "escalate_to": attrs.get("escalate_to"),
        "message": attrs.get("message"),
        "policy_details": {
            "storage_type": data_source.get("data_source_type"),
            "storage_name": data_source.get("storage_name"),
            "scan_name": pol_details.get("sname"),
            "policy_name": pol_details.get("pname"),
            "policy_notes": pol_details.get("pnotes", ""),
            "data_classifier_name": pol_details.get("data_classifier_name", "")
        },
        "action": {
            "copy_target": remediation_info.get("copy_targets"),
            "quarantine_target": remediation_info.get("quarantine_targets"),
            "remove_public_link": remediation_info.get("remove_public_link") or False,
            "remove_external_link": remediation_info.get("remove_external_link") or False,
        },
        "activity": {
            "operator": activity.get("event_type"),
            "actor": activity.get("actor_email"),
            "actor_ip": activity.get("ip"),
        }
    }
    if "Excessive" in activity.get("event_type"):
        payload["file_information"] = {}
    else:
        payload["file_information"] = {
            "file_id": file_info.get("id"),
            "file_hash": file_info.get("hash"),
            "file_name": file_info.get("name"),
            "file_full_path": file_info.get("full_path"),
            "file_type": file_info.get("type"),
            "file_size": file_info.get("size"),
            # "file_owner": file_info.get("owner"),
            "collaboration": file_info.get("collaboration"),
            "file_last_modified": file_info.get("last_modified"),
            "sensitivity": attrs.get("sensitivity"),
        }
    return json.dumps(payload)


def record_ddr_incident(file_uuid, file_info, remediation_info, scan_info, remote_info, 
                       mpolicy, edm_match_info, idm_match_info, logger=logger):
    try:
        attr = {
            "risk": get_severity_level(mpolicy["risk"]).name,
            "sensitivity": mpolicy["sensitivity"],
            "status": ICDStatus.NEW.value,
            "ignored": False,
            "assigned_to": None,
            "escalate_to": None,
            "action": mpolicy["action"],
            "message": "",
            "comments": "",
            "false_positive": False
        }
        attr_ext = {
            "scan_status": True,
            "action_details": remediation_info,
            "edm_match_info": edm_match_info,
            "idm_match_info": idm_match_info
        }
        pol_details = {
            "pid": mpolicy["pid"],
            "pname": mpolicy["pname"],
            "pnotes": mpolicy.get("pnotes", ""),
            "sid": scan_info['scan_uuid'],
            "sname": scan_info['scan_name'],
            "sctime": scan_info['scan_created_at'],
            "snotes": scan_info['scan_description'],
            "stype": remote_info["storage_type"],
            "starget": scan_info["target"],
            "data_classifier_name": mpolicy.get("data_classifier_name", ""),
        }
        data_source = {
            "data_source_type": DATA_SOURCE_TYPE_MAPPING.get(remote_info["storage_type"], ""),
            "storage_name": remote_info.get("storage_name", ""),
            "data_source_info": {k: v for k, v in remote_info.items() if k in DATA_SOURCE_INFO_CRITERIA.get(remote_info["storage_type"], [])}
        }
        event_type = remote_info.get("event_type", "")
        if "Excessive" in event_type:
            fileinfo = {}
        else:
            fileinfo = {
                "id": file_uuid,
                "name": remote_info.get('file_name'),
                "hash": remote_info.get("file_hash"),
                "full_path": remote_info.get('file_display_path', "N/A") \
                    if remote_info.get('file_display_path', "N/A") != "N/A"\
                    else remote_info.get('file', "N/A"),
                "type": file_info['file_type'],
                "size": remote_info.get("file_size"),
                "last_modified": remote_info.get("last_modified"),
                "collaboration": remote_info.get("collaborator"),
            }
        activity = {
            "actor": remote_info.get("actor"),
            "actor_ip": remote_info.get("actor_ip"),
            "operator": event_type
        }

        # write incident into database
        incident = create_ddr_incident(attr=attr, 
                           attr_ext=attr_ext,
                           pol_details=pol_details,
                           data_source=data_source,
                           file_info=fileinfo,
                           activity=activity,
                           logger=logger)
        if incident is None:
            raise ValueError("Create data scanning incident failed")

        send_ddr_incident(incident=incident)

        notification_ids = mpolicy.get('notification_id')
        if notification_ids and len(notification_ids) > 0:
            emaiL_payload = build_incident_email_payload(incident)
            from notifications.sdk.email_client import send_email_by_template
            for id in notification_ids:
                send_email_by_template(id, emaiL_payload)
    except Exception as e:
        logger.error(e)
        return None


def send_ddr_incident(incident:dict, logger=logger):
    log_servers, _ = get_log_servers_by_conditions(conditions={"status":True})
    if len(log_servers) == 0:
        return
    try: 
        payload = build_ddr_incident_payload(incident)
        INCIDENT_RECORDER.log_ddr_incident(payload, get_severity_level(incident["attributes"]["risk"]))
    except Exception as e:
        logger.error(e)


def delete_ddr_incident_by_time(timestamp_to_delete):
    try:
        session = Session()
        session.query(DDRIncident).filter(
            DDRIncident.ctime < func.to_timestamp(timestamp_to_delete)
        ).delete(synchronize_session=False)
        session.commit()
    except Exception as e:
        logger.error(e)
    finally:
        session.close()
