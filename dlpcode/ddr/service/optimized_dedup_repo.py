"""
优化的去重缓存实现
解决原有实现的性能问题：
1. 分片存储避免热点
2. 使用Redis原生命令提高性能
3. 优化清理机制
4. 减少内存占用
"""

import time
import json
import hashlib
from typing import List, Optional, Dict, Any
from exts import rs_client, get_logger

logger = get_logger("ddr")


class OptimizedDedupRepo:
    SHARD_COUNT = 16
    KEY_PREFIX = "ddr:dedup:shard"
    CLAIM_PREFIX = "ddr:claim"
    GROUP_PREFIX = "ddr:group"
    
    # 默认TTL
    DEFAULT_TTL = 3600  # 1小时
    
    @classmethod
    def _get_shard_id(cls, dedup_key: str) -> int:
        """根据去重键计算分片ID"""
        return hash(dedup_key) % cls.SHARD_COUNT
    
    @classmethod
    def _get_shard_key(cls, dedup_key: str) -> str:
        """获取分片键名"""
        shard_id = cls._get_shard_id(dedup_key)
        return f"{cls.KEY_PREFIX}:{shard_id}"
    
    @classmethod
    def _get_claim_key(cls, dedup_key: str) -> str:
        """获取声明键名"""
        return f"{cls.CLAIM_PREFIX}:{dedup_key}"
    
    @classmethod
    def _get_group_key(cls, dedup_key: str) -> str:
        """获取组键名"""
        return f"{cls.GROUP_PREFIX}:{dedup_key}"
    
    @classmethod
    def exists(cls, dedup_key: str) -> bool:
        """检查去重键是否存在"""
        try:
            claim_key = cls._get_claim_key(dedup_key)
            return rs_client.exists(claim_key) == 1
        except Exception as e:
            logger.error(f"Failed to check dedup key existence {dedup_key}: {e}")
            return True  # 保守返回True避免重复处理
    
    @classmethod
    def try_claim(cls, dedup_key: str, ttl_sec: int = None) -> bool:
        """
        尝试声明去重键
        使用Redis SET NX EX命令，原子操作，性能更好
        """
        if ttl_sec is None:
            ttl_sec = cls.DEFAULT_TTL
            
        try:
            claim_key = cls._get_claim_key(dedup_key)
            result = rs_client.set(claim_key, "1", nx=True, ex=ttl_sec)
            
            if result:
                logger.debug(f"Successfully claimed dedup key: {dedup_key}")
                return True
            else:
                logger.debug(f"Dedup key already claimed: {dedup_key}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to claim dedup key {dedup_key}: {e}")
            return False
    
    @classmethod
    def add_member(cls, dedup_key: str, activity_id: str, ttl_sec: int = None) -> bool:
        """
        添加成员到去重组
        使用Redis SADD + EXPIRE命令
        """
        if ttl_sec is None:
            ttl_sec = cls.DEFAULT_TTL
            
        try:
            group_key = cls._get_group_key(dedup_key)
            
            # 使用pipeline提高性能
            pipe = rs_client.pipeline()
            pipe.sadd(group_key, str(activity_id))
            pipe.expire(group_key, ttl_sec)
            pipe.execute()
            
            logger.debug(f"Added member {activity_id} to dedup group {dedup_key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add member to dedup group {dedup_key}: {e}")
            return False
    
    @classmethod
    def pop_members(cls, dedup_key: str, keep_in_redis: bool = False) -> List[str]:
        """
        弹出去重组的所有成员
        """
        try:
            group_key = cls._get_group_key(dedup_key)
            
            # 获取所有成员
            members = rs_client.smembers(group_key) or set()
            member_list = [m.decode() if isinstance(m, bytes) else str(m) for m in members]
            
            # 如果不保留，则删除组
            if not keep_in_redis and members:
                rs_client.delete(group_key)
                logger.debug(f"Deleted dedup group {dedup_key}")
            
            return member_list
            
        except Exception as e:
            logger.error(f"Failed to pop members from dedup group {dedup_key}: {e}")
            return []
    
    @classmethod
    def get_members(cls, dedup_key: str) -> List[str]:
        """获取去重组成员（不删除）"""
        try:
            group_key = cls._get_group_key(dedup_key)
            members = rs_client.smembers(group_key) or set()
            return [m.decode() if isinstance(m, bytes) else str(m) for m in members]
        except Exception as e:
            logger.error(f"Failed to get members from dedup group {dedup_key}: {e}")
            return []
    
    @classmethod
    def cleanup_expired(cls) -> int:
        """
        清理过期的去重键
        优化：使用SCAN命令避免阻塞，分批处理
        """
        deleted_count = 0
        
        try:
            # 清理声明键
            claim_pattern = f"{cls.CLAIM_PREFIX}:*"
            deleted_count += cls._cleanup_keys_by_pattern(claim_pattern)
            
            # 清理组键
            group_pattern = f"{cls.GROUP_PREFIX}:*"
            deleted_count += cls._cleanup_keys_by_pattern(group_pattern)
            
            logger.info(f"Cleanup expired dedup keys: {deleted_count} deleted")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired dedup keys: {e}")
            return 0
    
    @classmethod
    def _cleanup_keys_by_pattern(cls, pattern: str, batch_size: int = 1000) -> int:
        """按模式批量清理键"""
        deleted_count = 0
        cursor = 0
        
        while True:
            try:
                cursor, keys = rs_client.scan(cursor, match=pattern, count=batch_size)
                
                if keys:
                    # 批量删除过期键
                    pipe = rs_client.pipeline()
                    for key in keys:
                        pipe.delete(key)
                    results = pipe.execute()
                    deleted_count += sum(results)
                
                if cursor == 0:
                    break
                    
            except Exception as e:
                logger.error(f"Error during cleanup scan: {e}")
                break
        
        return deleted_count
    
    @classmethod
    def get_stats(cls) -> Dict[str, Any]:
        """获取去重缓存统计信息"""
        try:
            stats = {
                "claim_keys": 0,
                "group_keys": 0,
                "total_members": 0
            }
            
            # 统计声明键
            claim_pattern = f"{cls.CLAIM_PREFIX}:*"
            cursor, keys = rs_client.scan(0, match=claim_pattern, count=10000)
            stats["claim_keys"] = len(keys)
            
            # 统计组键和成员
            group_pattern = f"{cls.GROUP_PREFIX}:*"
            cursor = 0
            while True:
                cursor, keys = rs_client.scan(cursor, match=group_pattern, count=1000)
                stats["group_keys"] += len(keys)
                
                # 统计成员数量
                for key in keys:
                    try:
                        member_count = rs_client.scard(key)
                        stats["total_members"] += member_count
                    except:
                        pass
                
                if cursor == 0:
                    break
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get dedup stats: {e}")
            return {}


class DedupRepoSingleKeyOptimized:
    """
    优化的单键去重仓库
    保持与原有接口兼容，但内部使用优化实现
    """
    
    @staticmethod
    def exists(dedup_key: str) -> bool:
        return OptimizedDedupRepo.exists(dedup_key)
    
    @staticmethod
    def try_claim(dedup_key: str, ttl_sec: int) -> bool:
        return OptimizedDedupRepo.try_claim(dedup_key, ttl_sec)
    
    @staticmethod
    def add_member(dedup_key: str, activity_id: str, ttl_sec: int) -> bool:
        return OptimizedDedupRepo.add_member(dedup_key, activity_id, ttl_sec)
    
    @staticmethod
    def pop_members(dedup_key: str, keep_in_redis: bool = False) -> List[str]:
        return OptimizedDedupRepo.pop_members(dedup_key, keep_in_redis)
    
    @staticmethod
    def get_members(dedup_key: str) -> List[str]:
        return OptimizedDedupRepo.get_members(dedup_key)
    
    @staticmethod
    def cleanup_expired() -> int:
        return OptimizedDedupRepo.cleanup_expired()
    
    @staticmethod
    def get_all_keys() -> List[str]:
        """获取所有去重键（兼容性方法）"""
        try:
            pattern = f"{OptimizedDedupRepo.CLAIM_PREFIX}:*"
            cursor, keys = rs_client.scan(0, match=pattern, count=10000)
            # 移除前缀返回原始键名
            prefix_len = len(OptimizedDedupRepo.CLAIM_PREFIX) + 1
            return [key.decode()[prefix_len:] if isinstance(key, bytes) else key[prefix_len:] for key in keys]
        except Exception as e:
            logger.error(f"Failed to get all dedup keys: {e}")
            return []
