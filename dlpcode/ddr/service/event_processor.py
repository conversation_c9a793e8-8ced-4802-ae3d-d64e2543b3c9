import os
import hashlib
import copy
import fnmatch
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from storage.util.enum import ActivityStatus, TrustLevel
from storage.model.activity import update_activities_status
from util.config import configs, get_supported_file_type, get_scan_config
from util.enum_ import StorageType, ScanMethod, ScanScope
from util.common_log import get_logger
from ddr.service.mgmt_service import DDRTaskMgmtService
from ddr.service.dedup_repo import DedupRepoSingleKey

logger = get_logger("ddr")

class DDRProcessService:
    """Data Detection Response Process Service (process activities)"""

    def __init__(self, task: dict):
        # Create DDR task management service to get session
        self.tms = DDRTaskMgmtService(task['id'])

        self._storage_type = task['storage_type']
        self._storage_id = task['storage_id']
        self._analyze_setting = task['analyze_setting']
        self._ddr_task_id = task['id']
        self._scan_folders = task['scan_folders']
        self._excluded_scan_folders = task['excluded_scan_folders']
        self._scan_scope = task['scan_scope']
        self._scan_file_type = get_supported_file_type(task['scan_file_type'])
        self._task = task
        self._dedup_ttl = int(configs.get("ddr_task", {}).get("dedup_ttl", 10))
        self._max_activities = int(configs.get("ddr_task", {}).get("max_activities", 800))
        self._cleanup_max_activities = int(configs.get("ddr_task", {}).get("cleanup_max_activities", 1600))
        self._skip_paths, self._skip_files = self.get_skip_item()

    def process_events(self,
                       start_time: datetime, end_time: datetime,
                       status: int = ActivityStatus.NEW,
                       target_operations: Optional[List[str]] = None,
                       scan_trigger_events: Optional[Dict[str, List[int]]] = None,
                       cleanup_mode: bool = False) -> Dict[str, Any]:
        """Process platform events (activities)"""
        try:
            # Set cleanup mode flag for smart limits
            self._cleanup_mode = cleanup_mode

            # 1. Get platform events (activities) from storage_activity table with pagination
            activities = self._get_fetched_logs(start_time, end_time, status, target_operations)
            if not activities:
                return {"processed": 0, "scans_triggered": 0, "skipped": 0}

            scan_count = 0
            skipped_count = 0
            # 2. Process and update activities (schedule download and analyze)
            for activity in activities:
                should_skip, should_log = self._should_skip(activity)
                if should_skip:
                    logger.debug(f"Skipped file | Event ID: {activity['event_id']} "
                                 f"DB set skipped: {should_log}")
                    if should_log:
                        activity["status"] = ActivityStatus.SKIPPED
                        skipped_count += 1
                    else:
                        activity["status"] = ActivityStatus.NEW
                    continue

                trigger_events = self._task.get('trigger_events', {})
                if scan_trigger_events:
                    trigger_events = scan_trigger_events

                if self._should_trigger_scan(activity, trigger_events):
                    activity["status"] = ActivityStatus.PROCESSING
                    activity["scan_triggered"] = True
                    scan_count += 1
                    # Schedule file processing (download and analyze)
                    self._schedule_ddr_file_processing(activity)
                else:
                    activity["status"] = ActivityStatus.SKIPPED
                    activity["scan_triggered"] = False
                    skipped_count += 1

            # 3. Save activities to DB (update status)
            update_activities_status(activities, logger=logger)

            result = {
                "processed": len(activities),
                "scans_triggered": scan_count,
                "skipped": skipped_count
            }
            logger.info(f"DDR processing completed for {self._storage_type}: {result}")
            return result

        except Exception as e:
            logger.error(f"Error in DDR event processing: {e}")
            return {"processed": 0, "scans_triggered": 0, "skipped": 0, "error": str(e)}

    def _get_fetched_logs(self, start_time: datetime, end_time: datetime,
                          status: int = ActivityStatus.NEW,
                          target_operations: Optional[List[str]] = None,
                          batch_size: int = 1000) -> List[dict]:
        """Get platform logs (activities) from storage_activity table with atomic locking to prevent concurrent processing"""
        from storage.model.activity import get_and_lock_activities_atomic
        try:
            filters = {
                "storage_id": self._storage_id,
                "status": status,
                "created_at__gte": start_time,
                "created_at__lt": end_time,
            }
            if self._scan_scope == ScanScope.ALL_FOLDERS:
                filters["ddr_fields__jsonb__filter__not_in"] = self._excluded_scan_folders
            else:
                filters["ddr_fields__jsonb__filter"] = self._scan_folders

            if target_operations:
                filters["ddr_fields__jsonb__display_type"] = target_operations

            all_activities = []
            offset = 0

            while True:
                # Atomically get and lock activities to prevent concurrent processing
                activities = get_and_lock_activities_atomic(
                    limit=batch_size,
                    offset=offset,
                    **filters
                )

                if not activities:
                    break

                all_activities.extend(activities)

                # If the number returned is less than batch_size, it means it is the last page
                if len(activities) < batch_size:
                    break

                offset += batch_size

                # Smart limit based on mode
                max_activities = self._cleanup_max_activities if getattr(self, '_cleanup_mode', False) else self._max_activities

                if len(all_activities) >= max_activities:
                    logger.warning(f"DDR activity count reached limit ({max_activities}), remaining activities will be processed in next cycle")
                    break

            logger.info(f"Retrieved and locked {len(all_activities)} activities for DDR processing")
            return all_activities

        except Exception as e:
            logger.error(f"Error processing {self._storage_type} logs: {e}")
            return []

    def _schedule_ddr_file_processing(self, activity: dict):
        """
        DDR file processing scheduling
        Args:
            activity: DDR activity data
        """
        try:
            from huey_worker.ddr_download_worker import add_analyze_task, download_file, download_huey
            from ddr.service.smart_backoff import smart_backoff

            if not self.tms.is_scanning():
                logger.error(f"Failed to create session for DDR task {self._ddr_task_id}")
                activity["status"] = ActivityStatus.PENDING
                activity["scan_triggered"] = False
                return

            # Construct file info
            file_info = self._build_file_info_from_activity(activity)
            if not file_info:
                logger.info(f"No folder found for activity {activity.get('id', '')}, skipping")
                activity["status"] = ActivityStatus.SKIPPED
                activity["scan_triggered"] = False
                return

            dedup_key = self._get_dedup_key(activity)
            if DedupRepoSingleKey.exists(dedup_key):
                logger.info(f"[dedup] duplicate found: act={activity.get('id')} key={dedup_key}")
                activity["status"] = ActivityStatus.IGNORED
                activity["scan_triggered"] = False
                return

            if smart_backoff.is_needed_to_back_off(self._ddr_task_id, "ddr"):
                logger.info(f"Backoff needed for DDR task {self._ddr_task_id}, skipping")
                activity["status"] = ActivityStatus.PENDING
                activity["scan_triggered"] = False
                return

            claim_ttl = self._dedup_ttl * 60 + 60
            if DedupRepoSingleKey.try_claim(dedup_key, claim_ttl):
                backlog_hash = hashlib.md5(f"{self._ddr_task_id}:{dedup_key}".encode()).hexdigest()
                logger.info(f"[dedup] leader scheduled: act={activity.get('id')} key={dedup_key}")

                params = self._get_params(activity, dedup_key)
                # Directly schedule download task, analyze phase controlled by Redis Stream
                download_file_task = download_file.s(
                    task_uuid=self._ddr_task_id,
                    file_info=file_info,
                    params=params,
                    session_key="",
                    backlog_hash=backlog_hash
                )
                pipeline = download_file_task.then(
                    add_analyze_task,
                    previous_task_id=download_file_task.id
                )
                download_huey.enqueue(pipeline)

                # Add self to dedup group, leader ends and clean up
                DedupRepoSingleKey.add_member(dedup_key, str(activity.get('id')), claim_ttl)

        except Exception as e:
            logger.error(f"Error scheduling DDR file processing: {e}")
            activity["status"] = ActivityStatus.PENDING
            activity["scan_triggered"] = False


    def _build_file_info_from_activity(self, activity: dict) -> dict:
        """build file info from DDR activity"""
        try:
            ddr_fields = activity.get("ddr_fields", {})
            display_path = ddr_fields.get("display_path", "")
            file_name = ddr_fields.get("file_name", "")
            folder = ddr_fields.get("folder", "")

            if not display_path or not folder:
                return None

            if not file_name:
                file_name = display_path.rsplit('/', 1)[-1]

            return {
                "folder": folder,
                "file_name": file_name,
                "display_path": display_path,
                # DDR specific fields
                "display_type": ddr_fields.get("display_type", "unknown"),
                "actor": ddr_fields.get("actor", "unknown")
            }
        except Exception as e:
            logger.error(f"Error building file info from activity: {e}")
            return None

    def _should_trigger_scan(self, event: dict, scan_trigger_events: Optional[Dict[str, List[int]]] = None) -> bool:
        """Determine whether to trigger scan, judge by event_type and trust_level"""
        if scan_trigger_events:
            ddr_fields = event.get("ddr_fields", {})
            display_type = ddr_fields.get("display_type", "UNKNOWN")
            trust_level = ddr_fields.get("trust_level", TrustLevel.UNKNOWN.value)
            return display_type in scan_trigger_events.keys() and trust_level in scan_trigger_events[display_type]
        return False

    def get_skip_item(self):
        try:
            skip_files = []
            skip_paths = []
            scan_configs = get_scan_config()
            if 'global' in scan_configs:
                skip_files.extend(scan_configs['global'].get('skip_files', []))
                skip_paths.extend(scan_configs['global'].get('skip_paths', []))

            if self._ddr_task_id in scan_configs:
                skip_files.extend(scan_configs[self._ddr_task_id].get('skip_files', []))
                skip_paths.extend(scan_configs[self._ddr_task_id].get('skip_paths', []))

            if self._task['excluded_scan_folders'] is not None:
                skip_paths.extend(self._task['excluded_scan_folders'])
            return skip_paths, skip_files
        except Exception as e:
            logger.error(f"Error getting skip items: {e}")
            return [], []

    def _should_skip_file(self, event: dict) -> bool:
        """Determine whether to skip file"""
        try:
            ddr_fields = event.get("ddr_fields", {})

            file_size_limit = self._task["file_size_limit"]
            # Check file size
            max_file_size_mb = file_size_limit.get("max", 0)
            if max_file_size_mb and ddr_fields["file_size"]:
                file_size_mb = ddr_fields["file_size"] / (1024 * 1024)
                if file_size_mb > max_file_size_mb:
                    return True

            # Check file extension
            if self._scan_file_type["ext"]:
                ext = ddr_fields.get("file_ext", "").lower() or os.path.splitext(ddr_fields["file_name"])[1].lstrip(".").lower()
                if ext and ext not in self._scan_file_type["ext"]:
                    return True

            # Check file path and file name, skip_files and skip_paths are lists
            for skip_path in self._skip_paths:
                filepath = ddr_fields["display_path"]
                if filepath.startswith(skip_path) or fnmatch.fnmatch(filepath, skip_path):
                    return True

            for skip_file in self._skip_files:
                filename = ddr_fields.get("file_name", "")
                if skip_file == filename or fnmatch.fnmatch(filename, skip_file):
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking skip rules: {e}")
            return False

    def _should_skip(self, event: dict) -> Tuple[bool, bool]:
        """
        Determine whether to skip event
        Args:
            event: Event data
        Returns:
            bool: Whether to skip event
            bool: Whether to log skip event
        """
        if self._should_skip_file(event):
            return True, True
        if self._storage_type == StorageType.AWS:
            if event["event_type"] == "":
                return True, False
        elif self._storage_type == StorageType.SHAREPOINT_OL:
            # Focus on task configured folders, skip if not in configured folders
            site_url = event.get("raw_data", {}).get("SiteUrl", "").strip("/")
            if site_url not in self._scan_folders:
                return True, False
        elif self._storage_type == StorageType.GOOGLE:
            if event["event_type"] == "":
                return True, False
        return False, False

    def _get_params(self, activity: dict, dedup_key: str = "") -> dict:
        """
        Get the parameters for the connector service.

        Returns:
            dict: A dictionary containing the parameters for the connector service.
            Return empty dictionary if any error occurs.
        """
        from storage.service.profiles import get_storage_profile_by_id
        try:
            scan_info = copy.deepcopy(self._analyze_setting or {})
            scan_info.update({
                "scan_uuid": self._ddr_task_id,
                "scan_name": self._task["name"],
                "scan_created_at": self._task["created_at"],
                "scan_description": self._task["description"],
                "scan_file_type": self._task["scan_file_type"],
                "ddr_policy": self._task["ddr_policy_ids"],
                "activity_id": activity["id"],
                "is_ddr": True
            })
            if dedup_key:
                scan_info["dedup_key"] = dedup_key

            storage = get_storage_profile_by_id(self._storage_id)
            auth_info = storage['auth_info']
            ddr_fields = activity["ddr_fields"]

            remote_info = {
                "storage_id": self._storage_id,
                "storage_name": storage["name"],
                "file": "",
                "file_name": "",
                "file_display_path": ddr_fields.get("display_path", ""),
                "actor": ddr_fields.get("actor", ""),
                "actor_ip": ddr_fields.get("ip", ""),
                "event_type": ddr_fields.get("display_type") or activity["event_type"],
                "trust_level": ddr_fields.get("trust_level", TrustLevel.UNKNOWN.value)
            }
            if self._storage_type == StorageType.AWS:
                scan_info["target"] = str(auth_info.get("region_name", "GLOBAL"))
                remote_info.update({
                    "type": "aws",
                    "storage_type": 1,
                    "key_id": auth_info["key_id"],
                    "access_key": auth_info["access_key"],
                    "cloudtrail_arn": auth_info.get("cloudtrail_arn", ""),
                    "Bucket": self._scan_folders,
                })
            elif self._storage_type == StorageType.SHAREPOINT_OL and auth_info["usecredentials"] == False:
                scan_info["target"] = str(auth_info.get("tenantid", "UNKNOWN"))
                remote_info.update({
                    "type": "sharepoint",
                    "usecredentials": auth_info["usecredentials"],
                    "storage_type": 2,
                    "tenantid": auth_info["tenantid"],
                    "clientid": auth_info["clientid"],
                    "clientsecret": auth_info["clientsecret"],
                    "allow_ntlm": auth_info["allow_ntlm"],
                })
            elif self._storage_type == StorageType.GOOGLE:
                scan_info["target"] = str(auth_info.get("customer_id", "UNKNOWN"))
                remote_info.update({
                    "type": "google",
                    "storage_type": 6,
                    "customer_id": auth_info["customer_id"],
                    "delegated_admin_email": auth_info["delegated_admin_email"],
                    #"service_account_info": auth_info["service_account_info"],
                })
            return {
                "remote_info": remote_info,
                "scan_info": scan_info,
            }
        except KeyError as e:
            logger.error(f"KeyError occurred: {e}")
            return {}
        except Exception as e:
            logger.error(e)
            return {}

    def _get_dedup_key(self, activity: dict) -> str:
        """Get dedup key from activity"""
        ddr_fields = activity.get("ddr_fields", {})
        raw_dedup = f"{self._storage_id}:{ddr_fields.get('actor', '')}:{ddr_fields.get('folder', '')}:{ddr_fields.get('file_name', '')}"
        return hashlib.md5(raw_dedup.encode("utf-8")).hexdigest()

    @staticmethod
    def set_dedup_group_status(dedup_key: str, status: ActivityStatus = ActivityStatus.SKIPPED) -> None:
        """Set dedup group status, keep the group"""
        # logger.info(f"[dedup] start: key={dedup_key} status={status}")
        try:
            if not DedupRepoSingleKey.exists(dedup_key):
                logger.warning(f"[dedup] Key not found: {dedup_key}")
                return

            is_keep = True
            if status in [ActivityStatus.DL_FAILED, ActivityStatus.FAILED]:
                is_keep = False
            ids = DedupRepoSingleKey.pop_members(dedup_key, keep_in_redis=is_keep)
            if ids:
                if status in [ActivityStatus.SKIPPED, ActivityStatus.IGNORED, ActivityStatus.DL_FAILED]:
                    triggered = False
                else:
                    triggered = True
                acts = [{"id": x, "status": status, "scan_triggered": triggered} for x in ids if x]
                update_activities_status(acts, logger=logger)
                logger.info(f"[dedup]: key={dedup_key} ids={ids} status={status} triggered={triggered} keep={is_keep}")
        except Exception:
            logger.error("[dedup] failed", exc_info=True)