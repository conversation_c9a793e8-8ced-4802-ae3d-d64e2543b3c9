import hashlib
import json
import time
from typing import List, Dict, Any
from exts import rs_client
from util.common_log import get_logger

logger = get_logger("ddr")

class DedupRepoSingleKey:
    _SHARD_COUNT = 16
    _MAIN_KEY_PREFIX = "ddr:dedup:shard"

    @classmethod
    def _get_shard_key(cls, dedup_key: str) -> str:
        """Get shard key based on dedup key."""
        shard_id = hash(dedup_key) % cls._SHARD_COUNT
        return f"{cls._MAIN_KEY_PREFIX}:{shard_id}"

    @classmethod
    def exists(cls, dedup_key: str) -> bool:
        """Check if dedup key exists."""
        key = cls._get_shard_key(dedup_key)
        return rs_client.hexists(key, dedup_key)

    @classmethod
    def try_claim(cls, dedup_key: str, ttl_sec: int) -> bool:
        """
        Try to claim the dedup key, only leader can get it.
        Returns:
            bool: True if claimed successfully, False if already claimed.
        """
        shared_key = cls._get_shard_key(dedup_key)
        # Use Lua script to ensure atomicity
        lua_script = """
        local main_key = KEYS[1]
        local dedup_key = ARGV[1]

        -- Check if the key already exists
        if redis.call('HEXISTS', main_key, dedup_key) == 1 then
            return false
        end

        -- Create the key
        local data = {
            type = "claim",
            timestamp = ARGV[2],
            members = {},
            expire_at = ARGV[3]
        }

        return redis.call('HSET', main_key, dedup_key, cjson.encode(data)) >= 0
        """

        current_time = time.time()
        expire_at = current_time + ttl_sec

        result = rs_client.eval(
            lua_script,
            1,
            shared_key,
            dedup_key,
            current_time,
            expire_at
        )

        return bool(result)

    @classmethod
    def add_member(cls, dedup_key: str, activity_id: str, ttl_sec: int) -> bool:
        shared_key = cls._get_shard_key(dedup_key)
        lua_script = """
        local main_key = KEYS[1]
        local dedup_key = ARGV[1]
        local activity_id = ARGV[2]
        local expire_at = ARGV[3]

        -- Get existing data
        local data_json = redis.call('HGET', main_key, dedup_key)
        if not data_json then
            return false
        end

        local data = cjson.decode(data_json)

        if data.type == 'claim' then
            -- Add member
            table.insert(data.members, activity_id)
            data.expire_at = expire_at

            -- Update Redis
            return redis.call('HSET', main_key, dedup_key, cjson.encode(data)) >= 0
        end

        return false
        """

        expire_at = time.time() + ttl_sec
        result = rs_client.eval(
            lua_script,
            1,
            shared_key,
            dedup_key,
            activity_id,
            expire_at
        )

        return bool(result)

    @classmethod
    def pop_members(cls, dedup_key: str, keep_in_redis: bool = False) -> List[str]:
        """
        Pop all members from dedup group,
        if keep_in_redis is True, keep the group, else delete it.
        Args:
            keep_in_redis: Whether to keep the dedup key in redis.
        Returns:
            List[str]: members list
        """
        shared_key = cls._get_shard_key(dedup_key)
        lua_script = """
        local main_key = KEYS[1]
        local dedup_key = ARGV[1]
        local keep_in_redis = ARGV[2] == 'true'

        local data_json = redis.call('HGET', main_key, dedup_key)
        if not data_json then
            return cjson.encode({})
        end

        local data = cjson.decode(data_json)
        local members = data.members or {}

        if not keep_in_redis then
            -- Cleanup dedup key
            redis.call('HDEL', main_key, dedup_key)
        end

        return cjson.encode(members)
        """

        result = rs_client.eval(
            lua_script,
            1,
            shared_key,
            dedup_key,
            str(keep_in_redis).lower()
        )

        if result:
            return json.loads(result)
        return []

    @classmethod
    def get_members(cls, dedup_key: str) -> List[str]:
        """Get members from dedup group.(Not pop members)"""
        shared_key = cls._get_shard_key(dedup_key)
        data_json = rs_client.hget(shared_key, dedup_key)
        if not data_json:
            return []

        data = json.loads(data_json)
        return data.get("members", [])

    @classmethod
    def cleanup_expired(cls) -> int:
        """Clean up expired dedup keys. Return deleted count."""
        total_deleted = 0
        current_time = time.time()
        
        # cleanup each shard
        for shard_id in range(cls._SHARD_COUNT):
            shard_key = f"{cls._MAIN_KEY_PREFIX}:{shard_id}"

            lua_script = """
            local main_key = KEYS[1]
            local current_time = tonumber(ARGV[1])
            local deleted_count = 0

            -- Get all fields
            local all_fields = redis.call('HKEYS', main_key)

            for _, field in ipairs(all_fields) do
                local data_json = redis.call('HGET', main_key, field)
                if data_json then
                    local data = cjson.decode(data_json)
                    if data.expire_at and tonumber(data.expire_at) < current_time then
                        redis.call('HDEL', main_key, field)
                        deleted_count = deleted_count + 1
                    end
                end
            end

            return deleted_count
            """

            try:
                result = rs_client.eval(
                    lua_script,
                    1,
                    shard_key,
                    current_time
                )
                total_deleted += result or 0
            except Exception as e:
                logger.error(f"Failed to cleanup shard {shard_key}: {e}")
                continue

        logger.info(f"[dedup] cleanup_expired: {total_deleted} keys deleted")
        return total_deleted

    @classmethod
    def get_all_keys(cls) -> List[str]:
        """Get all dedup keys from all shards."""
        all_keys = []
        
        for shard_id in range(cls._SHARD_COUNT):
            shard_key = f"{cls._MAIN_KEY_PREFIX}:{shard_id}"
            try:
                keys = rs_client.hkeys(shard_key)
                if keys:
                    decoded_keys = [key.decode('utf-8') if isinstance(key, bytes) else str(key) 
                                  for key in keys]
                    all_keys.extend(decoded_keys)
            except Exception as e:
                logger.error(f"Failed to get keys from shard {shard_key}: {e}")
                continue

        return all_keys

    @classmethod
    def get_stats(cls) -> Dict[str, Any]:
        """Statistics of the dedup repo."""
        stats = {
            "total_keys": 0,
            "total_members": 0,
            "shard_details": {}
        }

        for shard_id in range(cls._SHARD_COUNT):
            shard_key = f"{cls._MAIN_KEY_PREFIX}:{shard_id}"
            try:
                # Get key count
                key_count = rs_client.hlen(shard_key)
                stats["total_keys"] += key_count

                if key_count > 0:
                    all_data = rs_client.hgetall(shard_key)
                    member_count = 0
                    for value in all_data.values():
                        try:
                            data = json.loads(value)
                            member_count += len(data.get("members", []))
                        except:
                            pass
                    stats["total_members"] += member_count

                stats["shard_details"][shard_id] = {
                    "key_count": key_count,
                    "memory_usage": rs_client.memory_usage(shard_key) or 0
                }

            except Exception as e:
                logger.error(f"Failed to get stats for shard {shard_key}: {e}")
                continue

        return stats