import json
from exts import get_logger, rs_client

logger = get_logger("rs_analyze")

STREAM_NAME = "stream:analyze_tasks"
SOURCES = ["ddr", "scan_policy"]

def is_valid_kwargs(kwargs: dict, required_fields: list[str]) -> bool:
    return all(kwargs.get(k) for k in required_fields)

def build_payload_from_kwargs(kwargs: dict) -> dict:
    keys = [
        "local_file", 
        "remote_info", 
        "file_uuid",
        "scan_info", 
        "task_uuid", 
        "session_key", 
        "backlog_hash"
    ]
    if not is_valid_kwargs(kwargs, keys):
        return None

    return {k: kwargs[k] for k in keys if k in kwargs}


def push_to_analyze_stream(source: str, args: dict):
    try:
        if source not in SOURCES:
            logger.warning(f"[StreamProducer] Unknown source: {source}")
            return

        payload = build_payload_from_kwargs(args)
        if not payload:
            logger.warning(f"[StreamProducer] Invalid payload: {args}")
            return

        rs_client.xadd(STREAM_NAME, {
            "source": source,
            "payload": json.dumps(payload)
        })
    except Exception as e:
        logger.error(f"[StreamProducer] Failed to push to analyze stream: {e}")
        return


