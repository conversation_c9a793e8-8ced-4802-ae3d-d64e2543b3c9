import json

from google.oauth2 import service_account
from googleapiclient.discovery import build

SCOPES = ['https://www.googleapis.com/auth/apps.licensing',
          "https://www.googleapis.com/auth/admin.directory.user.readonly"]

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES
).with_subject(DELEGATED_ADMIN_EMAIL)

directory_service = build('admin', 'directory_v1', credentials=credentials)
results = directory_service.users().list(maxResults=1, customer='my_customer').execute()
customer_id = results['users'][0]['customerId']
print(f"\n✅ Customer ID: {customer_id}")

service = build('licensing', 'v1', credentials=credentials)
result = service.licenseAssignments().listForProduct(
    productId='Google-Apps', customerId=customer_id
).execute()

print(json.dumps(result))
