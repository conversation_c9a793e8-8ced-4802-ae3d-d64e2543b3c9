from google.oauth2 import service_account
import google.auth.transport.requests
import requests
import json


SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'

SCOPES = ['https://www.googleapis.com/auth/apps.licensing']
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES
).with_subject(DELEGATED_ADMIN_EMAIL)

# 刷新 token
request = google.auth.transport.requests.Request()
credentials.refresh(request)

# 打印 access token
print("\nAccess Token:\n", credentials.token)

# 验证 token 信息
token_info_url = f"https://oauth2.googleapis.com/tokeninfo?access_token={credentials.token}"
resp = requests.get(token_info_url)
print("\nToken Info:\n", json.dumps(resp.json(), indent=2))
