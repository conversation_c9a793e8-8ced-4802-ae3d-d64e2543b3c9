import json
from typing import List, Optional

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

DRIVE_SCOPES = ['https://www.googleapis.com/auth/drive.readonly']


# Create a Google Drive API service instance using service account credentials
def get_drive_service(service_account_file: str, delegated_user: str, scopes: List[str]):
    credentials = service_account.Credentials.from_service_account_file(
        service_account_file, scopes=scopes
    ).with_subject(delegated_user)
    return build('drive', 'v3', credentials=credentials)


# List all files in a specified Shared Drive by drive ID
def list_files_in_shared_drive(service, drive_id: str, page_size: int = 100) -> List[dict]:
    all_files = []
    page_token: Optional[str] = None
    query = "trashed = false"  # Filter to exclude trashed files

    while True:
        try:
            response = service.files().list(
                corpora='drive',  # Search within a shared drive
                driveId=drive_id,  # The ID of the shared drive
                includeItemsFromAllDrives=True,
                supportsAllDrives=True,
                q=query,
                pageSize=page_size,
                pageToken=page_token,
                fields="nextPageToken, files(id, name, mimeType, createdTime, modifiedTime, size)"
            ).execute()

            files = response.get('files', [])
            all_files.extend(files)

            page_token = response.get('nextPageToken')
            if not page_token:
                print("All files retrieved.")
                break
            else:
                print(f"Fetched {len(files)} files, continuing to next page...")
        except HttpError as e:
            print(f"An error occurred: {e}")
            break

    return all_files


def main():
    try:
        drive_service = get_drive_service(SERVICE_ACCOUNT_FILE, DELEGATED_ADMIN_EMAIL, DRIVE_SCOPES)
        # Replace with your Shared Drive ID
        drive_id = '0AGLmOJT-vE9TUk9PVA'
        files = list_files_in_shared_drive(drive_service, drive_id, page_size=50)
        print(json.dumps(files))
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
