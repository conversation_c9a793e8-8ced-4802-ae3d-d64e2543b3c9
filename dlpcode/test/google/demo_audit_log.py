import json
import argparse
from google.oauth2 import service_account
from googleapiclient.discovery import build

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

SCOPES = ['https://www.googleapis.com/auth/admin.reports.audit.readonly']

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES
).with_subject(DELEGATED_ADMIN_EMAIL)

reports_service = build('admin', 'reports_v1', credentials=credentials)


def get_drive_audit_logs(user_email='all', start_time=None, end_time=None, max_results=10):
    kwargs = {
        'userKey': user_email,
        'applicationName': 'drive',
        'maxResults': max_results,
    }

    if start_time:
        kwargs['startTime'] = start_time
    if end_time:
        kwargs['endTime'] = end_time

    response = reports_service.activities().list(**kwargs).execute()
    activities = response.get('items', [])

    if not activities:
        print("No audit logs found.")
    else:
        print(json.dumps(activities, indent=2))


def main():
    parser = argparse.ArgumentParser(description="Query Google Drive audit logs from Google Workspace")
    parser.add_argument('--user', default='all', help="User email to filter logs (default: all users)")
    parser.add_argument('--start', help="Start time (RFC3339 format, e.g. 2025-05-01T00:00:00Z)")
    parser.add_argument('--end', help="End time (RFC3339 format, e.g. 2025-05-22T23:59:59Z)")
    parser.add_argument('--max', type=int, default=10, help="Maximum number of results (default: 10)")

    args = parser.parse_args()

    get_drive_audit_logs(
        user_email=args.user,
        start_time=args.start,
        end_time=args.end,
        max_results=args.max
    )


if __name__ == '__main__':
    main()
