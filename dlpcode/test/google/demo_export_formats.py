from google.oauth2 import service_account
from googleapiclient.discovery import build
import json

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'
SCOPES = ['https://www.googleapis.com/auth/drive.readonly']

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES
).with_subject(DELEGATED_ADMIN_EMAIL)

service = build('drive', 'v3', credentials=credentials)

about = service.about().get(fields='exportFormats').execute()

print(json.dumps(about))