from google.oauth2 import service_account
from googleapiclient.discovery import build

# Configuration
SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'
SCOPES = ['https://www.googleapis.com/auth/drive']
FILE_ID = '11NRAsUksnKqhyCLoIQ4LfGtfvegx6vTlrgrzcqi8Nbo'

# Initial properties
INITIAL_PROPERTIES = {
    'project': 'dlp',
    'confidential': 'true',
    'owner': 'QA_Team'
}


def get_drive_service():
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_FILE,
        scopes=SCOPES,
        subject=DELEGATED_ADMIN_EMAIL
    )
    return build('drive', 'v3', credentials=credentials)


def update_file_properties(service, file_id, properties):
    """
    Updates custom file properties (labels) on a specific Google Drive file.
    """
    updated_file = service.files().update(
        fileId=file_id,
        body={'properties': properties},
        fields='id, name, properties'
    ).execute()
    print("Properties updated:")
    print(updated_file['properties'])


def get_file_properties(service, file_id):
    file = service.files().get(
        fileId=file_id,
        fields='id, name, properties'
    ).execute()
    print("Current properties:")
    print(file.get('properties', {}))


def delete_property(service, file_id, key_to_delete):

    delete_body = {key_to_delete: None}
    service.files().update(
        fileId=file_id,
        body={'properties': delete_body},
        fields='id, properties'
    ).execute()
    print(f"Property '{key_to_delete}' deleted.")


def main():
    drive_service = get_drive_service()

    # Step 1: Set initial properties
    update_file_properties(drive_service, FILE_ID, INITIAL_PROPERTIES)

    # Step 2: Get and print current properties
    get_file_properties(drive_service, FILE_ID)

    # Step 3: Delete 'confidential' property
    delete_property(drive_service, FILE_ID, 'confidential')

    # Step 4: Get and print properties again to confirm deletion
    get_file_properties(drive_service, FILE_ID)


if __name__ == '__main__':
    main()
