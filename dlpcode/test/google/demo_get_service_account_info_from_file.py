import base64

def file_to_base64(file_path):
    """
    Read file content and encode it as Base64
    
    Args:
        file_path (str): Path to the file to be read
        
    Returns:
        str: Base64 encoded string of the file content
        
    Raises:
        FileNotFoundError: If the specified file doesn't exist
        Exception: For other file processing errors
    """
    try:
        # Open file in binary mode
        with open(file_path, 'rb') as file:
            # Read file content
            file_content = file.read()
            # Encode content to Base64
            base64_encoded = base64.b64encode(file_content)
            # Convert bytes to string
            return base64_encoded.decode('utf-8')
    except FileNotFoundError:
        raise FileNotFoundError(f"File {file_path} not found")
    except Exception as e:
        raise Exception(f"Error processing file: {str(e)}")


# Usage example
if __name__ == "__main__":
    try:
        encoded_content = file_to_base64("fourth-landing-460518-p2-107287671db2.json")
        print("Base64 encoded result:")
        print(encoded_content)
    except Exception as e:
        print(f"Error: {e}")