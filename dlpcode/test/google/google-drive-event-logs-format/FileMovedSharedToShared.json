{"id": {"time": "2025-08-26T03:58:23.367Z", "customerId": "C01birj3q", "applicationName": "drive", "uniqueQualifier": "-8997301119692906628"}, "etag": "\"-MCiy9F4kgq1ZhQB4shKgK04W2CzTXcWI051NVmUJkY/x5pTqFOAisa7ApmRKygJ9Ib2WlY\"", "kind": "admin#reports#activity", "actor": {"email": "<EMAIL>", "profileId": "118122581219327755866"}, "events": [{"name": "move", "type": "access", "parameters": [{"name": "primary_event", "boolValue": true}, {"name": "billable", "boolValue": true}, {"name": "source_folder_title", "multiValue": ["shared_drive_created_by_forti"]}, {"name": "source_folder_id", "multiValue": ["0ALU_DPeCBdaeUk9PVA"]}, {"name": "destination_folder_title", "multiValue": ["A"]}, {"name": "destination_folder_id", "multiValue": ["0ADsTxzY3sbCeUk9PVA"]}, {"name": "owner_is_shared_drive", "boolValue": true}, {"name": "owner_team_drive_id", "value": "0ALU_DPeCBdaeUk9PVA"}, {"name": "owner", "value": "shared_drive_created_by_forti"}, {"name": "doc_id", "value": "1YO5Kgg6S2KKcGKi3lNoPs4cTuXKe9O0ke7u_kdC15n8"}, {"name": "doc_type", "value": "document"}, {"name": "is_encrypted", "boolValue": false}, {"name": "doc_title", "value": "file_created_by_forti"}, {"name": "visibility", "value": "shared_internally"}, {"name": "shared_drive_id", "value": "0ADsTxzY3sbCeUk9PVA"}, {"name": "originating_app_id", "value": "************"}, {"name": "actor_is_collaborator_account", "boolValue": false}, {"name": "owner_is_team_drive", "boolValue": true}, {"name": "team_drive_id", "value": "0ADsTxzY3sbCeUk9PVA"}], "resourceIds": ["1YO5Kgg6S2KKcGKi3lNoPs4cTuXKe9O0ke7u_kdC15n8"]}], "ipAddress": "***************", "networkInfo": {"ipAsn": [56300], "regionCode": "SG", "subdivisionCode": ""}, "resourceDetails": [{"id": "1YO5Kgg6S2KKcGKi3lNoPs4cTuXKe9O0ke7u_kdC15n8", "type": "DRIVE_ITEM", "title": "file_created_by_forti", "relation": "DRIVE_PRIMARY", "appliedLabels": [{"id": "jRoRUQB8ndJR6GHxYCfvfAoROYXbat4JrXMRNNEbbFcb", "title": "FortiTag", "reason": {"reasonType": "USER_APPLIED"}, "fieldValues": [{"id": "f3d30e309c", "type": "TEXT", "reason": {"reasonType": "USER_APPLIED"}, "textValue": "testccc,chenjili", "displayName": "FortiTag"}]}]}]}