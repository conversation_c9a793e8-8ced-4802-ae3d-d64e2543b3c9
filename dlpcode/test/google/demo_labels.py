import json
import time
from typing import List, Optional, Dict, Any

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

DRIVE_SCOPES = ['https://www.googleapis.com/auth/drive']

FILE_FIELDS_FOR_LABELS = "id,name,labelInfo(labels)"

FILE_ID_TO_TEST = "11NRAsUksnKqhyCLoIQ4LfGtfvegx6vTlrgrzcqi8Nbo"
LABEL_ID_TO_APPLY = "chenjili-label-id-1"


def get_drive_service(service_account_file: str, delegated_user: str, scopes: List[str]):
    try:
        credentials = service_account.Credentials.from_service_account_file(
            service_account_file, scopes=scopes
        ).with_subject(delegated_user)
        service = build('drive', 'v3', credentials=credentials)
        print("Drive service built successfully.")
        return service
    except Exception as e:
        print(f"Error building Drive service: {e}")
        raise


def get_file_labels(service, file_id: str) -> Optional[List[Dict[str, Any]]]:
    """Gets the labels for a specific file."""
    print(f"\nFetching labels for file ID: {file_id}...")
    try:
        file_metadata = service.files().get(
            fileId=file_id,
            fields=FILE_FIELDS_FOR_LABELS
        ).execute()

        print(f"File Name: {file_metadata.get('name')}")
        label_info = file_metadata.get('labelInfo', {})
        labels = label_info.get('labels', [])

        if not labels:
            print("No labels found on this file.")
        else:
            print("Current labels:")
            for label in labels:
                print(f"  - Label ID: {label.get('id')}, Kind: {label.get('kind')}")
                # You can inspect label.get('fields', {}) if your label has fields
        return labels
    except HttpError as e:
        print(f"An error occurred while fetching labels for file '{file_id}': {e}")
        error_content = e.content.decode('utf-8') if e.content else "No content"
        try:
            error_json = json.loads(error_content)
            print(f"Error details: {json.dumps(error_json, indent=2)}")
        except json.JSONDecodeError:
            print(f"Raw error content: {error_content}")
    return None


def modify_file_labels(service, file_id: str, label_modifications: List[Dict[str, Any]]) -> Optional[
    List[Dict[str, Any]]]:
    """
    Modifies labels on a file.
    label_modifications is a list of LabelModification objects.
    """
    print(f"\nAttempting to modify labels for file ID: {file_id}...")
    request_body = {
        "kind": "drive#modifyLabelsRequest",
        "labelModifications": label_modifications
    }
    print("Modification request body:")
    print(json.dumps(request_body, indent=2))

    try:
        modified_labels_response = service.files().modifyLabels(
            fileId=file_id,
            body=request_body
        ).execute()

        modified_labels = modified_labels_response.get('modifiedLabels', [])
        print("Labels modified successfully!")
        if modified_labels:
            print("Response - Modified Labels (API response):")
            for label in modified_labels:
                print(f"  - Label ID: {label.get('id')}, Kind: {label.get('kind')}")
        else:
            print(
                "No labels were returned in the 'modifiedLabels' field of the response (this is normal for removals or if the label was already set/unset).")
        return modified_labels
    except HttpError as e:
        print(f"An error occurred while modifying labels for file '{file_id}': {e}")
        error_content = e.content.decode('utf-8') if e.content else "No content"
        try:
            error_json = json.loads(error_content)
            print(f"Error details: {json.dumps(error_json, indent=2)}")
        except json.JSONDecodeError:
            print(f"Raw error content: {error_content}")
    return None


def main():
    try:
        drive_service = get_drive_service(SERVICE_ACCOUNT_FILE, DELEGATED_ADMIN_EMAIL, DRIVE_SCOPES)
        if not drive_service:
            return

        # 1. Get initial labels for the specified file
        print("\n--- Step 1: Get Initial Labels ---")
        get_file_labels(drive_service, FILE_ID_TO_TEST)
        time.sleep(1)  # Short pause

        # 2. Add a label
        add_label_modification = [
            {
                "kind": "drive#labelModification",
                "labelId": '',
                "fieldModifications": [
                    {
                        "kind": "drive#labelFieldModification",
                        "fieldId": "textFieldId123",
                        "setTextValues": ["My Custom Text Value"]
                    }
                ]
            }
        ]
        modify_file_labels(drive_service, FILE_ID_TO_TEST, add_label_modification)
        time.sleep(2)  # Pause to allow propagation if needed

        # 3. Get labels again to verify the addition
        print("\n--- Step 3: Get Labels After Addition ---")
        get_file_labels(drive_service, FILE_ID_TO_TEST)
        time.sleep(1)
        #
        # 4. Remove the label
        print(f"\n--- Step 4: Remove Label '{LABEL_ID_TO_APPLY}' ---")
        remove_label_modification = [
            {
                "kind": "drive#labelModification",
                "labelId": LABEL_ID_TO_APPLY,
                "removeLabel": True
            }
        ]
        modify_file_labels(drive_service, FILE_ID_TO_TEST, remove_label_modification)
        time.sleep(2)  # Pause

        # 5. Get labels again to verify the removal
        print("\n--- Step 5: Get Labels After Removal ---")
        get_file_labels(drive_service, FILE_ID_TO_TEST)

        print("\n--- Demo Complete ---")

    except Exception as e:
        print(f"An unexpected error occurred in main: {e}")


if __name__ == "__main__":
    main()
