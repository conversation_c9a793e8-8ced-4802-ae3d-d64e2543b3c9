import io
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
from googleapiclient.errors import HttpError

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'
DRIVE_SCOPES = ['https://www.googleapis.com/auth/drive.readonly']

EXPORT_MIME_MAP = {
    "application/vnd.google-apps.document": [
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",  # MS Word
        "application/rtf",
        "application/vnd.oasis.opendocument.text",
        "text/html",
        "application/pdf",
        "text/x-markdown",
        "text/markdown",
        "application/epub+zip",
        "application/zip",
        "text/plain"
    ],
    "application/vnd.google-apps.vid": [
        "video/mp4"
    ],
    "application/vnd.google-apps.spreadsheet": [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # MS Excel
        "application/x-vnd.oasis.opendocument.spreadsheet",
        "text/tab-separated-values",
        "application/pdf",
        "text/csv",
        "application/zip",
        "application/vnd.oasis.opendocument.spreadsheet"
    ],
    "application/vnd.google-apps.jam": [
        "application/pdf"
    ],
    "application/vnd.google-apps.script": [
        "application/vnd.google-apps.script+json"
    ],
    "application/vnd.google-apps.presentation": [
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",  # MS PowerPoint
        "application/vnd.oasis.opendocument.presentation",
        "application/pdf",
        "text/plain"
    ],
    "application/vnd.google-apps.form": [
        "application/zip"
    ],
    "application/vnd.google-apps.drawing": [
        "image/svg+xml",
        "image/png",
        "application/pdf",
        "image/jpeg"
    ],
    "application/vnd.google-apps.site": [
        "text/plain"
    ],
    "application/vnd.google-apps.mail-layout": [
        "text/plain"
    ]
}


# Google Drive files fall into two categories:

# Google Docs types (Docs, Sheets, Slides)
# These are not stored as traditional binary files but in Google’s proprietary online document format—an internal structured data format.
# Therefore, there is no direct raw binary content to download. Instead, you must use the export_media API to export these files into common standard formats (like PDF, Word, Excel) before downloading.

# Regular binary files
# Files like images, PDFs, and other uploads exist as actual binary files and can be downloaded directly using the get_media API.

# Reference:
# https://developers.google.com/workspace/drive/api/guides/ref-export-formats
# https://developers.google.com/workspace/drive/api/guides/mime-types
def download_file(drive_service, file_id, file_name, mime_type):
    destination_file_path = file_name
    try:
        if mime_type in EXPORT_MIME_MAP:
            # If the file is a Google Docs type, use export_media to export it to a supported format
            export_mime = EXPORT_MIME_MAP[mime_type][0]  # We choose MS format for now
            print(f"File '{file_name}' is Google Docs type. Exporting as {export_mime} ...")
            request = drive_service.files().export_media(fileId=file_id, mimeType=export_mime)
        else:
            # Otherwise, treat the file as binary and download its content directly
            print(f"File '{file_name}' is NOT Google Docs type. Downloading directly ...")
            request = drive_service.files().get_media(fileId=file_id)

        fh = io.FileIO(destination_file_path, 'wb')
        downloader = MediaIoBaseDownload(fh, request)
        done = False
        while not done:
            status, done = downloader.next_chunk()
            print(f"Download {int(status.progress() * 100)}% for file '{file_name}'.")
        print(f"Downloaded file: {destination_file_path}\n")
    except HttpError as error:
        # Print error if download or export fails
        print(f"Download failed: {error}")


def get_export_formats(drive_service):
    # Actually, we can use about.get() to get exportFormats
    # EXPORT_MIME_MAP is from this API
    formats = drive_service.about().get(fields='exportFormats').execute()
    return formats


def main():
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_FILE, scopes=DRIVE_SCOPES
    ).with_subject(DELEGATED_ADMIN_EMAIL)

    drive_service = build('drive', 'v3', credentials=credentials)

    query_string = "'root' in parents and trashed = false"
    fields = "nextPageToken, files(id, name, mimeType)"
    page_token = None
    all_files = []
    # First, get all files in root folder
    while True:
        response = drive_service.files().list(
            q=query_string,
            pageSize=100,
            fields=fields,
            pageToken=page_token
        ).execute()

        files = response.get('files', [])
        all_files.extend(files)
        page_token = response.get('nextPageToken', None)
        if not page_token:
            break
    # Second, download all files in root folder
    for file in all_files:
        # Skip folders and shortcuts
        if file['mimeType'] in ['application/vnd.google-apps.folder', 'application/vnd.google-apps.shortcut']:
            print(f"Skipping {file['name']} ({file['mimeType']})")
            continue
        print(f"Downloading file: {file['name']} ({file['mimeType']})")
        download_file(drive_service, file['id'], file['name'], file['mimeType'])


if __name__ == '__main__':
    main()
