from typing import List, Dict, Optional
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'
SCOPES = ['https://www.googleapis.com/auth/admin.directory.user.readonly', 'https://www.googleapis.com/auth/admin.directory.group.readonly']


def get_user_list(customer_id: str = 'my_customer') -> List[Dict]:
    try:
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        delegated_credentials = credentials.with_subject(DELEGATED_ADMIN_EMAIL)
        service = build('admin', 'directory_v1', credentials=delegated_credentials)

        users = []
        page_token: Optional[str] = None

        while True:
            response = service.users().list(
                customer=customer_id,
                maxResults=300,
                orderBy='email',
                pageToken=page_token
            ).execute()

            users.extend(response.get('users', []))
            page_token = response.get('nextPageToken')
            if not page_token:
                break

        return users

    except HttpError as error:
        print(f'API error occurred: {error}')
    except Exception as e:
        print(f'Unexpected error occurred: {e}')

    return []

def get_all_groups():
    """
    获取所有 Google 群组，并判断 group_type（Internal/External）
    Returns:
        List[dict]: 包含 name, email, id, group_type 的群组列表
    """
    try:
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        delegated_credentials = credentials.with_subject(DELEGATED_ADMIN_EMAIL)
        service = build('admin', 'directory_v1', credentials=delegated_credentials)

        # 3. 获取所有群组
        groups = []
        page_token = None
        while True:
            result = service.groups().list(
                domain='qa.forticasb.com',  # 替换为你的域名
                customer='my_customer',
                maxResults=300,
                orderBy='email',
                pageToken=page_token
            ).execute()
            groups.extend(result.get('groups', []))
            page_token = result.get('nextPageToken')
            if not page_token:
                break

        # 4. 检查每个群组的成员类型
        for group in groups:
            members = []
            try:
                members_result = service.members().list(
                    groupKey=group['email']
                ).execute()
                members = members_result.get('members', [])
            except HttpError as e:
                print(f"Error fetching members for group {group['email']}: {e}")
                continue

            # 判断 group_type：只要有一个成员不是内部用户，则为 External
            group['group_type'] = 'Internal'
            for member in members:
                if 'email' in member and not member['email'].endswith('@qa.forticasb.com'):
                    group['group_type'] = 'External'
                    break

        # 5. 返回所需字段
        return [{
            'name': group.get('name'),
            'email': group.get('email'),
            'id': group.get('id'),
            'group_type': group.get('group_type', 'Unknown')
        } for group in groups]

    except HttpError as error:
        print(f"Google API error: {error}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []


def main():
    users = get_user_list()
    if not users:
        print("No users found.")
        return

    print(f"Total users retrieved: {len(users)}")
    for user in users:
        #email = user.get('primaryEmail')
        #full_name = user.get('name', {}).get('fullName', 'Unknown')
        #print(f"{email} (Name: {full_name})")
        print(f"{user}")


    all_groups = get_all_groups()
    print(f"Total group retrieved: {len(all_groups)}")
    for group in all_groups:
        print(group)


if __name__ == '__main__':
    main()
