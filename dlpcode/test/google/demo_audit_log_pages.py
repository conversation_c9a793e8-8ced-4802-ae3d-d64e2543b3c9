import json
import argparse
from google.oauth2 import service_account
from googleapiclient.discovery import build
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import pytz

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

SCOPES = ['https://www.googleapis.com/auth/admin.reports.audit.readonly']

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=SCOPES
).with_subject(DELEGATED_ADMIN_EMAIL)

reports_service = build('admin', 'reports_v1', credentials=credentials)


def query_activities(
        service,
        application_name: str,
        start_time: datetime,
        end_time: datetime,
        target_operations: List[str] = None,
        page_size: int = 1000
) -> Tuple[datetime, datetime, List[Dict]]:
    """
    Query activity logs with native eventName filtering
    
    Args:
        service: Initialized Admin SDK service
        application_name: e.g. 'drive', 'login', 'calendar'
        start_time: Start time (UTC datetime)
        end_time: End time (UTC datetime)
        event_names: List of event names to filter (e.g. ['create', 'edit'])
        page_size: Results per page (max 1000)
    
    Returns:
        tuple: (last_event_time, first_event_time, filtered_activities)
    """
    # Convert datetime to RFC 3339 format
    start_time_str = start_time.isoformat(timespec='seconds').replace('+00:00', 'Z')
    end_time_str = end_time.isoformat(timespec='seconds').replace('+00:00', 'Z')

    all_activities = []
    page_token = None

    while True:
        try:
            # Build request with optional eventName filter
            request = service.activities().list(
                userKey='all',
                applicationName=application_name,
                startTime=start_time_str,
                endTime=end_time_str,
                maxResults=page_size,
                pageToken=page_token
            )

            results = request.execute()
            current_page = results.get('items', [])

            if target_operations:
                current_page = [
                    act for act in current_page
                    if any(
                        event['name'] in target_operations
                        for event in act.get('events', [])
                    )
                ]
            if not current_page:
                break

            all_activities.extend(current_page)
            page_token = results.get('nextPageToken')

            if not page_token:
                break

        except Exception as e:
            print(f"API Error: {str(e)}")
            break

    # Extract time range from results
    first_event_time = datetime.fromisoformat(
        all_activities[-1]['id']['time'].replace('Z', '+00:00')
    ).replace(tzinfo=pytz.UTC) if all_activities else start_time

    last_event_time = datetime.fromisoformat(
        all_activities[0]['id']['time'].replace('Z', '+00:00')
    ).replace(tzinfo=pytz.UTC) if all_activities else end_time

    return last_event_time, first_event_time, all_activities


if __name__ == "__main__":
    # Set query time range (UTC)
    end_time = datetime.utcnow().replace(tzinfo=pytz.UTC)
    # start_time = end_time - timedelta(hours=1)
    start_time = end_time - timedelta(minutes=23)

    # Target operation types
    # target_ops = ['create', 'edit', 'delete']
    # target_ops = ['create', 'delete']
    # target_ops = ['add_to_folder']
    target_ops = ['create']
    # target_ops = None

    # Execute query
    last_time, first_time, activities = query_activities(
        service=reports_service,
        application_name='drive',
        start_time=start_time,
        end_time=end_time,
        target_operations=target_ops,
        page_size=500
    )

    for activitie in activities:
        print(f'{activitie["id"]} {activitie["events"]}')

    print(f'Query time range: {start_time.isoformat()} to {end_time.isoformat()}')
    print(f"Actual logs time range: {first_time.isoformat()} to {last_time.isoformat()}")
    print(f"Retrieved {len(activities)} log entries")
    # Calculate next query start time (latest event + 1 second)
    # next_query_start = last_time
    # print(f"Suggested start time for next query: {next_query_start.isoformat()}")
