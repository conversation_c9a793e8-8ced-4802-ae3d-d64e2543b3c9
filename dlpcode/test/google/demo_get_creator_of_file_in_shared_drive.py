import json
from typing import List, Optional, Dict, Any

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'
DRIVE_SCOPES = ['https://www.googleapis.com/auth/drive.readonly']


def get_drive_service(service_account_file: str, delegated_user: str, scopes: List[str]):
    credentials = service_account.Credentials.from_service_account_file(
        service_account_file, scopes=scopes
    ).with_subject(delegated_user)

    return build('drive', 'v3', credentials=credentials)


def get_first_revision_info(service, file_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve the first revision of the specified file.
    This revision typically represents the file's creator.
    """
    try:
        response = service.revisions().list(
            fileId=file_id,
            fields=(
                'revisions(id, modifiedTime, lastModifyingUser(emailAddress, permissionId, displayName, kind))'
            ),
            pageSize=1,
        ).execute()

        revisions = response.get('revisions', [])
        if revisions:
            return revisions[0]
        else:
            return None

    except HttpError as e:
        print(f"An error occurred while retrieving first revision: {e}")
        return None


def get_permission_details(service, file_id: str, permission_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve permission details (email, display name) associated with a given permission ID.
    """
    try:
        permission = service.permissions().get(
            fileId=file_id,
            permissionId=permission_id,
            fields='id, emailAddress, displayName',
            supportsAllDrives=True
        ).execute()
        return permission
    except HttpError as e:
        print(f"An error occurred while retrieving permission details: {e}")
        return None


def main():
    # The file ID to check
    file_id = '1n4gyFtrTW4gFtJnJreHK41g8KhTrMek2a37cqC_2or4'

    try:
        drive_service = get_drive_service(SERVICE_ACCOUNT_FILE, DELEGATED_ADMIN_EMAIL, DRIVE_SCOPES)

        # Get the first revision, which typically corresponds to the file's creator
        first_revision = get_first_revision_info(drive_service, file_id)
        if not first_revision:
            print("No revisions found for the file.")
            return

        print("First Revision Info:", json.dumps(first_revision, indent=2))

        # Extract the permission ID of the user who created the file
        last_modifying_user = first_revision.get('lastModifyingUser', {})
        permission_id = last_modifying_user.get('permissionId')

        if not permission_id:
            print("No permission ID found for the creator.")
            return

        print("Creator's Permission ID:", permission_id)

        # Retrieve the creator's details (email and display name)
        creator = get_permission_details(drive_service, file_id, permission_id)
        if creator:
            print("Creator Details:", json.dumps(creator, indent=2))
        else:
            print("No permission details found for the creator.")

    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
