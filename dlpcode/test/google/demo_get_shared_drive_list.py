import json
from typing import List, Optional

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
# DELEGATED_ADMIN_EMAIL = '<EMAIL>'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

DRIVE_SCOPES = ['https://www.googleapis.com/auth/drive.readonly']


# 获取 drive API service
def get_drive_service(service_account_file: str, delegated_user: str, scopes: List[str]):
    credentials = service_account.Credentials.from_service_account_file(
        service_account_file, scopes=scopes
    ).with_subject(delegated_user)
    return build('drive', 'v3', credentials=credentials)


def list_all_shared_drives(service, page_size: int = 100) -> List[dict]:
    all_drives = []
    page_token: Optional[str] = None

    while True:
        try:
            response = service.drives().list(
                pageSize=page_size,
                pageToken=page_token
            ).execute()

            drives = response.get('drives', [])
            all_drives.extend(drives)

            page_token = response.get('nextPageToken')
            if not page_token:
                print("All shared drives retrieved.")
                break
            else:
                print(f"Fetched {len(drives)} shared drives, continuing to next page...")
        except HttpError as e:
            print(f"An error occurred: {e}")
            break

    return all_drives

def get_all_shared_drives(service):
    """
    获取所有 Shared Drives 的 id、name 和 manager（管理员）
    Returns:
        List[dict]: 包含 id, name, manager_email 的 Shared Drives 列表
    """
    try:
        shared_drives = []
        page_token = None
        while True:
            result = service.drives().list(
                pageSize=100,
                pageToken=page_token,
                useDomainAdminAccess=True,
                fields="drives(id,name), nextPageToken"
            ).execute()
            
            shared_drives.extend(result.get('drives', []))
            page_token = result.get('nextPageToken')
            if not page_token:
                break
        
        print(f"Get dirves: {shared_drives}")
        for drive in shared_drives:
            try:
                permissions = service.permissions().list(
                    fileId=drive['id'],
                    fields="permissions(emailAddress,role)",
                    supportsAllDrives=True,
                    useDomainAdminAccess=True
                ).execute()
                
                managers = [
                    perm['emailAddress']
                    for perm in permissions.get('permissions', [])
                    if perm.get('role') == 'organizer'
                ]
                drive['manager_email'] = managers[0] if managers else None
            except HttpError as e:
                print(f"Error fetching managers for drive {drive['name']}: {e}")
                drive['manager_email'] = None

        return [{
            'id': drive.get('id'),
            'name': drive.get('name'),
            'manager_email': drive.get('manager_email')
        } for drive in shared_drives]

    except HttpError as error:
        print(f"Google API error: {error}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []

def main():
    try:
        drive_service = get_drive_service(SERVICE_ACCOUNT_FILE, DELEGATED_ADMIN_EMAIL, DRIVE_SCOPES)
        #shared_drives = list_all_shared_drives(drive_service, 100)
        shared_drives = get_all_shared_drives(drive_service)
        print(json.dumps(shared_drives, indent=2))
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
