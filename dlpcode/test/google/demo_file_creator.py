from google.oauth2 import service_account
from googleapiclient.discovery import build
from datetime import datetime, timedelta

# 你的配置
SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

# 使用 Reports API 所需 scope（注意不同于 Drive）
REPORTS_SCOPES = ['https://www.googleapis.com/auth/admin.reports.audit.readonly']

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE, scopes=REPORTS_SCOPES
).with_subject(DELEGATED_ADMIN_EMAIL)

# 构建 Reports API 客户端
reports_service = build('admin', 'reports_v1', credentials=credentials)

# 要查的文件 ID
target_file_id = '11tGUgYmVV336viewID9rOo1O1rHoAnP9_L5VS37opPE'
target_file_id = '1sFYwNzTsRI14Z8GmQXPZbzlcY7vU3x42lKguyvdmwDc'
# 设定时间范围（Reports API 仅支持最近 6 个月）
start_time = (datetime.utcnow() - timedelta(days=180)).isoformat() + 'Z'

# 调用 reports.activities.list API
results = reports_service.activities().list(
    userKey='all',
    applicationName='drive',
    eventName='create',
    startTime=start_time,
    maxResults=100,
).execute()

activities = results.get('items', [])
creator = None

for activity in activities:
    events = activity.get('events', [])
    for event in events:
        for param in event.get('parameters', []):
            if param['name'] == 'doc_id' and param['value'] == target_file_id:
                creator = activity['actor'].get('email')
                print(f"File {target_file_id} was created by: {creator}")
                break
    if creator:
        break

if not creator:
    print(f"No create event found for file {target_file_id} in the past 6 months.")
