import json
from typing import List, Optional

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

SERVICE_ACCOUNT_FILE = 'fourth-landing-460518-p2-107287671db2.json'
#DELEGATED_ADMIN_EMAIL = '<EMAIL>'
DELEGATED_ADMIN_EMAIL = '<EMAIL>'

DRIVE_SCOPES = ['https://www.googleapis.com/auth/drive.readonly', 'https://www.googleapis.com/auth/drive']
FIELDS = "nextPageToken,files(kind,driveId,fileExtension,copyRequiresWriterPermission,md5Checksum,contentHints(indexableText,thumbnail(image,mimeType)),writersCanShare,viewedByMe,mimeType,exportLinks,parents,thumbnailLink,iconLink,shared,lastModifyingUser,owners,headRevisionId,sharingUser,webViewLink,webContentLink,size,viewersCanCopyContent,permissions,hasThumbnail,spaces,folderColorRgb,id,name,description,starred,trashed,explicitlyTrashed,createdTime,modifiedTime,modifiedByMeTime,viewedByMeTime,sharedWithMeTime,quotaBytesUsed,version,originalFilename,ownedByMe,fullFileExtension,properties,appProperties,isAppAuthorized,teamDriveId,capabilities(canChangeViewersCanCopyContent,canMoveChildrenOutOfDrive,canReadDrive,canEdit,canCopy,canComment,canAddChildren,canDelete,canDownload,canListChildren,canRemoveChildren,canRename,canTrash,canReadRevisions,canReadTeamDrive,canMoveTeamDriveItem,canChangeCopyRequiresWriterPermission,canMoveItemIntoTeamDrive,canUntrash,canModifyContent,canMoveItemWithinTeamDrive,canMoveItemOutOfTeamDrive,canDeleteChildren,canMoveChildrenOutOfTeamDrive,canMoveChildrenWithinTeamDrive,canTrashChildren,canMoveItemOutOfDrive,canAddMyDriveParent,canRemoveMyDriveParent,canMoveItemWithinDrive,canShare,canMoveChildrenWithinDrive,canModifyContentRestriction,canAddFolderFromAnotherDrive,canChangeSecurityUpdateEnabled,canAcceptOwnership,canReadLabels,canModifyLabels,canModifyEditorContentRestriction,canModifyOwnerContentRestriction,canRemoveContentRestriction,canDisableInheritedPermissions,canEnableInheritedPermissions),hasAugmentedPermissions,trashingUser,thumbnailVersion,trashedTime,modifiedByMe,permissionIds,imageMediaMetadata(flashUsed,meteringMode,sensor,exposureMode,colorSpace,whiteBalance,width,height,location(latitude,longitude,altitude),rotation,time,cameraMake,cameraModel,exposureTime,aperture,focalLength,isoSpeed,exposureBias,maxApertureValue,subjectDistance,lens),videoMediaMetadata(width,height,durationMillis),shortcutDetails(targetId,targetMimeType,targetResourceKey),contentRestrictions,resourceKey,linkShareMetadata(securityUpdateEligible,securityUpdateEnabled),labelInfo(labels),sha1Checksum,sha256Checksum,inheritedPermissionsDisabled)"


def get_drive_service(service_account_file: str, delegated_user: str, scopes: List[str]):
    credentials = service_account.Credentials.from_service_account_file(
        service_account_file, scopes=scopes
    ).with_subject(delegated_user)

    return build('drive', 'v3', credentials=credentials)


def list_all_drive_files(service, folder_id: str, fields: str, page_size: int = 100) -> List[dict]:
    all_items = []
    page_token: Optional[str] = None
    query = f"'{folder_id}' in parents and trashed = false"

    while True:
        try:
            response = service.files().list(
                q=query,
                pageSize=page_size,
                fields=fields,
                pageToken=page_token
            ).execute()

            items = response.get('files', [])
            all_items.extend(items)

            page_token = response.get('nextPageToken')
            if not page_token:
                print("All items retrieved.")
                break
            else:
                print(f"Fetched {len(items)} items, continuing to next page...")
        except HttpError as e:
            print(f"An error occurred: {e}")
            break

    return all_items


def create_folder_atomically(service, parent_folder_id, folder_name):
    folder_metadata = {
        'name': folder_name,
        'mimeType': 'application/vnd.google-apps.folder',
        'parents': [parent_folder_id]
    }
    
    try:
        # 使用If-None-Match: * 头确保原子性创建
        '''
        folder = service.files().create(
            body=folder_metadata,
            fields='id',
            supportsAllDrives=True,
            headers={'If-None-Match': '*'}
        ).execute()
        return folder.get('id')
        '''
    
        request = service.files().create(
            body=folder_metadata,
            fields='id',
            supportsAllDrives=True
        )
        request.headers['If-None-Match'] = '*'  # 添加条件头
        
        folder = request.execute()
        return folder.get('id')
    except HttpError as error:
        if error.resp.status == 412:  # Precondition Failed
            raise FileExistsError(f"Folder '{folder_name}' already exists in the parent directory")
        raise


def main():
    try:
        drive_service = get_drive_service(SERVICE_ACCOUNT_FILE, DELEGATED_ADMIN_EMAIL, DRIVE_SCOPES)
        folder_id = 'root'  # or any specific folder ID like '1a2b3c4d5e...'
        #files = list_all_drive_files(drive_service, folder_id, FIELDS, 1)
        #print(json.dumps(files))
        folder_id = create_folder_atomically(drive_service, '1cgKwuNEbLnYthVMMptiWWPfyBZNJZXnX', 'FDT-Sensitive-Files-e2693e2d-mmm')
        print(f"Created folder with ID: {folder_id}")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
