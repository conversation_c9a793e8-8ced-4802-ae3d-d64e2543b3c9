#!/usr/bin/env python
# -*- coding: utf-8 -*-
import ipaddress
import json
import logging
import multiprocessing
import os
import re
import signal
import subprocess
import sys
import traceback
import shlex
import glob
import datetime
import uuid
from logging.handlers import RotatingFileHandler

import readline

import psutil
import socket
import struct
import fcntl

#import util.repl as repl
from system import repl
from system import misc
from system import dlp_time
from system import dlp_shell
from system import dlp_cmd
from system import dlp_file

from system.cli.base_nodes import Console
from system.cli.base_nodes import FortiNode

from system.global_conf import global_conf
from domain_model import user_interface
from domain_model.psql_obj import global_dbobj
from system.system_platform import Platform, GAControl
#from system_control import tester_license
from system.i18n_initializer import get_str
from system import interface as sys_intf
from flask_module.system_reqmodel import InterfaceCfg as port_model
from system.system_config import interface_config as intf_cfg
from system.system_config import route_config as route_cfg
from system import route as sys_route
from system.system_log import record_event_log, LogLevel, LogType, LogAction
from system import system_util


# from util.db.db_wrapper import DBWrapper
# db = DBWrapper()

from util.postgresql_db.db_wrapper import DBWrapper
db = DBWrapper()

is_debug = Platform().is_debug()
# if the GA version will disable the shell function.
is_GA_version = GAControl().is_ga_version()

fdtlog_debug_path = '/var/log/dlp'
os.makedirs(fdtlog_debug_path, exist_ok=True)
command_log_handler = logging.handlers.RotatingFileHandler(filename=os.path.join(fdtlog_debug_path, 'command.log'),
                                                           mode='a',
                                                           maxBytes=1024 * 512,
                                                           backupCount=1,
                                                           encoding='utf-8',
                                                           delay=False)
logging.basicConfig(handlers=[command_log_handler],
                    level=logging.DEBUG,
                    format="[%(asctime)s] %(levelname)s [%(name)s.%(funcName)s:%(lineno)d] %(message)s",
                    datefmt='%Y-%m-%dT%H:%M:%S')

def netmask_is_valid(user, port, ip, netmask):
    try:
        ipaddress.ip_network(ip + '/' + netmask, strict=False)
    except ValueError as e:
        errmsg = 'Invalid netmask! {0}'.format(e)
        print(errmsg)
        record_event_log(user=user, level=LogLevel.WARNING.value,
                         message=f"Set port {port} config failed: {errmsg}",
                         desc='Set port config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        return False

    return True


def is_ipv4_valid(user, port, ip):
    try:
        obj = ipaddress.ip_address(ip)
    except ValueError as e:
        errmsg = f'Invalid address! "{ip}" does not appear to be an IPv4 address {e}'
        record_event_log(user=user, level=LogLevel.WARNING.value,
                         message=f"Set port {port} config failed: {errmsg}",
                         desc='Set port config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        return False
    return True


def is_ipv6_valid(user, port, ip):
    try:
        obj = ipaddress.ip_address(ip)
    except ValueError as e:
        errmsg = f'Invalid address! "{ip}" does not appear to be an IPv6 address {e}'
        print(errmsg)
        record_event_log(user=user, level=LogLevel.WARNING.value,
                         message=f"Set port {port} config failed: {errmsg}",
                         desc='Set port config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        return False
    return True

class SessionTimeoutError(Exception):
    pass

def InputWrapper(prompt):
    signal.alarm(global_conf["session_timeout"])
    return input(prompt)

class EnableCmd(FortiNode):
    def __init__(self, info={}, runnable=None):
        super().__init__(name="enable", help="Enable.", info=info, runnable=runnable)
        self.add_arg(EnterArg(info=info))

class DisableCmd(FortiNode):
    def __init__(self, info={}, runnable=None):
        super().__init__(name="disable", help="Disable.", info=info, runnable=runnable)
        self.add_arg(EnterArg(info=info))

class EnterArg(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="<Enter>", help="", info=info)

class RestArg(FortiNode):
    def __init__(self, name,  help, info={}):
        super().__init__(name=name, help=help, info=info)
        self.is_rest_arg = True

    def walk(self, commands_list=[], completing=''):
        self.remaining = commands_list
        return self, completing



class EndCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="end", help="End and save last config.", info=info, runnable=lambda command: -1)
        self.add_arg(EnterArg(info=info))

class ExitCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="exit", help="Exit the CLI.", info=info, runnable=lambda command: -1)
        self.add_arg(EnterArg(info=info))

def SessionTimeoutHandler(*args):
    if repl.backup_restore_in_progress:
        return
    print("\nSession is timeout, Exit!!!\n")
    raise SessionTimeoutError("Session is timeout")


class SetCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="set", help="Set configuration.", info=info)

class StatusCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="status", help="System status.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        print(self.get_system_status(self.info))

    @classmethod
    def get_system_status(cls, info):
        from system.dlp_cmd import get_sys_uuid
        from system.dlp_cmd import get_sys_sn

        version = info['prod_name'] + ' ' + info['major_num'] + '.' + \
                  info['minor_num'] + '.' + info['patch_num'] + ' build' + \
                  info['build_num'] + ' ' + info['build_date']
        msg = 'Version: {0}\n'.format(version)
        #msg += 'Serial-Number: {0}\n'.format(info['sn'])

        hostname = repl.get_system_hostname()
        msg += 'Hostname: {0}\n'.format(hostname)
        sn = get_sys_sn()
        if sn:
            msg += 'Serial-Number: {0}\n'.format(sn)
        device_uuid = get_sys_uuid()
        if device_uuid:
            msg += "Device UUID: {0}\n".format(device_uuid)
        time = dlp_time.get_system_time()
        msg += 'System time: {0}\n'.format(time)
        uptime = dlp_time.get_system_uptime()
        msg += 'System uptime: {0}\n'.format(uptime)
        msg += 'Data Disk: {}\n'.format(dlp_cmd.get_log_disk_status())
        # memdisk mode for logdisk
        memdisk = "/data/etc/log_memdisk"
        if os.path.exists(memdisk):
            try:
                with open(memdisk) as f:
                    content = f.read()
                msg += 'Log Memory: {}M\n'.format(int(content.strip(" \n")))
            except:
                pass
        ######

        return msg

class SetHostnameCmd(FortiNode):
    def __init__(self, info={}, data={}):
        super().__init__(name="hostname", help="Set hostname.", info=info)
        self.data = data
        self.add_arg(FortiNode(name="<string>", help="new hostname", info=info))

    def run(self, command_string):
        cmd_list = command_string.split(' ')
        hostname = cmd_list[-1] if len(cmd_list) == 3 else ''
        regex_hostname = '[0-9a-zA-Z\-\_]+[ ]*$'
        MAX_HOSTNAME_LENGTH = 64
        if hostname and re.match(regex_hostname, hostname) and len(hostname) < MAX_HOSTNAME_LENGTH:
            self.data['hostname'] = hostname
        else:
            from system.i18n_initializer import get_str
            errmsg = f'Invalid format! {get_str("hostname.hint")}'
            print(errmsg)
            record_event_log(user=self.info.get('user', ''), level=LogLevel.WARNING.value,
                             message=f"Change the host name to {hostname} failed",
                             desc='Edit host name', type=LogType.SYSTEM.value,
                             action=LogAction.SETUP.value)
        return 0


class HostnameConsole(Console):
    def __init__(self, prompt="Prompt", prompt_delim=">", info={}):
        Console.__init__(self, prompt, prompt_delim, info)
        self.prev_prompt = ''
        self.data = {}
        self.add_leaf(SetCmd(info=info)).add_leaf(SetHostnameCmd(self.info, self.data))
        self.add_leaf(EndCmd(info=info))

    def loop(self):
        from system import fabric
        readline.set_completer(self.complete)
        readline.set_completion_display_matches_hook(self.print_all_leafs)
        self.prev_prompt = self.info.get('prompt', 'FortiData')

        while 1:
            try:
                self.info['prompt'] = f'{repl.get_system_hostname()} ({self.prompt}) {self.prompt_delim} '
                sys.stdout.write("\r")
                sys.stdout.write("\033[K")
                input_ = InputWrapper(self.info['prompt'])
                if not input_.strip():
                    pass
                else:
                    result = self.walk_and_run(input_)
                    if result and result < 0:
                        user = self.info['user']
                        if 'hostname' in self.data:
                            hostname = self.data.get('hostname', repl.glob_hostname)
                            repl.set_hostname(hostname)
                            repl.glob_hostname = hostname
                            fabric.update_config_n_reload_cfg({'hostname': hostname})
                            record_event_log(user=user, level=LogLevel.INFO.value,
                                             message=f"Change the host name to {hostname} successfully",
                                             desc='Edit host name', type=LogType.SYSTEM.value,
                                             action=LogAction.SETUP.value)
                        return result
            except (KeyboardInterrupt, EOFError, Exception):
                logging.error(traceback.format_exc())
                print()
                pass
        return 0


class ConfigHostnameCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="hostname", help="Configure hostname.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        HostnameConsole(prompt='hostname'.format(repl.get_system_hostname()), prompt_delim='#',
                        info=self.info).loop()
        return 0

class IPCmd(FortiNode):
    def __init__(self, info={}, data={}, port=''):
        super().__init__(name="ip", help="IPv4 configuration.", info=info)
        self.data = data
        self.port = port
        self.add_arg(FortiNode(name="<ip>", help="IP address", info=info)) \
            .add_arg(FortiNode(name="<mask>", help="mask", info=info))

    def run(self, command_string):
        user = self.info.get('user', '')
        ip_re = '[a-z ]*' + \
                '\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}[ ]*' + \
                '\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$'
        form = command_string.split()
        if len(form) != 4:
            print('Command parse error!')
        elif re.findall(ip_re, command_string) and len(form) == 4:
            if is_ipv4_valid(user, self.port, form[2]) and netmask_is_valid(user, self.port, form[2], form[3]):
                self.data[self.port]['ip'] = form[2]
                self.data[self.port]['netmask'] = form[3]
        else:
            errmsg = 'Invalid IP address'
            print(errmsg)
            record_event_log(user=self.info.get('user', ''), level=LogLevel.WARNING.value,
                             message=f"Set port {self.port} config failed: {errmsg}",
                             desc='Set port config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        return 0


class IPv6Cmd(FortiNode):
    def __init__(self, info={}, data={}, port=''):
        super().__init__(name="ip6", help="IPv6 configuration.", info=info)
        self.data = data
        self.port = port
        self.add_arg(FortiNode(name="<ipv6>", help="IPv6 address", info=info)) \
            .add_arg(FortiNode(name="<mask>", help="IPv6 mask", info=info))

    def run(self, command_string):
        form = command_string.split()
        if len(form) == 4:
            user = self.info.get('user', '')
            if is_ipv6_valid(user, self.port, form[2]) and netmask_is_valid(user, self.port, form[2], form[3]):
                self.data[self.port]['ipv6'] = form[2]
                self.data[self.port]['ipv6Netmask'] = form[3]
        else:
            print('Command parse error!')
        return 0


class ModeCmd(FortiNode):
    def __init__(self, info={}, data={}, port=''):
        super().__init__(name="mode", help="Interface ipv4 mode configuration.", info=info)
        self.data = data
        self.port = port
        self.add_arg(FortiNode(name="<mode>", help="manual/dhcp", info=info))

    def run(self, command_string):
        item = command_string.split()
        if item[2] == "manual" or item[2] == "dhcp":
            self.data[self.port]['mode'] = item[2]
        else:
            print('Command parse error!')
        return 0


class IPv6ModeCmd(FortiNode):
    def __init__(self, info={}, data={}, port=''):
        super().__init__(name="ip6-mode", help="Interface ipv6 mode configuration.", info=info)
        self.data = data
        self.port = port
        self.add_arg(FortiNode(name="<mode>", help="manual/dhcp", info=info))

    def run(self, command_string):
        item = command_string.split()
        if item[2] == "manual" or item[2] == "dhcp":
            self.data[self.port].setdefault('ipv6Mode', item[2])
        else:
            print('Command parse error!')
        return 0

class SetCmdIP(FortiNode):
    def __init__(self, info={}, data={}, port=''):
        super().__init__(name="set", help="Set configuration.", info=info)
        self.data = data
        self.port = port
        self.add_leaf(IPCmd(self.info, self.data, self.port))
        #self.add_leaf(IPv6Cmd(self.info, self.data, self.port))
        self.add_leaf(ModeCmd(self.info, self.data, self.port))
        #self.add_leaf(IPv6ModeCmd(self.info, self.data, self.port))

class EditIPConsole(Console):
    def __init__(self, prompt="Prompt", prompt_delim=">", info={}, data={}, port=''):
        Console.__init__(self, prompt, prompt_delim, info)
        self.prev_prompt = ''

        self.data = data
        self.port = port
        # self.add_leaf(SetCmd(info=info)).add_leaf(IPCmd(self.info, data, port))
        self.add_leaf(SetCmdIP(self.info, self.data, self.port))

        self.add_leaf(FortiNode(name="next", help="Set next ip.", info=info, runnable=lambda command: -1))
        self.add_leaf(EndCmd(info=info))

    def loop(self):
        readline.set_completer(self.complete)
        readline.set_completion_display_matches_hook(self.print_all_leafs)
        self.prev_prompt = self.info.get('prompt', 'FortiData')

        while 1:
            try:
                self.info['prompt'] = f'{repl.get_system_hostname()} ({self.prompt}) {self.prompt_delim} '
                sys.stdout.write("\r")
                sys.stdout.write("\033[K")
                input_ = InputWrapper(self.info['prompt'])
                if not input_.strip():
                    pass
                else:
                    result = self.walk_and_run(input_)
                    if result and result < 0:
                        self.info['prompt'] = self.prev_prompt
                        self.update_interface_config(self.info, self.data)
                        return result
            except (KeyboardInterrupt, EOFError, Exception):
                logging.error(traceback.format_exc())
                print()
                pass
        return 0
    def update_interface_config(self, info, data):
        if not data[self.port]:
            return


        user = self.info['user']
        update_cfg = data[self.port]

        if update_cfg.get('ip') and update_cfg.get('netmask'):
            update_cfg['mode'] = 'manual'

        if update_cfg.get('ipv6') and update_cfg.get('ipv6Netmask'):
            update_cfg['ipv6Mode'] = 'manual'


        cfg = port_model.parse_obj(update_cfg)
        cfg.name = self.port

        erro_code, message = sys_intf.update_interface_config(cfg)
        if erro_code.value != 0:
            print(message)
            record_event_log(user=user, level=LogLevel.WARNING.value,
                             message=f"Set port {self.port} config failed: {message}",
                             desc='Set port config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        else:
            record_event_log(user=user, level=LogLevel.INFO.value,
                             message=f"Set port {self.port} config successfully: {message}",
                             desc='Set port config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)


class EditCmd(FortiNode):
    def __init__(self, info={}, data={}):
        super().__init__(name="edit", help="Edit port.", info=info)
        self.data = data
        self.add_arg(FortiNode(name="<string>", help="interface name", info=info))

    def run(self, command_string):
        ary = command_string.split()

        self.info['interfaces'] =  intf_cfg.get_interfaces_config()
        if len(ary) != 2 or not self.port_is_valid(ary[1], self.info):
            print('Invalid port!')
        else:
            self.data[ary[1]] = {}
            EditIPConsole(prompt=f'{ary[1]}',
                          prompt_delim='#',
                          info=self.info,
                          data=self.data,
                          port=ary[1]).loop()
        return 0

    def port_is_valid(self, port, info):

        intf_cfg = info['interfaces']

        if port not in intf_cfg:
            return False

        return True

class FullConfigurationCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="full-configuration", help="Show full configuration.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        msg = self.get_system_status(info=self.info)
        msg += self.get_hostname_configuration(info=self.info)
        msg += self.get_interface_configuration(info=self.info)
        msg += self.get_route_configuration(info=self.info)
        msg += self.get_fds_proxy_setting(info=self.info)
        # msg += self.get_mode_setting(info=self.info)
        msg += self.get_csf_configuration(info=self.info)
        print(msg)

    def get_hostname_configuration(self, info):
        msg = 'config system hostname\n' + \
              '    set hostname {}\n' + \
              'end\n'
        return msg.format(repl.glob_hostname)

    def get_interface_configuration(self, info):
        #info['cfg'] = repl.get_network_cfg()
        info['interfaces'] = intf_cfg.get_system_interfaces_config()

        msg = 'config system interface\n'

        for ifname, if_config in self.info.get('interfaces', {}).items():
            msg += "    edit {0}\n".format(ifname)
            if if_config.get('ip') and if_config.get('netmask'):
                msg += '        set ip {0} {1}\n'.format(if_config['ip'], if_config['netmask'])
            # if if_config.get('ipv6') and if_config.get('ipv6Netmask'):
            #     msg += '        set ip6 {0} {1}\n'.format(if_config['ipv6'], if_config['ipv6Netmask'])
            if if_config.get('mode'):
                msg += '        set mode {0}\n'.format(if_config['mode'])
            # if if_config.get('ipv6Mode'):
            #     msg += '        set ip6-mode {0}\n'.format(if_config['ipv6Mode'])
            msg += '    next\n'
        msg += 'end\n'

        return msg

    def get_route_configuration(self, info):
        msg = 'config system route\n'
        info['routes'] = route_cfg.get_route_config()
        for value in self.info['routes'].values():
            if value.get('dst') == 'default':
                msg += '    set gateway {}\n'.format(value['gateway'])
        msg += 'end\n'
        return msg

    def get_system_status(self, info):
        msg = StatusCmd.get_system_status(info=info)
        msg = "#" + msg
        msg = msg.replace('\n', '\n#')
        replace_last_occurrence = lambda my_str, old, new: (my_str[::-1].replace(old[::-1], new[::-1], 1))[::-1]
        return replace_last_occurrence(msg, '\n#', '\n')

    def get_fds_proxy_setting(self, info):
        proxy_config = db.fdsproxyserver.find_one() or {}

        msg = "config system tunneling"
        if proxy_config.get('address', ''):
            msg += '\n    set address {}'.format(proxy_config.get('address', ''))
        if proxy_config.get('port', ''):
            msg += '\n    set port {}'.format(proxy_config.get('port', ''))
        if proxy_config.get('username', ''):
            msg += '\n    set username {}'.format(proxy_config.get('username', ''))
        if proxy_config.get('password', ''):
            import base64
            msg += '\n    set password ********'
        if proxy_config.get('status', ''):
            msg += '\n    set status {}'.format('enable' if proxy_config.get('status', '') == '1' else 'disable')
        msg += '\nend\n'
        return msg

    def get_mode_setting(self, info):
        hwmode_config = repl.read_hwmode_config()

        msg = "config system setting"
        if 'MaintainerLogin' in hwmode_config:
            msg += '\n    set admin-maintainer {}'.format(hwmode_config.get('MaintainerLogin', ''))
        msg += '\nend\n'
        return msg

    @classmethod
    def get_csf_configuration(cls, info):
        from system.fabric_json import CsfJson
        csf_json = CsfJson()
        csf_dic = csf_json.get_all_config()
        csf_status = 'enable' if csf_dic.get('enable') else 'disable'
        msg = 'config system csf\n' + \
              '    set ip {}\n' + \
              '    set port {}\n' + \
              '    set status {}\n' + \
              'end\n'
        return msg.format(csf_dic.get('upstream_ip'),
                          csf_dic.get('upstream_port'),
                          csf_status)
class ShowCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="show", help="Show configuration.", info=info)

class ShowSystemCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="system", help="System operation configuration.", info=info)

class ShowInterfaceCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="interface", help="Show network interfaces and configurations.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        #self.info['interfaces'] = intf_cfg.get_interfaces_config()

        system_ports = sys_intf.get_all_interfaces()
        msg = 'config system interface\n'

        for port in system_ports:
            msg += "    edit {0}\n".format(port['name'])
            if port.get('address') and port.get('netmask'):
                msg += '        set ip {0} {1}\n'.format(port['address'], port['netmask'])
            if port.get('ipv6') and port.get('ipv6Netmask'):
                msg += '        set ip6 {0} {1}\n'.format(port['ipv6'], port['ipv6Netmask'])
            if port.get('mode'):
                msg += '        set mode {0}\n'.format(port['mode'])
            if port.get('ipv6Mode'):
                msg += '        set ip6-mode {0}\n'.format(port['ipv6Mode'])
            msg += '    next\n'
        msg += 'end'
        print(msg)

        return 0

class InterfaceConsole(Console):
    def __init__(self, prompt="Prompt", prompt_delim=">", info={}):
        Console.__init__(self, prompt, prompt_delim, info)
        self.prev_prompt = ''
        self.data = {}
        self.add_leaf(EditCmd(self.info, self.data))
        self.add_leaf(EndCmd(info=info))
        self.add_leaf(ShowCmd(self.info)).add_leaf(ShowSystemCmd(self.info)).add_leaf(ShowInterfaceCmd(self.info))

    def loop(self):
        self.prev_prompt = self.info.get('prompt', 'FortiData')
        while 1:
            try:
                self.info['prompt'] = f'{repl.get_system_hostname()} ({self.prompt}) {self.prompt_delim} '
                readline.set_completer(self.complete)
                readline.set_completion_display_matches_hook(self.print_all_leafs)

                sys.stdout.write("\r")
                sys.stdout.write("\033[K")
                input_ = InputWrapper(self.info['prompt'])
                if not input_.strip():
                    pass
                else:
                    result = self.walk_and_run(input_)
                    if result and result < 0:
                        self.info['prompt'] = self.prev_prompt
                        return result
            except SessionTimeoutError:
                return -1
            except (KeyboardInterrupt, EOFError, Exception) as e:
                logging.error(traceback.format_exc())
                print(traceback.format_exc())
                pass
        return 0

def get_default_routeID():
    cfg = route_cfg.get_route_config()
    for key, value in cfg.items():
        if value['dst'] == 'default':
            return key
    return None

class RouteConsole(Console):
    def __init__(self, prompt="Prompt", prompt_delim=">", info={}):
        Console.__init__(self, prompt, prompt_delim, info)
        self.prev_prompt = ''
        self.data = {}
        # self.add_leaf(SetCmd(info=info)).add_leaf(GatewayCmd(self.info, self.data))
        self.add_leaf(SetCmdRoute(self.info, self.data))
        self.add_leaf(EndCmd(info=info))

    def loop(self):
        readline.set_completer(self.complete)
        readline.set_completion_display_matches_hook(self.print_all_leafs)
        self.prev_prompt = self.info.get('prompt', 'FortiData')
        while 1:
            try:
                self.info['prompt'] = f'{repl.get_system_hostname()} ({self.prompt}) {self.prompt_delim} '
                sys.stdout.write("\r")
                sys.stdout.write("\033[K")
                input_ = InputWrapper(self.info['prompt'])
                if not input_.strip():
                    pass
                else:
                    result = self.walk_and_run(input_)
                    if result and result < 0:
                        self.info['prompt'] = self.prev_prompt
                        self.update_default_gateway(self.info, self.data)
                        return result
            except (KeyboardInterrupt, EOFError, Exception):
                logging.error(traceback.format_exc())
                print()
                pass
        return 0

    def update_default_gateway(self, info, data):
        user = info.get('user', '')
        if not data.get('gateway') and not data.get('ipv6Gateway'):
            return
        gw = data.get('gateway') or data.get('ipv6Gateway') or "''"

        id = get_default_routeID()
        # err, msg = sys_route.update_default_route(self.data.get('gateway', ''), self.data.get('ipv6Gateway', ''))
        err, msg = sys_route.update_static_route(None, "default", self.data.get('gateway', ''), id)
        if err != 0:
            print(msg)
            record_event_log(user=user, level=LogLevel.WARNING.value,
                             message=f"Set gateway {gw} failed: {msg}",
                             desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        else:
            record_event_log(user=user, level=LogLevel.INFO.value,
                             message=f"Set gateway {gw} successfully",
                             desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)


class SetCmdRoute(FortiNode):
    def __init__(self, info={}, data={}):
        super().__init__(name="set", help="Set configuration.", info=info)
        self.data = data
        self.add_leaf(GatewayCmd(self.info, self.data))
        #self.add_leaf(Gateway6Cmd(self.info, self.data))


class GatewayCmd(FortiNode):
    def __init__(self, info={}, data={}):
        super().__init__(name="gateway", help="Configure route.", info=info)
        self.data = data
        self.add_arg(FortiNode(name="<ip>", help="gateway ip", info=info))

    def run(self, command_string):
        user = self.info.get('user', '')
        route_re = '[a-z ]*\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$'
        if re.findall(route_re, command_string):
            form = command_string.split()[2]
            if self.gateway_is_valid(user, form):
                self.data['gateway'] = form
        else:
            errmsg = 'Invalid format!'
            print(errmsg)
            record_event_log(user=user, level=LogLevel.WARNING.value,
                             message=f"Set gateway failed: {errmsg}",
                             desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)

    def gateway_is_valid(self, user, gw):
        try:
            ip = ipaddress.ip_address(gw)
            if ip.is_multicast or ip.is_loopback or ip.is_unspecified:
                errmsg = f'Invalid gateway! "{gw}" is not a valid unicast address'
                print(errmsg)
                record_event_log(user=user, level=LogLevel.WARNING.value,
                                 message=f"Set gateway {gw} failed: {errmsg}",
                                 desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
                return False
            return True
        except ValueError as e:
            logging.error(traceback.format_exc())
            errmsg = f'Invalid gateway! {e}'
            print(errmsg)
            record_event_log(user=user, level=LogLevel.WARNING.value,
                             message=f"Set gateway {gw} failed: {errmsg}",
                             desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
            return False

class Gateway6Cmd(FortiNode):
    def __init__(self, info={}, data={}):
        super().__init__(name="gateway6", help="Configure route.", info=info)
        self.data = data
        self.add_arg(FortiNode(name="<ipv6>", help="gateway6 ip", info=info))

    def run(self, command_string):
        user = self.info.get('user', '')
        route_re = '[a-z ]*'
        if re.findall(route_re, command_string):
            form = command_string.split()[2]
            if self.gateway6_is_valid(user, form):
                self.data['ipv6Gateway'] = form
        else:
            errmsg = 'Invalid format!'
            print(errmsg)
            record_event_log(user=user, level=LogLevel.WARNING.value,
                             message=f"Set gateway failed: {errmsg}",
                             desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)

    def gateway6_is_valid(self, user, gw):
        try:
            ip = ipaddress.ip_address(gw)
            if ip.is_multicast or ip.is_loopback or ip.is_unspecified or ip.is_link_local:
                errmsg = f'Invalid gateway6! "{gw}" is not a valid unicast address'
                print(errmsg)
                record_event_log(user=user, level=LogLevel.WARNING.value,
                                 message=f"Set gateway6 {gw} failed: {errmsg}",
                                 desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
                return False
            return True
        except Exception as e:
            errmsg = 'Invalid gateway6! {0}'.format(e)
            print(errmsg)
            logging.error(traceback.format_exc())
            record_event_log(user=user, level=LogLevel.WARNING.value,
                             message=f"Set gateway6 {gw} failed: {errmsg}",
                             desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
            return False


class ConfigInterfaceCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="interface", help="Configure interfaces.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        # from system_config.hwmode import HWMode
        # hwmode = HWMode()
        # dic = hwmode.get_all_config()
        # work_mode = repl.read_hwmode_config('TesterWorkMode')
        # cm_enabled = dic.get('TesterCMSlave') == 'Enable'
        # if cm_enabled:
        #     print(repl.glob_workmode_msg.format(get_str("system.cmClient.name")))
        #     return
        # elif work_mode != 'Standalone':
        #     print(repl.glob_workmode_msg.format(work_mode))
        #     return

        InterfaceConsole(prompt='interface', prompt_delim='#',
                         info=self.info).loop()

class ConfigRouteCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="route", help="Configure route.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        self.info['cfg'] = intf_cfg.get_interfaces_config()
        RouteConsole(prompt="route".format(repl.get_system_hostname()),
                     prompt_delim='#',
                     info=self.info).loop()
        return 0



def validate_password(value: str) -> str:
    if len(value) < 8:
        raise ValueError("Password must be at least 8 characters long")
    if not any(char.islower() for char in value):
        raise ValueError("Password must contain at least one lowercase letter")
    if not any(char.isupper() for char in value):
        raise ValueError("Password must contain at least one uppercase letter")
    if not any(char.isdigit() for char in value):
        raise ValueError("Password must contain at least one number")
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", value):
        raise ValueError("Password must contain at least one special character (!@#$%^&* etc.)")
    return value

class SettingsConsole(Console):
    def __init__(self, prompt="Prompt", prompt_delim=">", info={}):
        Console.__init__(self, prompt, prompt_delim, info)
        self.prev_prompt = ''
        self.data = {}
        setcmd = SetCmd(info=info)
        try:
            #from postgresql_interface import ftsuser_interface
            from domain_model import user_interface
            class AdminPassword(FortiNode):

                def __init__(self, info):
                    super().__init__(name='admin-password', help='Reset admin password', info=info)
                    self.add_arg(FortiNode(name='<passwd>',
                                        help='Reset admin password. Password must be at least 8 characters long, contain at least one lowercase letter, \
                                              one uppercase letter, one number, and one special character (!@#$%^&* etc.)',
                                        info=info)).add_arg(EnterArg(info=info))

                def run(self, command_string):
                    cmds = command_string.split()
                    if len(cmds) != 3:
                        print('Invalid Format')
                    else:
                        new_pass = cmds[-1]

                        try:
                            validate_password(new_pass)

                            user_dict = user_interface.get_by_name('admin')
                            if user_dict:
                                print(user_interface.reset_user_password({'_id': user_dict['_id'],
                                                                         'password': new_pass})['msg'])
                            else:
                                print("Cannot find admin account")
                        except ValueError as e:
                            print(f"Error: {e}")
                    return 0

            # setcmd.add_leaf(AdminMaintainerLoginCmd(info=info))
            is_maintainer = self.info['user'] == user_interface.ACCOUNT_MAINTAINER
            if is_maintainer:
                setcmd.add_leaf(AdminPassword(info=info))
            self.add_leaf(setcmd)
            self.add_leaf(EndCmd(info=info))
        except:
            traceback.print_exc()

    def loop(self):
        readline.set_completer(self.complete)
        readline.set_completion_display_matches_hook(self.print_all_leafs)
        self.prev_prompt = self.info.get('prompt', 'FortiData')

        while 1:
            try:
                self.info['prompt'] = f'{repl.get_system_hostname()} ({self.prompt}) {self.prompt_delim} '
                sys.stdout.write("\r")
                sys.stdout.write("\033[K")
                input_ = InputWrapper(self.info['prompt'])
                if not input_.strip():
                    pass
                else:
                    result = self.walk_and_run(input_)
                    if result and result < 0:
                        return 0
            except (KeyboardInterrupt, EOFError, Exception):
                logging.error(traceback.format_exc())
                print()
                pass
        return 0

class ConfigSettingsCmd(FortiNode):
    def __init__(self, info={}):
        #super().__init__(name="setting", help="Configure system setting. (Maintainer Login, Telnet Daemon...)",
        # super().__init__(name="setting", help="Configure system setting. (Maintainer Login)",
        super().__init__(name="setting", help="Configure system setting.",
                         info=info,
                         runnable=lambda command: SettingsConsole(prompt='setting'.format(repl.get_system_hostname()),
                                                                  prompt_delim='#',
                                                                  info=self.info).loop())
        self.add_arg(EnterArg(info=info))


class ProxyAddress(FortiNode):
    def __init__(self, info):
        super().__init__(name="address", help="Web proxy IP address or FQDN.", info=info)
        self.add_arg(FortiNode(name='<proxy_address>',
                               help="Address of proxy.",
                               info=info)).add_arg(EnterArg(info=info))

    def run(self, command_string):
        proxy_address = command_string.split()[-1]
        # Code change by AngelaShih for DB Migration
        config = list(db.fdsproxyserver.find())
        if len(config) == 1:
            db.fdsproxyserver.update(config[0], {'$set': {'address': proxy_address}})
        else:
            db.fdsproxyserver.drop()
            db.fdsproxyserver.insert_one({'address': proxy_address})


class ProxyUnsetCmd(FortiNode):
    def __init__(self, name, help, info):
        super().__init__(name=name, help=help, info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        try:
            db.fdsproxyserver.update_many({}, {'$unset': {self.name: True}})
        except Exception:
            pass


class ProxyPassword(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="password", help="Web proxy password.", info=info)
        self.add_arg(FortiNode(name='<password>', help="Password of proxy.", info=info)).add_arg(EnterArg(info=info))

    def run(self, command_string):
        from util.random_password.service import RandomPasswordService
        passwd = command_string.split()[-1]
        enc_psw = RandomPasswordService.encrypt_proxy_pwd(passwd)
        config = list(db.fdsproxyserver.find())
        if len(config) == 1:
            db.fdsproxyserver.update(config[0], {'$set': {'password': enc_psw, 'encrypt': True}})
        else:
            db.fdsproxyserver.drop()
            db.fdsproxyserver.insert_one({'password': passwd})

    def decrypt_FDS_proxy_to_base64(self, type='new'):
        import base64
        from util.random_password.service import RandomPasswordService

        results = list(db.fdsproxyserver.find())
        for result in results:
            fds_passwd = result.get('password')
            org_passwd = ''
            if type == 'old':
                org_passwd = RandomPasswordService.decrypt_securely_with_backup_key_file(fds_passwd)
            else:
                org_passwd = RandomPasswordService.decrypt_proxy_pwd(fds_passwd)
            hashed_psw = base64.standard_b64encode(bytes(org_passwd, 'ascii'))

            _id = result.get('_id', '')
            query_sql = {'_id': _id}
            db.fdsproxyserver.update(query_sql, {'$set': {'password': hashed_psw, 'encrypt': False}})

    def change_FDS_proxy(self, type='new'):
        import base64
        from util.random_password.service import RandomPasswordService

        # Code change by AngelaShih for DB Migration
        # detect table existed or not
        ret = global_dbobj.table_detect('fdsproxyserver')
        if not ret:
            return True

        results = list(db.fdsproxyserver.find())
        for result in results:
            is_encrypt = result.get('encrypt', False)
            if is_encrypt:
                fds_passwd = result.get('password')
                org_passwd = ''
                if type == 'old':
                    org_passwd = RandomPasswordService.decrypt_securely_with_backup_key_file(fds_passwd)
                else:
                    org_passwd = RandomPasswordService.decrypt_proxy_pwd(fds_passwd)
                new_passwd = RandomPasswordService.encrypt_proxy_pwd(org_passwd)

                _id = result.get('_id', '')
                query_sql = {'_id': _id}
                db.fdsproxyserver.update(query_sql, {'$set': {'password': new_passwd, 'encrypt': True}})
            else:
                fds_passwd = result.get('password')
                org_passwd = base64.standard_b64decode(fds_passwd).decode('ascii')
                new_passwd = RandomPasswordService.encrypt_proxy_pwd(org_passwd)

                _id = result.get('_id', '')
                query_sql = {'_id': _id}
                db.fdsproxyserver.update(query_sql, {'$set': {'password': new_passwd, 'encrypt': True}})

class ProxyPort(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="port", help="Web proxy port.", info=info)
        self.add_arg(FortiNode(name='<port>', help="Enter an integer value from <0> to <65535>.", info=info)).add_arg(
            EnterArg(info=info))

    def run(self, command_string):
        try:
            port = int(command_string.split()[-1])
            if 0 <= port <= 65535:
                config = list(db.fdsproxyserver.find())
                if len(config) == 1:
                    db.fdsproxyserver.update(config[0], {'$set': {'port': str(port)}})
                else:
                    db.fdsproxyserver.drop()
                    db.fdsproxyserver.insert_one({'port': str(port)})
            else:
                self._print_in_shell('Invalid port {}. Please enter an integer value from <0> to <65535>.'.format(port))
        except:
            self._print_in_shell('Invalid command {}'.format(command_string))


class ProxyStatus(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="status", help="Enable/disable web proxy tunnelling.", info=info)
        self.add_leaf(EnableCmd(info=info, runnable=self.run))
        self.add_leaf(DisableCmd(info=info, runnable=self.run))

    def run(self, command_string):
        status = command_string.split()[-1]
        if 'enable'.startswith(status):
            status = '1'
        elif 'disable'.startswith(status):
            status = '0'
        else:
            self._print_in_shell('Invalid status {}'.format(status))
            return

        config = list(db.fdsproxyserver.find())
        if len(config) == 1:
            db.fdsproxyserver.update(config[0], {'$set': {'status': status}})
        else:
            db.fdsproxyserver.drop()
            db.fdsproxyserver.insert_one({'status': status})


class ProxyUserName(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="username", help="Web proxy username.", info=info)
        self.add_arg(FortiNode(name='<string>', help="please input string value", info=info)).add_arg(
            EnterArg(info=info))

    def run(self, command_string):
        username = command_string.split()[-1]
        config = list(db.fdsproxyserver.find())
        if len(config) == 1:
            db.fdsproxyserver.update(config[0], {'$set': {'username': username}})
        else:
            db.fdsproxyserver.drop()
            db.fdsproxyserver.insert_one({'username': username})

class TunnlingCmd(FortiNode):

    def __init__(self, info):
        super().__init__(name="tunneling", help='Config license update proxy', info=info, hidden=False)
        self.add_arg(EnterArg(info))

    def run(self, command_string):
        TunnlingConsole(prompt='tunneling'.format(repl.get_system_hostname()),
                        prompt_delim='#',
                        info=self.info).loop()
        return 0

class TunnlingConsole(Console):
    def __init__(self, prompt="Prompt", prompt_delim=">", info={}):
        Console.__init__(self, prompt, prompt_delim, info)
        self.prev_prompt = ''
        self.data = {}
        set = SetCmd(info=info)
        set.add_leaf(ProxyAddress(info=info))
        set.add_leaf(ProxyPassword(info=info))
        set.add_leaf(ProxyUserName(info=info))
        set.add_leaf(ProxyPort(info=info))
        set.add_leaf(ProxyStatus(info=info))
        unset = FortiNode(name="unset", help="Set to default value.", info=info)
        unset.add_leaf(ProxyUnsetCmd(name='address', help='Web proxy IP address or FQDN.', info=info))
        unset.add_leaf(ProxyUnsetCmd(name='port', help='Web proxy port.', info=info))
        unset.add_leaf(ProxyUnsetCmd(name='username', help='Web proxy username.', info=info))
        unset.add_leaf(ProxyUnsetCmd(name='password', help='Web proxy password.', info=info))
        unset.add_leaf(ProxyUnsetCmd(name='status', help='Enable/disable web proxy tunnelling.', info=info))
        self.add_leaf(set)
        self.add_leaf(unset)
        self.add_leaf(EndCmd(info=info))

    def update_tunnling(self):
        try:
            tunnel_file = '/var/log/fds_tunnel'
            proxy_config = db.fdsproxyserver.find_one() or {}
            proxy_config_list = [str(proxy_config.get('status', ' ')),
                                    str(proxy_config.get('address', ' ')),
                                    str(proxy_config.get('port', ' ')),
                                    str(proxy_config.get('username', ' ')),
                                    str(proxy_config.get('password', ' ')), ]

            sync_proxy_config = "['" + "','".join(proxy_config_list) + "']"
            sync_proxy_config_cmd = "mkdir -p /var/log"
            dlp_shell.output(sync_proxy_config_cmd)
            dlp_file.text_to_file(tunnel_file, sync_proxy_config)
        except Exception as e:
            logging.error(f"An error occurred: {e}")

    def loop(self):
        self.prev_prompt = self.info.get('prompt', 'FortiTester')
        while 1:
            try:
                self.update_tunnling()
                self.info['prompt'] = f'{repl.get_system_hostname()} ({self.prompt}) {self.prompt_delim} '
                readline.set_completer(self.complete)
                readline.set_completion_display_matches_hook(self.print_all_leafs)

                sys.stdout.write("\r")
                sys.stdout.write("\033[K")
                input_ = InputWrapper(self.info['prompt'])
                if not input_.strip():
                    pass
                else:
                    result = self.walk_and_run(input_)
                    if result and result < 0:
                        self.info['prompt'] = self.prev_prompt
                        return result
            except (KeyboardInterrupt, EOFError, Exception) as e:
                logging.error(traceback.format_exc())
                print()
                pass
        return 0

class ConfigSystemCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="system", help="Config system.", info=info)
        self.add_leaf(ConfigHostnameCmd(self.info))
        self.add_leaf(ConfigInterfaceCmd(self.info))
        self.add_leaf(ConfigRouteCmd(self.info))
        #self.add_leaf(ConfigSettingsCmd(self.info))
        self.add_leaf(ConfigCsfCmd(self.info))
        self.add_leaf(TunnlingCmd(self.info))


class RebootCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="reboot", help="Reboot FortiData.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        why = 'This operation will reboot the system !\n' + \
              'Do you want to continue? (y/n)'
        ret = repl.merge_input_blank(input(why).strip())
        if ret == 'y':
            user = self.info['user']
            record_event_log(user=user, level=LogLevel.INFO.value, message=f"System reboot by {user}",
                             desc='System reboot', type=LogType.SYSTEM.value, action=LogAction.REBOOT.value)
            dlp_cmd.system_reboot()
        else:
            print('')

class ImageFtpCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="ftp", help="Upgrade image from ftp server.", info=info)
        self.add_arg(FortiNode(name='<string>', help="filename", info=info)) \
            .add_arg(FortiNode(name='<ip>', help="ftp server ip", info=info)) \
            .add_arg(FortiNode(name='<string>', help="username(option)", info=info)) \
            .add_arg(FortiNode(name='<string>', help="password(option)", info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        from system import dlp_cmd
        if not dlp_cmd.check_log_disk():
            print("Log disk is not available")
            return 0

        repl.execute_ftp(self.info, command_string)

class ImageTftpCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="tftp", help="Upgrade image from tftp server.", info=info)
        self.add_arg(FortiNode(name='<string>', help="filename", info=info)) \
            .add_arg(FortiNode(name='<ip>', help="tftp server ip", info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        from system import dlp_cmd
        if not dlp_cmd.check_log_disk():
            print("Log disk is not available")
            return 0

        # TODO maybe remove cmd.split() in repl.execute_tftp
        repl.execute_tftp(self.info, command_string)

class ImageCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="image", help="Upgrade image from tftp/ftp server.", info=info)
        self.add_leaf(ImageTftpCmd(self.info))
        self.add_leaf(ImageFtpCmd(self.info))

class RestoreCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="restore", help="Restore FortiData.", info=info)
        self.add_leaf(FortiNode(name="config", help="Restore system config from tftp server. Usage:execute restore config tftp <filename> <ip> [pwd <password>]", info=info)).add_leaf(
            RestoreConfigTftpCmd(self.info))
        self.add_leaf(ImageCmd(self.info))
        self.add_leaf(
            FortiNode(name="vmlicense", help="Update VM platform license from tftp server.", info=info)).add_leaf(
            RestoreVmLicenseTftpCmd(self.info))


class RestoreVmLicenseTftpCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="tftp", help="Update VM platform license from tftp server.", info=info,
                         runnable=lambda command: repl.execute_tftp(info, command))
        self.add_arg(FortiNode(name='<string>', help="filename", info=info)) \
            .add_arg(FortiNode(name='<ip>', help="tftp server ip", info=info)) \
            .add_arg(EnterArg(info=info))


class RestoreConfigTftpCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="tftp", help="Restore system config from tftp server.", info=info,
                         runnable=lambda command: repl.execute_tftp(info, command))
        self.add_arg(FortiNode(name='<string>', help="filename", info=info)) \
            .add_arg(FortiNode(name='<ip>', help="tftp server ip", info=info)) \
            .add_arg(FortiNode(name='[<pwd> password]', help="specifies the password used for encrypting the zip file", info=info)) \
            .add_arg(EnterArg(info=info))


class BackupCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="backup", help="Backup system config to tftp server.", info=info)
        self.add_leaf(FortiNode(name="system", help="Backup system configuration to tftp server.", info=info)).add_leaf(
            BackupTftpCmd(self.info))
        self.add_leaf(FortiNode(name="config", help="Backup all data protection configuration to tftp server.", info=info)).add_leaf(
            BackupTftpCmd(self.info))
        self.add_leaf(FortiNode(name="all", help="Backup all config to tftp server.", info=info)).add_leaf(
            BackupTftpCmd(self.info))


class BackupTftpCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="tftp", help="Backup config to tftp server.", info=info,
                         runnable=lambda command: repl.execute_tftp(info, command))
        self.add_arg(FortiNode(name='<string>', help="Please input filename.", info=info)) \
            .add_arg(FortiNode(name='<ip>', help="Please input tftp server ip.", info=info)) \
            .add_arg(FortiNode(name='[<pwd> password]', help="specifies the password used for encrypting the zip file", info=info)) \
            .add_arg(EnterArg(info=info))


class ShutDownCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="shutdown", help="Shutdown FortiData.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        why = 'This operation will shutdown the system !\n' + \
              'Do you want to continue? (y/n)'
        ret = repl.merge_input_blank(input(why).strip())
        if ret == 'y':
            user = self.info['user']
            record_event_log(user=user, level=LogLevel.INFO.value,
                             message=f"System shutdown by {user}",
                             desc='System shutdown', type=LogType.SYSTEM.value, action=LogAction.SHUTDOWN.value)

            dlp_cmd.system_shutdown()
        else:
            print('')

class PingCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="ping", help="PING command.", info=info)
        self.add_arg(FortiNode(name='<ip>', help="IP address", info=info)).add_arg(EnterArg(info=info))

    def run(self, command_string):
        from ipaddress import ip_address

        ping_re_host = '[0-9a-zA-Z.-]+'
        len_cmds = len(command_string.split())
        if len_cmds == 3 and re.findall(ping_re_host, command_string):
            host = command_string.split()[2]

            try:
                if self._is_valid_hostname(hostname=host) or ip_address(host):
                    pass  # It's a valid hostname or ip
            except ValueError as e:
                print('Invalid IP address or hostname')
                return 0

            # use a command list to prevent command line injecion.
            cmd_list = ['ping', '-c', '3', host]
            p = subprocess.Popen(cmd_list, shell=False, stderr=subprocess.STDOUT, stdout=subprocess.PIPE)
            while True:
                out = p.stdout.readline()
                msg = out.decode('utf-8')
                if msg == '' and p.poll() != None:
                    break
                if msg != '':
                    sys.stdout.write(msg)
                    sys.stdout.flush()
        else:
            print('Invalid format!')
        return 0

    @staticmethod
    def _is_valid_hostname(hostname: str) -> bool:
        if len(hostname) > 255:
            return False
        if hostname[-1] == ".":
            hostname = hostname[:-1]
        allowed = re.compile(r"(?!-)[A-Z\d-]{1,63}(?<!-)$", re.IGNORECASE)
        return all(allowed.match(x) for x in hostname.split("."))

class ExecuteCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="execute", help="Execute static commands.", info=info)

        if repl.is_admin(self.info):
            self.add_leaf(BackupCmd(self.info))
            # self.add_leaf(DateCmd(self.info))
            # self.add_leaf(FactoryResetCmd(self.info))
            # self.add_leaf(FormatdiskCmd(self.info))
            self.add_leaf(RebootCmd(self.info))
            self.add_leaf(RestoreCmd(self.info))
            self.add_leaf(ShutDownCmd(self.info))
            # self.add_leaf(OptimizeDB(self.info))
            # self.add_leaf(FortiNode(name='workmode', help='Work Mode', info=self.info)).add_leaf(
            #     DisconnectedSlaveBySn(info=self.info))

        self.add_leaf(PingCmd(self.info))

class ShowRouteCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="route", help="Show system route.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        routes = self.get_system_routes()

        if not routes:
            print("No routes found.")
            return

        print("{:<20} {:<20} {:<15}".format("Destination", "Gateway", "Iface"))
        # print("{:<20} {:<20} {:<20} {:<15}".format("Destination", "Gateway", "Genmask", "Iface"))
        print("-" * 75)

        for route in routes:
            destination = route.get('destination', 'N/A')
            gateway = route.get('gateway', 'N/A')
            # genmask = route.get('genmask', 'N/A')
            iface = route.get('iface', 'N/A')
            print("{:<20} {:<20} {:<15}".format(destination, gateway, iface))
            # print("{:<20} {:<20} {:<20} {:<15}".format(destination, gateway, genmask, iface))

    def get_system_routes(self):
        def hex_to_ip(hex_str):
            return socket.inet_ntoa(struct.pack("<L", int(hex_str, 16)))
        route_table = []
        with open('/proc/net/route') as f:
            next(f)
            for line in f:
                fields = line.strip().split()
                iface, dest, gateway, flags, refcnt, use, metric, mask = fields[:8]

                dest_ip = hex_to_ip(dest)
                gateway_ip = hex_to_ip(gateway)
                mask_ip = hex_to_ip(mask)

                route_info = {
                    "destination": dest_ip,
                    "gateway": gateway_ip,
                    "genmask": mask_ip,
                    "iface": iface
                }
                route_table.append(route_info)
        return route_table

class ShowConfigRouteCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="config route", help="Show system config route.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        #self.info['cfg'] = repl.get_network_cfg()
        self.info['routes'] = route_cfg.get_route_config()
        print("{:<20} {:<20} {:<15}".format("Destination", "Gateway", "Iface"))
        print("-" * 75)
        for item in self.info['routes'].values():
            destination = item.get('dst', 'N/A')
            gateway = item.get('gateway', 'N/A')
            iface = item.get('interface', 'N/A')
            print("{:<20} {:<20} {:<15}".format(destination or "N/A", gateway or "N/A", iface or "N/A"))

class ShowHostnameCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="hostname", help="Show hostname configuration.", info=info,
                         runnable=lambda command: self._print_in_shell(msg=repl.glob_hostname))
        self.add_arg(EnterArg(info=info))

class ShowSettingCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="setting", help="Show system setting.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        hwmode_config = repl.read_hwmode_config()

        # show maintainer login
        if 'MaintainerLogin' in hwmode_config:
            print("Maintainer Login:", hwmode_config['MaintainerLogin'])

        # # show burnin traffic
        # if 'Mode' in hwmode_config:
        #     mode = hwmode_config['Mode'].lower()
        #     print("Mode:", hwmode_config['Mode'])

        # # show telnet daemon
        # if 'TelnetDaemon' in hwmode_config:
        #     print("Telnet Daemon:", hwmode_config['TelnetDaemon'])

class ShowTunnelingCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="tunneling", help="Show system tunneling.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        try:
            proxy_config = db.fdsproxyserver.find_one() or {}
            msg = "config system tunneling"
            if proxy_config.get('address', ''):
                msg += '\n    set address {}'.format(proxy_config.get('address', ''))
            if proxy_config.get('port', ''):
                msg += '\n    set port {}'.format(proxy_config.get('port', ''))
            if proxy_config.get('username', ''):
                msg += '\n    set username {}'.format(proxy_config.get('username', ''))
            if proxy_config.get('password', ''):
                import base64
                msg += '\n    set password ********'
            if proxy_config.get('status', ''):
                msg += '\n    set status {}'.format('enable' if proxy_config.get('status', '') == '1' else 'disable')
            msg += '\nend'
            print(msg)
        except Exception as e:
            print("An error occurred while fetching or processing the proxy configuration:", str(e))

class TimezoneCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="timezone", help="Display support time zone options.", info=info)
        self.add_leaf(TimezoneHelpCmd(self.info))

class TimezoneHelpCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="help", help="Display support time zone options.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        print(self.get_timezone())

    def get_timezone(self):
        json_data = open('/dlpcode/system/config/zones.json')
        ret = ""
        try:
            timezones = json.load(json_data)
            for zone in timezones:
                ret += "{} == {}\n".format(zone['value'], zone['label'])
        except Exception as e:
            from system.dlp_cmd import TIMEZONE_PATH
            cmd = 'ls {}'.format(TIMEZONE_PATH)
            errno, errmsg = dlp_shell.output(cmd)
            if errno != 0:
                logging.error(errmsg)
            else:
                ret = errmsg
        finally:
            json_data.close()

        return ret


class FnshCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="fts-fnsh", help="", hidden=True, info=info)

    def run(self, command_string):
        # disable alarm handler
        signal.alarm(0)
        signal.signal(signal.SIGALRM, signal.SIG_DFL)
        # disable signal handler
        signal.signal(signal.SIGINT, signal.SIG_DFL)
        os.system('. /etc/profile; /bin/sh')
        # restore signal handler
        signal.signal(signal.SIGINT, signal.SIG_IGN)
        signal.signal(signal.SIGALRM, SessionTimeoutHandler)
        signal.alarm(global_conf["session_timeout"])

        # re-handle ctrl+c signal using default behavior
        signal.signal(signal.SIGINT, signal.default_int_handler)
        signal.signal(signal.SIGTERM, signal.default_int_handler)


BACKEND_CHECK = '/bin/backend_check'

def validate_token(token: str) -> tuple[bool, str]:
    """
    Invoke the backend_check executable to verify the token.
    Returns True if the token is valid (exit code 0), False otherwise.
    """
        
       
    try:
        # Run the backend_check binary, capture stdout and stderr
        result = subprocess.run(
            [BACKEND_CHECK, token],
            capture_output=True,
            text=True,
            check=False
        )
    except FileNotFoundError:
        # Executable not found
        print(f"Error: could not find executable {BACKEND_CHECK}", file=sys.stderr)
        return False, "Executable not found"
    except subprocess.CalledProcessError as e:
        # Handle the case where the command fails with a non-zero exit code
        print(f"Error: backend_check failed with return code {e.returncode}", file=sys.stderr)
        return False, f"Backend check failed: {e}"
    
    if result.stderr and result.stderr.strip():
        # If there is an error message in stderr, treat it as an invalid token
        return False, result.stderr.strip()
    else:
        return True, "Token is valid."


class DlpFnshCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="dlp-fnsh", help="", hidden=True, info=info)

    def run(self, command_string):
        import getpass
        import datetime
        from system import dlp_cmd
        from system import system_platform
        from system.dlp_cmd import get_sys_uuid, get_sys_sn
        #data = getpass.getpass(prompt="dynamic token: ")

        if system_platform.is_debug() == False:
            print("Please enter your access token to continue.")
            
            sn = get_sys_sn()
            
            if sn != "FDTVM0UNLICENSED":
                msg = "If you don't have a token, contact the FortiData Team. Use SN {} or UUID {}".format(sn, get_sys_uuid())
            else:
                msg = "If you don't have a token, contact the FortiData Team. Default SN detected. Use UUID {} only.".format(get_sys_uuid())

            print(msg)

            token = input("Access Token: ").strip()
            if not token:
                print("Error: Access token cannot be empty")
                return 1
            
            # Validate the token before proceeding
            is_valid, message = validate_token(token)
            #print(is_valid, message)
            if not is_valid:
                print(message)
                return 1
            else:
                print("Access token is valid. Proceeding to shell...\n")

        # disable alarm handler
        signal.alarm(0)
        signal.signal(signal.SIGALRM, signal.SIG_DFL)
        # disable signal handler
        signal.signal(signal.SIGINT, signal.SIG_DFL)
        # os.system('. /etc/profile; /bin/sh')
        try:
            subprocess.run(['/bin/sh', '--login'], check=True)
        except KeyboardInterrupt:
            logging.warning("shell interrupted")
        except Exception as e:
            logging.error("Error running shell: %s", e)

        # restore signal handler
        signal.signal(signal.SIGINT, signal.SIG_IGN)
        signal.signal(signal.SIGALRM, SessionTimeoutHandler)
        signal.alarm(global_conf["session_timeout"])

        # re-handle ctrl+c signal using default behavior
        signal.signal(signal.SIGINT, signal.default_int_handler)
        signal.signal(signal.SIGTERM, signal.default_int_handler)


class UserCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="user", help="Show current user.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        print("Name: {}, Roles: {}".format(self.info['user'], ",".join(user_interface.get_user_role(self.info['user']))))

class DeviceCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="deviceinfo", help="Device information.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        msg = ''

        try:
            disk_list = SystemCmd.get_disk_name()
            disk_count = 0
            for disk_name in disk_list:
                disk_count += 1
                msg += f'Disk {disk_count}:\n'
                cmd = f"fdisk -l {disk_name}"
                res = dlp_shell.output(cmd)[1]
                msg += res
                msg += '\n\n'
        except:
            logging.error(traceback.format_exc())
            pass

        print(msg)
        return 0

class SystemCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="disk", help="Disk information.", info=info)
        self.add_leaf(SystemErrorCmd(self.info))
        self.add_leaf(SystemAttrCmd(self.info))
        self.add_leaf(SystemHealthCmd(self.info))
        self.add_leaf(SystemDiskCmd(self.info))

    @classmethod
    def get_disk_name(cls):
        disk_list = []
        cmd = "/sbin/fdisk -l | grep 'Disk /dev/'"
        outputs = dlp_shell.output(cmd)[1]
        for line in outputs.split('\n'):
            matched = re.search(r'Disk (\S)+:', line)
            if matched:
                disk_name = matched.group().replace('Disk ', '').replace(':', '').strip()
                disk_list.append(disk_name)
        return disk_list

class SystemDiskCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="info", help="Disk information.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        msg = ''
        hint = '=== START OF INFORMATION SECTION ==='

        try:
            disk_list = SystemCmd.get_disk_name()
            disk_count = 0
            for disk_name in disk_list:
                disk_count += 1
                cmd = f"/bin/smartctl --info {disk_name}"
                res = dlp_shell.output(cmd)[1]
                if res.find(hint) >= 0:
                    msg += f'Disk {disk_count}:\n'
                    msg += res[res.find(hint) + len(hint) + 1: len(res)]
                    msg += '\n\n'
        except:
            logging.error(traceback.format_exc())
            pass

        print(msg)
        return 0

class SystemErrorCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="errors", help="Error information.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        msg = ''
        hint = '=== START OF READ SMART DATA SECTION ==='

        try:
            disk_list = SystemCmd.get_disk_name()
            disk_count = 0
            for disk_name in disk_list:
                disk_count += 1
                msg += f'Disk {disk_count}:\n'
                cmd = f"/bin/smartctl --log=error {disk_name}"
                res = dlp_shell.output(cmd)[1]
                if res.find(hint) >= 0:
                    msg += res[res.find(hint) + len(hint) + 1: len(res)]
                    msg += '\n\n'
                elif res.find(disk_name) > 0:
                    cmd = f"{cmd} | grep {disk_name}"
                    res = dlp_shell.output(cmd)[1]
                    msg += res
                    msg += '\n\n'
        except:
            logging.error(traceback.format_exc())
            pass

        print(msg)
        return 0

class SystemAttrCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="attributes", help="Attributes information.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        msg = ''
        hint = '=== START OF READ SMART DATA SECTION ==='

        try:
            disk_list = SystemCmd.get_disk_name()
            disk_count = 0
            for disk_name in disk_list:
                disk_count += 1
                cmd = f"/bin/smartctl --attributes {disk_name}"
                res = dlp_shell.output(cmd)[1]
                if res.find(hint) >= 0:
                    msg += f'Disk {disk_count}:\n'
                    msg += res[res.find(hint) + len(hint) + 1: len(res)]
                    msg += '\n\n'
        except:
            logging.error(traceback.format_exc())
            pass

        print(msg)
        return 0

class SystemHealthCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="health", help="Health information.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        msg = ''
        hint = '=== START OF READ SMART DATA SECTION ==='

        try:
            disk_list = SystemCmd.get_disk_name()
            disk_count = 0
            for disk_name in disk_list:
                disk_count += 1
                cmd = f"/bin/smartctl --health {disk_name} | grep -v '{hint}' | grep 'SMART'"
                res = dlp_shell.output(cmd)[1]
                if res:
                    msg += f'Disk {disk_count}: {res}\n'
        except:
            logging.error(traceback.format_exc())
            pass

        print(msg)
        return 0

class InfoCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="info", help="Hardware information.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        msg = ''
        ssl_model_name = ''

        try:
            # CPU info
            msg += '### CPU info\n'
            cmd = "cat /proc/cpuinfo"
            outputs = dlp_shell.output(cmd)[1]
            msg += outputs

            msg += '\n\n'
            # Memory info
            msg += '### Memory info\n'
            cmd = "cat /proc/meminfo | grep Mem"
            outputs = dlp_shell.output(cmd)[1].splitlines()
            for output in outputs:
                msg += '{}\n'.format(output)

            msg += '\n'
        except:
            logging.error(traceback.format_exc())
            pass

        try:
            # Disk info
            msg += '### Disk info\n'
            cmd = "/sbin/fdtinit.sh -s"
            outputs = dlp_shell.output(cmd)[1].splitlines()

            for output in outputs:
                if output.find('SSL Accelerator') >= 0:
                    ssl_model_name = output
                    continue

                if 'HardDisk:' in output:
                    hdd_name = output.split(':')[1].strip()
                    hdd_model = ''
                    hdd_firmware = ''

                    cmd = "/sbin/hdparm -i {} |grep Model".format(hdd_name)
                    _, errmsg = dlp_shell.output(cmd)
                    if errmsg.find('Model=') >= 0:
                        hdd_model = errmsg.split(',')[0].split('=')[1].strip()

                    if hdd_model == '':
                        sd_name = hdd_name.split('/')
                        if sd_name:
                            sd_name = sd_name[-1]
                        else:
                            sd_name = ""
                        rev_path = f"/sys/block/{sd_name}/device/model"
                        if os.path.exists(rev_path):
                            _, errmsg = dlp_shell.output(f"cat {rev_path}")
                            if "can't open" not in errmsg:
                                hdd_model = errmsg

                    cmd = "/sbin/hdparm -I {} |grep Firmware".format(hdd_name)
                    _, errmsg = dlp_shell.output(cmd)
                    if errmsg.find('Firmware Revision:') >= 0:
                        hdd_firmware = errmsg.split('Firmware Revision:')[1].strip()

                    # mantis-0681841 unable to show firmware revision, retry with cat
                    if hdd_firmware == '':
                        sd_name = hdd_name.split('/')
                        if sd_name:
                            sd_name = sd_name[-1]
                        else:
                            sd_name = ""
                        rev_path = f"/sys/block/{sd_name}/device/rev"
                        if os.path.exists(rev_path):
                            _, errmsg = dlp_shell.output(f"cat {rev_path}")
                            if "can't open" not in errmsg:
                                hdd_firmware = errmsg

                    if hdd_model != '':
                        msg += '{} Model: {}, Firmware: {}\n'.format(
                            hdd_name, hdd_model, hdd_firmware if hdd_firmware != '' else 'Unkown')

            msg += '\n'
        except:
            msg += '\n'
            logging.error(traceback.format_exc())
            pass

        # try:
        #     # Accelerator info
        #     msg += '### Accelerator info\n'
        #     if ssl_model_name != '':
        #         msg += '{}\n'.format(ssl_model_name)
        #     else:
        #         msg += 'SSL Accelerator Unavailable\n'

        #     msg += '\n'
        # except:
        #     logging.error(traceback.format_exc())
        #     pass

        try:
            msg += '### NIC info\n'
            port_list = intf_cfg.get_interfaces_config()

            for ifname, if_config in port_list.items():
                port_name = ifname
                nic_version = ''
                nic_speed = ''
                nic_status = ''
                nic_duplex = ''

                cmd = "/bin/ethtool -i {}".format(port_name)
                _, outputs = dlp_shell.output(cmd)
                for output in outputs.splitlines():
                    if output.find('firmware-version:') >= 0:
                        nic_version = output.split(':')[1].strip()

                cmd = "/bin/ethtool {}".format(port_name)
                _, outputs = dlp_shell.output(cmd)
                for output in outputs.splitlines():
                    if output.find('Speed:') >= 0:
                        nic_speed = output.split(':')[1].strip()
                    if output.find('Link detected:') >= 0:
                        if output.split(':')[1].strip() == 'yes':
                            nic_status = 'up'
                        else:
                            nic_status = 'down'
                    if output.find('Duplex:') >= 0:
                        nic_duplex = output.split(':')[1].strip()
                msg += '{} firmware-version: {}, Speed: {}, Status: {}, Duplex: {}\n'.format(
                    port_name, nic_version, nic_speed, nic_status, nic_duplex)

            msg += '\n'
        except:
            logging.error(traceback.format_exc())
            pass

        try:
            # System time
            msg += '### System time\n'
            cmd = "date -R"
            outputs = dlp_shell.output(cmd)[1]
            msg += '{}\n'.format(outputs)

        except:
            logging.error(traceback.format_exc())
            pass

        print(msg)

class HardwareCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="hardware", help="Hardware information.", info=info)
        self.add_leaf(DeviceCmd(self.info))
        self.add_leaf(InfoCmd(self.info))


class GetCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="get", help="Get dynamic and system information.", info=info)
        self.add_leaf(FortiNode(name="system", help="System status.", info=info)).add_leaf(StatusCmd(self.info))

        # from system_control import tester_license
        # if (not tester_license.is_vm_platform()) and repl.is_admin(info):
        #     self.add_leaf(FortiNode(name="interface", help="Interface firmware version.(Do not support VM platform)",
        #                             info=info)).add_leaf(FirmwareCmd(self.info))


class UploadCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="upload", help="Enable/disable gui upload.", hidden=False, info=info)
        self.add_leaf(EnableCmd(info=info, runnable=lambda command: repl.update_gui_setting('Enable')))
        self.add_leaf(DisableCmd(info=info, runnable=lambda command: repl.update_gui_setting('Disable')))


class ConnectCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="connect", help="Connect facility.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        from system.fds.connection import Connection
        from system import dlp_cmd
        from system.dlp_license import get_register_platform_version
        version = get_register_platform_version()

        sn = dlp_cmd.get_sys_sn()

        conn = Connection(sn, version)
        conn.connect()

        # PMDB-13992
        from system import dlp_file
        from system import dlp_shell

        dlp_file.remove_files("/tmp/fds_connection_*")
        errno, errmsg = dlp_shell.output("tail -50 /var/log/fds_update.log")
        if errno:
            print("No results.")
        else:
            logs = errmsg.split('\n')
            simplified_logs = []
            for l in logs:
                if 'Packing obj=' in l:
                    pass
                elif 'output file' in l:
                    pass
                else:
                    simplified_logs.append(l)
            print('\n'.join(simplified_logs))


MODEL_LOG = dlp_file.file_to_json('/dlpcode/system/config/logs_cli_file.json')

def is_valid_grep_arg(arg):
    ALLOWED_GREP_FLAGS = set("ivcoqABC")
    for option in arg[:-1]:
        for ch in option:
            if ch == "-":
                continue
            elif ch.isdigit():
                continue
            elif ch not in ALLOWED_GREP_FLAGS:
                return False
    return True

def load_sensitive_words(file_path):
    sensitive_patterns = []
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if line and not line.startswith('#'):
                    sensitive_patterns.append(line)
        return sensitive_patterns
    except FileNotFoundError:
        logging.error("Sensitive words file not found.")
        return []
    except Exception as e:
        logging.error(f"Error loading sensitive words: {e}")
        return []

def replace_sensitive_data(text, patterns, replacement='***'):
    for pattern in patterns:
        key_text, replace_text = pattern.split(' -> ')
        replace_text = replace_text.strip("'")
        text = re.sub(key_text, replace_text, text, flags=re.IGNORECASE)
    return text


def get_all_log_files_from_current_logfiles(current_logfiles):
    """
    Get the absolute directory from current_logfiles and return all .log files in that directory.
    """
    try:
        with open(current_logfiles, "r") as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) == 2 and parts[1].startswith("log/"):
                    base_dir = os.path.dirname(current_logfiles)
                    log_dir = os.path.join(base_dir, os.path.dirname(parts[1]))
                    abs_log_dir = os.path.abspath(log_dir)
                    pattern = os.path.join(abs_log_dir, "*.log")
                    return glob.glob(pattern)
    except Exception as e:
        logging.error(f"Failed to read {current_logfiles}: {e}")
    return []


def get_all_log_files(log_file, mod):
    """Get all log files matching the pattern based on the provided log file."""

    if mod == "db":
        return get_all_log_files_from_current_logfiles(log_file)
    else:
        log_dir = os.path.dirname(log_file)
        base_name = os.path.basename(log_file)
        pattern = os.path.join(log_dir, base_name + '*')
        return glob.glob(pattern)

def format_size(size):
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size < 1024:
            return f"{size:.2f} {unit}"
        size /= 1024
    return f"{size:.2f} PB"


class LogsStatsCmd(FortiNode):
    def __init__(self, info={}):
        supported_modules = "|".join(MODEL_LOG.keys())

        stats_help_text = (
            "Show logs statistics for a specific module or all modules.\n"
            "Usage:\n"
            "    diagnose logs stats <module_name|all>\n"
            f"Available modules:\n"
            f"    {supported_modules}\n"
            "Use 'all' to show statistics for all modules."
        )

        super().__init__(
            name="stats",
            help="Show logs statistics for a specific module or all modules.",
            info=info,
            runnable=self.run
        )

        self.add_arg(FortiNode(name='<module_name|all>', help=stats_help_text, info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):

        cmd_args = command_string.split()
        if len(cmd_args) < 2:
            print("Invalid command. Usage: diagnose logs stats <module_name|all>")
            return 0

        module_name = cmd_args[3]
        modules = MODEL_LOG.keys() if module_name == "all" else [module_name]

        print(f"{'Module':<20} {'Total Size':>15}")
        print("-" * 35)
        for mod in modules:
            if mod not in MODEL_LOG:
                print(f'{mod:<20} {"Invalid module":>15}')
                continue

            log_file = MODEL_LOG[mod]
            log_files = get_all_log_files(log_file, mod)
            total_size = 0

            for lf in sorted(log_files):
                if os.path.exists(lf):
                    size = os.path.getsize(lf)
                    total_size += size
                    #print(f"  {lf}: {format_size(size)}")
            print(f"{mod:<20} {format_size(total_size):>15}")
        return 0

class LogsDeleteCmd(FortiNode):
    def __init__(self, info={}):
        supported_modules = "|".join(MODEL_LOG.keys())

        delete_help_text = (
            "Delete logs for a specific module or all modules.\n"
            "Usage:\n"
            "    diagnose logs delete <module_name|all>\n"
            f"Available modules:\n"
            f"    {supported_modules}\n"
            "Use 'all' to delete logs for all modules."
        )

        super().__init__(
            name="delete",
            help="Delete logs for a specific module or all modules.",
            info=info
        )
        self.add_arg(FortiNode(name='<module_name|all>', help=delete_help_text, info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) < 2:
            print("Invalid command. Usage: diagnose logs delete <module_name|all>")
            return 0

        module_name = cmd_args[3]
        modules = MODEL_LOG.keys() if module_name == "all" else [module_name]

        for mod in modules:
            if mod not in MODEL_LOG:
                print(f'Invalid module name {mod}')
                continue

            log_file = MODEL_LOG[mod]

            if mod == "db":
                system_util.clear_postgresql_logs_keep_latest("/var/log/postgresql/log/", keep=1)
                continue

            log_dir = os.path.dirname(log_file)
            base_name = os.path.basename(log_file)
            pattern = os.path.join(log_dir, base_name + '*')
            log_files = sorted(glob.glob(pattern))

            errors = []
            if len(log_files) <= 1:
                # Only one file, just truncate it
                try:
                    with open(log_file, 'w'):
                        pass
                    print(f"The {mod} log files has been cleared.")
                except Exception as e:
                    errors.append(f"Error clearing log file {log_file}: {e}")
            else:
                # More than one file, remove all except the main log file
                for lf in log_files:
                    if lf != log_file:
                        try:
                            os.remove(lf)
                        except Exception as e:
                            errors.append(f"Error removing {lf}: {e}")
                # # Truncate the main log file
                # try:
                #     with open(log_file, 'w'):
                #         pass
                #     print(f"The {mod} log files has been cleared.")
                # except Exception as e:
                #     errors.append(f"Error clearing log file {log_file}: {e}")
            if errors:
                print("Some errors occurred:")
                for err in errors:
                    print(err)
        return 0

class LogsCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="logs", help="logs output.", info=info)

        self.add_leaf(LogsShowCmd(self.info))
        self.add_leaf(LogsStatsCmd(self.info))
        self.add_leaf(LogsDeleteCmd(self.info))
        self.add_leaf(LogsExportCmd(self.info))


def get_daily_log_filename(prefix="postgresql", ext="log"):
    today = datetime.date.today()
    filename = f"{prefix}-{today.strftime('%Y-%m-%d')}.{ext}"
    return filename


def get_current_postgresql_logfile(current_logfiles_path="/var/log/postgresql/current_logfiles"):
    """
    Read the current_logfiles file and return the latest postgresql log file (relative path).
    """
    try:
        with open(current_logfiles_path, "r") as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) == 2 and parts[1].startswith("log/postgresql-"):
                    return "/var/log/postgresql/" + parts[1]
    except Exception as e:
        logging.error(f"Failed to read {current_logfiles_path}: {e}")
    return None

class LogsShowCmd(FortiNode):
    def __init__(self, info={}):

        supported_modules = "|".join(MODEL_LOG.keys())
        help_text = f"<{supported_modules}>"

        help_log_text ="Show logs for a specific module. Usage:diagnose logs show <module_name> [lines or all] [| grep options \'keyword\']"

        super().__init__(name="show", help=help_log_text, info=info, runnable= self.run)

        self.add_arg(FortiNode(name='<name>', help=help_text, info=info)) \
            .add_arg(FortiNode(name='[lines or all]',
                               help="Show the last N lines (default 10), or all lines with 'all'.",
                               info=info)) \
            .add_arg(RestArg(name='[| grep [options] "keyword"]',
                            help="Optional grep expression (fixed format: | grep [options] 'keyword'). "
                                "Example: | grep -i 'error'. Supported options: "
                                "-i (ignore case), -v (invert match), -c (count), "
                                "-A NUM (after context), -B NUM (before context), -C NUM (context).",
                            info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) < 2:
            print("Invalid command.")
            return 0

        module_name = cmd_args[3]
        if module_name not in MODEL_LOG.keys():
            print(f'Invalid module name {module_name}')
            return 0

        if module_name == "db":
            log_file = get_current_postgresql_logfile(MODEL_LOG[module_name])

        else:
            log_file = MODEL_LOG[module_name]

        if not os.path.exists(log_file):
            print(f"Log file does not exist: {log_file}")
            return 0

        show_all= False
        try:
            if cmd_args[4] == "all":
                show_all = True
                grep_start = 5
            else:
                lines = int(cmd_args[4])
                grep_start = 5
        except:
            lines = 10
            grep_start = 4

        if show_all:
            tail_cmd = f"cat {shlex.quote(log_file)}"
        else:
            tail_cmd = f"tail -n {lines} {shlex.quote(log_file)}"

        if len(cmd_args) > grep_start and cmd_args[grep_start] == "|" and \
             grep_start + 1 < len(cmd_args) and cmd_args[grep_start + 1] == "grep":
                grep_args = cmd_args[grep_start + 2:]

                if not grep_args:
                    print('Invalid grep arguments. Supported format: | grep [options] "keyword". '
                            'Options: -i (ignore case), -v (invert match), -c (count), '
                            '-A NUM (after), -B NUM (before), -C NUM (context).')

                    return 0

                if not is_valid_grep_arg(grep_args):
                    print('Invalid grep arguments. Supported format: | grep [options] "keyword". '
                            'Options: -i (ignore case), -v (invert match), -c (count), '
                            '-A NUM (after), -B NUM (before), -C NUM (context).')

                    return 0

                grep_cmd = "grep " + " ".join(grep_args)

                final_cmd = f"{tail_cmd} | {grep_cmd}"
        else:
            final_cmd = tail_cmd

        msg = dlp_shell.output(final_cmd)[1]

        if "grep: invalid option" in msg:
            print('Invalid grep arguments. Supported format: | grep [options] "keyword". '
                    'Options: -i (ignore case), -v (invert match), -c (count), '
                    '-A NUM (after), -B NUM (before), -C NUM (context).')
            return 0


        patterns = load_sensitive_words("/dlpcode/system/config/sensitive_words.txt")
        if patterns:
            msg = replace_sensitive_data(msg, patterns)
            print(msg)
        else:
            print(msg)

        return 0


class LogsExportCmd(FortiNode):
    DLP_DIR = "/var/log/dlp/"
    DLP_DAEMON_DIR = "/var/log/dlp_daemon/"
    POSTGRESQL_DIR = "/var/log/postgresql/log/"
    COMPRESSION_LEVEL = 5

    def __init__(self, info={}):
        super().__init__(name="export", help="Export logs files. Usage:diagnose logs export [pwd <password>]", info=info, runnable=self.run)
        self.add_arg(FortiNode(name='[pwd <password>]', help="Encrypt the file", info=info)) \
            .add_arg(EnterArg(info=info))

    def _generate_zip_filename(self, prefix="FDT"):
        from pathlib import Path
        from util.config import get_global_config

        gui_upload_dir = get_global_config().get("system_gui_upload_dir")
        Path(str(gui_upload_dir)).mkdir(exist_ok=True, parents=True)

        current_time = datetime.datetime.now()
        timestamp = current_time.strftime("%Y%m%d_%H%M%S")
        return f"{gui_upload_dir}/{prefix}_{timestamp}.zip"

    def _collect_files(self):
        files_to_zip = []
        prefixes = []

        dlp_files = glob.glob(os.path.join(self.DLP_DIR, "*.log"))
        for file_path in dlp_files:
            if os.path.exists(file_path) and os.access(file_path, os.R_OK):
                files_to_zip.append(file_path)
                prefixes.append("dlp/")

        dlp_daemon_files = glob.glob(os.path.join(self.DLP_DAEMON_DIR, "*.log"))
        for file_path in dlp_daemon_files:
            if os.path.exists(file_path) and os.access(file_path, os.R_OK):
                files_to_zip.append(file_path)
                prefixes.append("dlp_daemon/")

        postgresql_files = glob.glob(os.path.join(self.POSTGRESQL_DIR, "*.log"))
        for file_path in postgresql_files:
            if os.path.exists(file_path) and os.access(file_path, os.R_OK):
                files_to_zip.append(file_path)
                prefixes.append("postgresql/")

        return files_to_zip, prefixes

    def _create_encrypted_zip(self, password):
        import pyminizip
        import threading

        thread_lock = threading.Lock()
        files_to_zip, prefixes = self._collect_files()
        if not files_to_zip:
            print("No files collected.")
            return 0

        zip_pathfile = self._generate_zip_filename(prefix="FDT_logs_files")
        with thread_lock:
            try:
                pyminizip.compress_multiple(files_to_zip, prefixes, zip_pathfile, password, self.COMPRESSION_LEVEL)
                base_name = os.path.basename(zip_pathfile)
                print(f"Create {base_name} successfully")
            except Exception as e:
                print(f"Create zip file failed: {e}")

    def run(self, command_string):
        pwd = ""
        cmd_args = command_string.split()
        if len(cmd_args) < 2:
            print("Invalid command.")
            return 0

        if len(cmd_args) > 4:
            if cmd_args[3] != "pwd":
                print("Invalid command.")
                return 0
            elif len(cmd_args) == 5:
                pwd = cmd_args[4] if len(cmd_args[4]) else ""

        self._create_encrypted_zip(pwd)
        return 0


class DebugCmd(FortiNode):
    def __init__(self, info={}):

        supported_modules = "|".join(MODEL_LOG.keys())
        help_text = f"<{supported_modules}>"

        help_log_text ="Show logs for a specific module. Usage:diagnose logs <module_name> [lines or all] [| grep options \'keyword\']\n"

        super().__init__(name="logs", help=help_log_text, info=info, runnable= self.run)

        self.add_arg(FortiNode(name='<name>', help=help_text, info=info)) \
            .add_arg(FortiNode(name='[lines or all]',
                               help="Show the last N lines (default 10), or all lines with 'all'.",
                               info=info)) \
            .add_arg(RestArg(name='[| grep [options] "keyword"]',
                            help="Optional grep expression (fixed format: | grep [options] 'keyword'). "
                                "Example: | grep -i 'error'. Supported options: "
                                "-i (ignore case), -v (invert match), -c (count), "
                                "-A NUM (after context), -B NUM (before context), -C NUM (context).",
                            info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) < 2:
            print("Invalid command.")
            return 0

        module_name = cmd_args[2]
        if module_name not in MODEL_LOG.keys():
            print(f'Invalid module name {module_name}')
            return 0

        if module_name == "db":
            log_file = get_current_postgresql_logfile(MODEL_LOG[module_name])

        else:
            log_file = MODEL_LOG[module_name]

        if not os.path.exists(log_file):
            print(f"Log file does not exist: {log_file}")
            return 0

        show_all= False
        try:
            if cmd_args[3] == "all":
                show_all = True
                grep_start = 4
            else:
                lines = int(cmd_args[3])
                grep_start = 4
        except:
            lines = 10
            grep_start = 3

        if show_all:
            tail_cmd = f"cat {shlex.quote(log_file)}"
        else:
            tail_cmd = f"tail -n {lines} {shlex.quote(log_file)}"

        if len(cmd_args) > grep_start and cmd_args[grep_start] == "|" and \
             grep_start + 1 < len(cmd_args) and cmd_args[grep_start + 1] == "grep":
                grep_args = cmd_args[grep_start + 2:]

                if not grep_args:
                    print('Invalid grep arguments. Supported format: | grep [options] "keyword". '
                            'Options: -i (ignore case), -v (invert match), -c (count), '
                            '-A NUM (after), -B NUM (before), -C NUM (context).')

                    return 0

                if not is_valid_grep_arg(grep_args):
                    print('Invalid grep arguments. Supported format: | grep [options] "keyword". '
                            'Options: -i (ignore case), -v (invert match), -c (count), '
                            '-A NUM (after), -B NUM (before), -C NUM (context).')

                    return 0

                grep_cmd = "grep " + " ".join(grep_args)

                final_cmd = f"{tail_cmd} | {grep_cmd}"
        else:
            final_cmd = tail_cmd

        msg = dlp_shell.output(final_cmd)[1]

        if "grep: invalid option" in msg:
            print('Invalid grep arguments. Supported format: | grep [options] "keyword". '
                    'Options: -i (ignore case), -v (invert match), -c (count), '
                    '-A NUM (after), -B NUM (before), -C NUM (context).')
            return 0


        patterns = load_sensitive_words("/dlpcode/system/config/sensitive_words.txt")
        if patterns:
            msg = replace_sensitive_data(msg, patterns)
            print(msg)
        else:
            print(msg)

        return 0

SUPERVISOR_COMMAND_LIST = ["start", "stop", "restart", "status"]
SUPERVISOR_PROGRAM_ALIAS_MAP = {
    "scan_analyzer": "analyze_worker",
    "scheduler": "ap_scheduler",
    "db_refresh": "database_refresh",
    "scan_dispatcher": "dispatch_download_task",
    "scan_downloader": "doing_download_task",
    "scan_event_handler": "event_listener",
    "fetch_storage": "fetch_storage",
    "api_http": "flask_app_http",
    "api_https": "flask_app_https",
    "api_local": "flask_app_local",
    "http2": "http2_server",
    "log_daemon": "log_daemon",
    "scan_protection_engine": "protection_action_task",
    "report_generator": "report_generation_task",
    "system": "system",
    "all": "all"
}

SUPERVISOR_PROGRAM_ALIAS_RE_MAP = {
    "analyze_worker": "scan_analyzer",
    "ap_scheduler": "scheduler",
    "database_refresh": "db_refresh",
    "dispatch_download_task": "scan_dispatcher",
    "doing_download_task": "scan_downloader",
    "event_listener": "scan_event_handler",
    "fetch_storage": "fetch_storage",
    "flask_app_http": "api_http",
    "flask_app_https": "api_https",
    "flask_app_local": "api_local",
    "http2_server": "http2",
    "log_daemon": "log_daemon",
    "protection_action_task": "scan_protection_engine",
    "report_generation_task": "report_generator",
    "system": "system",
    "all": "all"
}

def replace_program_names(msg, alias_map):
    def replacer(match):
        name = match.group(1)
        sep = match.group(2)
        new_name = alias_map.get(name, name)
        if sep == ':':
            return f"{new_name}:{' ' * (33 - len(new_name))}"
        else:
            return f"{new_name.ljust(33)}"

    pattern = re.compile(r"^([a-zA-Z0-9_]+)(:|\s+)", re.MULTILINE)
    return pattern.sub(replacer, msg)

class DaemonCmd(FortiNode):
    def __init__(self, info={}):

        supported_commands = "|".join(SUPERVISOR_COMMAND_LIST)
        command_help_text = f"<{supported_commands}>"
        supported_programs = "|".join(SUPERVISOR_PROGRAM_ALIAS_MAP.keys())
        program_help_text = f"<{supported_programs}>"

        help_log_text ="Show daemon programs. Usage:diagnose daemon <command> <program>"

        super().__init__(name="daemon", help=help_log_text, info=info, runnable= self.run)

        self.add_arg(FortiNode(name='<command>', help=command_help_text, info=info)) \
            .add_arg(FortiNode(name='<program name or all>',
                               help=program_help_text,
                               info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) != 4:
            print("Invalid command.")
            return 0

        command = cmd_args[2]
        if command not in SUPERVISOR_COMMAND_LIST:
            print(f'Invalid command name {command}')
            return 0

        program = cmd_args[3]
        if program not in SUPERVISOR_PROGRAM_ALIAS_MAP.keys():
            print(f'Invalid program name {program}')
            return 0

        supervisor_cmd = f"supervisorctl {command} {SUPERVISOR_PROGRAM_ALIAS_MAP.get(program)}"

        sys_msg = dlp_shell.output(supervisor_cmd)[1]

        msg = replace_program_names(sys_msg, SUPERVISOR_PROGRAM_ALIAS_RE_MAP)

        if command == "status":
            for line in msg.strip().splitlines():
                parts = line.strip().split()
                name = parts[0]
                status = parts[1]
                print(f"{name:<30} {status:<10}")
        else:
            print(msg)

        return 0

SCAN_SKIP_KIND_MAP = {
    "file": "skip_files",
    "path": "skip_paths"
}

def scan_skip_operation(cmd_args, scan_skip_conf_file=None):
    if scan_skip_conf_file is None:
        return -1

    if len(cmd_args) < 4:
        print("Invalid command arguments.")
        return -1

    scan_skip_conf = dlp_file.file_to_json(scan_skip_conf_file)

    if cmd_args[2] == "scan-id":
        del cmd_args[2]

    action = cmd_args[3]

    if action == 'clear':
        if len(cmd_args) != 4:
            print("Invalid command arguments.")
            return -1
        if cmd_args[2] in scan_skip_conf:
            scan_skip_conf.pop(cmd_args[2])
            dlp_file.json_to_file(scan_skip_conf_file, scan_skip_conf)
        else:
            print(f"{cmd_args[2]} not found in skip list.")
        return 0

    if action == 'show':
        if len(cmd_args) != 5:
            print("Invalid command arguments.")
            return -1
        skip_kind = cmd_args[4]
        if skip_kind in ['file', 'path']:
            if cmd_args[2] in scan_skip_conf:
                for skip_type, items in scan_skip_conf[cmd_args[2]].items():
                    if skip_type == SCAN_SKIP_KIND_MAP[skip_kind]:
                        print(f"{skip_kind}: {' '.join(items)}")
            else:
                print(f"{cmd_args[2]} not found in skip list.")
        return 0

    if len(cmd_args) < 6:
        print("Invalid command arguments.")
        return -1

    skip_kind = cmd_args[4]
    item = " ".join(cmd_args[5:])
    if action == 'add':
        if cmd_args[2] not in scan_skip_conf:
            scan_skip_conf[cmd_args[2]] = {value: [] for value in SCAN_SKIP_KIND_MAP.values()}

        if skip_kind in ['file', 'path']:
            if item in scan_skip_conf[cmd_args[2]][SCAN_SKIP_KIND_MAP.get(skip_kind)]:
                print(f"{item} already in skip list.")
            else:
                scan_skip_conf[cmd_args[2]][SCAN_SKIP_KIND_MAP.get(skip_kind)].append(item)
        else:
            print(f"Unsupported skip type: {skip_kind}")
            return -2

    elif action == 'del':
        if cmd_args[2] in scan_skip_conf:
            if skip_kind in ['file', 'path']:
                if item in scan_skip_conf[cmd_args[2]][SCAN_SKIP_KIND_MAP.get(skip_kind)]:
                    scan_skip_conf[cmd_args[2]][SCAN_SKIP_KIND_MAP.get(skip_kind)].remove(item)
            else:
                print(f"Unsupported skip type: {skip_kind}")
                return -3

    else:
        print(f"Unsupported action: {action}")
        return -4

    dlp_file.json_to_file(scan_skip_conf_file, scan_skip_conf)

    return 0

class ScanSkipFileCmd(FortiNode):
    def __init__(self, info={}):
        help_text ="Skip file. Usage:file <name>"
        super().__init__(name="file", help=help_text, info=info, runnable= self.run)

        self.add_arg(FortiNode(name='<name>',help="File name",info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) < 6:
            print("Invalid command.")
            return 0

        scan_skip_operation(cmd_args, self.info.get("scan_conf"))

        return 0

class ScanSkipPathCmd(FortiNode):
    def __init__(self, info={}):
        help_text ="Skip path. Usage:path <name>"
        super().__init__(name="path", help=help_text, info=info, runnable= self.run)

        self.add_arg(FortiNode(name='<name>',help="Path name",info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) < 6:
            print("Invalid command.")
            return 0

        scan_skip_operation(cmd_args, self.info.get("scan_conf"))

        return 0

class ScanSkipAddCmd(FortiNode):
    def __init__(self, info={}):
        help_log_text ="Add file. Usage:add <kind: file|path> <name>"
        super().__init__(name="add", help=help_log_text, info=info)

        self.add_leaf(ScanSkipFileCmd(self.info))
        self.add_leaf(ScanSkipPathCmd(self.info))

class ScanSkipDelCmd(FortiNode):
    def __init__(self, info={}):
        help_log_text ="Del file. Usage:del <kind: file|path> <name>"
        super().__init__(name="del", help=help_log_text, info=info)

        self.add_leaf(ScanSkipFileCmd(self.info))
        self.add_leaf(ScanSkipPathCmd(self.info))

class ScanSkipShowCmd(FortiNode):
    def __init__(self, info={}):
        help_log_text ="Show file. Usage:show <kind: file|path>"
        super().__init__(name="show", help=help_log_text, info=info)

        self.add_arg(FortiNode(name='<kind>', help="<file|path>", info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) != 5:
            print("Invalid command.")
            return 0

        scan_skip_operation(cmd_args, self.info.get("scan_conf"))

        return 0

class ScanSkipClearCmd(FortiNode):
    def __init__(self, info={}):
        help_log_text ="Clear file. Usage:clear"
        super().__init__(name="clear", help=help_log_text, info=info)

        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) != 4:
            print("Invalid command.")
            return 0

        scan_skip_operation(cmd_args, self.info.get("scan_conf"))

        return 0

class ScanSkipGlobalCmd(FortiNode):
    def __init__(self, info={}):
        help_log_text ="Global config. Usage:global <action: add|del|show|clear> [<kind: file|path> [<name>]]"
        super().__init__(name="global", help=help_log_text, info=info)

        self.add_leaf(ScanSkipAddCmd(self.info))
        self.add_leaf(ScanSkipDelCmd(self.info))
        self.add_leaf(ScanSkipShowCmd(self.info))
        self.add_leaf(ScanSkipClearCmd(self.info))

class ScanSkipIdCmd(FortiNode):
    def __init__(self, info={}):
        help_log_text ="Scan-id config. Usage:scan-id <id> <action: add|del|show|clear> [<kind: file|path> [<name>]]"
        super().__init__(name="scan-id", help=help_log_text, info=info)

        scanId_node = self.add_arg(FortiNode(name='<scan-id>', help="<id>", info=info))
        scanId_node.add_leaf(ScanSkipAddCmd(self.info))
        scanId_node.add_leaf(ScanSkipDelCmd(self.info))
        scanId_node.add_leaf(ScanSkipShowCmd(self.info))
        scanId_node.add_leaf(ScanSkipClearCmd(self.info))

    def run(self, command_string):
        cmd_args = command_string.split()
        if len(cmd_args) < 5:
            print("Invalid command.")
            return 0

        scan_skip_operation(cmd_args, self.info.get("scan_conf"))

        return 0

class ScanSkipCmd(FortiNode):
    def __init__(self, info={}):
        info["scan_conf"] = "/var/log/scan/scan_config.json"
        if not os.path.exists(info["scan_conf"]):
            os.makedirs(os.path.dirname(info["scan_conf"]), exist_ok=True)

        help_log_text ="Skip scanning file. Usage:diagnose scan-skip <scope: global|scan-id> <action: add|del|show|clear> [<kind: file|path> [<name>]]"
        super().__init__(name="scan-skip", help=help_log_text, info=info)

        self.add_leaf(ScanSkipGlobalCmd(self.info))
        self.add_leaf(ScanSkipIdCmd(self.info))

class DbUpgradeCmd(FortiNode):
    def __init__(self, info={}):

        super().__init__(
            name="db",
            help="Upgrade the database to the latest or a specified release version.",
            info=info,
            runnable=self.run
        )

        self.add_arg(FortiNode(name='<version>', help="Target version to upgrade to (e.g., 'latest' or a specific release(7.6.1)version).", info=info)) \
            .add_arg(EnterArg(info=info))

    def run(self, command_string):
        from system import dlp_db

        args = command_string.strip().split()

        if not args:
            print("Please specify a version to upgrade to.(e.g., 'latest' or 7.6.1).")
            return 0

        # mantis-1174332: Stop the process before upgrading the database
        logging.debug("Stop the process before upgrading the database.")
        dlp_db.before_upgrade_process()
        version = args[2]
        if version == "latest":
            err, err_msg = dlp_db.migration_db_tables()
        else:
            err, err_msg = dlp_db.migration_upgrade_scheam_to_speci_version(version)
        logging.debug("Start the process after upgrading the database.")
        dlp_db.restore_after_upgrade()

        if err:
            print(f"Upgrade failed: {err_msg}")
        else:
            print(f"Upgrade completed successfully to version {version}.")

        return 0

class DiagnoseCmd(FortiNode):
    def __init__(self, info={}, hidden=False):
        super().__init__(name="diagnose", help="Diagnose facility.", info=info, hidden=hidden)
        if repl.is_admin(self.info):
            self.add_leaf(FortiNode(name="fds", help="Connect fds facility.", info=self.info)).add_leaf(
                ConnectCmd(self.info))
            self.add_leaf(HardwareCmd(self.info))
            self.add_leaf(LogsCmd(self.info))
            self.add_leaf(DbUpgradeCmd(self.info))
            self.add_leaf(DaemonCmd(self.info))
            self.add_leaf(ScanSkipCmd(self.info))
            # self.add_leaf(FortiNode(name="system", help="System info.", info=self.info)).add_leaf(
            #    SystemCmd(self.info))
            # self.add_leaf(FortiNode(name="config", help="Diagnose upgrade configuration.", info=info)).add_leaf(
            #     DiagnoseUpgradeCmd(self.info))
            self.add_leaf(
                FortiNode(name="gui", help="Enable/disable gui upload.", hidden=False, info=self.info)).add_leaf(
                UploadCmd(self.info))
            # self.add_leaf(FortiNode(name="vmlicense", help="vmlicense.", info=info)).add_leaf(
            #     VMLicenseActiveCmd(info=info))
            #self.add_leaf(DiagnoseSysCmd(self.info))
            # self.add_leaf(
            #     FortiNode(name="weighttype", help="Configure weight type for iMIX object. "
            #                                       "The default disabled is flow. Enable is bandwidth.",
            #               hidden=False, info=self.info)).add_leaf(WeightTypeSettingCmd(self.info))

        # self.add_leaf(FortiNode(name="cases", help="Test cases information.", info=info, hidden=True)).add_leaf(
        #     CasesLogCmd(self.info))
        # self.add_leaf(DiagnoseCaseCmd(self.info))

class EndAndExecuteCmd(FortiNode):
    def __init__(self, info={}, runnable=None):
        super().__init__(name="end", help="End and save last config.", info=info, runnable=runnable)
        self.add_arg(EnterArg(info=info))


class ConfigCsfCmd(FortiNode):

    def __init__(self, info={}):
        super().__init__(name="csf", help="Config security fabric.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        CsfConsole(prompt='csf', prompt_delim='#',
                   info=self.info).loop()


class CsfConsole(Console):
    def __init__(self, prompt="Prompt", prompt_delim=">", info={}):
        Console.__init__(self, prompt, prompt_delim, info)
        self.prev_prompt = ''
        self.data = {}
        self.add_leaf(EndAndExecuteCmd(info=info, runnable=self.run))
        self.add_leaf(SetCmdCsf(self.info, self.data))

    def loop(self):
        readline.set_completer(self.complete)
        readline.set_completion_display_matches_hook(self.print_all_leafs)
        self.prev_prompt = self.info.get('prompt', 'FortiData')
        while 1:
            try:
                self.info['prompt'] = f'{repl.get_system_hostname()} ({self.prompt}) {self.prompt_delim} '
                sys.stdout.write("\r")
                sys.stdout.write("\033[K")
                input_ = InputWrapper(self.info['prompt'])
                if not input_.strip():
                    pass
                else:
                    result = self.walk_and_run(input_)
                    if result and result < 0:
                        self.info['prompt'] = self.prev_prompt
                        # TODO
                        return result
            except (KeyboardInterrupt, EOFError, Exception):
                logging.error(traceback.format_exc())
                print()
                pass
        return 0

    def run(self, command_string):
        # Mantis-0737369 auto restart csf daemon

        from system.fabric_json import CsfJson
        from system.fabric import run_csfd_daemon, stop_csfd_daemon
        csf_json = CsfJson()
        csf_enable = csf_json.get_config('enable')
        if csf_enable:
            run_csfd_daemon(1)
        if command_string.strip() == 'end':
            return -1
        return super().run(command_string)


class SetCmdCsf(FortiNode):
    def __init__(self, info={}, data={}):
        super().__init__(name="set", help="Set configuration.", info=info)
        self.data = data
        self.add_leaf(self.UpstreamIp(self.info, self.data))
        self.add_leaf(self.UpstreamPort(self.info, self.data))
        self.add_leaf(CsfSettingCmd(info=info))

    class UpstreamIp(FortiNode):
        def __init__(self, info={}, data={}):
            super().__init__(name="ip", help="Set IP.", info=info)
            self.data = data
            self.add_arg(FortiNode(name="<ip>", help="FortiGate root ip", info=info))

        def run(self, command_string):
            ary = command_string.split()
            if len(ary) != 3:
                print('Invalid argument.')
                return 0

            form = ary[2]
            route_re = '[a-z ]*\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$'
            if not re.findall(route_re, form):
                print('Invalid format!')
            else:
                from system.fabric_json import CsfJson
                csf_json = CsfJson()
                csf_json.set_config('upstream_ip', form)

            return 0

    class UpstreamPort(FortiNode):
        def __init__(self, info={}, data={}):
            super().__init__(name="port", help="Set Port.", info=info)
            self.data = data
            self.add_arg(FortiNode(name="<port>", help="FortiGate root port", info=info))

        def run(self, command_string):
            ary = command_string.split()
            if len(ary) != 3:
                print('Invalid argument.')
                return 0

            form = ary[2]
            try:
                port = int(form)
                if 0 < port <= 65535:
                    from system.fabric_json import CsfJson
                    csf_json = CsfJson()
                    csf_json.set_config('upstream_port', int(form))
                else:
                    print('Invalid format!')
            except:
                print('Invalid format!')
            return 0


class CsfSettingCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="status", help="Set csf status Enable/Disable.", info=info)
        self.add_leaf(EnableCmd(info=info, runnable=self.run))
        self.add_leaf(DisableCmd(info=info, runnable=self.run))

    def run(self, command_string):
        if len(command_string.split()) < 3:
            for arg in self.args:
                print("%s  %s" % (arg.name.ljust(self.longest_arg_len + 2), arg.help))
            self._print_in_shell('')
        else:
            self.setup_status(command_string)
        return 0

    def setup_status(self, cmd):
        mode = cmd.split()[2].strip().lower()
        if mode == 'enable':
            self.start()
        elif mode == 'disable':
            self.stop()
        return

    @classmethod
    def start(cls):
        from system.fabric_json import CsfJson
        from system.fabric import run_csfd_daemon
        from system import dlp_license
        #from system_control import tester_license

        csf_json = CsfJson()
        if not csf_json.get_config('upstream_ip'):
            print('Invalid UpStream IP')
        elif csf_json.get_config('sn') in ["", "FDTVM0UNLICENSED"]:
            print('Invalid license(SN:{})'.format(csf_json.get_config('sn')))
        elif dlp_license.get_license_status() == dlp_license.STATUS_TRIAL:
            print('Invalid license status({})'.format(dlp_license.STATUS_TRIAL))
        else:
            csf_json.set_config('enable', True)
            run_csfd_daemon(1)
        return

    @classmethod
    def stop(cls):
        from system.fabric_json import CsfJson
        from system.fabric import stop_csfd_daemon
        csf_json = CsfJson()
        csf_json.set_config('enable', False)
        stop_csfd_daemon()
        return


class ShowCsfCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="csf", help="Show security fabric configurations.", info=info)
        self.add_arg(EnterArg(info=info))

    def run(self, command_string):
        msg = FullConfigurationCmd.get_csf_configuration(info=self.info)
        print(msg)
        return 0


class FortiDataConsole(Console):
    def __init__(self, prompt="Prompt", prompt_delim=">", info={}):
        # info["role"] = user_interface.get_user_role(info['user'])

        Console.__init__(self, prompt, prompt_delim, info)

        self.add_leaf(ExecuteCmd(self.info))
        self.add_leaf(ExitCmd(self.info))
        if repl.is_admin(self.info):
            self.add_leaf(FortiNode(name="config", help="Configure object.", info=info)).add_leaf(
                ConfigSystemCmd(self.info))
            self.add_leaf(DiagnoseCmd(self.info))
        else:
            self.add_leaf(DiagnoseCmd(self.info, hidden=True))
        self.add_leaf(GetCmd(self.info))

        show = ShowCmd(self.info)

        system = ShowSystemCmd(self.info)
        system.add_leaf(ShowInterfaceCmd(self.info))
        system.add_leaf(ShowRouteCmd(self.info))
        system.add_leaf(ShowConfigRouteCmd(self.info))
        system.add_leaf(ShowCsfCmd(self.info))
        # system.add_leaf(ShowWorkMode(self.info))
        # system.add_leaf(ShowWebServiceCmd(self.info))
        # if repl.is_admin(self.info):
        #     system.add_leaf(ShowSettingCmd(self.info))
        #     system.add_leaf(MemsizeCmd(self.info))
        system.add_leaf(ShowHostnameCmd(self.info))
        system.add_leaf(ShowTunnelingCmd(self.info))

        show.add_leaf(system)
        show.add_leaf(TimezoneCmd(self.info))
        show.add_leaf(UserCmd(self.info))
        if repl.is_admin(self.info):
            show.add_leaf(FullConfigurationCmd(self.info))
        self.add_leaf(show)

        if not is_GA_version:
            if repl.is_admin(self.info) and (is_debug or global_conf["fnsh"] == "Enable"):
                self.add_leaf(DlpFnshCmd(self.info))

    def loop(self):
        # handle ctrl+c signal using default behavior
        signal.signal(signal.SIGINT, signal.default_int_handler)
        signal.signal(signal.SIGTERM, signal.default_int_handler)

        readline.parse_and_bind("tab: menu-complete")
        readline.parse_and_bind("?: complete")

        result = 0
        while 1:
            readline.set_completer(self.complete)
            readline.set_completion_display_matches_hook(self.print_all_leafs)
            self.info.update({'prompt': '{0} {1} '.format(repl.get_system_hostname(), self.prompt_delim)})
            try:
                sys.stdout.write("\r")
                sys.stdout.write("\033[K")
                input_ = InputWrapper(self.info['prompt'])
                if not input_.strip():
                    pass
                else:
                    result = self.walk_and_run(input_)
                    if result and result < 0:
                        self.info.update({'logged': False})
                        self.info.update({'prompt': '{0} # '.format(repl.get_system_hostname())})
                        break
            except SessionTimeoutError:
                return -1
            except (KeyboardInterrupt, EOFError, Exception) as e:
                logging.error(traceback.format_exc())
                print()
                pass
        return result

class AdminMaintainerLoginCmd(FortiNode):
    def __init__(self, info={}):
        super().__init__(name="admin-maintainer", help="Set maintainer login Enable/Disable.", info=info)
        self.add_leaf(EnableCmd(info=info, runnable=self.run))
        self.add_leaf(DisableCmd(info=info, runnable=self.run))

    def run(self, command_string):
        if len(command_string.split()) < 3:
            for arg in self.args:
                print("%s  %s" % (arg.name.ljust(self.longest_arg_len + 2), arg.help))
            self._print_in_shell('')
        else:
            self.system_maintainer_login_set(command_string)
        return 0

    def system_maintainer_login_set(self, cmd):

        '''
            Set maintainer login:
                config system setting
                    set maintainerlogin disable
                end
        '''
        from system.system_config import mode_setting_config as mode_setting
        mode = cmd.split()[2].strip().lower()
        mode_setting.save_mode_setting_config(mode, 'MaintainerLogin')
        return


if __name__ == "__main__":
    # This is for testing purposes only.
    is_valid, message = validate_token()
        
    print(is_valid, message)