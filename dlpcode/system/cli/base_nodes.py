#!/usr/bin/env python
# -*- coding: utf-8 -*-
import sys

import readline

err_command_not_complete = "Unknown action!"


class Node(object):
    def __init__(self, name, help='No help provided'):
        self.name = name
        self.help = help
        self.leafs = {}
        self.longest_childname_len = 0

    def add_leaf(self, cmd):
        self.leafs[cmd.name] = cmd
        self.longest_childname_len = max(len(cmd.name), self.longest_childname_len)
        return cmd

    def get_leafs(self, name=None):
        """
        :param name:
        :return: all leafs or leafs starting with name.
        """
        if name:
            if name == self.name:
                candidates = list(self.leafs.keys())
            else:
                candidates = [cmd.name for cmd in self.leafs.values()
                              if cmd.name.startswith(name)]
        else:
            candidates = list(self.leafs.keys())
        return candidates

    def run(self, commands_string):
        return 0

    def walk(self, commands_list=[], completing=''):
        """
        if "completing" is a node, return the node.
        if "completing" is partial word, walk to "completing"'s parent node
        :param commands_list: list of current input commands
        :return: the lowest tree node whose name perfectly matches given keyword, return root if nothing matches
        """
        # print(str(commands_list) + ' current name:' + str(self.name) + " leafs:" + str(self.leafs.keys()))
        if commands_list \
                and commands_list[0] \
                and commands_list[0] in self.leafs.keys() \
                and commands_list[0] != completing:
            return self.leafs[commands_list[0]].walk(commands_list[1:], completing)
        else:
            # print("walk cmd return " + str(self.name) + " by " + str(commands_list))
            return self, completing

    def print_all_leafs(self, substution, matches, longest_match_length):
        if not matches:
            return
        print('')
        for command_name in sorted(matches):
            print("%s  %s" % (command_name.ljust(self.longest_childname_len + 2), self.leafs[command_name].help))

    def __repr__(self):
        return "<Command:(%s), Childs(%s)>" % (self.name, "-".join(self.leafs))


class FortiNode(Node):
    def __init__(self, name, help='', info={}, hidden=False, runnable=None):
        Node.__init__(self, name, help)
        self.args = []
        self.longest_arg_len = 0
        self.info = info
        self.is_hidden_node = hidden
        if runnable is not None:
            self.run = runnable

    def add_arg(self, fortinode=None):
        if fortinode:
            self.args.append(fortinode)
            self.longest_arg_len = max(len(fortinode.name), self.longest_arg_len)
        return fortinode

    def print_all_args(self):
        if self.args:
            print('')
            for arg in self.args:
                print("%s  %s" % (arg.name.ljust(self.longest_arg_len + 2), arg.help))
            self._print_in_shell('')

    def run(self, command_string):
        global err_command_not_complete
        self._print_in_shell(err_command_not_complete)
        return 0

    def _print_in_shell(self, msg=''):
        line_buffer = readline.get_line_buffer()
        # Clearing prompt
        sys.stdout.write("\r")
        sys.stdout.write("\033[K")
        sys.stdout.write(str(msg))
        # Restoring prompt
        sys.stdout.write("\n%s" % self.info['prompt'])
        if line_buffer:
            sys.stdout.write("%s" % line_buffer)
        sys.stdout.flush()

    def print_all_leafs(self, substution='', matches=[], longest_match_length=0):
        '''
        :param substution: default input for readline, DO NOT REMOVE
        :param matches: default input for readline, DO NOT REMOVE
        :param longest_match_length: default input for readline, DO NOT REMOVE
        :return: None
        '''
        print()
        if not matches:
            return
        for command_name in sorted(matches):
            if not self.leafs[command_name].is_hidden_node:
                print("%s  %s" % (command_name.ljust(self.longest_childname_len + 2), self.leafs[command_name].help))
        self._print_in_shell('')
        sys.stdout.flush()

    def get_leafs(self, name=None):
        """
        :param name:
        :return: all leafs's name except hidden leafs or all leafs starting with name except hidden leafs.
        """
        if name:
            if name == self.name:
                candidates = [cmd.name for cmd in self.leafs.values()
                              if not cmd.is_hidden_node]
            else:
                candidates = [cmd.name for cmd in self.leafs.values()
                              if cmd.name.startswith(name) and not cmd.is_hidden_node]
        else:
            candidates = [cmd.name for cmd in self.leafs.values()
                          if not cmd.is_hidden_node]
        if len(candidates) == 1:
            candidates[0] = candidates[0] + ' '
        return candidates

    def walk(self, commands_list=[], completing=''):
        """
        :param commands_list:
        :param completing:
        :return: return final node if exist, None if fail to find path on the tree
        """
        readline.set_completion_display_matches_hook(self.print_all_leafs)
        if self.args:
            if len(self.args) == 1 \
                    and commands_list \
                    and commands_list[0] \
                    and readline.get_line_buffer() \
                    and readline.get_line_buffer()[-1] == ' ':
                return self.args[0].walk(commands_list[1:], completing)
            else:   
                #print("walk cmd return " + str(self.name) + " by " + str(commands_list))
                return self, completing
        else:
            #print(str(commands_list) + ' current name:' + str(self.name) + " leafs:" + str(self.leafs.keys()))
            if commands_list \
                    and commands_list[0] \
                    and commands_list[0] != completing:
                if commands_list[0] in self.leafs.keys():
                    return self.leafs[commands_list[0]].walk(commands_list[1:], completing)
                elif self.is_legit_shortcut(commands_list[0]):
                    recovered_shortword = super().get_leafs(commands_list[0])[0]
                    return self.leafs[recovered_shortword].walk(commands_list[1:], completing)
                else:
                    #print("walk cmd return " + str(self.name) + " by " + str(commands_list))
                    return None, commands_list[0] if commands_list else ''
            else:
                #print("walk cmd return " + str(self.name) + " by " + str(commands_list))
                return self, completing

    def is_legit_shortcut(self, shortcut):
        """
        To support abbreviation
        :param shortcut:
        :return:
        """
        return len(self.get_leafs(shortcut)) == 1


class Console(FortiNode):
    COMPLETE_MODE_1ST_CLICK = 9

    def __init__(self, prompt="Prompt", prompt_delim=">", info={}):
        FortiNode.__init__(self, name='', help='', info=info)
        self.prompt = prompt
        self.prompt_delim = prompt_delim
        self.candidates = []
        self.candidate_args = []

    def walk_and_run(self, command):
        # get current cmd node
        current_cmd, last_keyword = self.walk(command.split(), '')
        if current_cmd:
            return current_cmd.run(command)
        else:
            return self.run(command)

    def complete(self, text, state):
        if state == 0:
            try:
                # Current commands in line
                line = readline.get_line_buffer()

                # clean line
                line_commands = line.split()

                # get current cmd node
                beg = readline.get_begidx()
                end = readline.get_endidx()
                keyword = line[beg:end]
                current_cmd, last_kw = self.walk(line_commands, keyword)
                if current_cmd:
                    self.candidates = current_cmd.get_leafs(keyword)
                    self.candidate_args = current_cmd.args
                    if self.candidate_args:
                        current_cmd.print_all_args()
                    # To support printing candidates at 1st '?' key down.
                    elif readline.get_completion_type() == self.COMPLETE_MODE_1ST_CLICK \
                            and len(self.candidates) > 1:
                        current_cmd.print_all_leafs(matches=self.candidates)
                else:
                    self.candidates = []
                    print('')
                    self._print_in_shell("Command parse error before '{}'.\n".format(last_kw))
            except (Exception):
                print()
                self.candidates = []
                pass
        if state < len(self.candidates):
            return self.candidates[state]
        else:
            return None

    def run(self, commands_string):
        if commands_string:
            global err_command_not_complete
            self._print_in_shell(err_command_not_complete)
        return 0

    def loop(self):
        pass
