#!/usr/bin/env python3
'''
$Id: v 1.0, 09/02/2019 14:19:05 Exp$

Copyright (c) 2019 Fortinet, Inc. All rights reserved
Written by <PERSON><PERSON><PERSON> <<EMAIL>>

Usage: python3 shell.py -U <user> -H <src_ip> -F <from>

'''
import sys
import os
import getpass
from bcrypt import hashpw


# this daemon is called by gettty not shell, need to set the PYTHONPATH
sys.path.insert(0, '/dlpcode')



from system.repl import *
from system import dlp_cmd
from system.cli.command import FortiDataConsole,SessionTimeoutHandler,validate_password
from system.system_log import record_event_log, LogLevel, LogType, LogAction


def main():
    os.chdir('/dlpcode')
    ignore_fatal_signal()
    signal.signal(signal.SIGALRM, SessionTimeoutHandler)

    init_hostname()
    info = init_sys_info()
    #check_invalid_license()


    if dlp_cmd.check_log_disk() == False:
        print("The data disk is invalid. Please check it and attach a valid data disk.")
        sys.exit(1)

    username = info['user']
    login_from = info['from']
    if info["src_ip"]:
        login_from = "{}({})".format(info['from'], info["src_ip"])

    from domain_model import user_interface
    if(username != user_interface.ACCOUNT_MAINTAINER):
        u = user_interface.get_by_name(username)
        if not u:
            print("Username is invalid.")
            sys.exit(1)

        hashed_psw = u.get('password')
        default_pwd = ''
        if username == 'admin' and hashpw(default_pwd, hashed_psw) == hashed_psw:
            print("\r\nWarning!!! This account is using the default password, please change it immediately.\n")
            print("Password must be at least 8 characters long, contain at least one lowercase letter, one uppercase letter, one number, and one special character (!@#$%^&* etc.)\n")

            while True:
                new_pwd = getpass.getpass("Enter new password: ")
                try:
                    validate_password(new_pwd)
                except ValueError as e:
                    print(f"Error: {e}")
                    continue

                if new_pwd != getpass.getpass("Re-enter new password: "):
                    print("Passwords do not match.")
                    continue
                ret = user_interface.update_default_user_password(new_pwd)
                if ret == 0:
                    print("Password updated successfully. please relogin")
                    sys.exit(1)
                break

    print('\nWelcome !\n')
    print('For interactive help, Please type "?".\n')

    #sys_log.save_info(username, log_type=sys_log.TYPE_USER,
    #                  message="User {} logged in successfully from {}".format(username, login_from),
    #                  action=sys_log.LOG_ACT_LOGIN)
    record_event_log(user=username, level=LogLevel.INFO.value,
                     message=f"User {username} logged in successfully from {login_from}",
                     desc='User login', type=LogType.USERS.value, action=LogAction.LOGIN.value)
    while True:
        dlp_console = FortiDataConsole(prompt=get_system_hostname(),
                                         prompt_delim='#',
                                         info=info)
        if dlp_console.loop() < 0:
            #sys_log.save_info(username, log_type=sys_log.TYPE_USER,
            #          message="User {} logout from {}".format(username, login_from),
            #          action=sys_log.LOG_ACT_LOGOUT)
            record_event_log(user=username, level=LogLevel.INFO.value,
                             message=f"User {username} logout successfully from {login_from}",
                             desc='User logout', type=LogType.USERS.value, action=LogAction.LOGOUT.value)
            sys.exit(0)

if __name__ == '__main__':
    main()
