from tutorial_interfaces.srv import EvalDLPPolicyService 
import sys
import rclpy
import ipaddress

from rclpy.node import Node


class DLPEvalPolicyServiceClient(Node):

    def __init__(self):
        super().__init__('dlpevalpolicyservice_client_async')
        self.cli = self.create_client(EvalDLPPolicyService, 'getEvalPolicy')
        while not self.cli.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('service not available, waiting again...')
        self.req = EvalDLPPolicyService.Request()

    def send_request(self):
        self.req.srcip = int(ipaddress.ip_address('***********'))
        self.req.dstip = int(ipaddress.ip_address('***********'))
        self.req.protocol = 'testprotocol'
        self.req.doc.filename = 'test.txt'
        self.future = self.cli.call_async(self.req)


def main(args=None):
    rclpy.init(args=args)

    minimal_client = DLPEvalPolicyServiceClient()
    minimal_client.send_request()

    while rclpy.ok():
        rclpy.spin_once(minimal_client)
        if minimal_client.future.done():
            try:
                response = minimal_client.future.result()
            except Exception as e:
                minimal_client.get_logger().info(
                    'Service call failed %r' % (e,))
            else:
                minimal_client.get_logger().info(
                    'Result of DLPEvalPolicyService: %r' % response)
            break

    minimal_client.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()
