from tutorial_interfaces.srv import VersionDetails # CHANGE
import sys
import rclpy
from rclpy.node import Node


class GetDLPVersionClientAsync(Node):

    def __init__(self):
        super().__init__('dlpgetversion_client_async')
        self.cli = self.create_client(VersionDetails, 'getVersion')       # CHANGE
        while not self.cli.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('service not available, waiting again...')
        self.req = VersionDetails.Request()                                   # CHANGE

    def send_request(self):
        self.future = self.cli.call_async(self.req)


def main(args=None):
    rclpy.init(args=args)

    minimal_client = GetDLPVersionClientAsync()
    minimal_client.send_request()

    while rclpy.ok():
        rclpy.spin_once(minimal_client)
        if minimal_client.future.done():
            try:
                response = minimal_client.future.result()
            except Exception as e:
                minimal_client.get_logger().info(
                    'Service call failed %r' % (e,))
            else:
                minimal_client.get_logger().info(
                    'Result of getDLPVersion: %r' % response)                                # CHANGE
            break

    minimal_client.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()
