from tutorial_interfaces.srv import DLPConfiguration # CHANGE

import rclpy
from rclpy.node import Node


class DLPConfigurationUpdateService(Node):

    def __init__(self):
        super().__init__('set_dlp_configuration')
        self.srv = self.create_service(DLPConfiguration, 'set_dlp_configuration', self.set_dlp_config_callback)       # CHANGE

    def set_dlp_config_callback(self, request, response):
        self.get_logger().info('Incoming request (setDLPConfiguration) %r' % request.data)  # CHANGE
        response.status = 0
        response.description = 'Successfully update DLP configuration'
        return response

def main(args=None):
    rclpy.init(args=args)

    minimal_service = DLPConfigurationUpdateService()

    rclpy.spin(minimal_service)

    rclpy.shutdown()

if __name__ == '__main__':
    main()
