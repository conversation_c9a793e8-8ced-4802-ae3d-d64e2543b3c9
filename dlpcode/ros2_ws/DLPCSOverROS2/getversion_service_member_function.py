from tutorial_interfaces.srv import VersionDetails # CHANGE

import rclpy
from rclpy.node import Node


class DLPGetVersionService(Node):

    def __init__(self):
        super().__init__('dlp_getversion_service')
        self.srv = self.create_service(VersionDetails, 'getVersion', self.getVersion_callback)       # CHANGE

    def getVersion_callback(self, request, response):
        self.get_logger().info('Incoming request (getVersion)')  # CHANGE
        response.status = 0
        response.description = 'Return version details'
        response.details = '{ "versionNumber" : "1.4.23" , "serialNumber" : "789" }'
        return response

def main(args=None):
    rclpy.init(args=args)

    dlp_getversion_service = DLPGetVersionService()

    rclpy.spin(dlp_getversion_service)

    rclpy.shutdown()

if __name__ == '__main__':
    main()
