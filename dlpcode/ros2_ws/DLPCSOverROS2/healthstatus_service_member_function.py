from tutorial_interfaces.srv import HealthStatus # CHANGE

import rclpy
from rclpy.node import Node


class HealthStatusService(Node):

    def __init__(self):
        super().__init__('health_status_service')
        self.srv = self.create_service(HealthStatus, 'healthstatus', self.get_healthstatus_callback)       # CHANGE

    def get_healthstatus_callback(self, request, response):
        self.get_logger().info('Incoming request: (healthStatus)')  # CHANGE
        response.status = 0
        response.description = 'DLP service is healthy'
        return response

def main(args=None):
    rclpy.init(args=args)

    health_service = HealthStatusService()

    rclpy.spin(health_service)

    rclpy.shutdown()

if __name__ == '__main__':
    main()
