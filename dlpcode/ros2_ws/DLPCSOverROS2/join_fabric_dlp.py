import rclpy
from rclpy.action import ActionClient
from rclpy.node import Node

from custom_action_interfaces.action import Jo<PERSON><PERSON><PERSON><PERSON> 


class JoinFabricActionClient(Node):

    def __init__(self):
        super().__init__('dlp_joinfabric_action_client')
        self._action_client = ActionClient(self, <PERSON><PERSON><PERSON><PERSON><PERSON>, 'joinFabric')

    def send_goal(self, fosser, dlpser, dlpname):
        goal_msg = JoinFabric.Goal()
        goal_msg.rootfosserialnum = fosser
        goal_msg.fortidataserialnum = dlpser 
        goal_msg.fortidataname = dlpname 

        self._action_client.wait_for_server()

        self._send_goal_future = self._action_client.send_goal_async(goal_msg, feedback_callback=self.feedback_callback)

        self._send_goal_future.add_done_callback(self.goal_response_callback)

    def goal_response_callback(self, future):
        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().info('Goal rejected :(')
            return

        self.get_logger().info(f'Goal accepted :) {goal_handle}')

        self._get_result_future = goal_handle.get_result_async()
        self._get_result_future.add_done_callback(self.get_result_callback)

    def get_result_callback(self, future):
        result = future.result().result
        self.get_logger().info('Result: status {0} description - {1}'.format(result.status, result.description))
        rclpy.shutdown()

    def feedback_callback(self, feedback_msg):
        feedback = feedback_msg.feedback
        self.get_logger().info('Update feedback: status {0} description - {1}'.format(feedback.status, feedback.description))


def main(args=None):
    rclpy.init(args=args)

    action_client = JoinFabricActionClient()

    action_client.send_goal('123', '789', 'DLP1')

    rclpy.spin(action_client)


if __name__ == '__main__':
    main()
