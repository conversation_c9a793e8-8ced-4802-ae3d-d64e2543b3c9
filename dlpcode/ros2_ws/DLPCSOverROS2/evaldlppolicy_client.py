import rclpy
from rclpy.action import ActionClient
from rclpy.node import Node

from custom_action_interfaces.action import EvalDLPP<PERSON>y 
from custom_action_interfaces.msg import DocumentInfo 
from custom_action_interfaces.msg import KeyValue 


class EvalDLPActionClient(Node):

    def __init__(self):
        super().__init__('eval_dlp_policy_client')
        self._action_client = ActionClient(self, EvalDLPPolicy, 'evalDLPPolicy')

    def send_goal(self, docs):
        self.get_logger().info('SendGoal: {0}'.format(docs))
        goal_msg = EvalDLPPolicy.Goal()
        goal_msg.request = []
        for doc in docs:
            goal_msg.request.append(doc)

        self._action_client.wait_for_server()

        self._send_goal_future = self._action_client.send_goal_async(goal_msg, feedback_callback=self.feedback_callback)

        self._send_goal_future.add_done_callback(self.goal_response_callback)

    def goal_response_callback(self, future):
        goal_handle = future.result()
        if not goal_handle.accepted:
            self.get_logger().info('Goal rejected :(')
            return

        self.get_logger().info(f'Goal accepted :) {goal_handle}')

        self._get_result_future = goal_handle.get_result_async()
        self._get_result_future.add_done_callback(self.get_result_callback)

    def get_result_callback(self, future):
        result = future.result().result
        self.get_logger().info('Result: {0}'.format(result))
        rclpy.shutdown()

    def feedback_callback(self, feedback_msg):
        feedback = feedback_msg.feedback
        self.get_logger().info('Update feedback: {0}'.format(feedback))


def main(args=None):
    rclpy.init(args=args)

    action_client = EvalDLPActionClient()

    documents = []
    
    for i in range(1,2):
        doc = DocumentInfo()
        doc.context = []
        docinfo = KeyValue()
        docinfo.key = 'srcIp'
        docinfo.value= '**************' 
        doc.context.append(docinfo)

        doc.documenthash = '343434'
        doc.filename = 'ABC.doc'
        doc.mimetype = 'text/plain'
        doc.filesize = 232343
        documents.append(doc)
    

    action_client.send_goal(documents)

    rclpy.spin(action_client)


if __name__ == '__main__':
    main()
