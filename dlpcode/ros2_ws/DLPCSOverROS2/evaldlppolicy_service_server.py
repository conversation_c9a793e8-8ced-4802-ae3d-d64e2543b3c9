from tutorial_interfaces.srv import EvalDLPPolicyService

import rclpy
import random
import ipaddress
from rclpy.node import Node


dlp_db = {
  "testallow.txt": 0,
  "testlog.txt": 1,
  "testblock.txt": 2,
}


class DLPGetEvalPolicyService(Node):

    def __init__(self):
        super().__init__('dlp_getevalpolicy_service')
        self.srv = self.create_service(EvalDLPPolicyService, 'getEvalPolicy', self.getEvalPolicy_callback)

    def generate_random_response(self):
        # -1:error 0:no match 1:match
        # statuses = [-1, 0, 1]
        # 0:allow 1:log-only 2:block 3:quaratine-ip
        # actions = [0, 1, 2, 3]
        status_action_mapping = {
            -1: [1, 2],
            0: [1, 2, 3],
            1: [0, 1]
        }
        statuses = list(status_action_mapping.keys())
        random_status = random.choice(statuses)
        random_action = random.choice(status_action_mapping[random_status])
        return random_status, random_action

    def getEvalPolicy_callback(self, request, response):
        self.get_logger().info('Incoming request (getEvalPolicy)')
        self.get_logger().info('Handle request\n' 
                                ' srcip: {src}\n dstip: {dst}\n proto: {proto}\n filename: {name}\n filehash: {hash}'
                               .format(src = ipaddress.ip_address(request.srcip), 
                                       dst = ipaddress.ip_address(request.dstip),
                                       proto = request.protocol,
                                       name = request.doc.filename,
                                       hash = b''.join(request.doc.documenthash).hex()))
        response.action = dlp_db.get(request.doc.filename, 0)
        response.status = 0
        self.get_logger().info('Send response\n status {stat} verdict {act}\n'
                                .format(stat = response.status, act = response.action))
        return response

def main(args=None):
    rclpy.init(args=args)

    dlp_getevalpolicy_service = DLPGetEvalPolicyService()

    rclpy.spin(dlp_getevalpolicy_service)

    rclpy.shutdown()

if __name__ == '__main__':
    main()


