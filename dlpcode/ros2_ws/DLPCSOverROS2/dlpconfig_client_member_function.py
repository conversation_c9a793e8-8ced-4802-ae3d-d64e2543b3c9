from tutorial_interfaces.srv import DLPConfiguration # CHANGE
import sys
import rclpy
from rclpy.node import Node


class DLPSetConfigurationClient(Node):

    def __init__(self):
        super().__init__('dlpsetconfig_client_async')
        self.cli = self.create_client(DLPConfiguration, 'set_dlp_configuration')       # CHANGE
        while not self.cli.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('service not available, waiting again...')
        self.req = DLPConfiguration.Request()                                   # CHANGE

    def send_request(self):
        self.req.data = '{ "ntp_server" : "127.0.0.1" }'
        self.future = self.cli.call_async(self.req)


def main(args=None):
    rclpy.init(args=args)

    minimal_client = DLPSetConfigurationClient()
    minimal_client.send_request()

    while rclpy.ok():
        rclpy.spin_once(minimal_client)
        if minimal_client.future.done():
            try:
                response = minimal_client.future.result()
            except Exception as e:
                minimal_client.get_logger().info(
                    'Service call failed %r' % (e,))
            else:
                minimal_client.get_logger().info(
                    'Result of setDLPConfiguration: %r' % response)                               # CHANGE
            break

    minimal_client.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()
