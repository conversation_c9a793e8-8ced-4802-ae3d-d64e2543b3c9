import time

import rclpy
from rclpy.action import ActionServer
from rclpy.node import Node

from custom_action_interfaces.action import JoinFabric 


class FosJoinFabricActionServer(Node):

    def __init__(self):
        super().__init__('FOSJoinFabric')
        self._action_server = ActionServer(
            self,
            Jo<PERSON><PERSON><PERSON><PERSON>,
            'joinFabric',
            self.execute_callback)

    def execute_callback(self, goal_handle):
        self.get_logger().info('Executing goal...')

        self.get_logger().info(f'Root FOS serial # {goal_handle.request.rootfosserialnum}')
        self.get_logger().info(f'FortiData name  {goal_handle.request.fortidataname}')
        self.get_logger().info(f'FortiData serial # {goal_handle.request.fortidataserialnum}')

        feedback_msg = JoinFabric.Feedback()
        feedback_msg.status = 1

        for i in range(1, 5):
            feedback_msg.status = 1
            feedback_msg.description = 'Pending approval from FOS Admin'
            self.get_logger().info('Feedback: {0}'.format(feedback_msg.description))
            goal_handle.publish_feedback(feedback_msg)
            time.sleep(1)

        goal_handle.succeed()
        result = JoinFabric.Result()
        result.status = 0 
        result.description= 'FortiData name - ' + goal_handle.request.fortidataname + ' successfully added to the fabric'
        self.get_logger().info(f'FortiData Serial # - {goal_handle.request.fortidataserialnum} successfully added to the fabric')
        return result


def main(args=None):
    rclpy.init(args=args)

    action_server = FosJoinFabricActionServer()

    rclpy.spin(action_server)


if __name__ == '__main__':
    main()
