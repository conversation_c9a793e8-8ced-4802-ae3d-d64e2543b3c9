import time

import rclpy
from rclpy.action import ActionServer
from rclpy.node import Node

from custom_action_interfaces.action import EvalDLPPolicy

from custom_action_interfaces.msg import KeyValue 
from custom_action_interfaces.msg import DocumentInfo 
from custom_action_interfaces.msg import EvalDLPResult 
import json

class EvalDLPPolicyActionServer(Node):

    def __init__(self):
        super().__init__('EvalDLPPolicy')
        self._action_server = ActionServer(
            self,
            EvalDLPPolicy,
            'evalDLPPolicy',
            self.execute_callback)

    def execute_callback(self, goal_handle):
        self.get_logger().info('Executing goal...')

        self.get_logger().info(f'Eval DLP request  {goal_handle.request}')

        feedback_msg = EvalDLPPolicy.Feedback()
        feedback_msg.status = 1
        feedback_msg.description = 'DLP Policy evaluation in progress...'
        
        i = 0
        for req in goal_handle.request.request:
            feedback_msg.status = 1
            feedback_msg.description = 'DLP Policy evaluation in progress for document ' + req.documenthash
            dlpres = EvalDLPResult()
            dlpres.actions = KeyValue()
            dlpres.actions.key = str(i)+'33434'  # hash
            if (i % 2 == 0):
                dlpres.actions.value = json.dumps(' { "actions" : [ { "severity" : " High" }, "networkChannel" : [ { "Audit" : "true" }, { "Email" : "Drop Attachment" }, { "LAN" : "Block" } , { "HTTPS" : "Block" }]  ]  } ')
            else:
                dlpres.actions.value = json.dumps(' { "actions" : [ { "severity" : " Medium" }, "networkChannel" : [ { "Audit" : "false" }, { "Email" : "Allow" }, { "LAN" : "Allow" } , { "HTTPS" : "Allow" }]  ]  } ') 
            feedback_msg.result.append(dlpres)
            self.get_logger().info('Feedback: {0}'.format(feedback_msg))
            goal_handle.publish_feedback(feedback_msg)
            i += 1
            time.sleep(1)

        goal_handle.succeed()
        result = EvalDLPPolicy.Result()
        result.status = 0 
        result.description= 'Completed DLP Policy Evaluation for Request'
        self.get_logger().info('DLP Eval complete for request - {goal_handle.request}')
        return result


def main(args=None):
    rclpy.init(args=args)

    action_server = EvalDLPPolicyActionServer()

    rclpy.spin(action_server)


if __name__ == '__main__':
    main()
