// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from custom_action_interfaces:action/JoinFabric.idl
// generated code does not contain a copyright notice

#ifndef CUSTOM_ACTION_INTERFACES__ACTION__DETAIL__JOIN_FABRIC__TRAITS_HPP_
#define CUSTOM_ACTION_INTERFACES__ACTION__DETAIL__JOIN_FABRIC__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "custom_action_interfaces/action/detail/join_fabric__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace custom_action_interfaces
{

namespace action
{

inline void to_flow_style_yaml(
  const JoinFabric_Goal & msg,
  std::ostream & out)
{
  out << "{";
  // member: rootfosserialnum
  {
    out << "rootfosserialnum: ";
    rosidl_generator_traits::value_to_yaml(msg.rootfosserialnum, out);
    out << ", ";
  }

  // member: fortidataname
  {
    out << "fortidataname: ";
    rosidl_generator_traits::value_to_yaml(msg.fortidataname, out);
    out << ", ";
  }

  // member: fortidataserialnum
  {
    out << "fortidataserialnum: ";
    rosidl_generator_traits::value_to_yaml(msg.fortidataserialnum, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JoinFabric_Goal & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: rootfosserialnum
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "rootfosserialnum: ";
    rosidl_generator_traits::value_to_yaml(msg.rootfosserialnum, out);
    out << "\n";
  }

  // member: fortidataname
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "fortidataname: ";
    rosidl_generator_traits::value_to_yaml(msg.fortidataname, out);
    out << "\n";
  }

  // member: fortidataserialnum
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "fortidataserialnum: ";
    rosidl_generator_traits::value_to_yaml(msg.fortidataserialnum, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JoinFabric_Goal & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace action

}  // namespace custom_action_interfaces

namespace rosidl_generator_traits
{

[[deprecated("use custom_action_interfaces::action::to_block_style_yaml() instead")]]
inline void to_yaml(
  const custom_action_interfaces::action::JoinFabric_Goal & msg,
  std::ostream & out, size_t indentation = 0)
{
  custom_action_interfaces::action::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use custom_action_interfaces::action::to_yaml() instead")]]
inline std::string to_yaml(const custom_action_interfaces::action::JoinFabric_Goal & msg)
{
  return custom_action_interfaces::action::to_yaml(msg);
}

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_Goal>()
{
  return "custom_action_interfaces::action::JoinFabric_Goal";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_Goal>()
{
  return "custom_action_interfaces/action/JoinFabric_Goal";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_Goal>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_Goal>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<custom_action_interfaces::action::JoinFabric_Goal>
  : std::true_type {};

}  // namespace rosidl_generator_traits

namespace custom_action_interfaces
{

namespace action
{

inline void to_flow_style_yaml(
  const JoinFabric_Result & msg,
  std::ostream & out)
{
  out << "{";
  // member: status
  {
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << ", ";
  }

  // member: description
  {
    out << "description: ";
    rosidl_generator_traits::value_to_yaml(msg.description, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JoinFabric_Result & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << "\n";
  }

  // member: description
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "description: ";
    rosidl_generator_traits::value_to_yaml(msg.description, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JoinFabric_Result & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace action

}  // namespace custom_action_interfaces

namespace rosidl_generator_traits
{

[[deprecated("use custom_action_interfaces::action::to_block_style_yaml() instead")]]
inline void to_yaml(
  const custom_action_interfaces::action::JoinFabric_Result & msg,
  std::ostream & out, size_t indentation = 0)
{
  custom_action_interfaces::action::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use custom_action_interfaces::action::to_yaml() instead")]]
inline std::string to_yaml(const custom_action_interfaces::action::JoinFabric_Result & msg)
{
  return custom_action_interfaces::action::to_yaml(msg);
}

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_Result>()
{
  return "custom_action_interfaces::action::JoinFabric_Result";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_Result>()
{
  return "custom_action_interfaces/action/JoinFabric_Result";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_Result>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_Result>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<custom_action_interfaces::action::JoinFabric_Result>
  : std::true_type {};

}  // namespace rosidl_generator_traits

namespace custom_action_interfaces
{

namespace action
{

inline void to_flow_style_yaml(
  const JoinFabric_Feedback & msg,
  std::ostream & out)
{
  out << "{";
  // member: status
  {
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << ", ";
  }

  // member: description
  {
    out << "description: ";
    rosidl_generator_traits::value_to_yaml(msg.description, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JoinFabric_Feedback & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << "\n";
  }

  // member: description
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "description: ";
    rosidl_generator_traits::value_to_yaml(msg.description, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JoinFabric_Feedback & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace action

}  // namespace custom_action_interfaces

namespace rosidl_generator_traits
{

[[deprecated("use custom_action_interfaces::action::to_block_style_yaml() instead")]]
inline void to_yaml(
  const custom_action_interfaces::action::JoinFabric_Feedback & msg,
  std::ostream & out, size_t indentation = 0)
{
  custom_action_interfaces::action::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use custom_action_interfaces::action::to_yaml() instead")]]
inline std::string to_yaml(const custom_action_interfaces::action::JoinFabric_Feedback & msg)
{
  return custom_action_interfaces::action::to_yaml(msg);
}

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_Feedback>()
{
  return "custom_action_interfaces::action::JoinFabric_Feedback";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_Feedback>()
{
  return "custom_action_interfaces/action/JoinFabric_Feedback";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_Feedback>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_Feedback>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<custom_action_interfaces::action::JoinFabric_Feedback>
  : std::true_type {};

}  // namespace rosidl_generator_traits

// Include directives for member types
// Member 'goal_id'
#include "unique_identifier_msgs/msg/detail/uuid__traits.hpp"
// Member 'goal'
#include "custom_action_interfaces/action/detail/join_fabric__traits.hpp"

namespace custom_action_interfaces
{

namespace action
{

inline void to_flow_style_yaml(
  const JoinFabric_SendGoal_Request & msg,
  std::ostream & out)
{
  out << "{";
  // member: goal_id
  {
    out << "goal_id: ";
    to_flow_style_yaml(msg.goal_id, out);
    out << ", ";
  }

  // member: goal
  {
    out << "goal: ";
    to_flow_style_yaml(msg.goal, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JoinFabric_SendGoal_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: goal_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "goal_id:\n";
    to_block_style_yaml(msg.goal_id, out, indentation + 2);
  }

  // member: goal
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "goal:\n";
    to_block_style_yaml(msg.goal, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JoinFabric_SendGoal_Request & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace action

}  // namespace custom_action_interfaces

namespace rosidl_generator_traits
{

[[deprecated("use custom_action_interfaces::action::to_block_style_yaml() instead")]]
inline void to_yaml(
  const custom_action_interfaces::action::JoinFabric_SendGoal_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  custom_action_interfaces::action::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use custom_action_interfaces::action::to_yaml() instead")]]
inline std::string to_yaml(const custom_action_interfaces::action::JoinFabric_SendGoal_Request & msg)
{
  return custom_action_interfaces::action::to_yaml(msg);
}

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_SendGoal_Request>()
{
  return "custom_action_interfaces::action::JoinFabric_SendGoal_Request";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_SendGoal_Request>()
{
  return "custom_action_interfaces/action/JoinFabric_SendGoal_Request";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_SendGoal_Request>
  : std::integral_constant<bool, has_fixed_size<custom_action_interfaces::action::JoinFabric_Goal>::value && has_fixed_size<unique_identifier_msgs::msg::UUID>::value> {};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_SendGoal_Request>
  : std::integral_constant<bool, has_bounded_size<custom_action_interfaces::action::JoinFabric_Goal>::value && has_bounded_size<unique_identifier_msgs::msg::UUID>::value> {};

template<>
struct is_message<custom_action_interfaces::action::JoinFabric_SendGoal_Request>
  : std::true_type {};

}  // namespace rosidl_generator_traits

// Include directives for member types
// Member 'stamp'
#include "builtin_interfaces/msg/detail/time__traits.hpp"

namespace custom_action_interfaces
{

namespace action
{

inline void to_flow_style_yaml(
  const JoinFabric_SendGoal_Response & msg,
  std::ostream & out)
{
  out << "{";
  // member: accepted
  {
    out << "accepted: ";
    rosidl_generator_traits::value_to_yaml(msg.accepted, out);
    out << ", ";
  }

  // member: stamp
  {
    out << "stamp: ";
    to_flow_style_yaml(msg.stamp, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JoinFabric_SendGoal_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: accepted
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accepted: ";
    rosidl_generator_traits::value_to_yaml(msg.accepted, out);
    out << "\n";
  }

  // member: stamp
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "stamp:\n";
    to_block_style_yaml(msg.stamp, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JoinFabric_SendGoal_Response & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace action

}  // namespace custom_action_interfaces

namespace rosidl_generator_traits
{

[[deprecated("use custom_action_interfaces::action::to_block_style_yaml() instead")]]
inline void to_yaml(
  const custom_action_interfaces::action::JoinFabric_SendGoal_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  custom_action_interfaces::action::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use custom_action_interfaces::action::to_yaml() instead")]]
inline std::string to_yaml(const custom_action_interfaces::action::JoinFabric_SendGoal_Response & msg)
{
  return custom_action_interfaces::action::to_yaml(msg);
}

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_SendGoal_Response>()
{
  return "custom_action_interfaces::action::JoinFabric_SendGoal_Response";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_SendGoal_Response>()
{
  return "custom_action_interfaces/action/JoinFabric_SendGoal_Response";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_SendGoal_Response>
  : std::integral_constant<bool, has_fixed_size<builtin_interfaces::msg::Time>::value> {};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_SendGoal_Response>
  : std::integral_constant<bool, has_bounded_size<builtin_interfaces::msg::Time>::value> {};

template<>
struct is_message<custom_action_interfaces::action::JoinFabric_SendGoal_Response>
  : std::true_type {};

}  // namespace rosidl_generator_traits

namespace rosidl_generator_traits
{

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_SendGoal>()
{
  return "custom_action_interfaces::action::JoinFabric_SendGoal";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_SendGoal>()
{
  return "custom_action_interfaces/action/JoinFabric_SendGoal";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_SendGoal>
  : std::integral_constant<
    bool,
    has_fixed_size<custom_action_interfaces::action::JoinFabric_SendGoal_Request>::value &&
    has_fixed_size<custom_action_interfaces::action::JoinFabric_SendGoal_Response>::value
  >
{
};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_SendGoal>
  : std::integral_constant<
    bool,
    has_bounded_size<custom_action_interfaces::action::JoinFabric_SendGoal_Request>::value &&
    has_bounded_size<custom_action_interfaces::action::JoinFabric_SendGoal_Response>::value
  >
{
};

template<>
struct is_service<custom_action_interfaces::action::JoinFabric_SendGoal>
  : std::true_type
{
};

template<>
struct is_service_request<custom_action_interfaces::action::JoinFabric_SendGoal_Request>
  : std::true_type
{
};

template<>
struct is_service_response<custom_action_interfaces::action::JoinFabric_SendGoal_Response>
  : std::true_type
{
};

}  // namespace rosidl_generator_traits

// Include directives for member types
// Member 'goal_id'
// already included above
// #include "unique_identifier_msgs/msg/detail/uuid__traits.hpp"

namespace custom_action_interfaces
{

namespace action
{

inline void to_flow_style_yaml(
  const JoinFabric_GetResult_Request & msg,
  std::ostream & out)
{
  out << "{";
  // member: goal_id
  {
    out << "goal_id: ";
    to_flow_style_yaml(msg.goal_id, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JoinFabric_GetResult_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: goal_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "goal_id:\n";
    to_block_style_yaml(msg.goal_id, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JoinFabric_GetResult_Request & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace action

}  // namespace custom_action_interfaces

namespace rosidl_generator_traits
{

[[deprecated("use custom_action_interfaces::action::to_block_style_yaml() instead")]]
inline void to_yaml(
  const custom_action_interfaces::action::JoinFabric_GetResult_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  custom_action_interfaces::action::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use custom_action_interfaces::action::to_yaml() instead")]]
inline std::string to_yaml(const custom_action_interfaces::action::JoinFabric_GetResult_Request & msg)
{
  return custom_action_interfaces::action::to_yaml(msg);
}

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_GetResult_Request>()
{
  return "custom_action_interfaces::action::JoinFabric_GetResult_Request";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_GetResult_Request>()
{
  return "custom_action_interfaces/action/JoinFabric_GetResult_Request";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_GetResult_Request>
  : std::integral_constant<bool, has_fixed_size<unique_identifier_msgs::msg::UUID>::value> {};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_GetResult_Request>
  : std::integral_constant<bool, has_bounded_size<unique_identifier_msgs::msg::UUID>::value> {};

template<>
struct is_message<custom_action_interfaces::action::JoinFabric_GetResult_Request>
  : std::true_type {};

}  // namespace rosidl_generator_traits

// Include directives for member types
// Member 'result'
// already included above
// #include "custom_action_interfaces/action/detail/join_fabric__traits.hpp"

namespace custom_action_interfaces
{

namespace action
{

inline void to_flow_style_yaml(
  const JoinFabric_GetResult_Response & msg,
  std::ostream & out)
{
  out << "{";
  // member: status
  {
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << ", ";
  }

  // member: result
  {
    out << "result: ";
    to_flow_style_yaml(msg.result, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JoinFabric_GetResult_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: status
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "status: ";
    rosidl_generator_traits::value_to_yaml(msg.status, out);
    out << "\n";
  }

  // member: result
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "result:\n";
    to_block_style_yaml(msg.result, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JoinFabric_GetResult_Response & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace action

}  // namespace custom_action_interfaces

namespace rosidl_generator_traits
{

[[deprecated("use custom_action_interfaces::action::to_block_style_yaml() instead")]]
inline void to_yaml(
  const custom_action_interfaces::action::JoinFabric_GetResult_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  custom_action_interfaces::action::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use custom_action_interfaces::action::to_yaml() instead")]]
inline std::string to_yaml(const custom_action_interfaces::action::JoinFabric_GetResult_Response & msg)
{
  return custom_action_interfaces::action::to_yaml(msg);
}

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_GetResult_Response>()
{
  return "custom_action_interfaces::action::JoinFabric_GetResult_Response";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_GetResult_Response>()
{
  return "custom_action_interfaces/action/JoinFabric_GetResult_Response";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_GetResult_Response>
  : std::integral_constant<bool, has_fixed_size<custom_action_interfaces::action::JoinFabric_Result>::value> {};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_GetResult_Response>
  : std::integral_constant<bool, has_bounded_size<custom_action_interfaces::action::JoinFabric_Result>::value> {};

template<>
struct is_message<custom_action_interfaces::action::JoinFabric_GetResult_Response>
  : std::true_type {};

}  // namespace rosidl_generator_traits

namespace rosidl_generator_traits
{

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_GetResult>()
{
  return "custom_action_interfaces::action::JoinFabric_GetResult";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_GetResult>()
{
  return "custom_action_interfaces/action/JoinFabric_GetResult";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_GetResult>
  : std::integral_constant<
    bool,
    has_fixed_size<custom_action_interfaces::action::JoinFabric_GetResult_Request>::value &&
    has_fixed_size<custom_action_interfaces::action::JoinFabric_GetResult_Response>::value
  >
{
};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_GetResult>
  : std::integral_constant<
    bool,
    has_bounded_size<custom_action_interfaces::action::JoinFabric_GetResult_Request>::value &&
    has_bounded_size<custom_action_interfaces::action::JoinFabric_GetResult_Response>::value
  >
{
};

template<>
struct is_service<custom_action_interfaces::action::JoinFabric_GetResult>
  : std::true_type
{
};

template<>
struct is_service_request<custom_action_interfaces::action::JoinFabric_GetResult_Request>
  : std::true_type
{
};

template<>
struct is_service_response<custom_action_interfaces::action::JoinFabric_GetResult_Response>
  : std::true_type
{
};

}  // namespace rosidl_generator_traits

// Include directives for member types
// Member 'goal_id'
// already included above
// #include "unique_identifier_msgs/msg/detail/uuid__traits.hpp"
// Member 'feedback'
// already included above
// #include "custom_action_interfaces/action/detail/join_fabric__traits.hpp"

namespace custom_action_interfaces
{

namespace action
{

inline void to_flow_style_yaml(
  const JoinFabric_FeedbackMessage & msg,
  std::ostream & out)
{
  out << "{";
  // member: goal_id
  {
    out << "goal_id: ";
    to_flow_style_yaml(msg.goal_id, out);
    out << ", ";
  }

  // member: feedback
  {
    out << "feedback: ";
    to_flow_style_yaml(msg.feedback, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const JoinFabric_FeedbackMessage & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: goal_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "goal_id:\n";
    to_block_style_yaml(msg.goal_id, out, indentation + 2);
  }

  // member: feedback
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "feedback:\n";
    to_block_style_yaml(msg.feedback, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const JoinFabric_FeedbackMessage & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace action

}  // namespace custom_action_interfaces

namespace rosidl_generator_traits
{

[[deprecated("use custom_action_interfaces::action::to_block_style_yaml() instead")]]
inline void to_yaml(
  const custom_action_interfaces::action::JoinFabric_FeedbackMessage & msg,
  std::ostream & out, size_t indentation = 0)
{
  custom_action_interfaces::action::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use custom_action_interfaces::action::to_yaml() instead")]]
inline std::string to_yaml(const custom_action_interfaces::action::JoinFabric_FeedbackMessage & msg)
{
  return custom_action_interfaces::action::to_yaml(msg);
}

template<>
inline const char * data_type<custom_action_interfaces::action::JoinFabric_FeedbackMessage>()
{
  return "custom_action_interfaces::action::JoinFabric_FeedbackMessage";
}

template<>
inline const char * name<custom_action_interfaces::action::JoinFabric_FeedbackMessage>()
{
  return "custom_action_interfaces/action/JoinFabric_FeedbackMessage";
}

template<>
struct has_fixed_size<custom_action_interfaces::action::JoinFabric_FeedbackMessage>
  : std::integral_constant<bool, has_fixed_size<custom_action_interfaces::action::JoinFabric_Feedback>::value && has_fixed_size<unique_identifier_msgs::msg::UUID>::value> {};

template<>
struct has_bounded_size<custom_action_interfaces::action::JoinFabric_FeedbackMessage>
  : std::integral_constant<bool, has_bounded_size<custom_action_interfaces::action::JoinFabric_Feedback>::value && has_bounded_size<unique_identifier_msgs::msg::UUID>::value> {};

template<>
struct is_message<custom_action_interfaces::action::JoinFabric_FeedbackMessage>
  : std::true_type {};

}  // namespace rosidl_generator_traits


namespace rosidl_generator_traits
{

template<>
struct is_action<custom_action_interfaces::action::JoinFabric>
  : std::true_type
{
};

template<>
struct is_action_goal<custom_action_interfaces::action::JoinFabric_Goal>
  : std::true_type
{
};

template<>
struct is_action_result<custom_action_interfaces::action::JoinFabric_Result>
  : std::true_type
{
};

template<>
struct is_action_feedback<custom_action_interfaces::action::JoinFabric_Feedback>
  : std::true_type
{
};

}  // namespace rosidl_generator_traits


#endif  // CUSTOM_ACTION_INTERFACES__ACTION__DETAIL__JOIN_FABRIC__TRAITS_HPP_
