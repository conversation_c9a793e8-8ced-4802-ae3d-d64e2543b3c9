from typing import List, Dict, Any, Optional
from huey import crontab, RedisHuey
from storage.util.enum import ActivityStatus
from ddr.service.event_processor import DDRProcessService
from ddr.model.task import get_ddr_tasks_dict
from ddr.service.policy import get_trigger_dict_by_policy_ids
from ddr.service.dedup_repo import DedupRepoSingleKey
from datetime import datetime, timedelta, timezone
from util.common_log import get_logger
from util.config import configs
from util.enum_ import ScanScope

logger = get_logger("ddr")

ddr_huey = RedisHuey("ddr_task", db=configs["huey"]["db"])
interval_min = int(configs.get("ddr_task", {}).get("task_interval", 5))


@ddr_huey.task(retries=1, retry_delay=10)
def process_ddr_events(task: dict,
                       start_time: datetime, end_time: datetime,
                       target_operations: Optional[List[str]] = None,
                       scan_trigger_events: Optional[Dict[str, List[int]]] = None,
                       cleanup_mode: bool = False) -> Dict[str, Any]:
    """
    DDR event processing task with distributed locking.

    Args:
        task: DDRTask dict
        start_time: Start time for event processing
        end_time: End time for event processing
        target_operations: List of target operation types
        scan_trigger_events: Dict of scan trigger event types
        cleanup_mode: Whether this is a cleanup processing
    Returns:
        Result dictionary
    """
    try:
        logger.info(f"Starting DDR event processing for task: {task['name']} "
                    f"start_time: {start_time.isoformat()}, end_time: {end_time.isoformat()}")

        event_processor = DDRProcessService(task)

        if cleanup_mode:
            # In cleanup mode, process both NEW and PENDING activities
            # This catches both unprocessed NEW activities and stuck PENDING activities
            logger.info(f"DDR cleanup mode: processing NEW and PENDING activities for task {task['name']}")

            # Process NEW activities first (these might be from previous cycles that hit limits)
            new_result = event_processor.process_events(
                start_time=start_time,
                end_time=end_time,
                status=ActivityStatus.NEW,
                target_operations=target_operations,
                scan_trigger_events=scan_trigger_events,
                cleanup_mode=True
            )

            # Then process PENDING activities (these might be stuck in processing)
            pending_result = event_processor.process_events(
                start_time=start_time,
                end_time=end_time,
                status=ActivityStatus.PENDING,
                target_operations=target_operations,
                scan_trigger_events=scan_trigger_events,
                cleanup_mode=True
            )

            # Combine results
            result = {
                "processed": new_result.get("processed", 0) + pending_result.get("processed", 0),
                "scans_triggered": new_result.get("scans_triggered", 0) + pending_result.get("scans_triggered", 0),
                "skipped": new_result.get("skipped", 0) + pending_result.get("skipped", 0),
                "cleanup_mode": True
            }
        else:
            # Normal mode: process new activities
            result = event_processor.process_events(
                start_time=start_time,
                end_time=end_time,
                status=ActivityStatus.NEW,
                target_operations=target_operations,
                scan_trigger_events=scan_trigger_events
            )

        logger.info(f"DDR event processing completed for task {task['name']}: {result}")
        return

    except Exception as e:
        logger.error(f"Error in DDR event processing task: {e}")
        raise


# DDR task scheduler - runs every 5 minutes
@ddr_huey.periodic_task(crontab(minute=f'*/{interval_min}'))
def ddr_dispatcher():
    """
    DDR task scheduler:
    - Runs every 5 minutes
    - Processes tasks with actual activities
    - Simple and reliable
    """
    # Clean up expired dedup keys
    DedupRepoSingleKey.cleanup_expired()

    now = datetime.now(timezone.utc)
    try:
        tasks = get_ddr_tasks_dict(enabled=True)
        logger.info(f"Found {len(tasks)} active DDR tasks for processing")
    except Exception as e:
        logger.error(f"Failed to fetch active DDR tasks: {e}")
        return

    if not tasks:
        logger.info("No active DDR tasks found")
        return

    interval = interval_min + 2
    last = now - timedelta(minutes=interval)
    processed_count = 0

    for task in tasks:
        try:
            # Check if there are recent NEW activities for this storage
            has_new_activities = _has_recent_activity(task, last, now)

            if has_new_activities:
                scan_trigger_events = get_trigger_dict_by_policy_ids(task['ddr_policy_ids'])
                # Schedule normal processing for NEW activities
                process_ddr_events.schedule(
                    args=(task, last, now),
                    kwargs={
                        'scan_trigger_events': scan_trigger_events
                    },
                    delay=processed_count * 2  # Stagger tasks by 2 seconds each
                )
                processed_count += 1
                logger.info(f"Scheduled DDR processing for task {task['name']}")

            else:
                # No recent NEW activities, check for cleanup opportunities
                cleanup_start = now - timedelta(hours=2)

                if _has_cleanup_activities(task, cleanup_start, now):
                    scan_trigger_events = get_trigger_dict_by_policy_ids(task['ddr_policy_ids'])
                    # Schedule cleanup processing with wider time window
                    process_ddr_events.schedule(
                        args=(task, cleanup_start, now),
                        kwargs={
                            'scan_trigger_events': scan_trigger_events,
                            'cleanup_mode': True
                        },
                        delay=processed_count * 2
                    )
                    processed_count += 1
                    logger.info(f"Scheduled DDR cleanup for task {task['name']}")

        except Exception as e:
            logger.error(f"Error processing task {task.get('name', 'unknown')}: {e}")

    logger.info(f"DDR dispatcher completed: {processed_count} tasks scheduled")


def _setup_filters(task: dict, start_time: datetime, end_time: datetime, status: int = ActivityStatus.NEW) -> Dict[str, Any]:
        filters = {
            "storage_id": task["storage_id"],
            "status": status,
            "created_at__lt": end_time,
        }
        if start_time:
            filters["created_at__gte"] = start_time

        if task["scan_scope"] == ScanScope.ALL_FOLDERS:
            filters["ddr_fields__jsonb__filter__not_in"] = task["excluded_scan_folders"]
        else:
            filters["ddr_fields__jsonb__filter"] = task["scan_folders"]

        return filters


def _has_recent_activity(task: dict, start_time: datetime, end_time: datetime) -> bool:
    """
    Check if there are recent activities for a storage

    Args:
        task: DDR task dict
        start_time: Start time for checking
        end_time: End time for checking

    Returns:
        bool: True if there are recent activities
    """
    try:
        from storage.model.activity import has_activity
        filters = _setup_filters(task, start_time, end_time)

        return has_activity(**filters)
    except Exception as e:
        logger.error(f"Error checking for recent activity: {e}")
        return False


def _has_cleanup_activities(task: dict, cutoff_time: datetime, now: datetime) -> bool:
    """
    Check if there are old activities that need cleanup processing
    Checks both NEW and PENDING status - these might be unprocessed or stuck activities, or duplicates

    Args:
        task: DDR task dict
        cutoff_time: Activities older than this time will be considered for cleanup

    Returns:
        bool: True if there are old activities needing cleanup
    """
    try:
        from storage.model.activity import has_activity

        # Check for old NEW activities (might have been skipped due to limits)
        filters = _setup_filters(task, cutoff_time, now, ActivityStatus.NEW)
        has_old_new = has_activity(**filters)

        # Check for old PENDING activities (might be stuck in processing)
        filters = _setup_filters(task, cutoff_time, now, ActivityStatus.PENDING)
        has_old_pending = has_activity(**filters)

        return has_old_new or has_old_pending

    except Exception as e:
        logger.error(f"Error checking for cleanup activities: {e}")
        return False