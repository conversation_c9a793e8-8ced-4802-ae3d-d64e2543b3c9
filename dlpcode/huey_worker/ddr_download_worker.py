import os
import time
from contextlib import contextmanager
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Dict, Any
from enum import IntEnum

import magic
from huey import RedisHuey, crontab
from huey.exceptions import CancelExecution
from huey.signals import (
    SIGNAL_EXECUTING,
    SIGNAL_INTERRUPTED,
    SIGNAL_COMPLETE,
    SIGNAL_ERROR,
)
from util.config import configs, get_file_category_from_ext
from util.enum_ import ProtectionQuarantineStatus, ProtectionCopyStatus
from util.utils import calculate_sha1, get_utc_timestamp, calculate_tlsh_hash
from domain_model.global_cache import GlobalCache
from domain_model.tracker.pid_tracker import DDRDownloadWorkerPidTracker
from storage.util.enum import ActivityStatus
from exts import get_logger, rs_client
from ddr.service.mgmt_service import DDRTaskMgmtService
from ddr.tracker.download_tracker import DownloadLeaseTracker
from ddr.service.event_processor import DDRProcessService


download_huey = RedisHuey("ddr_download_worker", db=configs["huey"]["db"])
logger = get_logger("ddr_download")
analyze_stream_enabled = configs.get("file_analyzer", {}).get("analyze_stream_enabled", False)


def clean_result(huey_object: RedisHuey, huey_task_id: str) -> bool:
    """
    Cleans the result of a Huey task.

    Args:
        huey_object (RedisHuey): The Huey instance managing the task.
        huey_task_id (str): The unique identifier of the Huey task.

    Returns:
        bool: True if the result was successfully cleaned, False otherwise.

    Notes:
        We retrieve the results of the tasks to remove them from Redis.
        Huey automatically deletes task results from Redis when they are read.
    """
    try:
        huey_object.result(huey_task_id)
        huey_object.result(f"r:{huey_task_id}")
        return True
    except Exception as e:
        logger.error(e)
        return False

@contextmanager
def _timer():
    start = time.monotonic()
    yield lambda: round(time.monotonic() - start, 3)


_MAGIC = magic.Magic(mime=True)
def _safe_mime(path: str) -> str:
    try:
        return _MAGIC.from_file(path)
    except Exception:
        return "application/octet-stream"


def _null_result(task_uuid: str, session_key: str, backlog_hash: str) -> dict:
    return {
        "local_file": None,
        "remote_info": None,
        "file_uuid": None,
        "scan_info": None,
        "task_uuid": task_uuid,
        "session_key": session_key,
        "backlog_hash": backlog_hash,
    }


REQUIRED_KEYS = {
    "local_file": str,
    "remote_info": dict,
    "file_uuid": (str, int),
    "scan_info": dict,
    "task_uuid": str,
    "session_key": str,
    "backlog_hash": str,
}

def _validate_prev(prev: dict) -> tuple[bool, str]:
    if not isinstance(prev, dict):
        return False, "prev is not dict"
    for k, typ in REQUIRED_KEYS.items():
        if k not in prev:
            return False, f"missing key: {k}"
        if prev[k] is None:
            return False, f"key is None: {k}"
        if not isinstance(prev[k], typ):
            return False, f"type mismatch: {k}"
    return True, ""

class NeedDecision(IntEnum):
    NO_NEED = 0        # original is_need == 0
    NEED = 1           # original is_need == 1
    CONN_ERROR = 2     # original is_need == 2

def _is_download_needed(connector, file_info: dict, cutoff_time: Optional[int]) -> Tuple[NeedDecision, Dict[str, Any], float]:
    """
    unify is_download_required calls and timing.
    Returns: (decision, attr_ext(dict), time cost in seconds)
    """
    with _timer() as ts:
        is_need, attr_ext = connector.is_download_required(
            file_info["folder"],
            file_info["file_name"],
            cutoff_time,
            file_info.get("display_path"),
        )
    return NeedDecision(is_need), (attr_ext or {}), ts()

def _finalize_and_return(
    *,
    success: bool,
    lease: DownloadLeaseTracker,
    lease_id: str,
    null_result: dict,
    log: Optional[str] = None,
    level: str = "info",
    dedup_key: Optional[str] = None,
    status: ActivityStatus = ActivityStatus.SKIPPED,
):
    """
    finalize and return: release lease + log + return null_result
    """
    try:
        if log:
            getattr(logger, level)(log)
    finally:
        if dedup_key:
            DDRProcessService.set_dedup_group_status(dedup_key, status)
        if lease_id:
            lease.release(lease_id, error=not success)
        DDRDownloadWorkerPidTracker().delete(str(os.getpid()))
    return null_result

def _build_file_attributes(rinfo: dict, file_info: dict, attr_ext: dict) -> dict:
    file_attributes = {
        "file_size": attr_ext.get("file_size", 0),
        "file_ext": "",
        "file_cat": "",
        "file_user_name": attr_ext.get("user", "UNKNOWN"),
        "file_user_email": attr_ext.get("email", "UNKNOWN"),
        "file_user_id": attr_ext.get("id", ""),
        "file_location": attr_ext.get("location", "UNKNOWN"),
        "file_encryption": attr_ext.get("encryption"),
    }
    if len(rinfo["file_name"].split(".")) > 1:
        file_attributes["file_ext"] = rinfo["file_name"].split(".")[-1].lower()
        file_attributes["file_cat"] = get_file_category_from_ext(file_attributes["file_ext"])
        file_attributes["file_display_path"] = file_info.get("display_path", "N/A")
        file_attributes["site_id"] = file_info.get("site_id", "N/A")
        file_attributes["drive_id"] = file_info.get("drive_id", "N/A")
        if "catlog_folder" in file_info:
            file_attributes["catlog_folder"] = file_info["catlog_folder"]
    return file_attributes

def _build_payload(task_uuid: str, file_uuid: str, local_file_path: str,
        rinfo: dict, file_info: dict,attr_ext: dict, stats: dict) -> dict:

    file_metadata = {
        "collaborators": attr_ext.get("collaborators", {}),
        "share_link": attr_ext.get("share_link", []),
    }
    for k in ["owners", "create_by"]:
        if k in attr_ext:
            file_metadata[k] = attr_ext[k]

    init_remediation_info = {
        "copy_status": ProtectionCopyStatus.INIT,
        "copy_targets": [],
        "copy_failed_message": "",
        "quarantine_status": ProtectionQuarantineStatus.INIT,
        "quarantine_targets": [],
        "quarantine_failed_message": "",
        "restore_quarantine_failed_message": ""
    }

    payload = {
        "id": file_uuid,
        "file_hash": rinfo["file_hash"],
        "file_tlsh": calculate_tlsh_hash(local_file_path),
        "scan_uuid": task_uuid,
        "storage_id": rinfo.get("storage_id"),
        "full_path": rinfo["file"],
        "file_name": rinfo["file_name"],
        "file_attributes": _build_file_attributes(rinfo, file_info, attr_ext),
        "file_metadata": file_metadata,
        "storage_type": rinfo.get("storage_type"),
        "reserve_json2": stats,
        "reserve_json3": init_remediation_info,
        "tm": get_utc_timestamp(),
    }
    return payload


@download_huey.task()
def download_file(
    task_uuid: str, file_info: dict, params: dict, session_key: str, backlog_hash: str,
) -> dict:
    """
    Downloads a file from a remote server.

    Args:
        task_uuid (str): The UUID of the task.
        file_info (dict): Information about the file to be downloaded.
        params (dict): Additional parameters for the download.
        session_key (str): The session key for authentication.
        backlog_hash (str): The hash of the backlog.

    Returns:
        dict: A dictionary containing the following keys:
            - "local_file": The local file path where the file is downloaded.
            - "remote_info": Information about the remote file.
            - "file_uuid": The UUID of the downloaded file.
            - "scan_info": Information about the scan.
            - "task_uuid": The UUID of the task.
            - "session_key": The session key for authentication.
            - "backlog_hash": The hash of the backlog.
    """
    null_result = _null_result(task_uuid, session_key, backlog_hash)
    dedup_key = params.get("scan_info", {}).get("dedup_key")
    lease = DownloadLeaseTracker(task_uuid)
    tms = DDRTaskMgmtService(task_uuid, session_key)
    try:
        logger.debug(f"[download] begin task={task_uuid} file_info: {file_info} dedup_key: {dedup_key} pid={os.getpid()}")

        if not tms.is_scanning():
            logger.error(f"[download] not SCANNING: {task_uuid}")
            if dedup_key:
                DDRProcessService.set_dedup_group_status(dedup_key, ActivityStatus.DL_FAILED)
            return null_result

        connector = tms.get_connector()
        if not connector:
            logger.error(f"[download] no connector: {task_uuid}")
            if dedup_key:
                DDRProcessService.set_dedup_group_status(dedup_key, ActivityStatus.DL_FAILED)
            return null_result

        tms.scan_backoff()

        # lease: record necessary info for watchdog re-sort
        lease_id = lease.acquire(backlog_hash, file_info=file_info, 
                                 params=params, session_key=session_key, 
                                 lease_secs=600)

        with _timer() as total_cost:
            check_required = -1.0
            download_time = -1.0
            decision, attr_ext, check_required = _is_download_needed(connector, file_info, None)

            if decision == NeedDecision.NO_NEED:
                return _finalize_and_return(
                    success=True, lease=lease, lease_id=lease_id,
                    null_result=null_result,
                    log=f"[download] skip(no-need): {task_uuid} #{dedup_key}", level="info",
                    dedup_key=dedup_key,
                    status=ActivityStatus.SKIPPED,
                )

            if decision == NeedDecision.CONN_ERROR:
                return _finalize_and_return(
                    success=False, lease=lease, lease_id=lease_id,
                    null_result=null_result,
                    log=f"[download] stat conn-error: {task_uuid} #{dedup_key}", level="error",
                    dedup_key=dedup_key,
                    status=ActivityStatus.DL_FAILED,
                )

            # 2) download
            with _timer() as ts_dl:
                file_uuid, local_file_path, obj_uri = connector.download(
                    file_info["folder"], file_info["file_name"], task_uuid
                )
            download_time = ts_dl()

            if (file_uuid, local_file_path, obj_uri) == (-1, -1, -1):
                return _finalize_and_return(
                    success=False, lease=lease, lease_id=lease_id,
                    backlog_hash=backlog_hash, null_result=null_result,
                    log=f"[download] conn-error during download: {task_uuid} #{dedup_key}", level="error",
                    dedup_key=dedup_key,
                    status=ActivityStatus.DL_FAILED,
                )

            if not (file_uuid and local_file_path and obj_uri):
                return _finalize_and_return(
                    success=False, lease=lease, lease_id=lease_id,
                    backlog_hash=backlog_hash, null_result=null_result,
                    log=f"[download] unknown download error: {task_uuid} #{dedup_key}", level="error",
                    dedup_key=dedup_key,
                    status=ActivityStatus.DL_FAILED,
                )

            # 3) fill remote_info
            rinfo = params.get("remote_info", {}) or {}
            rinfo["file"] = obj_uri
            rinfo["file_name"] = file_info["file_name"].split("/")[-1]
            rinfo["file_size"] = attr_ext.get("file_size", 0)
            rinfo["last_modified"] = attr_ext.get("last_modified", "")
            rinfo["user"] = attr_ext.get("user", "UNKNOWN")
            rinfo["email"] = attr_ext.get("email", "UNKNOWN")
            rinfo["collaborators"] = attr_ext.get("collaborators", {})
            rinfo["share_link"] = attr_ext.get("share_link", [])
            rinfo["file_type"] = attr_ext.get("file_type")
            rinfo["shared_data"] = attr_ext.get("shared_data")
            rinfo["file_encryption"] = attr_ext.get("encryption")
            rinfo["file_location"] = attr_ext.get("location", "UNKNOWN")

            file_hash = calculate_sha1(local_file_path)
            rinfo["file_hash"] = file_hash
            rinfo["mime_type"] = _safe_mime(local_file_path)

            # 4) persist
            stats = {
                "check_required": check_required,
                "download": download_time,
                "total": total_cost(),
            }
            cache = GlobalCache(task_uuid, session_key, True)
            payload = _build_payload(task_uuid, file_uuid, local_file_path, 
                                     rinfo, file_info, attr_ext, stats)
            cache.put(payload)

            # release lease (success)
            lease.release(lease_id, error=False)

            return {
                "local_file": local_file_path,
                "remote_info": rinfo,
                "file_uuid": file_uuid,
                "scan_info": params.get("scan_info"),
                "task_uuid": task_uuid,
                "session_key": session_key,
                "backlog_hash": backlog_hash,
            }
    except Exception:
        logger.exception(f"[download] error: {task_uuid}")
        raise


@download_huey.task(retries=1, retry_delay=10)
def add_analyze_task(prev: dict | None = None, **kwargs):
    # logger.info(f"add_analyze_task: begin prev={prev}, kwargs={kwargs}, pid={os.getpid()}")
    if prev is None:
        prev_dict = dict(kwargs) if kwargs else {}
    elif isinstance(prev, dict):
        prev_dict = {**prev, **kwargs} if kwargs else prev
    else:
        prev_dict = dict(kwargs) if kwargs else {}
        logger.error(f"add_analyze_task: prev is not dict: {prev}")

    ok, msg = _validate_prev(prev_dict)
    if not ok:
        logger.error(f"add_analyze_task: invalid prev: {msg}, prev={prev_dict}")
        return

    from ddr.service.rs_producer import push_to_analyze_stream
    from huey_worker.analyze_worker import ddr_analyze_worker, huey as analyze_huey
    try:
        if analyze_stream_enabled:
            logger.info(f"add_analyze_task: push to analyze stream, pid={os.getpid()}")
            push_to_analyze_stream("ddr", prev_dict)
        else:
            logger.info(f"add_analyze_task: push to analyze worker, pid={os.getpid()}")
            analyze_huey.enqueue(ddr_analyze_worker.s(
                local_file=prev_dict["local_file"],
                remote_info=prev_dict["remote_info"],
                file_uuid=prev_dict["file_uuid"],
                scan_info=prev_dict["scan_info"],
                task_uuid=prev_dict["task_uuid"],
                session_key=prev_dict["session_key"],
                backlog_hash=prev_dict["backlog_hash"],
            ))

        dedup_key = prev_dict.get("scan_info", {}).get("dedup_key")
        if dedup_key:
            DDRProcessService.set_dedup_group_status(dedup_key, ActivityStatus.DOWNLOADED)
        logger.info(f"add_analyze_task: enqueued analyze for task={prev_dict['task_uuid']}")
    except Exception:
        logger.exception("add_analyze_task: enqueue analyze failed")
        # Trigger add_analyze_task retry
        raise


@download_huey.signal(SIGNAL_EXECUTING)
def on_executing(signal, task, exc=None):
    try:
        if task.name == "requeue_stalled_downloads":
            return
        logger.debug(f"[SIG EXEC] {task.name}, task.kwargs: {task.kwargs},pid={os.getpid()}")
        pid_tracker = DDRDownloadWorkerPidTracker()
        pid_tracker.set(str(os.getpid()), task.kwargs.get("task_uuid", "N/A"))
    except Exception:
        logger.exception("[SIG EXEC] failed")


@download_huey.signal(SIGNAL_INTERRUPTED)
def on_interrupted(signal, task, exc=None):
    try:
        logger.warning(f"[SIG INTR] {task.name}, task.kwargs: {task.kwargs}, interrupted pid={os.getpid()} exc={exc}")
        if task.name == "add_analyze_task":
            clean_result(download_huey, task.kwargs.get("previous_task_id"))
            clean_result(download_huey, task.id)
        elif task.name == "download_file":
            task_uuid = task.kwargs.get("task_uuid")
            backlog_hash = task.kwargs.get("backlog_hash")
            if task_uuid and backlog_hash:
                lease = DownloadLeaseTracker(task_uuid)
                lease_id = lease.get_lease_id(backlog_hash)
                lease.release(lease_id, error=True)
                logger.info(f"[SIG INTR] Released lease for interrupted download_file task. "
                            f"task_uuid={task_uuid}, backlog_hash={backlog_hash}")
    except Exception:
        logger.exception("[SIG INTR] failed")
    finally:
        try:
            DDRDownloadWorkerPidTracker().delete(str(os.getpid()))
        except Exception:
            logger.debug("[SIG INTR] pid cleanup failed", exc_info=True)


@download_huey.signal(SIGNAL_COMPLETE)
def on_complete(signal, task, exc=None):
    try:
        if task.name == "requeue_stalled_downloads":
            return
        logger.debug(f"[SIG OK] {task.name}, task_uuid={task.id}, complete pid={os.getpid()}")
        if task.name == "add_analyze_task":
            # logger.debug(f"[SIG OK, remove result] {task.kwargs}, complete pid={os.getpid()}")
            clean_result(download_huey, task.kwargs.get("previous_task_id"))
            clean_result(download_huey, task.id)
    except Exception:
        logger.exception("[SIG OK] failed")
    finally:
        try:
            DDRDownloadWorkerPidTracker().delete(str(os.getpid()))
        except Exception:
            logger.debug("[SIG OK] pid cleanup failed", exc_info=True)


@download_huey.signal(SIGNAL_ERROR)
def on_error(signal, task, exc=None):
    logger.error(f"[SIG ERR] {task.name}, task.kwargs: {task.kwargs}, pid={os.getpid()} exc={exc}")

    try:
        if getattr(task, 'retry_count', 0) >= getattr(task, 'retries', 0):
            dedup_key = task.kwargs.get("scan_info", {}).get("dedup_key")
            if dedup_key:
                DDRProcessService.set_dedup_group_status(dedup_key, ActivityStatus.DL_FAILED)
        DDRDownloadWorkerPidTracker().delete(str(os.getpid()))
        if task.name == "add_analyze_task":
            clean_result(download_huey, task.kwargs.get("previous_task_id"))
            clean_result(download_huey, task.id)
    except Exception:
        logger.debug("[SIG ERR] pid cleanup failed", exc_info=True)


# -------------------- Watchdog: requeue stalled downloads --------------------
@download_huey.periodic_task(crontab(minute='*/1'))
def requeue_stalled_downloads():
    """
    Every 1 minute: requeue stalled downloads.
    older_than=15: grace period in seconds to avoid false positives.
    """
    try:
        if not rs_client.set("wd:ddr:download:lock", "1", nx=True, ex=20):
            return
    except Exception:
        pass
    try:
        stalled = DownloadLeaseTracker.list_stalled(older_than=15)
        for task_uuid, backlog_hash in stalled:
            payload = DownloadLeaseTracker.fetch_payload(task_uuid, backlog_hash)
            if not payload:
                # cannot restore params, just clean index
                DownloadLeaseTracker.delete_lease(task_uuid, backlog_hash)
                continue

            file_info = payload.get("file_info") or {}
            params = payload.get("params") or {}
            session_key = payload.get("session_key") or ""
            if not (file_info and session_key):
                # params not complete, clean
                DownloadLeaseTracker.delete_lease(task_uuid, backlog_hash)
                continue

            # requeue download_file (idempotent)
            download_file_task = download_file.s(
                task_uuid=task_uuid,
                file_info=file_info,
                params=params,
                session_key=session_key,
                backlog_hash=backlog_hash
            )
            pipeline = download_file_task.then(
                add_analyze_task,
                previous_task_id=download_file_task.id
            )
            download_huey.enqueue(pipeline)

            # clean old lease
            DownloadLeaseTracker.delete_lease(task_uuid, backlog_hash)
            logger.warning(f"[watchdog] re-enqueue download: task={task_uuid} backlog={backlog_hash}")
    except Exception:
        logger.exception("[watchdog] failed")
    finally:
        try:
            rs_client.delete("wd:ddr:download:lock")
        except Exception:
            pass