{"Data Classification": {"Transaction Record": {"description": "Documents recording the details of financial transactions, including transaction confirmations, receipts, wire transfer instructions, and bank statements. These records typically contain information such as transaction amount, date, counterparties, and purpose, and are used to verify the existence, completion, and validity of financial activities.", "order": 101}, "Financial Report": {"description": "Documents that present the financial condition and performance of an organization or individual. Includes balance sheets, income statements, cash flow statements, and shareholder equity reports. These reports are critical for internal decision-making, external audits, regulatory compliance, and investor communication.", "order": 102}, "Credit and Risk Management": {"description": "Documents related to the evaluation and mitigation of financial risks, including credit reports, market risk assessments, stress test results, and risk management policies. These are used by financial institutions to establish controls and prevent losses from credit, market, liquidity, or operational risks.", "order": 103}, "Financial Strategy and Research": {"description": "Includes investment strategy documents, market research reports, economic forecasts, and industry analysis. These documents provide insight into market trends and guide strategic financial planning and product development.", "order": 104}, "Tax and Regulatory Documents": {"description": "Documents prepared for tax reporting, compliance with government regulations, and financial oversight. These are critical for ensuring legal compliance and are often submitted to tax authorities or regulatory agencies.", "order": 105}, "Expense and Reimbursement Records": {"description": "Documents that support expense tracking and reimbursement processes. These typically include supporting evidence for employee expenses, management approvals, and payment confirmation. They are frequently used in both internal audits and operational controls.", "order": 106}, "Loan and Financing Agreements": {"description": "Documents outlining the terms and structure of loans, credit facilities, or other financial arrangements. These define obligations between borrowers and lenders, repayment conditions, interest rates, and covenants, and are central to debt and capital management.", "order": 107}, "Audit and Assurance Documents": {"description": "Documents generated during internal or external audit processes to assess financial accuracy, internal controls, and regulatory compliance. These reports provide independent assurance and are critical for corporate governance and risk management.", "order": 108}, "Clinical Records": {"description": "Patient-centered medical documentation, including medical histories, treatment notes, progress charts, test reports, surgical records, and discharge summaries. These are essential for diagnosis, treatment planning, and ongoing patient care.", "order": 201}, "Medication Management": {"description": "Documentation associated with prescribing, dispensing, and monitoring medications. Includes prescription records, drug administration logs, dosage instructions, and reports of adverse drug reactions. These ensure safe and effective patient treatment and adherence to protocols.", "order": 202}, "Insurance and Settlement": {"description": "Documents related to healthcare billing and reimbursements, such as insurance claim forms, treatment cost statements, and payment records. These ensure transparent and accurate settlement between patients, providers, and payers.", "order": 203}, "Compliance and Risk Management": {"description": "Documents used to ensure healthcare compliance and protect patient rights, including Patient Consent forms, where patients provide informed and explicit authorization for treatments, surgeries, or medical data usage; and Privacy Policies, which outline the measures and protocols a medical institution implements to collect, use, and safeguard personal health information in accordance with regulations such as HIPAA or GDPR.", "order": 204}, "Clinical Studies": {"description": "Documentation of clinical research, including the design, procedures, data collection, and outcome analysis of clinical trials. These documents are critical for medical innovation, drug approval, and evidence-based medicine.", "order": 205}, "Patient Communication": {"description": "Official communication records between patients and providers, including consent, directives, and telehealth interactions.", "order": 206}, "Usage Documentation": {"description": "Usage Documentation provides guidance for IT professionals and end users, covering system management, maintenance, and user instructions like manuals, quick start guides, and FAQs to help effectively use and manage the product or system.", "order": 301}, "Development Documentation": {"description": "Technical documents for software developers and engineers, such as system architecture designs, API specifications, data models, and sample code. These documents support software development and maintain team alignment across the development lifecycle.", "order": 302}, "Test Documentation": {"description": "Artifacts generated during the software testing process. Includes test plans, test cases, bug reports, and QA results. These help ensure product stability, security, and compliance with requirements.", "order": 303}, "Training Materials": {"description": "Educational content for users, developers, or administrators to build technical skills. Includes internal onboarding manuals, product training decks, online courses, and instructional videos.", "order": 306}, "Configuration Files": {"description": "Machine-readable files (e.g., JSON, XML, YAML) that define software behavior, environment settings, or system parameters. Used for customizing deployments, automating tasks, and enabling reproducibility in IT systems.", "order": 307}, "Log": {"description": "Log is dedicated to recording detailed, time-stamped entries related to IT activities, system events, and operational workflows. It provides a chronological history of actions taken, system behavior, or incidents encountered, which is essential for auditing, troubleshooting, and maintaining system integrity.", "order": 308}, "Contracts and Agreements": {"description": "A formal document that records the terms of a financial transaction or service, including loan agreements, investment management contracts, and insurance policies. These documents establish the rights, obligations and responsibilities between the parties involved.", "order": 401}, "NDA Form": {"description": "Legal document in which one or more parties agree to maintain the confidentiality of proprietary or sensitive information disclosed during a business or professional relationship. NDAs are designed to protect trade secrets, business plans, technical data, or personal information, and typically outline the scope of confidentiality, duration, and consequences of breach.", "order": 402}, "Patent": {"description": "Official legal document granted by a governmental authority that confers exclusive rights to an inventor or applicant to make, use, sell, or license a specific invention for a fixed period (usually 20 years). A patent includes technical descriptions, claims, and drawings that define the scope of legal protection. It serves as a key instrument for protecting intellectual property and promoting innovation.", "order": 403}, "License Agreement": {"description": "Legally enforceable contract that allows one party (the licensee) to use certain intellectual property owned by another party (the licensor), such as software, trademarks, patents, or copyrighted content. The agreement specifies terms such as usage scope, payment or royalties, duration, restrictions, and termination clauses, ensuring both parties' rights and responsibilities are clearly defined.", "order": 404}, "Court Document": {"description": "Official documents generated during litigation or court proceedings, detailing case facts, legal arguments, and judicial outcomes.", "order": 405}, "Corporate Legal Document": {"description": "Documents related to corporate governance, internal rules, board decisions, and shareholder agreements that define the legal framework of a business.", "order": 406}, "Certificate or Permit": {"description": "Official documents issued by authorities that certify an organization’s legal eligibility to operate in a specific field or fulfill regulatory standards.", "order": 407}, "Timesheet": {"description": "Timesheet is used to record and track employee working hours, attendance, and time allocation across projects or tasks. Accurate timesheet documentation supports payroll processing, project management, and compliance with labor regulations.", "order": 501}, "Performance Review": {"description": "Documents related to employee performance evaluations. It helps track individual progress, identify areas for development, and support decisions related to promotions, training, or role adjustments. Consistent performance reviews contribute to employee growth and organizational success.", "order": 502}, "CV": {"description": "Candidate CVs submitted during the recruitment process. It provides quick access to applicants’ professional backgrounds, qualifications, and work experience, supporting informed hiring decisions and streamlined candidate evaluation.", "order": 503}, "Employee Record": {"description": "Record includes documents from the hiring process—such as job requisitions, applications, interviews, and offer letters—as well as comprehensive employee records throughout their employment, including personal information, contracts, training, and separation details. These records support HR operations, compliance, and organizational transparency.", "order": 504}, "Compensation and Benefits": {"description": "Documents related to employee salary, bonuses, stock options, allowances, and benefits plans. These are of interest to nearly all employees and can have serious consequences if disclosed.", "order": 505}, "C Language": {"description": "The 'C Language' here refers collectively to both C and C++ source files. C provides low-level programming capabilities, while C++ builds on C with object-oriented features. Their source files typically use extensions such as .c, .cpp, and .h, and are compiled into machine code for high-performance applications.", "order": 601}, "Golang": {"description": "Golang source files use the .go extension and start with a package declaration. They are compiled and used to build efficient, concurrent systems such as web servers, APIs, and CLI tools.", "order": 602}, "Java": {"description": "Java source files use the .java extension, typically containing a public class matching the filename. They are compiled into bytecode and run on the Java Virtual Machine (JVM) for platform independence.", "order": 603}, "JavaScript": {"description": "JavaScript files (.js, .mjs) contain code for browser or server environments. They define variables, functions, and modules, and are widely used for building interactive web applications.", "order": 604}, "Kotlin": {"description": "Kotlin source files (.kt, .kts) define classes, functions, and scripts. Interoperable with Java, Kotlin is widely used in Android and backend development for its concise syntax and modern features.", "order": 605}, "PHP": {"description": "PHP files (.php) embed code within HTML and are executed server-side. They handle logic, database access, and dynamic content generation for websites and web applications.", "order": 606}, "Python": {"description": "Python source files (.py) contain readable, indented code used in scripting, data science, web development, and automation. They are interpreted by the Python runtime environment.", "order": 607}, "Shell": {"description": "Shell scripts (.sh, .bash) contain command-line instructions, variables, and control structures. They automate tasks in Unix/Linux environments and are interpreted by shell programs like bash or zsh.", "order": 608}, "C#": {"description": "C# source files (.cs) define classes, methods, and application logic. They are compiled by the .NET runtime and are widely used in Windows applications, games (Unity), and enterprise systems.", "order": 609}}}