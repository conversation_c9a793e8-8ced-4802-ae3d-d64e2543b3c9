[{"main_class_name": "Finance", "main_class_id": "10000", "sub_class_list": [{"sub_class_name": "Transaction Record", "sub_class_id": "10001", "description": "Documents recording the details of financial transactions, including transaction confirmations, receipts, wire transfer instructions, and bank statements. These records typically contain information such as transaction amount, date, counterparties, and purpose, and are used to verify the existence, completion, and validity of financial activities."}, {"sub_class_name": "Financial Report", "sub_class_id": "10002", "description": "Documents that present the financial condition and performance of an organization or individual. Includes balance sheets, income statements, cash flow statements, and shareholder equity reports. These reports are critical for internal decision-making, external audits, regulatory compliance, and investor communication."}, {"sub_class_name": "Credit and Risk Management", "sub_class_id": "10003", "description": "Documents related to the evaluation and mitigation of financial risks, including credit reports, market risk assessments, stress test results, and risk management policies. These are used by financial institutions to establish controls and prevent losses from credit, market, liquidity, or operational risks."}, {"sub_class_name": "Financial Strategy and Research", "sub_class_id": "10004", "description": "Includes investment strategy documents, market research reports, economic forecasts, and industry analysis. These documents provide insight into market trends and guide strategic financial planning and product development."}, {"sub_class_name": "Other (Discarded)", "sub_class_id": "10006", "description": "Other financial documents or records that do not fall into the predefined categories."}, {"sub_class_name": "Tax and Regulatory Documents", "sub_class_id": "10007", "description": "Documents prepared for tax reporting, compliance with government regulations, and financial oversight. These are critical for ensuring legal compliance and are often submitted to tax authorities or regulatory agencies."}, {"sub_class_name": "Expense and Reimbursement Records", "sub_class_id": "10008", "description": "Documents that support expense tracking and reimbursement processes. These typically include supporting evidence for employee expenses, management approvals, and payment confirmation. They are frequently used in both internal audits and operational controls."}, {"sub_class_name": "Loan and Financing Agreements", "sub_class_id": "10009", "description": "Documents outlining the terms and structure of loans, credit facilities, or other financial arrangements. These define obligations between borrowers and lenders, repayment conditions, interest rates, and covenants, and are central to debt and capital management."}, {"sub_class_name": "Audit and Assurance Documents", "sub_class_id": "10010", "description": "Documents generated during internal or external audit processes to assess financial accuracy, internal controls, and regulatory compliance. These reports provide independent assurance and are critical for corporate governance and risk management."}]}, {"main_class_name": "Healthcare", "main_class_id": "20000", "sub_class_list": [{"sub_class_name": "Clinical Records", "sub_class_id": "20001", "description": "Patient-centered medical documentation, including medical histories, treatment notes, progress charts, test reports, surgical records, and discharge summaries. These are essential for diagnosis, treatment planning, and ongoing patient care."}, {"sub_class_name": "Medication Management", "sub_class_id": "20002", "description": "Documentation associated with prescribing, dispensing, and monitoring medications. Includes prescription records, drug administration logs, dosage instructions, and reports of adverse drug reactions. These ensure safe and effective patient treatment and adherence to protocols."}, {"sub_class_name": "Insurance and Settlement", "sub_class_id": "20003", "description": "Documents related to healthcare billing and reimbursements, such as insurance claim forms, treatment cost statements, and payment records. These ensure transparent and accurate settlement between patients, providers, and payers."}, {"sub_class_name": "Compliance and Risk Management", "sub_class_id": "20004", "description": "Documents used to ensure healthcare compliance and protect patient rights, including Patient Consent forms, where patients provide informed and explicit authorization for treatments, surgeries, or medical data usage; and Privacy Policies, which outline the measures and protocols a medical institution implements to collect, use, and safeguard personal health information in accordance with regulations such as HIPAA or GDPR."}, {"sub_class_name": "Clinical Studies", "sub_class_id": "20005", "description": "Documentation of clinical research, including the design, procedures, data collection, and outcome analysis of clinical trials. These documents are critical for medical innovation, drug approval, and evidence-based medicine."}, {"sub_class_name": "Other (Discarded)", "sub_class_id": "20006", "description": "Other healthcare documents or records that do not fall into the predefined categories."}, {"sub_class_id": "20007", "sub_class_name": "Patient Communication", "description": "Official communication records between patients and providers, including consent, directives, and telehealth interactions."}, {"sub_class_name": "Billing and Claims (Discarded)", "sub_class_id": "20008", "description": "Documents used for billing medical services and submitting claims, often including codes, invoices, and reimbursement forms."}]}, {"main_class_name": "Information Technology", "main_class_id": "30000", "sub_class_list": [{"sub_class_name": "User Documentation (Discarded)", "sub_class_id": "30001", "description": "For end users, it provides detailed guidance on how to use a product or system. Includes user manual, quick start guide, FAQ (Frequently Asked Questions) and use cases. User documentation helps users understand product functionality and use the product effectively."}, {"sub_class_name": "Development Documentation", "sub_class_id": "30002", "description": "Technical documents for software developers and engineers, such as system architecture designs, API specifications, data models, and sample code. These documents support software development and maintain team alignment across the development lifecycle."}, {"sub_class_name": "Test Documentation", "sub_class_id": "30003", "description": "Artifacts generated during the software testing process. Includes test plans, test cases, bug reports, and QA results. These help ensure product stability, security, and compliance with requirements."}, {"sub_class_name": "Operational Documentation (Discarded)", "sub_class_id": "30004", "description": "Provides the guidance and processes system administrators and IT professionals need to manage and maintain IT systems. Includes system administration manuals, backup and recovery guides, security protocols and maintenance plans."}, {"sub_class_name": "Source Code (Discarded)", "sub_class_id": "30005", "description": "Source code is written in a programming language, which can be a high-level language (such as Python, Java, C++) or a low-level language that is closer to machine language (such as assembly language). It exists in text form, so it can be created and edited using a text editor."}, {"sub_class_name": "Training Materials", "sub_class_id": "30006", "description": "Educational content for users, developers, or administrators to build technical skills. Includes internal onboarding manuals, product training decks, online courses, and instructional videos."}, {"sub_class_name": "Configuration Files", "sub_class_id": "30007", "description": "Machine-readable files (e.g., JSON, XML, YAML) that define software behavior, environment settings, or system parameters. Used for customizing deployments, automating tasks, and enabling reproducibility in IT systems."}, {"sub_class_name": "Other (Discarded)", "sub_class_id": "30008", "description": "Other information technology related documents or records that do not fall into the predefined categories."}, {"sub_class_name": "Log", "sub_class_id": "30009", "description": "Log is dedicated to recording detailed, time-stamped entries related to IT activities, system events, and operational workflows. It provides a chronological history of actions taken, system behavior, or incidents encountered, which is essential for auditing, troubleshooting, and maintaining system integrity."}, {"sub_class_name": "Usage Documentation", "sub_class_id": "30010", "description": "Usage Documentation provides guidance for IT professionals and end users, covering system management, maintenance, and user instructions like manuals, quick start guides, and FAQs to help effectively use and manage the product or system."}]}, {"main_class_name": "Education (Discarded)", "main_class_id": "40000"}, {"main_class_name": "Manufacture (Discarded)", "main_class_id": "50000"}, {"main_class_name": "Other", "main_class_id": "60000"}, {"main_class_name": "Legal", "main_class_id": "70000", "sub_class_list": [{"sub_class_name": "Contracts and Agreements", "sub_class_id": "10005", "description": "A formal document that records the terms of a financial transaction or service, including loan agreements, investment management contracts, and insurance policies. These documents establish the rights, obligations and responsibilities between the parties involved."}, {"sub_class_name": "NDA Form", "sub_class_id": "70002", "description": "Legal document in which one or more parties agree to maintain the confidentiality of proprietary or sensitive information disclosed during a business or professional relationship. NDAs are designed to protect trade secrets, business plans, technical data, or personal information, and typically outline the scope of confidentiality, duration, and consequences of breach."}, {"sub_class_name": "Patent", "sub_class_id": "70003", "description": "Official legal document granted by a governmental authority that confers exclusive rights to an inventor or applicant to make, use, sell, or license a specific invention for a fixed period (usually 20 years). A patent includes technical descriptions, claims, and drawings that define the scope of legal protection. It serves as a key instrument for protecting intellectual property and promoting innovation."}, {"sub_class_name": "License Agreement", "sub_class_id": "70004", "description": "Legally enforceable contract that allows one party (the licensee) to use certain intellectual property owned by another party (the licensor), such as software, trademarks, patents, or copyrighted content. The agreement specifies terms such as usage scope, payment or royalties, duration, restrictions, and termination clauses, ensuring both parties' rights and responsibilities are clearly defined."}, {"sub_class_name": "Court Document", "sub_class_id": "70005", "description": "Official documents generated during litigation or court proceedings, detailing case facts, legal arguments, and judicial outcomes."}, {"sub_class_name": "Corporate Legal Document", "sub_class_id": "70006", "description": "Documents related to corporate governance, internal rules, board decisions, and shareholder agreements that define the legal framework of a business."}, {"sub_class_name": "Certificate or Permit", "sub_class_id": "70007", "description": "Official documents issued by authorities that certify an organization’s legal eligibility to operate in a specific field or fulfill regulatory standards."}]}, {"main_class_name": "HR", "main_class_id": "80000", "sub_class_list": [{"sub_class_name": "Timesheet", "sub_class_id": "80001", "description": "Timesheet is used to record and track employee working hours, attendance, and time allocation across projects or tasks. Accurate timesheet documentation supports payroll processing, project management, and compliance with labor regulations."}, {"sub_class_name": "Performance Review", "sub_class_id": "80002", "description": "Documents related to employee performance evaluations. It helps track individual progress, identify areas for development, and support decisions related to promotions, training, or role adjustments. Consistent performance reviews contribute to employee growth and organizational success."}, {"sub_class_name": "Recruitment (Discarded)", "sub_class_id": "80003", "description": "Documents related to the hiring process, including job requisitions, job descriptions, candidate applications, interview records, background checks, and offer letters. These documents are used to track recruitment activities, evaluate candidate qualifications, and ensure compliance with hiring policies. Proper management of recruitment documentation supports transparency, consistency, and alignment with organizational hiring standards."}, {"sub_class_name": "CV", "sub_class_id": "80004", "description": "Candidate CVs submitted during the recruitment process. It provides quick access to applicants’ professional backgrounds, qualifications, and work experience, supporting informed hiring decisions and streamlined candidate evaluation."}, {"sub_class_name": "Employee Record", "sub_class_id": "80005", "description": "Record includes documents from the hiring process—such as job requisitions, applications, interviews, and offer letters—as well as comprehensive employee records throughout their employment, including personal information, contracts, training, and separation details. These records support HR operations, compliance, and organizational transparency."}, {"sub_class_name": "Compensation and Benefits", "sub_class_id": "80006", "description": "Documents related to employee salary, bonuses, stock options, allowances, and benefits plans. These are of interest to nearly all employees and can have serious consequences if disclosed."}]}, {"main_class_name": "Source Code", "main_class_id": "90000", "sub_class_list": [{"sub_class_name": "C Language", "sub_class_id": "90001", "description": "The 'C Language' here refers collectively to both C and C++ source files. C provides low-level programming capabilities, while C++ builds on C with object-oriented features. Their source files typically use extensions such as .c, .cpp, and .h, and are compiled into machine code for high-performance applications."}, {"sub_class_name": "Golang", "sub_class_id": "90002", "description": "Golang source files use the .go extension and start with a package declaration. They are compiled and used to build efficient, concurrent systems such as web servers, APIs, and CLI tools."}, {"sub_class_name": "Java", "sub_class_id": "90003", "description": "Java source files use the .java extension, typically containing a public class matching the filename. They are compiled into bytecode and run on the Java Virtual Machine (JVM) for platform independence."}, {"sub_class_name": "JavaScript", "sub_class_id": "90004", "description": "JavaScript files (.js, .mjs) contain code for browser or server environments. They define variables, functions, and modules, and are widely used for building interactive web applications."}, {"sub_class_name": "<PERSON><PERSON><PERSON>", "sub_class_id": "90005", "description": "Kotlin source files (.kt, .kts) define classes, functions, and scripts. Interoperable with Java, Kotlin is widely used in Android and backend development for its concise syntax and modern features."}, {"sub_class_name": "PHP", "sub_class_id": "90006", "description": "PHP files (.php) embed code within HTML and are executed server-side. They handle logic, database access, and dynamic content generation for websites and web applications."}, {"sub_class_name": "Python", "sub_class_id": "90007", "description": "Python source files (.py) contain readable, indented code used in scripting, data science, web development, and automation. They are interpreted by the Python runtime environment."}, {"sub_class_name": "Shell", "sub_class_id": "90008", "description": "Shell scripts (.sh, .bash) contain command-line instructions, variables, and control structures. They automate tasks in Unix/Linux environments and are interpreted by shell programs like bash or zsh."}, {"sub_class_name": "C#", "sub_class_id": "90009", "description": "C# source files (.cs) define classes, methods, and application logic. They are compiled by the .NET runtime and are widely used in Windows applications, games (Unity), and enterprise systems."}]}]