from connector.sharepoint_token_connector import SharePointTokenConnector
from connector.google_connector import GoogleConnector
from util.enum_ import StorageType, UserType
from storage.service.profiles import get_storage_profile_by_id
from exts import logger
from domain_model.global_cache import GlobalCache

def delete_permissions_db(cache, file_info: dict, perm_ids: list):
    """ Delete file permissions from db and update share stats"""
    from storage.service.identity import get_storage_identity_by_identifier

    # update file_metadata 
    file_metadata = file_info.get('file_metadata')
    collaborators = file_metadata.get('collaborators')
    links = file_metadata.get('share_link')
    coll_new = {k: v for k, v in collaborators.items() if v.get('perm_id') not in perm_ids}
    file_metadata['collaborators'] = coll_new
    links_new = [link for link in links if link.get('perm_id') not in perm_ids]
    file_metadata['share_link'] = links_new
        
    # update reserve_json1.shared_data
    file_uuid = file_info['id']
    storage_id = file_info['storage_id']
    reserve_json1 = file_info.get('reserve_json1')
    shared_data = reserve_json1.get('shared_data')

    if len(collaborators) != len(coll_new):
        identities = get_storage_identity_by_identifier(storage_id, list(coll_new.keys()))
        with_coll = {
            'with_internal_collaborators': any(i['type'] == UserType.MEMBER for i in identities),
            'with_external_collaborators': any(i['type'] == UserType.GUEST for i in identities)
            }
        shared_data.update(with_coll)
    
    if len(links) != len(links_new):
        with_link = {
            'with_public_shareable_link': False,
            'with_internal_shareable_link': False,
            'with_external_shareable_link': False,
            }    
        for link in links_new:
            scope = link['scope']
            if scope == 'public':
                with_link['with_public_shareable_link'] = True
            elif scope == 'organization' or scope == 'internal':
                with_link['with_internal_shareable_link'] = True
            else: # scope is 'external'
                with_link['with_external_shareable_link'] = True
        shared_data.update(with_link)
    
    cache.update_file(file_uuid, {'file_metadata': file_metadata, 
                                  'reserve_json1': reserve_json1})
    
def delete_permissions(cache: GlobalCache, file_info: dict, perm_ids: list) -> tuple[list, dict]:
    """ Delete file permissions """
    storage = get_storage_profile_by_id(file_info.get('storage_id'))
    if storage is None:
        err_msg = f"Could not get storage profile of {file_info.get('file_name')}"
        logger.error(err_msg)
        raise Exception(err_msg)
    storage_type = storage['type']
    auth_info = storage['auth_info']
    file_id = file_info.get('file_attributes', {}).get('remote_file_id')
    
    deleted = []
    errors = {}
    if storage_type == StorageType.SHAREPOINT_OL:
        drive_id = file_info.get('file_attributes', {}).get('drive_id')
        conn = SharePointTokenConnector(auth_info)
        for perm_id in perm_ids:
            success, err_msg = conn.delete_permission(drive_id, file_id, perm_id)
            if success:
                deleted.append(perm_id)
            else:
                errors[perm_id] = err_msg
    elif storage_type == StorageType.GOOGLE:
        conn = GoogleConnector(auth_info)
        for perm_id in perm_ids:
            success, err_msg = conn.delete_permission(file_id, perm_id)
            if success:
                deleted.append(perm_id)
            else:
                errors[perm_id] = err_msg
    
    if deleted:
        delete_permissions_db(cache, file_info, deleted)
    return deleted, errors

def modify_permission_db(cache, file_info: dict, perm_id: str, role: str):
    """ Change role of file permission in file metadata """
    file_metadata = file_info.get('file_metadata')
    collaborators = file_metadata.get('collaborators')
    links = file_metadata.get('share_link')
    for coll in collaborators.values():
        if perm_id == coll.get('perm_id'):
            coll['permission'] = role
            break
    else:
        for link in links:
            if perm_id == link.get('perm_id'):
                link['permission'] = role
                break
    cache.update_file(file_info['id'], {'file_metadata': file_metadata})

def modify_permission(cache, file_info: dict, perm_id: str, role: str) -> tuple[bool, str]:
    """ Change role of file permission """
    storage = get_storage_profile_by_id(file_info.get('storage_id'))
    if storage is None:
        err_msg = f"Could not get storage profile of {file_info.get('file_name')}"
        logger.error(err_msg)
        raise Exception(err_msg)
    storage_type = storage['type']
    auth_info = storage['auth_info']
    file_id = file_info.get('file_attributes', {}).get('remote_file_id')
    
    updated = False
    if storage_type == StorageType.SHAREPOINT_OL:
        drive_id = file_info.get('file_attributes', {}).get('drive_id')
        conn = SharePointTokenConnector(auth_info)
        updated, err_msg = conn.modify_permission(drive_id, file_id, perm_id, role)
    elif storage_type == StorageType.GOOGLE:
        conn = GoogleConnector(auth_info)
        updated, err_msg = conn.modify_permission(file_id, perm_id, role)
    
    if updated:
        modify_permission_db(cache, file_info, perm_id, role)
    return updated, err_msg

def get_perm_info(file_info: dict, perm_id: str) -> tuple[str, dict]:
    """ Get permission info for file action details """
    from storage.service.identity import get_storage_identity_by_identifier
    collaborators = file_info.get('file_metadata').get('collaborators', {})
    links = file_info.get('file_metadata').get('share_link', [])
    
    for identifier, coll in collaborators.items():
        if perm_id == coll.get('perm_id'):
            identity = get_storage_identity_by_identifier(file_info.get('storage_id'), [identifier])
            if not identity:
                return '', {}
            identity = identity[0]
            coll_info = {'name': identity.get('name'),
                         'email': identity.get('email'),
                         'type': identity.get('type')}
            return 'coll', coll_info
    for link in links:
        if perm_id == link.get('perm_id'):
            link_info = {'scope': link.get('scope'), 
                         'link': link.get('link')}
            return 'link', link_info
    return '', {}