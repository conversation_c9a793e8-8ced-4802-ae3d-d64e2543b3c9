import uuid
from exts import Base, Session
from sqlalchemy import Column, String, Integer, DateTime, and_, select
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB, UUID
from datetime import timezone
import traceback
from util.config import configs

class FileAction(Base):
    __tablename__ = "file_action"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_id = Column(UUID(as_uuid=True), nullable=False)
    scan_id = Column(UUID(as_uuid=True), nullable=False)
    action = Column(Integer,nullable=False)
    action_type = Column(Integer,nullable=False)
    action_detail = Column(JSONB,nullable=False)
    status = Column(Integer,nullable=False)
    failed_reason = Column(String)
    ctime = Column(DateTime, server_default=func.now())

    def __repr__(self):
        return (f"<FileAction(id={self.id}, file_id='{self.file_id}', "
                f"scan_id={self.scan_id}, action='{self.action}', action_type={self.action_type}, "
                f"action_detail={self.action_detail}, status={self.status}, failed_reason={self.failed_reason}, ctime={self.ctime})>")

    def to_dict(self):
        return {
            'id': str(self.id),
            'file_id': self.file_id,
            'scan_id':self.scan_id,
            'action': self.action,
            'action_type':self.action_type,
            'action_detail': self.action_detail,
            'status': self.status,
            'failed_reason': self.failed_reason,
            'ctime': self.ctime.replace(tzinfo=timezone.utc).timestamp() if self.ctime else None
        }

def get_file_actions(conditions, logger):
    condition_fields = ["scan_id", "file_id", "action", "action_type", "status"]

    try:
        session = Session()
        params = []
        # generate the query filters
        for key, value in conditions.items():
            if key not in condition_fields:
                continue

            params.append(getattr(FileAction, key) == value)

        #sort_field = conditions.get('sort_field') if conditions.get('sort_field') is not None  else 'ctime'
        #allow_sort_fields = ['ctime']
        #if sort_field not in allow_sort_fields:
        #sort_field = 'ctime'
        sort_method = conditions.get('sort_method') if conditions.get('sort_method') is not None  else 'desc'
        column = getattr(FileAction, 'ctime')
        if sort_method == 'desc':
            results = session.query(FileAction).filter(*params).order_by(column.desc())
        else:
            results = session.query(FileAction).filter(*params).order_by(column.asc())

        total = results.count()
        page = conditions.get('page')
        per_page = conditions.get('per_page', 10)

        if page is not None:
            page = int(page)
            per_page = int(per_page)
            slice_from = per_page * (page - 1)
            slice_to = per_page * page
            results = results.slice(slice_from, slice_to)

        actions = results.all()
        if actions:
            return [action.to_dict() for action in actions], total
        else:
            return [], 0
    except:
        logger.exception(traceback.format_exc())
        return [], 0
    finally:
        session.close()

def get_file_action_set(scan_id, file_id):
    query = (select(FileAction.action).distinct()
            .where(FileAction.file_id == file_id, FileAction.scan_id == scan_id))
    with Session() as session:
        return session.execute(query).scalars().all()

def add_file_action(payload: dict, logger):
    try:
        max_actions = configs["file_action"]["max_actions_count"]
        session = Session()
        actions, total = get_file_actions({"scan_id": payload['scan_id'], "file_id": payload['file_id'], "sort_method": 'asc'}, logger)
        if total >= max_actions:
            delete_file_actions({'id':actions[0].id}, logger)
        session.add(FileAction(**payload))
        session.commit()
    except Exception as e:
        logger.error(e)
    finally:
        session.close()

def delete_file_actions(conditions, logger):
    try:
        params = []
        for key, value in conditions.items():
            params.append(getattr(FileAction, key) == value)

        with Session() as session:
            session.query(FileAction).filter(and_(*params)).delete()
            session.commit()
    except:
        logger.exception(traceback.format_exc())