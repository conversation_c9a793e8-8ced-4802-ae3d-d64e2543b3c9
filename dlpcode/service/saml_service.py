from onelogin.saml2.auth import OneLogin_Saml2_Auth
from onelogin.saml2.settings import OneLogin_Saml2_Settings
from sqlalchemy.exc import SQLAlchemyError
from flask import request



google_idp = {
    "entityId": "https://sts.windows.net/112dbc6b-af5d-4564-b13a-22909f6d053e/",
    "singleSignOnService": {
        "url": "https://login.microsoftonline.com/112dbc6b-af5d-4564-b13a-22909f6d053e/saml2",
        "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
    },
    "singleLogoutService": {
        "url": "https://login.microsoftonline.com/112dbc6b-af5d-4564-b13a-22909f6d053e/saml2",
        "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
    },
    "x509cert": "MIIC8DCCAdigAwIBAgIQFQPUaVnemKRPC/sGW0cJDTANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yNTA4MDcxNzMwMjNaFw0yODA4MDcxNzMwMjNaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3B/QBJP/jwLRYWJ0Smfpin7osyhntFZd69WHScJLIhZ1hnIxHR7W4K6KqPGBXWBMx603u+vuJDQxU9Cp6pr2wqRn/WnOIiQjmPUpwHaLYTcmKoyKm2OU8RMxzhpLmNxkdBS/wkP43cA/uIr6Pa2cGu/VWH7RGvWedLDXRqWgZ1Nt1W7m/2SnxGLNz/WmaJYuzzhvd8FCqKbab6/CZUwfuIf1L7/V510FmeDgnUHPGvPlACBmnj9vzFLADIpbSdMXcEjgPvQwaxwQ1ioE0DjNFmMrzz/jZpsyapYYGpCRb1DfE2SWAyUrzngmv396Ids2451k7g+umzQaB2IWl2yNeQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCv3QeX0eBJvcCHYcGJtilYXHdDfWQpbl2RbEae/YScc16REcM057PKO5C63J3csCTPQiu/H3OTpXV09tn4EbnTG/U4fNTNzHTW2MgBcYfeclBvEENgDDZl/KMrI7oyGhZQK9bUCfj4UCggT78T3b5SbaAn+kCE0kM7q/7/jsKx220870tPnI4y9bQMYjEmWl5fvy1D+88wVAfCpyQNwTlwAnct578/OYvzJN9m1q9t4UMgEBfPY7PSOae7PgtaA2ZOYNgcCj93BuPoa1JVKnTovcvLobRHkbWyQJ79JAnenCla7Si+gQ9lN3cYM44/g/UKrIrOpB/m/N7UyMFznTaH"
}

ms_idp = {
    "entityId": "https://accounts.google.com/o/saml2?idpid=C01k0vjqg",
    "singleSignOnService": {
        "url": "https://accounts.google.com/o/saml2/idp?idpid=C01k0vjqg",
        "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
    },
    "x509cert": "MIIDdDCCAlygAwIBAgIGAZYXdrnNMA0GCSqGSIb3DQEBCwUAMHsxFDASBgNVBAoTC0dvb2dsZSBJbmMuMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MQ8wDQYDVQQDEwZHb29nbGUxGDAWBgNVBAsTD0dvb2dsZSBGb3IgV29yazELMAkGA1UEBhMCVVMxEzARBgNVBAgTCkNhbGlmb3JuaWEwHhcNMjUwNDA4MjIxMjU4WhcNMzAwNDA3MjIxMjU4WjB7MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEPMA0GA1UEAxMGR29vZ2xlMRgwFgYDVQQLEw9Hb29nbGUgRm9yIFdvcmsxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtV1IFFVlo5Q4tzpfE6LjfT4nqOrb1vIJQiV3DZ/bbt3aKoDkFNuxnHdz9L5qczeuL3DfXuYZX76967rYgm3Yflhyt05NFW7Rev9N1KNJW8A6WM3gG6hw/srApIDLAIaLQJnakLCNLilgwrg875Ub136fuWCWIzCivNX/49799WPK2rCvjyE+KtZEXE3ztHwn9AX/7Xjq+8z3etx/uS6zFrdMaNZJJa/C0Hl5R3FocJFNYym3x+DypqnajdMwyRk4Ky71EgtIDUHofnyWe9ERLbH/el2CobIbtNZ/W6qnhvbLHFh3hO/UftBJ1sY2zpEoIICz+NmH5pibQ0zfhsL+cQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAXzyFmlMCBdsRyXMpP2Hh2iUvh0XrbHtdeoGRgb6ke/GwMoJpYWvHAgqM8o66uaZam2Vb6lrIrdiEAMhof8Jn83SQQbue0Le/onsouNxNv16oFEhVHblOvwfQf3uP95qFkJx5JIHzeguR0pFNEOOxtm62r3ZGCQrprFjaJjlTdbPhKCu1m02fO+dACmjgZTUXgyrn2CPrRfB4N2f/NgfNVvkTzGci1r2yJQUXZhoeSdz81m7SYy2TTRL7wXyu9L7T3qPhzzVPEvr2/dstorDOQ1Yb2Bcjd5UEsyc+OKb8LvcME4t/iBo+SBkCmrlC7EIeMlGwfEvOgiyQxW//44Yyw"
}

settings_data = {
    "strict": True,
    "debug": True,
    # "idp": {},
    "sp": {
        "entityId": "https://10.59.88.99/api/v1/saml/metadata",
        "assertionConsumerService": {
            "url": "https://10.59.88.99/api/v1/saml/login",
            "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
        },
        # "singleLogoutService": {
        #     "url": "https://api-dev1.fortidevice-dev.forticloud.com/uem/saml/logout",
        #     "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
        # },
        "x509cert": "",
        "privateKey": ""
    },
    "security": {
        "authnRequestsSigned": True,
        "wantAssertionsSigned": True,
        "wantMessagesSigned": False
    }
}
env_type = "dev" 


def prepare_flask_request(request):
    # Convert Flask request to dict format expected by OneLogin
    # url_data = request.url.split('?')
    return {
        'https': 'on' if request.scheme == 'https' else 'off',
        'http_host': request.host,
        'server_port': request.environ.get('SERVER_PORT'),
        'script_name': request.path,
        'get_data': request.args.copy(),
        'post_data': request.form.copy()
    }

def get_fortidata_cert_secret():
    #nancy todo change the env_type
    secret_data = {}
    if env_type == "test":
        cert_data = secret_data.get("client_cert_test.pem", "")
        key_data = secret_data.get("client_cert_key_test.pem", "")
        ca_cert_data = secret_data.get("Fortinet-CA2+fortinet-subca2003_test.pem", "")
    elif env_type == "dev":
        cert_data = secret_data.get("client_cert_dev.pem", "")
        key_data = secret_data.get("client_cert_key_dev.pem", "")
        ca_cert_data = secret_data.get("Fortinet-CA2+fortinet-subca2003_dev.pem", "")
    else:
        cert_data = secret_data.get("client_cert_prod.pem", "")
        key_data = secret_data.get("client_cert_key_prod.pem", "")
        ca_cert_data = secret_data.get("Fortinet-CA2+fortinet-subca2003_prod.pem", "")

    return cert_data, key_data, ca_cert_data


def get_saml_settings_data(external_idp_id: str):
    #todo nancy get saml settings from database
    # saml_cert, saml_key, _ = get_fortidata_cert_secret()
    # settings_data["sp"]["x509cert"] = saml_cert
    # settings_data["sp"]["privateKey"] = saml_key

    use_google_sso = True
    if use_google_sso:
        settings_data["idp"] = google_idp
    else:
        settings_data["idp"] = ms_idp

    #nancy todo, use logger
    # logger.info(f"setting_data:{json.dumps(settings_data, indent=2)}")

    saml_settings = OneLogin_Saml2_Settings(settings=settings_data)
    return saml_settings

def get_saml_login_auth(flask_request):
    request_data = prepare_flask_request(flask_request)
    external_idp_id = "<external_idp_id>" # todo nancy, get external_idp_id from request
    settings_data = get_saml_settings_data(external_idp_id)
    auth = OneLogin_Saml2_Auth(request_data, old_settings=settings_data)
    return auth