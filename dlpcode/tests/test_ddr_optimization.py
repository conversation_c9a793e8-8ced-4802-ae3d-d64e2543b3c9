"""
DDR优化功能测试
验证各个优化组件的功能和性能
"""

import time
import pytest
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# 导入优化的组件
from ddr.service.optimized_dedup_repo import OptimizedDedupRepo, DedupRepoSingleKeyOptimized
from ddr.service.optimized_event_processor import OptimizedDDRProcessService, AdaptiveBatchProcessor
from ddr.service.optimized_task_scheduler import OptimizedTaskScheduler, TaskPriority
from ddr.service.optimized_db_operations import OptimizedDBManager, OptimizedActivityOperations
from ddr.service.monitoring_and_logging import DDRMonitor, MetricType


class TestOptimizedDedupRepo:
    """测试优化的去重仓库"""
    
    def test_claim_and_release(self):
        """测试声明和释放"""
        dedup_key = "test_key_001"
        
        # 第一次声明应该成功
        assert OptimizedDedupRepo.try_claim(dedup_key, 60) == True
        
        # 重复声明应该失败
        assert OptimizedDedupRepo.try_claim(dedup_key, 60) == False
        
        # 检查存在性
        assert OptimizedDedupRepo.exists(dedup_key) == True
    
    def test_member_operations(self):
        """测试成员操作"""
        dedup_key = "test_key_002"
        
        # 声明键
        assert OptimizedDedupRepo.try_claim(dedup_key, 60) == True
        
        # 添加成员
        assert OptimizedDedupRepo.add_member(dedup_key, "activity_1", 60) == True
        assert OptimizedDedupRepo.add_member(dedup_key, "activity_2", 60) == True
        
        # 获取成员
        members = OptimizedDedupRepo.get_members(dedup_key)
        assert len(members) == 2
        assert "activity_1" in members
        assert "activity_2" in members
        
        # 弹出成员
        popped_members = OptimizedDedupRepo.pop_members(dedup_key)
        assert len(popped_members) == 2
    
    def test_performance_comparison(self):
        """性能对比测试"""
        num_operations = 1000
        
        # 测试优化版本性能
        start_time = time.time()
        for i in range(num_operations):
            key = f"perf_test_{i}"
            OptimizedDedupRepo.try_claim(key, 60)
        optimized_time = time.time() - start_time
        
        print(f"Optimized version: {optimized_time:.3f}s for {num_operations} operations")
        print(f"Throughput: {num_operations/optimized_time:.1f} ops/sec")
        
        # 性能应该合理（每秒至少1000次操作）
        assert num_operations / optimized_time > 1000


class TestAdaptiveBatchProcessor:
    """测试自适应批处理器"""
    
    def test_batch_size_calculation(self):
        """测试批次大小计算"""
        processor = AdaptiveBatchProcessor()
        
        # 测试不同场景下的批次大小
        size1 = processor.get_optimal_batch_size(queue_depth=1000, last_processing_time=10.0)
        size2 = processor.get_optimal_batch_size(queue_depth=50000, last_processing_time=5.0)
        size3 = processor.get_optimal_batch_size(queue_depth=100, last_processing_time=60.0)
        
        # 高队列深度应该增加批次大小
        assert size2 > size1
        
        # 长处理时间应该减小批次大小
        assert size3 < size1
        
        # 所有批次大小都应该在合理范围内
        assert processor.min_batch_size <= size1 <= processor.max_batch_size
        assert processor.min_batch_size <= size2 <= processor.max_batch_size
        assert processor.min_batch_size <= size3 <= processor.max_batch_size
    
    def test_performance_recording(self):
        """测试性能记录"""
        processor = AdaptiveBatchProcessor()
        
        # 记录一些性能数据
        processor.record_performance(1000, 15.0, 66.7)
        processor.record_performance(1500, 20.0, 75.0)
        processor.record_performance(800, 12.0, 66.7)
        
        # 验证历史记录
        assert len(processor._performance_history) == 3


class TestOptimizedTaskScheduler:
    """测试优化的任务调度器"""
    
    def test_priority_calculation(self):
        """测试优先级计算"""
        scheduler = OptimizedTaskScheduler()
        
        # 模拟任务
        task = {
            'id': 'test_task_001',
            'name': 'Test Task'
        }
        
        # 测试不同场景的优先级
        priority1 = scheduler.calculate_task_priority(task, has_new_activities=True, cleanup_activities=False)
        priority2 = scheduler.calculate_task_priority(task, has_new_activities=False, cleanup_activities=True)
        priority3 = scheduler.calculate_task_priority(task, has_new_activities=False, cleanup_activities=False)
        
        # 有新活动的优先级应该高于清理任务
        assert priority1.value >= priority2.value
        
        # 清理任务优先级应该高于无活动任务
        assert priority2.value >= priority3.value
    
    def test_load_management(self):
        """测试负载管理"""
        scheduler = OptimizedTaskScheduler()
        scheduler.max_concurrent_tasks = 2
        
        task = {
            'id': 'test_task_002',
            'name': 'Load Test Task'
        }
        
        # 在负载限制内应该可以调度
        assert scheduler.should_schedule_task(task, TaskPriority.NORMAL) == True
        
        # 模拟高负载
        scheduler._current_load = 2
        
        # 普通优先级任务应该被拒绝
        assert scheduler.should_schedule_task(task, TaskPriority.NORMAL) == False
        
        # 高优先级任务应该可以调度
        assert scheduler.should_schedule_task(task, TaskPriority.HIGH) == True


class TestOptimizedDBOperations:
    """测试优化的数据库操作"""
    
    @patch('ddr.service.optimized_db_operations.rs_client')
    @patch('ddr.service.optimized_db_operations.Session')
    def test_activity_count_fast(self, mock_session, mock_redis):
        """测试快速活动计数"""
        # 模拟数据库查询结果
        mock_query = Mock()
        mock_query.scalar.return_value = 150
        mock_session.return_value.__enter__.return_value.query.return_value = mock_query
        
        count = OptimizedActivityOperations.count_activities_fast(
            storage_id="test_storage",
            status=1,
            created_at__gte=datetime.now() - timedelta(hours=1),
            created_at__lt=datetime.now()
        )
        
        assert count == 150
    
    def test_db_manager_initialization(self):
        """测试数据库管理器初始化"""
        # 这个测试需要实际的数据库配置，在实际环境中运行
        pass


class TestDDRMonitor:
    """测试DDR监控器"""
    
    def test_metrics_collection(self):
        """测试指标收集"""
        monitor = DDRMonitor()
        
        # 记录一些指标
        monitor.metrics.increment_counter("test.counter", 5.0, {"tag": "value"})
        monitor.metrics.set_gauge("test.gauge", 100.0)
        monitor.metrics.record_histogram("test.histogram", 1.5)
        
        # 获取指标摘要
        summary = monitor.metrics.get_metrics_summary(minutes=1)
        
        assert "counters" in summary
        assert "gauges" in summary
        assert "histograms" in summary
        assert summary["total_metrics"] > 0
    
    def test_trace_operation(self):
        """测试操作追踪"""
        monitor = DDRMonitor()
        
        with monitor.trace_operation("test_operation", task_id="test_task"):
            time.sleep(0.1)  # 模拟操作
        
        # 验证指标被记录
        summary = monitor.metrics.get_metrics_summary(minutes=1)
        assert any("operations" in key for key in summary["counters"].keys())
    
    def test_health_checks(self):
        """测试健康检查"""
        monitor = DDRMonitor()
        
        # 注册一个测试健康检查
        def test_check():
            return True
        
        monitor.health_checker.register_check("test_check", test_check)
        
        # 运行健康检查
        status = monitor.health_checker.run_checks()
        
        assert "overall_healthy" in status
        assert "checks" in status
        assert "test_check" in status["checks"]
        assert status["checks"]["test_check"]["healthy"] == True


class TestIntegration:
    """集成测试"""
    
    def test_end_to_end_processing_simulation(self):
        """端到端处理模拟测试"""
        # 这是一个简化的端到端测试
        
        # 1. 创建去重键
        dedup_key = "integration_test_001"
        assert OptimizedDedupRepo.try_claim(dedup_key, 300) == True
        
        # 2. 添加活动到去重组
        OptimizedDedupRepo.add_member(dedup_key, "activity_001", 300)
        OptimizedDedupRepo.add_member(dedup_key, "activity_002", 300)
        
        # 3. 模拟处理完成，获取成员
        members = OptimizedDedupRepo.pop_members(dedup_key)
        assert len(members) == 2
        
        # 4. 记录处理指标
        monitor = DDRMonitor()
        monitor.record_processing_metrics(
            task_name="integration_test",
            processed=2,
            triggered=2,
            skipped=0,
            duration=1.5
        )
        
        # 5. 验证指标
        summary = monitor.metrics.get_metrics_summary(minutes=1)
        assert summary["total_metrics"] > 0
    
    def test_concurrent_processing(self):
        """并发处理测试"""
        def worker(worker_id):
            for i in range(10):
                key = f"concurrent_test_{worker_id}_{i}"
                if OptimizedDedupRepo.try_claim(key, 60):
                    OptimizedDedupRepo.add_member(key, f"activity_{worker_id}_{i}", 60)
                    time.sleep(0.01)  # 模拟处理时间
        
        # 启动多个工作线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有异常发生（如果有异常，测试会失败）
        assert True


if __name__ == "__main__":
    # 运行基本测试
    print("Running DDR optimization tests...")
    
    # 测试去重仓库
    test_dedup = TestOptimizedDedupRepo()
    test_dedup.test_claim_and_release()
    test_dedup.test_member_operations()
    print("✓ Dedup repository tests passed")
    
    # 测试批处理器
    test_batch = TestAdaptiveBatchProcessor()
    test_batch.test_batch_size_calculation()
    test_batch.test_performance_recording()
    print("✓ Batch processor tests passed")
    
    # 测试任务调度器
    test_scheduler = TestOptimizedTaskScheduler()
    test_scheduler.test_priority_calculation()
    test_scheduler.test_load_management()
    print("✓ Task scheduler tests passed")
    
    # 测试监控器
    test_monitor = TestDDRMonitor()
    test_monitor.test_metrics_collection()
    test_monitor.test_trace_operation()
    test_monitor.test_health_checks()
    print("✓ Monitor tests passed")
    
    # 测试集成
    test_integration = TestIntegration()
    test_integration.test_end_to_end_processing_simulation()
    test_integration.test_concurrent_processing()
    print("✓ Integration tests passed")
    
    print("\nAll DDR optimization tests completed successfully! 🎉")
