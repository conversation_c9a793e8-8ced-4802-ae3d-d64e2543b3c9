from typing import Optional, List
from googleapiclient.errors import HttpError
from pydantic import BaseModel
from exts import logger


class TextOptions(BaseModel):
    maxLength: int


class FieldProperties(BaseModel):
    displayName: str


class Lifecycle(BaseModel):
    state: str


class EnabledApp(BaseModel):
    app: str


class EnabledAppSettings(BaseModel):
    enabledApps: List[EnabledApp]


class LabelProperties(BaseModel):
    title: str


class FieldItem(BaseModel):
    id: str
    queryKey: Optional[str] = None
    properties: FieldProperties
    lifecycle: Optional[Lifecycle] = None
    textOptions: Optional[TextOptions] = None


class LabelItem(BaseModel):
    name: str
    id: str
    labelType: str
    lifecycle: Lifecycle
    enabledAppSettings: EnabledAppSettings
    fields: List[FieldItem]
    properties: LabelProperties

    class Config:
        extra = "ignore"


def is_valid_fortitag_label(label_item: LabelItem) -> bool:
    if label_item.labelType != "ADMIN":
        return False
    if label_item.lifecycle.state != "PUBLISHED":
        return False
    if label_item.properties.title != "FortiTag":
        return False
    if not any(app.app == "DRIVE" for app in label_item.enabledAppSettings.enabledApps):
        return False
    return any(
        f.properties.displayName == "FortiTag" and f.textOptions and f.textOptions.maxLength == 255
        for f in label_item.fields
    )


def fetch_all_labels(labels_service, page_size=200, only_valid_fortitag: bool = False) -> List[LabelItem]:
    all_labels = []
    page_token = None

    while True:
        request = labels_service.labels().list(
            useAdminAccess=True,
            view="LABEL_VIEW_FULL",
            publishedOnly=True,
            pageSize=page_size,
            pageToken=page_token
        )
        logger.info("Calling Google API to fetch label list...")
        response = request.execute()

        if "labels" in response:
            for raw_label in response["labels"]:
                try:
                    label = LabelItem(**raw_label)
                    if not only_valid_fortitag or is_valid_fortitag_label(label):
                        all_labels.append(label)
                except Exception as e:
                    logger.warning(f"Skipped invalid label: {e}")

        page_token = response.get("nextPageToken")
        if not page_token:
            break

    return all_labels


def create_and_publish_label(labels_service, label_name: str) -> None:
    label_body = {
        "labelType": "ADMIN",
        "properties": {"title": label_name},
        "fields": [
            {
                "properties": {"displayName": label_name},
                "textOptions": {"maxLength": 255},
            }
        ],
        "enabledAppSettings": {"enabledApps": [{"app": "DRIVE"}]},
    }

    try:
        logger.info(f"Creating draft label '{label_name}'...")
        created_label = labels_service.labels().create(
            body=label_body, useAdminAccess=True
        ).execute()
        logger.info(f"✅ Draft label created: {created_label['name']}")

        logger.info(f"Publishing label '{label_name}'...")
        labels_service.labels().publish(
            name=created_label['name'],
            body={'use_admin_access': True}
        ).execute()
        logger.info("Label published successfully")

    except HttpError as e:
        logger.error(f"API error: {e}")
    except Exception as e:
        logger.error(f"Other error: {e}")


def ensure_fortitag_label(labels_service):
    """
    Ensure a FortiTag label exists in Google Drive.
    Returns:
        label_name (for labelId), field_id
    """
    try:
        labels = fetch_all_labels(labels_service, only_valid_fortitag=True)

        if labels:
            label = labels[0]
            logger.info("FortiTag label already exists.")
        else:
            logger.info("FortiTag label not found. Creating...")
            create_and_publish_label(labels_service, "FortiTag")
            labels = fetch_all_labels(labels_service, only_valid_fortitag=True)
            if not labels:
                logger.error("Failed to create FortiTag label.")
                return None, None
            label = labels[0]

        label_id = label.name
        field_id = label.fields[0].id
        return label_id, field_id

    except Exception as e:
        logger.error(f"Error ensuring FortiTag label: {e}")
        return None, None


def assign_label_to_file(drive_service, file_id, label_id, field_id, value):
    """
    Assign a label to a Google Drive file with a specific value.
    """
    try:
        label_modifications = {
            "labelModifications": [
                {
                    "labelId": label_id,
                    "fieldModifications": [
                        {
                            "fieldId": field_id,
                            "setTextValues": [value]
                        }
                    ]
                }
            ]
        }

        logger.info(f"Assigning label '{label_id}' to file ID {file_id} with value '{value}'...")
        drive_service.files().modifyLabels(
            fileId=file_id,
            body=label_modifications
        ).execute()
        logger.info("Label assigned successfully.")

    except HttpError as e:
        logger.error(f"API error assigning label: {e}")
    except Exception as e:
        logger.error(f"Other error assigning label: {e}")


def ensure_and_assign_fortitag_label(labels_service, drive_service, file_id: str, value: str):
    """
    Ensure the FortiTag label exists, then assign it to the given file.

    Args:
        labels_service: Google Drive Labels API service instance.
        drive_service: Google Drive API service instance.
        file_id (str): ID of the file to which the label will be assigned.
        value (str): The value to set in the FortiTag label field.
    """
    if not value or not isinstance(value, str):
        logger.error("Value must be a non-empty string.")
        return False
    if not file_id or not isinstance(file_id, str):
        logger.error("File ID must be a non-empty string.")
        return False
    try:
        label_id, field_id = ensure_fortitag_label(labels_service)
        if not label_id or not field_id:
            logger.error("Cannot assign label — FortiTag label creation/lookup failed.")
            return False

        assign_label_to_file(drive_service, file_id, label_id, field_id, value)
        return True

    except Exception as e:
        logger.error(f"Error ensuring and assigning FortiTag label: {e}")
        return False
