import json
import time
import uuid
import pytz
import os
import fnmatch
from connector.interface import ConnectorInterface
from datetime import datetime, timedelta, timezone
from domain_model.global_cache import GlobalCache
from domain_model.tracker.session_tracker import SessionTracker
from domain_model.tracker.task_tracker import TaskTracker
from exts import logger, NonRetriableException, SessionExpired, get_logger
from pathlib import Path
from tenacity import RetryError
from typing import List, Dict, Tuple, Union, Optional, Any
from util.config import configs, get_supported_file_type
from util.err_codes import ModErrCode
import requests
import msal
import re
import math
import urllib.parse
from util.enum_ import (ErrorCode, ScanScope, StorageType, ConnectorCollectionType, ScanMethod, ScanResult,
                        ProtectionQuarantineStatus, ProtectionConst, IdentityType, UserType, SyncVersion)
from concurrent.futures import ThreadPoolExecutor
import threading
from domain_model.scan_history import get_scan_history
from domain_model.connector_event_collection_history import (
    get_connector_event_collection_history,
    create_connector_event_collection_history,
    get_connector_event_collection_historys,
)
from natsort import natsorted

from storage.service.identity import (
    get_storage_identity_by_identifier,
    add_storage_identity,
)
from storage.model.sites import get_storage_sites
from service.file_action_service import delete_file_actions
from storage.model.activity import (
    get_fetched_activities,
    get_earliest_created_time,
    get_earliest_event_time,
)


file_download_dir = Path(configs["download_queue"]["file_download_path"])

sp_hidden_folders = ["Forms"]
list_title = "Documents"
logger_recycle = get_logger("recycle")
skip_files = get_logger("skip_files")
fetch_storage_log = get_logger("fetch_storage_log")
chunk_size_mb = 4


class SharePointTokenConnector(ConnectorInterface):
    def __init__(
        self,
        params: dict,
        session_key: str = "",
        pwd_encrypted: int = 1,
        pwd_encrypted2: int = 1,
    ):
        try:
            super().__init__()
            if pwd_encrypted == 1:
                params["clientid"] = self.decrypt_pwd(params["clientid"])
            if pwd_encrypted2 == 1:
                params["clientsecret"] = self.decrypt_pwd(params["clientsecret"])
            self._session_key = session_key
            self._session_tracker = None
            self._task_tracker = None
            self._scan_storage_id = params.get("storage_id")
            self._scan_task_id = params.get("scan_task_id")
            self._is_ddr = params.get("is_ddr", False)

            # Define the scope for Microsoft Graph API (app permissions)
            self.scope = ["https://graph.microsoft.com/.default"]
            self.client_id = params["clientid"]
            self.client_secret = params["clientsecret"]
            self.tenant_id = params["tenantid"]
            self.audit_log_monitoring = params.get("audit_log_monitoring", False)
            self.access_token = None
            self.drive_name_id_map = {}
            self.site_name_id_map = {}
            self.site_id = None
            self.list_title = list_title
            self.forti_tag = configs["connector"]["sharepoint"]["forti_tag"]
            self.retry_count = configs["connector"]["sharepoint"]["retry_count"]
            connect_timeout = configs["connector"]["sharepoint"]["connect_timeout"]
            read_timeout = configs["connector"]["sharepoint"]["read_timeout"]
            self.top_count = configs["connector"]["sharepoint"]["top_count"]
            self.max_workers = configs["connector"]["sharepoint"]["max_workers"]
            self.max_audit_log_fetch_hours = configs["connector"]["sharepoint"][
                "max_audit_log_fetch_hours"
            ]
            self.max_audit_log_once_fetch_hours = configs["connector"]["sharepoint"][
                "max_audit_log_once_fetch_hours"
            ]
            self.all_files_fetch_scan_min_interval_count = configs["connector"][
                "sharepoint"
            ]["all_files_fetch_scan_min_interval_count"]
            self.all_files_fetch_scan_min_interval_time = configs["connector"][
                "sharepoint"
            ]["all_files_fetch_scan_min_interval_time"]
            self.all_files_fetch_recycle_min_interval_count = configs["connector"][
                "sharepoint"
            ]["all_files_fetch_recycle_min_interval_count"]
            self.target_operations = configs["connector"]["sharepoint"][
                "target_operations"
            ]
            self.timeout = (connect_timeout, read_timeout)
            self.scheme = "https"
            self.host = "graph.microsoft.com"
            self.manage_scheme = "https"
            self.manage_host = "manage.office.com"
            self.manage_scope = ["https://manage.office.com/.default"]
            self.task_list = []
            self.token_cache = msal.SerializableTokenCache()
            self.client_app = None
            self.user_agent = "DataPilot/1.0 (Automation-Script; FDT; production)"
        except Exception as e:
            logger.error(e)

    def build_encoded_url(self, path, scheme=None, host=None, query=None):
        if scheme is None:
            scheme = self.scheme
        if host is None:
            host = self.host
        query_encode = ""
        if query is not None:
            query_encode = f"?{urllib.parse.urlencode(query)}"
        path_encode = urllib.parse.quote(path, safe=":/")
        url = f"{scheme}://{host}{path_encode}{query_encode}"
        # logger.debug(f"build_encoded_url, url: {url}")
        return url

    def requests_with_retry(
        self,
        url,
        method=None,
        headers=None,
        data=None,
        json=None,
        timeout=None,
        scope=None,
        authorization=True,
    ):
        retries = 0
        backoff = 1
        skip_retry_status = [200, 201, 202, 204, 400, 403, 404, 409]

        if timeout is None:
            timeout = self.timeout

        if scope is None:
            scope = self.scope

        if method is None:
            method = "GET"

        if headers is None:
            headers = {}

        while retries < self.retry_count:
            try:
                if authorization == True:
                    (ret, access_token) = self.acquire_refresh_token(scope)
                    if not ret:
                        response = requests.Response()
                        response.status_code = 999
                        response._content = access_token.encode("utf-8")
                        retries += 1
                        time.sleep(backoff)
                        continue

                    headers["Authorization"] = f"Bearer {access_token}"
                    headers["User-Agent"] = self.user_agent

                if method == "GET":
                    response = requests.get(url, headers=headers, timeout=timeout)
                elif method == "POST":
                    response = requests.post(
                        url, headers=headers, data=data, json=json, timeout=timeout
                    )
                elif method == "PUT":
                    response = requests.put(
                        url, headers=headers, data=data, timeout=timeout
                    )
                elif method == "DELETE":
                    response = requests.delete(url, headers=headers, timeout=timeout)
                elif method == "PATCH":
                    response = requests.patch(
                        url, headers=headers, json=json, timeout=timeout
                    )
                else:
                    logger.error(f"Unknown method: {method}")
                    response = requests.Response()
                    response.status_code = 999
                    response._content = repr(f"Unknown method: {method}").encode(
                        "utf-8"
                    )
                    return response

                if response.status_code not in skip_retry_status:
                    if response.status_code == 429:
                        retry_after = (
                            response.json()
                            .get("error", {})
                            .get("retryAfterSeconds", backoff)
                        )
                    else:
                        retry_after = backoff

                    retries += 1
                    logger.info(
                        f"Need-retry, completed request counts: {retries}, url: {url}, "
                        f"status_code: {response.status_code}, reason: {response.reason}, "
                        f"retrying after {retry_after} seconds..."
                    )
                    time.sleep(retry_after)
                    backoff = min(backoff * 2, 60)

                    if retries == self.retry_count:
                        logger.error(
                            f"Failed-retry, completed request counts: {retries}, "
                            f"url: {url}, status_code: {response.status_code}"
                        )
                else:
                    break
            except Exception as e:
                retry_after = 3
                retries += 1
                logger.info(
                    f"Need-retry, completed request counts: {retries}, url: {url}, "
                    f"reason: capture exception {e}, retrying after {retry_after} seconds..."
                )
                time.sleep(retry_after)
                if retries == self.retry_count:
                    logger.error(
                        f"Failed-retry, completed request counts: {retries}, "
                        f"url: {url}, exception: {e}"
                    )
                    response = requests.Response()
                    response.status_code = 999
                    response._content = repr(e).encode("utf-8")

        return response

    def test_connection(self) -> Tuple[bool, str, Optional[str]]:
        """
        Test the connection to the Sharepoint  storage.

        Returns:
            bool: True if the connection is successful, False otherwise.

        When Exception occurs:
            return False
        """
        try:
            test_url = self.build_encoded_url("/v1.0/sites/root")
            test_response = self.requests_with_retry(test_url)
            if test_response.status_code == 200:
                if self.audit_log_monitoring:
                    # self.stop_subscription_audit_log(logger)
                    if not self.is_subscription_audit_log(logger):
                        if self.start_subscription_audit_log(logger) == False:
                            logger.error(
                                f"Test Connection Failed: start subscription audit log error"
                            )
                            return False, ModErrCode.ErrCode02030001, None
                return True, ModErrCode.ErrCode02000000, None
            else:
                logger.error(
                    f"Test Connection Failed: {test_response.status_code}, {test_response.text}"
                )
                return False, ModErrCode.ErrCode02030002, test_response.text

        except requests.exceptions.ConnectionError as e:
            err_str = "Connection test failed: Unable to reach the SharePoint server"
            logger.error(f"{err_str} - {repr(e)}")
            return False, ModErrCode.ErrCode02030003, None

        except requests.exceptions.Timeout as e:
            return False, ModErrCode.ErrCode02030004, None

        except requests.exceptions.RequestException as e:
            err_str = f"Connection test failed: Unexpected request error - {repr(e)}"
            logger.error(err_str)
            return False, ModErrCode.ErrCode02030005, repr(e)

        except Exception as e:
            logger.error(f"Error test_connection: {e}")
            return False, ModErrCode.ErrCode02000001, repr(e)

    def set_batch_param(self, batch_params: Dict[str, str]) -> bool:
        """
        Set the batch parameters for the connector.

        Args:
            batch_params (Dict[str, str]): A dictionary containing the batch parameters.

        Returns:
            bool: A boolean indicating if the batch parameters are set successfully.
                True if successful, False otherwise.

        When Exception occurs:
            return False.
        """
        try:
            self._uuid = batch_params["scan_task_uuid"]
            self._scan_scope = batch_params["scan_scope"]
            self._selected_folders = batch_params["scan_folders"]
            self._excluded_folders = batch_params["excluded_scan_folders"]
            self._size_limit = batch_params["file_size_limit"]
            self._scan_file_type = batch_params["scan_file_type"]
            self._scan_method = batch_params["scan_method"]
            self._scan_policy_updated_at = batch_params["scan_policy_updated_at"]
            self._scan_skip_paths = batch_params["scan_skip_paths"]
            self._scan_skip_files = batch_params["scan_skip_files"]
            if self._session_key:
                self._session_tracker = SessionTracker(self._uuid, self._session_key)
                self._task_tracker = TaskTracker(self._uuid, self._session_key)
            return True
        except Exception as e:
            logger.error(f"Error set_batch_param: {e}")
            return False

    def get_folders(self) -> Union[List, None]:
        """
        Get the list of folders in the storage.

        Returns:
            List: A list of folders in the storage.

        When Exception occurs:
            return None
        """
        from huey_worker.fetch_storage import get_last_fetch_time

        while get_last_fetch_time(self._scan_storage_id,"sites") is None:
            time.sleep(0.5)
        try:
            result = []
            sites, _ = get_storage_sites(conditions={"sid": self._scan_storage_id})

            for site in sites:
                result.append(site["site_url"])

            return natsorted(result)
        except Exception as e:
            logger.error(f"Error get_folders: {e}")
            return None

    def is_download_required_for_config_file(
        self, folder: str, file_name: str
    ) -> Tuple[bool, int, Dict[str, str]]:
        """
        Determines whether a download is required for a given configuration file.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file.

        Returns:
            Tuple[bool, int]: A tuple containing:
                - First bool:
                    False: If the file is not required.
                    True: If the file is required.
                - Second int: file size in KB
        """
        try:
            code, file_info = self.get_file_info(folder, file_name)
            if code == ErrorCode.CONNECTION_ERROR:
                return False, None
            elif code == ErrorCode.OTHER_ERROR:
                return False, None
            file_size = int(file_info["size"])  # in bytes
            if file_size > configs["config_file_size_limit"]:
                logger.debug(f"File {file_name} is too large: {file_size} bytes")
                return False, None

            finfo = {
                "size": file_size,
                "last_modified": file_info["last_modified"],
                "user": file_info["user"],
                "email": file_info["email"],
                "id": file_info["id"],
            }
            return True, finfo

        except Exception as e:
            logger.error(
                f"Error is_download_required_for_config_file: {type(e).__name__}, {e}"
            )
            return 0, None

    def is_download_required(
        self,
        folder: str,
        file_name: str,
        cutoff_time: datetime,
        display_path: str = None,
    ) -> Tuple[int, dict]:
        """
        Determines whether a file needs to be downloaded based on various criteria.

        Args:
            folder (str): The folder path where the file is located.
            file_name (str): The name of the file.
            cutoff_time (datetime): The cutoff time for considering a file as outdated.

        Returns:
            Tuple[int, dict]: A tuple containing:
                - First int:
                    0: If the file is not required.
                    1: If the file is required.
                    2: Connection error.
                - Second dict: file extended attribute
        """
        try:
            if display_path and self.file_should_be_skipped(display_path, file_name):
                skip_files.info(
                    f"Task {self._uuid} {display_path}/{file_name} "
                    f"does not require downloading, because it matches "
                    f"skip_files or skip_paths"
                )
                logger.debug(
                    f"Is download required, file be skipped: {display_path} - {file_name}"
                )
                return 0, None

            full_path = f"{folder}/{file_name}"
            cache = GlobalCache(self._uuid, is_ddr=self._is_ddr)
            records, record_count = cache.query_items({"full_path": full_path})
            supported_file_types = get_supported_file_type(self._scan_file_type)
            min_size, max_size = self.get_size_limit()
            file_ext = file_name.split(".")[-1].lower()

            code, file_info = self.get_file_info(folder, file_name, display_path)
            if code == ErrorCode.CONNECTION_ERROR:
                skip_files.error(
                    f"Task {self._uuid} {folder}/{file_name} pass, "
                    f"connection errors occurred while getting file info"
                )
                return 2, None
            elif code == ErrorCode.OTHER_ERROR:
                skip_files.error(
                    f"Task {self._uuid} {folder}/{file_name} pass, "
                    f"other errors occurred while getting file info"
                )
                return 0, None

            file_size = int(file_info["size"]) / 1024  # to KB
            if file_size < min_size or file_size > max_size:
                skip_files.info(
                    f"Task:{self._uuid} {folder}/{file_name} does not require downloading, "
                    f"because it does not meet the file size limit"
                )
                return 0, None

            if (
                supported_file_types["ext"]
                and file_ext not in supported_file_types["ext"]
            ):
                skip_files.info(
                    f"Task:{self._uuid} {folder}/{file_name} does not require downloading, "
                    f"because extension not in support file types"
                )
                return 0, None

            finfo = {
                "file_size": file_size,
                "last_modified": file_info["last_modified"],
                "created_time": file_info["created_time"],
                "user": file_info["user"],
                "email": file_info["email"],
                "id": file_info["id"],
                "file_type": str(file_info.get("mime_type", "")),
                "collaborators": file_info.get("collaborators", {}),
                "share_link": file_info.get("share_link", []),
                "owners": file_info.get("owners", []),
                "shared_data": file_info.get("shared_data", {}),
                "location": file_info.get("location", "UNKNOWN"),
                "encryption": file_info["encryption"],
                "file_id": file_info.get("file_id", ""),
                "create_by": file_info.get("create_by"),
            }

            if "last_access_time" in file_info:
                finfo["last_access_time"] = file_info["last_access_time"]

            if "last_access_operation" in file_info:
                finfo["last_access_operation"] = file_info["last_access_operation"]

            if (
                record_count != 0
                and cutoff_time
                and not self._is_after_cutoff_time(
                    file_info["last_modified"], cutoff_time
                )
                and (not self.is_file_attr_changed(records[0], finfo))
            ):
                skip_files.info(
                    f"Task {self._uuid} {folder}/{file_name} does not require downloading, "
                    f"because it has not been modified after the cutoff time"
                )
                return 0, None

            return 1, finfo

        except Exception as e:
            logger.error(f"Error is_download_required: {type(e).__name__}, {e}")
            skip_files.info(
                f"Task {self._uuid} Error encountered during download {folder}/{file_name}"
            )
            return 0, None

    def is_root_site(self, web_url):
        pattern = r"https://([^/]+)\.sharepoint\.com/?$"
        return bool(re.match(pattern, web_url))

    def is_search_site(self, web_url):
        pattern = r"https://[^/]+\.sharepoint\.com/search/?$"
        return bool(re.match(pattern, web_url))

    def is_personal_site(self, web_url):
        personal_pattern = r"^https://[^/]+-my\.sharepoint\.com/personal/[^/]+/?$"
        return bool(re.match(personal_pattern, web_url))

    def get_site_id_by_web_url(self, web_url):
        try:
            if self.is_root_site(web_url):
                site_info_url = self.build_encoded_url("/v1.0/sites/root")
                # logger.debug(f"get_site_id_by_web_url: {web_url}, is root site")
            else:
                tenant_name, site_name = self.extract_tenant_and_site(web_url)
                if tenant_name is None or site_name is None:
                    logger.error(f"Error parsing web url: {web_url}")
                    return None
                site_type = "sites"
                if self.is_personal_site(web_url):
                    site_type = "personal"
                site_info_url = self.build_encoded_url(
                    f"/v1.0/sites/{tenant_name}:/{site_type}/{site_name}"
                )

            response = self.requests_with_retry(site_info_url)
            if response.status_code == 200:
                site_info = response.json()
                if "id" in site_info:
                    return site_info["id"]
                else:
                    return None
            else:
                logger.error(f"Error: {response.status_code}, {response.json()}")
                return None
        except Exception as e:
            logger.error(e)
            return None

    def get_site_all_drives(self, site_name, site_id, drive_list):
        try:
            # Fetch subsites for the current site
            sub_site_info = self.get_sub_sites(site_id)
            if sub_site_info:
                for subsite in sub_site_info:
                    subsite_id = subsite["id"]
                    subsite_name = subsite["name"]
                    logger.info(f"Subsite name: {subsite_name}, id: {subsite_id}")
                    # Recursively get drives from the subsite
                    self.get_site_all_drives(subsite_name, subsite_id, drive_list)

            drive_info = self.get_drive_info(site_id)
            if not drive_info:
                logger.error(
                    f"Error get_site_all_drives: get_drive_info, "
                    f"site_name: {site_name}, site_id: {site_id}"
                )
                return
            for drive in drive_info:
                item = {
                    "drive_id": drive["id"],
                    "drive_name": drive["name"],
                    "site_id": site_id,
                    "site_name": site_name,
                }
                drive_list.append(item)
        except Exception as e:
            # logger.error(f"Error get_site_all_drives: {e}")
            raise e

    def get_drive_files_worker(self, task):
        if self._session_tracker and not self._session_tracker.is_alive():
            logger.info(
                f"SessionExpired, pid: {os.getpid()}, tid: {threading.get_ident()}"
            )
            raise SessionExpired()

        drive_id = task.get("drive_id", "")
        drive_name = task.get("drive_name", "")
        site_id = task.get("site_id", "")
        site_name = task.get("site_name", "")
        file_array = []
        if drive_id != "" and drive_name != "" and site_id != "" and site_name != "":
            self.fetch_drive_files_with_delta(
                site_name, site_id, drive_name, drive_id, file_array
            )
        logger.debug(
            f"get_drive_files_worker: pid: {os.getpid()}, tid: {threading.get_ident()}, "
            f"site_name: {site_name}, drive_name: {drive_name}, files count: {len(file_array)}"
        )
        return file_array

    def get_group_info(self, group_array):
        if not self.site_name_id_map:
            self.fetch_site_map()
        logger.info(
            f"Sharepoint-flow get_group_info, total sites count: {len(self.site_name_id_map)}"
        )

        if self._scan_scope == ScanScope.ALL_FOLDERS:
            for site_name, site_id in self.site_name_id_map.items():
                if site_name not in self._excluded_folders:
                    group_array.append(
                        {
                            "site_name": site_name,
                            "site_id": site_id,
                        }
                    )
        else:
            for site_name in self._selected_folders:
                site_id = self.site_name_id_map.get(site_name)
                if site_id is None:
                    site_id = self.get_site_id_by_web_url(site_name)
                    if site_id is None:
                        logger.error(f"Can not get site id for site: {site_name}")
                        continue

                group_array.append(
                    {
                        "site_name": site_name,
                        "site_id": site_id,
                    }
                )

    def fetch_audit_events_content_uris(
        self, start_time, end_time, logger, check_session
    ):
        try:
            content_uris = []
            query = {
                "contentType": "Audit.SharePoint",
                "PublisherIdentifier": self.tenant_id,
                "startTime": start_time,
                "endTime": end_time,
            }
            list_content_url = self.build_encoded_url(
                f"/api/v1.0/{self.tenant_id}/activity/feed/subscriptions/content",
                scheme=self.manage_scheme,
                host=self.manage_host,
                query=query,
            )

            while list_content_url:
                if check_session:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, fetch_audit_events")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                response = self.requests_with_retry(
                    list_content_url, scope=self.manage_scope
                )
                if response.status_code == 200:
                    content_uris.extend(response.json())
                    list_content_url = response.headers.get("NextPageUri", None)
                else:
                    logger.error(
                        f"Error get audit events content uris, "
                        f"response.status_code: {response.status_code}, "
                        f"response.text: {response.text}, "
                        f"list_content_url: {list_content_url}"
                    )
                    return content_uris

            return content_uris
        except Exception as e:
            logger.error(e)
            return []

    def fetch_audit_events_from_content_uri(self, content_uri):
        try:
            response = self.requests_with_retry(content_uri, scope=self.manage_scope)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(
                    f"Error get audit events from content_uri, "
                    f"response.status_code: {response.status_code}, "
                    f"response.text: {response.text}"
                )
                return []
        except Exception as e:
            logger.error(e)
            return []

    def fetch_audit_events(
        self, start_time, end_time, target_operations, logger, check_session=True
    ):
        try:
            if not self.is_subscription_audit_log(logger):
                if self.start_subscription_audit_log(logger) == False:
                    logger.error(f"Error start subscription audit log")
                    return None, None, None

            events = []
            last_event_time = None
            first_event_time = None

            while True:
                if check_session:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, fetch_audit_events")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                if (
                    start_time + timedelta(hours=self.max_audit_log_once_fetch_hours)
                    < end_time
                ):
                    offset_time = start_time + timedelta(
                        hours=self.max_audit_log_once_fetch_hours
                    )
                else:
                    offset_time = end_time

                logger.info(
                    f"Sharepoint-flow fetch audit events start time: {start_time.isoformat()}, "
                    f"offset_time time: {offset_time.isoformat()}"
                )
                content_uris = self.fetch_audit_events_content_uris(
                    start_time, offset_time, logger, check_session
                )
                for content in content_uris:
                    content_uri = content["contentUri"]
                    content_created = content["contentCreated"]
                    content_created_datetime = datetime.strptime(
                        content_created, "%Y-%m-%dT%H:%M:%S.%fZ"
                    )
                    content_created_dt = content_created_datetime.replace(
                        tzinfo=timezone.utc
                    )
                    if last_event_time is None or content_created_dt > last_event_time:
                        last_event_time = content_created_dt
                    if (
                        first_event_time is None
                        or content_created_dt < first_event_time
                    ):
                        first_event_time = content_created_dt

                    records = self.fetch_audit_events_from_content_uri(content_uri)
                    for record in records:
                        if record.get("Operation", "NA") in target_operations:
                            events.append(record)

                if offset_time == end_time:
                    break
                start_time = offset_time

            return events, first_event_time, last_event_time
        except Exception as e:
            logger.error(f"Error fetch_audit_events: {e}")
            return None, None, None

    def generate_site_info(self, site_url):
        site_id = self.get_site_id_by_web_url(site_url)
        if site_id is None:
            logger.error(f"Can not find site id for site_url: {site_url}")
            return None

        driver_list = []
        self.get_site_all_drives(site_url, site_id, driver_list)
        site_info = {"site_id": site_id, "drives": driver_list}
        return site_info

    def match_driver(self, driver_list, target_url):
        for driver in driver_list:
            if target_url.startswith(driver.get("drive_name", "NA")):
                return driver
        return None

    def site_should_scan(self, site_url):
        if (
            self._scan_scope == ScanScope.ALL_FOLDERS
            and site_url not in self._excluded_folders
        ) or (
            self._scan_scope == ScanScope.SELECTED_FOLDERS
            and site_url in self._selected_folders
        ):
            return True

        return False

    def group_fill_file_info(
        self,
        group_map,
        sites_info,
        activity,
        site_url_trip,
        relative_url_encode,
        file_name,
        item_type,
    ):
        try:
            if not self.site_should_scan(site_url_trip):
                return

            if (
                site_url_trip in group_map
                and "folder_event_exist" in group_map[site_url_trip]
            ):
                logger.debug(
                    f"Sharepoint-flow this group have folder event, skip file event: {activity}"
                )
                return

            if item_type != "File":
                logger.error(f"Item type is not File, event: {activity}")
                return

            if site_url_trip not in sites_info:
                site_info = self.generate_site_info(site_url_trip)
                if site_info is None:
                    logger.error(f"Can not generate site info, event: {activity}")
                    return
                sites_info[site_url_trip] = site_info
            else:
                site_info = sites_info[site_url_trip]

            driver_list = site_info.get("drives", [])
            target_url = site_url_trip + "/" + relative_url_encode
            driver = self.match_driver(driver_list, target_url)
            logger.debug(
                f"Sharepoint-flow fill file info, top site url: {site_url_trip} "
                f"target_url: {target_url}, driver: {driver}"
            )
            if driver is None:
                logger.error(
                    f"Can not find driver for event: {activity}, {driver_list}"
                )
                return

            folder_path_encode = target_url.removeprefix(driver.get("drive_name", "NA"))
            folder_path = urllib.parse.unquote(folder_path_encode)
            folder = f"/drives/{driver.get('drive_id', 'NA')}/root:{folder_path}"
            file_name_encode = urllib.parse.quote(file_name, safe=":/")
            full_display_path = f"{driver.get('drive_name', 'NA')}{folder_path_encode}/{file_name_encode}"

            file = {
                "folder": folder,
                "file_name": file_name,
                "display_path": full_display_path,
                "site_id": driver.get("site_id", "NA"),
                "drive_id": driver.get("drive_id", "NA"),
                "skip_modify_time_check": True,
            }

            if site_url_trip not in group_map:
                file_array = {}
                file_array[full_display_path] = file
                group_map[site_url_trip] = {
                    "site_name": site_url_trip,
                    "site_id": site_info.get("site_id", "NA"),
                    "file_array": file_array,
                }
            else:
                if full_display_path not in group_map[site_url_trip]["file_array"]:
                    group_map[site_url_trip]["file_array"][full_display_path] = file
                else:
                    logger.debug(
                        f"The file {full_display_path} is already in file_array, skip"
                    )
        except Exception as e:
            logger.error(f"Error group_fill_file_info: {e}")
            return

    def group_fill_folder_info(
        self, group_map, sites_info, event, site_url_trip, item_type
    ):
        try:
            if not self.site_should_scan(site_url_trip):
                return

            if item_type != "Folder":
                logger.error(f"Item type is not Folder, event: {event}")
                return

            if site_url_trip not in sites_info:
                site_info = self.generate_site_info(site_url_trip)
                if site_info is None:
                    logger.error(f"Can not generate site info, event: {event}")
                    return
                sites_info[site_url_trip] = site_info
            else:
                site_info = sites_info[site_url_trip]

            logger.debug(
                f"Sharepoint-flow fill folder info, top site url: {site_url_trip}"
            )
            if site_url_trip not in group_map:
                group_map[site_url_trip] = {
                    "site_name": site_url_trip,
                    "site_id": site_info.get("site_id", "NA"),
                    "folder_event_exist": 1,
                }
            else:
                group_map[site_url_trip]["folder_event_exist"] = 1
        except Exception as e:
            logger.error(f"Error group_fill_folder_info: {e}")
            return

    def get_top_site_url_from_item_url(self, item_url):
        sites_pattern = "https://[^/]+\.sharepoint\.com/(sites|teams|personal)/"
        sites_match = re.match(sites_pattern, item_url)
        if sites_match:
            prefix_match = sites_match.group()
            item_path = item_url[sites_match.end() :]
            top_site_name = item_path.split("/")[0]
            site_url_trip = prefix_match + top_site_name
            return site_url_trip

        root_site_pattern = "https://[^/]+\.sharepoint\.com"
        root_site_match = re.match(root_site_pattern, item_url)
        if root_site_match:
            site_url_trip = root_site_match.group()
            return site_url_trip

        return "NA"

    def parse_event_data(self, event_data: dict, mode: str = "target") -> dict:
        result = {
            "site_url": "NA",
            "relative_url": "NA",
            "item_name": "NA",
            "item_url": "NA",
        }

        try:
            if event_data == "NA":
                logger.error(f"Error in process event: not find EventData")
                return result

            pattern = r"<([^>]+)>([^<]+)</\1>"
            matches = re.findall(pattern, event_data)
            data = {key: value for key, value in matches}
            logger.debug(f"Event data dict: {data}")

            if mode == "target":
                target_web_url = data.get("TargetWebUrl", "NA")
                target_file_url = data.get("TargetFileUrl", "NA")

                """
                'TargetFileUrl': 'https://dlp103.sharepoint.com/sites/teamsite1000/Shared Documents/upload test %hh/12 34 %56.txt',
                'TargetWebUrl': 'https://dlp103.sharepoint.com/sites/Ltest/l-subsite'
                """
                if "NA" in [target_web_url, target_file_url]:
                    logger.error(
                        f"Error in process target event: missing TargetWebUrl or TargetFileUrl"
                    )
                    return result

                site_url_trip = self.get_top_site_url_from_item_url(target_web_url)
                if site_url_trip == "NA":
                    logger.error(f"Error in process target event: not get top site url")
                    return result

                file_path = target_file_url.removeprefix(site_url_trip + "/")
                relative_url, _, item_name = file_path.rpartition("/")
                relative_url_encode = urllib.parse.quote(relative_url, safe=":/")

                result.update(
                    {
                        "site_url": site_url_trip,
                        "relative_url": relative_url_encode,
                        "item_name": item_name,
                        "item_url": target_file_url,
                    }
                )
            elif mode == "source":
                source_file_url = data.get("SourceFileUrl", "NA")

                """
                'SourceFileUrl':
                'https://dlp103.sharepoint.com/sites/Ltest/l-subsite/my-l-second-subsite/lsecondsubsitedrive/floder/folder_move_cross_site',
                """
                if source_file_url == "NA":
                    logger.error(
                        f"Error in process source event: missing SourceFileUrl"
                    )
                    return result

                site_url_trip = self.get_top_site_url_from_item_url(source_file_url)
                if site_url_trip == "NA":
                    logger.error(f"Error in process source event: not get top site url")
                    return result

                file_path = source_file_url.removeprefix(site_url_trip + '/')
                relative_url, _, item_name = file_path.rpartition('/')
                relative_url_encode = urllib.parse.quote(relative_url, safe=":/")

                result.update(
                    {
                        "site_url": site_url_trip,
                        "relative_url": relative_url_encode,
                        "item_name": item_name,
                        "item_url": source_file_url,
                    }
                )
            else:
                logger.error(f"Unsupported parse mode: {mode}")

            return result
        except Exception as e:
            logger.error(f"Error parse_event_data (mode={mode}): {e}")
            return result

    def process_audit_log_item(self, activity, group_map, process_events):
        sharing_ops = {
            "SharingSet",
            "SharingRevoked",
            "AnonymousLinkCreated",
            "AnonymousLinkRemoved",
            "SecureLinkCreated",
            "SecureLinkDeleted",
            "CompanyLinkCreated",
            "CompanyLinkRemoved",
            "AddedToSecureLink",
            "RemovedFromSecureLink",
        }

        try:
            sites_info = {}
            scan_policy_fields = activity.scan_policy_fields
            operation = activity.event_type

            item_type = scan_policy_fields.get("item_type", "NA")
            site_url = scan_policy_fields.get("site_url", "NA")
            site_url_trip = site_url.rstrip("/")
            process_events[activity.event_id] = {
                "Operation": operation,
                "CreationTime": scan_policy_fields.get("creation_time", "NA"),
            }

            logger.info(
                f"process_audit_log_item, operation: {operation} "
                f"scan_policy_fields: {scan_policy_fields}"
            )
            if (
                operation in ["FileModified", "FileUploaded", "FileAccessed"]
                or operation in sharing_ops
            ):
                relative_url = scan_policy_fields.get("src_relative_url", "NA")
                relative_url_encode = urllib.parse.quote(relative_url, safe=":/")
                file_name = scan_policy_fields.get("src_file_name", "NA")
                file_name = urllib.parse.unquote(file_name)
                self.group_fill_file_info(
                    group_map,
                    sites_info,
                    activity,
                    site_url_trip,
                    relative_url_encode,
                    file_name,
                    item_type,
                )
            elif operation in [
                "FileCopied",
                "FileMoved",
                "FileRenamed",
                "FileRestored",
            ]:
                relative_url = scan_policy_fields.get("dst_relative_url", "NA")
                relative_url_encode = urllib.parse.quote(relative_url, safe=":/")
                file_name = scan_policy_fields.get("dst_file_name", "NA")
                file_name = urllib.parse.unquote(file_name)

                # Fix bug: 1140578
                if site_url != "NA" and relative_url == "":
                    logger.debug(
                        f"Sharepoint-flow, it is a special event, "
                        f"can't find valid DestinationRelativeUrl, event: {activity}"
                    )
                    self.group_fill_folder_info(
                        group_map, sites_info, activity, site_url_trip, "Folder"
                    )
                    return

                if "NA" in [site_url, relative_url, file_name]:
                    site_url_trip = scan_policy_fields.get(
                        "event_target_site_url_trip", "NA"
                    )
                    relative_url_encode = scan_policy_fields.get(
                        "event_target_relative_url_encode", "NA"
                    )
                    file_name = scan_policy_fields.get("event_target_item_name", "NA")

                    if "NA" in [site_url_trip, relative_url_encode, file_name]:
                        logger.error(f"Error in process event: {activity}")
                        return

                self.group_fill_file_info(
                    group_map,
                    sites_info,
                    activity,
                    site_url_trip,
                    relative_url_encode,
                    file_name,
                    item_type,
                )
            elif operation in [
                "FolderCopied",
                "FolderMoved",
                "FolderRenamed",
                "FolderRestored",
            ]:
                relative_url = scan_policy_fields.get("dst_relative_url", "NA")
                relative_url_encode = urllib.parse.quote(relative_url, safe=":/")
                folder_name = scan_policy_fields.get("dst_file_name", "NA")
                folder_name = urllib.parse.unquote(folder_name)

                if site_url == "NA":
                    site_url_trip = scan_policy_fields.get(
                        "event_target_site_url_trip", "NA"
                    )
                    relative_url_encode = scan_policy_fields.get(
                        "event_target_relative_url_encode", "NA"
                    )
                    folder_name = scan_policy_fields.get("event_target_item_name", "NA")

                    if site_url_trip == "NA":
                        logger.error(f"Error in process event: {activity}")
                        return

                self.group_fill_folder_info(
                    group_map, sites_info, activity, site_url_trip, item_type
                )
            else:
                logger.error(f"Unknown operation: {operation}")

        except Exception as e:
            logger.error(f"Error process_aduit_log_item: {e}")

    def process_audit_log_item_recycle(self, activity, group_map, process_events):
        try:
            scan_policy_fields = activity.scan_policy_fields
            operation = activity.event_type

            item_type = scan_policy_fields.get("item_type", "NA")
            site_url = scan_policy_fields.get("site_url", "NA")
            site_url_trip = site_url.rstrip("/")
            process_events[activity.event_id] = {
                "Operation": operation,
                "CreationTime": scan_policy_fields.get("creation_time", "NA"),
            }
            logger.info(
                f"process_audit_log_item_recycle, operation: {operation} "
                f"scan_policy_fields: {scan_policy_fields}"
            )

            if operation in ["FileRecycled", "FileMoved", "FileRenamed"]:
                relative_url = scan_policy_fields.get("src_relative_url", "NA")
                file_name = scan_policy_fields.get("src_file_name", "NA")
                file_web_url_encode = scan_policy_fields.get("src_display_path", "NA")

                if "NA" in [site_url, relative_url, file_name]:
                    site_url_trip = scan_policy_fields.get(
                        "event_source_site_url_trip", "NA"
                    )
                    file_web_url_encode = scan_policy_fields.get(
                        "src_display_path", "NA"
                    )

                    if "NA" in [site_url_trip, file_web_url_encode]:
                        logger_recycle.error(f"Error in process event: {activity}")
                        return

                self.recycle_group_fill_info(
                    group_map, activity, site_url_trip, file_web_url_encode, item_type
                )
            elif operation in ["FolderRecycled", "FolderMoved", "FolderRenamed"]:
                folder_web_url_encode = scan_policy_fields.get("src_display_path", "NA")

                if site_url == "NA":
                    site_url_trip = scan_policy_fields.get(
                        "event_source_site_url_trip", "NA"
                    )
                    folder_web_url_encode = scan_policy_fields.get(
                        "src_display_path", "NA"
                    )

                    if site_url_trip == "NA":
                        logger_recycle.error(f"Error in process event: {activity}")
                        return

                self.recycle_group_fill_info(
                    group_map, activity, site_url_trip, folder_web_url_encode, item_type
                )
            elif operation in ["SiteDeleted"]:
                self.recycle_group_fill_info(
                    group_map, activity, site_url_trip, site_url_trip, "Folder"
                )
            else:
                logger_recycle.error(f"Unknown operation: {operation}")

        except Exception as e:
            logger.error(f"Error process_aduit_log_item: {e}")

    def generate_changed_groups(self, activities, group_array):
        try:
            group_map = {}
            process_events = {}

            for activity in activities:
                logger.debug(
                    f"Sharepoint-flow event type: {activity.event_type}, "
                    f"event_id: {activity.event_id}"
                )
                event_finished = get_connector_event_collection_history(
                    scan_policy_id=self._uuid,
                    connector_type=StorageType.SHAREPOINT_OL,
                    collection_type=ConnectorCollectionType.SCAN,
                    event_id=activity.event_id,
                )
                if event_finished is not None:
                    logger.debug(
                        f"Sharepoint-flow this event has been processed, skip it, "
                        f"event id: {activity.event_id}"
                    )
                    continue

                self.process_audit_log_item(activity, group_map, process_events)

            logger.debug(f"Sharepoint-flow: group_map: {group_map}")
            for value in group_map.values():
                if "folder_event_exist" in value:
                    group_array.append(
                        {
                            "site_name": value.get("site_name", "NA"),
                            "site_id": value.get("site_id", "NA"),
                        }
                    )
                else:
                    file_array = []
                    files = value.get("file_array", {})
                    for file in files.values():
                        file_array.append(file)

                    group_array.append(
                        {
                            "site_name": value.get("site_name", "NA"),
                            "site_id": value.get("site_id", "NA"),
                            "file_array": file_array,
                        }
                    )
            return process_events
        except Exception as e:
            logger.error(f"Error generate_changed_groups: {e}")
            return {}

    def supplement_event_collection_history(self, collection_type):
        try:
            process_events = {}
            _now = datetime.now(timezone.utc)
            payload = {
                "scan_policy_id": self._uuid,
                "connector_type": StorageType.SHAREPOINT_OL,
                "collection_type": collection_type,
                "event_count": -1,
                "events": process_events,
                "last_read_time": _now,
                "created_at": datetime.now(),
            }
            create_connector_event_collection_history(payload)
        except Exception as e:
            logger.error(f"Error supplement_event_collection_history: {e}")
            return

    def is_time_to_fetch_all_files(
        self, logger, collection_type, min_count, min_time=600
    ):
        try:
            event_historys = get_connector_event_collection_historys(
                scan_policy_id=self._uuid,
                connector_type=StorageType.SHAREPOINT_OL,
                collection_type=collection_type,
                limit=min_count,
            )
            if event_historys is None or len(event_historys) < min_count:
                return False

            for event_history in event_historys:
                if event_history.event_count == -1:
                    return False

            if collection_type == ConnectorCollectionType.SCAN:
                all_files_event_history = get_connector_event_collection_history(
                    scan_policy_id=self._uuid,
                    connector_type=StorageType.SHAREPOINT_OL,
                    collection_type=collection_type,
                    event_count=-1,
                )
                if all_files_event_history:
                    if (
                        all_files_event_history.created_at + timedelta(seconds=min_time)
                        > datetime.now()
                    ):
                        return False
            return True
        except Exception as e:
            logger.error(f"Error in check is_time_to_fetch_all_files: {e}")
            return True

    def get_group_info_by_events(self, group_array):
        try:
            scan_history = get_scan_history(
                scan_policy_id=self._uuid, scan_result=ScanResult.COMPLETED
            )
            if (
                scan_history is not None
                and scan_history.start_time
                > self._scan_policy_updated_at.replace(tzinfo=timezone.utc)
            ):
                if self.is_time_to_fetch_all_files(
                    logger,
                    ConnectorCollectionType.SCAN,
                    self.all_files_fetch_scan_min_interval_count,
                    min_time=self.all_files_fetch_scan_min_interval_time,
                ):
                    logger.info(
                        f"Sharepoint-flow, do all files fetch, reason is: "
                        f"reach min_interval_count and min_interval_time"
                    )
                    self.supplement_event_collection_history(
                        ConnectorCollectionType.SCAN
                    )
                    self.get_group_info(group_array)
                else:
                    logger.info(
                        f"Sharepoint-flow, do event files fetch, "
                        f"scan_history.start_time: {scan_history.start_time.isoformat()}, "
                        f"scan_policy_updated_at: {self._scan_policy_updated_at.replace(tzinfo=timezone.utc).isoformat()}"
                    )
                    event_history = get_connector_event_collection_history(
                        scan_policy_id=self._uuid,
                        connector_type=StorageType.SHAREPOINT_OL,
                        collection_type=ConnectorCollectionType.SCAN,
                    )
                    start_time = None
                    if event_history is not None:
                        start_time = event_history.last_read_time
                        logger.info(
                            f"Sharepoint-flow get start time from event history"
                        )
                    else:
                        start_time = scan_history.start_time
                        logger.info(f"Sharepoint-flow get start time from scan history")

                    _now = datetime.now(timezone.utc)
                    start_time = start_time.replace(tzinfo=None)
                    end_time = _now.replace(tzinfo=None)
                    earliest_created_time = get_earliest_created_time(
                        self._scan_storage_id
                    )
                    if earliest_created_time and start_time >= earliest_created_time:
                        target_operations = [
                            "FileModified",
                            "FileUploaded",
                            "FileRenamed",
                            "FileCopied",
                            "FileMoved",
                            "FileRestored",
                            "FolderRenamed",
                            "FolderCopied",
                            "FolderMoved",
                            "FolderRestored",
                            "SharingSet",
                            "SharingRevoked",
                            "AnonymousLinkCreated",
                            "AnonymousLinkRemoved",
                            "SecureLinkCreated",
                            "SecureLinkDeleted",
                            "CompanyLinkCreated",
                            "CompanyLinkRemoved",
                            "AddedToSecureLink",
                            "RemovedFromSecureLink",
                            "FileAccessed",
                        ]
                        logger.info(
                            f"Sharepoint-flow get events start time: {start_time.isoformat()}, "
                            f"end time: {_now.isoformat()}"
                        )

                        activities = get_fetched_activities(
                            storage_id=self._scan_storage_id,
                            event_type=target_operations,
                            created_at__gt=start_time,
                            created_at__lte=end_time,
                        )
                        logger.info(
                            f"Sharepoint-flow events counts: {len(activities) if activities else 0} "
                            f"events: {activities}"
                        )

                        process_events = self.generate_changed_groups(
                            activities, group_array
                        )
                        payload = {
                            "scan_policy_id": self._uuid,
                            "connector_type": StorageType.SHAREPOINT_OL,
                            "collection_type": ConnectorCollectionType.SCAN,
                            "event_count": len(process_events),
                            "events": process_events,
                            "last_read_time": _now,
                            "created_at": datetime.now(),
                        }
                        logger.info(
                            f"before create_connector_event_collection_history, payload: {payload}"
                        )
                        create_connector_event_collection_history(payload)
                    else:
                        logger.info(
                            f"Sharepoint-flow, do all files fetch, reason is: "
                            f"start_time is too early: {scan_history.start_time.isoformat()}, "
                            f"can not get event logs"
                        )
                        self.supplement_event_collection_history(
                            ConnectorCollectionType.SCAN
                        )
                        self.get_group_info(group_array)
            else:
                if scan_history is None:
                    logger.info(
                        f"Sharepoint-flow, do all files fetch, reason is: scan history is None"
                    )
                else:
                    logger.info(
                        f"Sharepoint-flow, do all files fetch, reason is: scan policy have changed"
                    )

                self.supplement_event_collection_history(ConnectorCollectionType.SCAN)
                self.get_group_info(group_array)
        except Exception as e:
            logger.error(f"Error get_group_info_by_events: {e}")
            return

    def batch_generate_queue_group_file(
        self,
    ) -> Union[Tuple[str, int], Tuple[None, int]]:
        """
        Get the group queue file path and queue size.

        Returns:
            Tuple[str, int]: A tuple containing the file path and size of the queue.

        When Exception occurs:
            return None

        When SessionExpired occurs:
            raise SessionExpired
        """
        try:
            logger.info("Sharepoint-flow batch_generate_queue_group_file begin:")
            group_array = []
            if (
                self._scan_method == ScanMethod.INCREMENTAL_SCAN
                and self.audit_log_monitoring == True
            ):
                self.get_group_info_by_events(group_array)
            else:
                self.get_group_info(group_array)
            logger.info(
                f"Sharepoint-flow batch_generate_queue_group_file end, "
                f"group counts: {len(group_array)}"
            )
            return self._persist_group_info(group_array), len(group_array)
        except SessionExpired as e:
            logger.info(f"SessionExpired: batch_generate_queue_group_file")
            raise e
        except Exception as e:
            logger.error(f"Error batch_generate_queue_group_file: {e}")
            return (None, 0)

    def batch_generate_queue_file(
        self, group_info: dict
    ) -> Union[Tuple[str, int], Tuple[None, int]]:
        """
        Get the queue file path and queue size. If a timestamp is provided,
        the queue file should only contain data after that UTC timestamp.

        Returns:
            Tuple[str, int]: A tuple containing the file path and size of the queue.

        When Exception occurs:
            return None

        When SessionExpired occurs:
            raise SessionExpired
        """
        try:
            logger.info(
                f"Sharepoint-flow batch_generate_queue_file start, "
                f"site name: {group_info['site_name']}, "
                f"file array len: {len(group_info.get('file_array', []))}"
            )
            file_array = []
            if "file_array" in group_info:
                file_array = group_info["file_array"]
            else:
                self.get_site_all_drives(
                    group_info["site_name"], group_info["site_id"], self.task_list
                )
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    futures = [
                        executor.submit(self.get_drive_files_worker, task)
                        for task in self.task_list
                    ]
                    for future in futures:
                        try:
                            file_array.extend(future.result())
                        except SessionExpired as e:
                            logger.info(f"Task SessionExpired.")
                            raise e
                        except Exception as e:
                            logger.error(f"Task failed with exception: {e}")
                            continue
            logger.info(
                f"Sharepoint-flow batch_generate_queue_file end, "
                f"total-file-count: {len(file_array)}"
            )
            return self._persist_file_info(file_array), len(file_array)
        except SessionExpired as e:
            logger.info(f"SessionExpired: batch_generate_queue_file")
            raise e
        except RetryError as err:
            logger.error(f"Error batch_generate_queue_file: {err}")
            return (None, 0)
        except Exception as e:
            logger.error(f"Error batch_generate_queue_file: {e}")
            return (None, 0)

    def check_file_exist(
        self, file_path: str, task_uuid: str = "", conn=None, logger=logger
    ) -> Union[bool, None]:
        """
        Check if a file exists on the server.

        Args:
            file_path (str): The path of the file to check.
            task_uuid (str): The UUID of the task.
            conn: It is only valid in smb connector.

        Returns:
            Union[bool, None]: Returns True if the file exists, False if it doesn't exist,
            or None if an error occurred.
        """
        try:
            # Make a GET request to check if the folder exists
            file_url = self.build_encoded_url(f"/v1.0{file_path}")
            file_response = self.requests_with_retry(file_url)

            if file_response.status_code == 200:
                file_info = file_response.json()
                return True
            elif file_response.status_code == 404:
                return False
            else:
                logger.error(f"check_file_exist: error: {file_response.text}")
                return None
        except Exception as e:
            logger.error(f"Error check_file_exist: {e}")
            return None

    def download(
        self, folder: str, file_name: str, task_uuid: str, download_dir: str = None
    ) -> Union[Tuple[str, str, str], Tuple[None, None, None]]:
        """
        Downloads a file from the specified `obj_uri` using the provided `client`
        and saves it to the `local_file_path`.

        Args:
            folder (str): The folder path where the file is located.
            file_name (str): The name of the file.
            task_uuid (str): The UUID of the task.

        Returns:
            Tuple[str, str, str]: A tuple containing the file UUID, local file path, and file URI.

        When Exception occurs:
            return None
        """
        file_uuid = str(uuid.uuid4())
        obj_uri = f"{folder}/{file_name}"
        if download_dir is None:
            if self._is_ddr:
                download_dir = Path(configs["download_queue"]["ddr_file_download_path"])
            else:
                download_dir = file_download_dir
        local_file_path = (
            self._create_target_dir(download_dir, task_uuid) / file_uuid
        )
        try:
            file_url = self.build_encoded_url(f"/v1.0{folder}/{file_name}:/content")
            # Make a GET request to check if the folder exists
            file_response = self.requests_with_retry(file_url)

            if file_response.status_code == 200:
                # Save the file locally
                with open(local_file_path, "wb") as local_file:
                    local_file.write(file_response.content)
                return file_uuid, str(local_file_path), obj_uri
            elif file_response.status_code == 404:
                logger.info(f"Error download: File not found {file_url}")
                return None, None, None
            else:
                logger.error(
                    f"Error download: {file_response.status_code}:{file_response.text}"
                )
                return -1, -1, -1
        except requests.exceptions.ConnectionError as e:
            logger.error(e)
            return -1, -1, -1
        except requests.exceptions.Timeout as e:
            logger.error(e)
            return -1, -1, -1
        except requests.exceptions.RequestException as e:
            logger.error(f"Error download: {e}")
            return -1, -1, -1
        except Exception as e:
            logger.error(f"Error download: {e}")
            return None, None, None

    def get_item_info_by_weburl(self, site_url, target_weburl):
        site_response = self.requests_with_retry(site_url)
        if site_response.status_code != 200:
            # logger.debug(f"can not get site info, site_url: {site_url}")
            return None

        site_id = site_response.json()["id"]
        site_name = site_response.json()["webUrl"]
        drives = self.get_drive_info(site_id)
        for d in drives:
            if target_weburl == d["name"]:
                item_path = "/"
                logger.debug(f"Is driver root path: {item_path}")
                return {
                    "site_id": site_id,
                    "site_name": site_name,
                    "drive_id": d["id"],
                    "drive_name": d["name"],
                    "item_path": item_path,
                }
            elif target_weburl.startswith(d["name"]):
                item_path = target_weburl.removeprefix(d["name"])
                logger.debug(f"item_path: {urllib.parse.unquote(item_path)}")
                return {
                    "site_id": site_id,
                    "site_name": site_name,
                    "drive_id": d["id"],
                    "drive_name": d["name"],
                    "item_path": urllib.parse.unquote(item_path),
                }
        return None

    def parse_weburl(self, target_weburl):
        try:
            parsed = urllib.parse.urlparse(target_weburl)
            hostname = parsed.netloc
            path_parts = urllib.parse.unquote(parsed.path).strip("/").split("/")
            logger.info(f"path_parts: {path_parts}")
            for i in range(len(path_parts), 0, -1):
                maybe_site_path = "/" + "/".join(path_parts[:i])
                logger.info(f"maybe_site_path: {maybe_site_path}")
                site_url = self.build_encoded_url(
                    f"/v1.0/sites/{hostname}:{maybe_site_path}"
                )
                info = self.get_item_info_by_weburl(site_url, target_weburl)
                if info is not None:
                    logger.info(f"parse weburl success")
                    return info

            site_url = self.build_encoded_url(f"/v1.0/sites/root")
            return self.get_item_info_by_weburl(site_url, target_weburl)
        except Exception as e:
            logger.error(f"Error parse weburl: {target_weburl} {e}")
            return None

    def upload_small(self, local_file: str, target_folder: str, target_filename: str):
        try:
            info = self.parse_weburl(target_folder)
            if info is None:
                logger.error(
                    f"Error upload small file: {target_folder} {target_filename}"
                )
                return None, None

            with open(local_file, "rb") as f:
                path = f"{info['item_path'].strip('/')}/{target_filename}"
                upload_url = self.build_encoded_url(
                    f"/v1.0/drives/{info['drive_id']}/root:/{path}:/content"
                )
                headers = {"Content-Type": "application/octet-stream"}
                file_response = self.requests_with_retry(
                    upload_url, method="PUT", headers=headers, data=f
                )
                if file_response.status_code in [200, 201]:
                    return info, file_response.json()
                else:
                    logger.error(
                        f"Error upload small file: {target_folder} {target_filename}"
                    )
                    return None, None
        except Exception as e:
            logger.error(
                f"Error upload small file: {target_folder} {target_filename} {e}"
            )
            return None, None

    def upload_large(
        self, local_file: str, target_folder: str, target_filename: str, chunk_size: int
    ):

        try:
            info = self.parse_weburl(target_folder)
            if info is None:
                logger.error(
                    f"Error upload large file: {target_folder} {target_filename}"
                )
                return None, None

            path = f"{info['item_path'].strip('/')}/{target_filename}"
            session_url = self.build_encoded_url(
                f"/v1.0/drives/{info['drive_id']}/root:/{path}:/createUploadSession"
            )
            headers = {"Content-Type": "application/json"}
            session_response = self.requests_with_retry(
                session_url, method="POST", headers=headers, json={}
            )
            if session_response.status_code != 200:
                logger.error(
                    f"Error upload large file: {target_folder} {target_filename}, "
                    f"error info: {session_response.text}"
                )
                return None, None

            upload_url = session_response.json()["uploadUrl"]
            file_size = os.path.getsize(local_file)
            total_chunks = math.ceil(file_size / chunk_size)

            with open(local_file, "rb") as f:
                for i in range(total_chunks):
                    start = i * chunk_size
                    end = min(start + chunk_size, file_size) - 1
                    f.seek(start)
                    chunk_data = f.read(end - start + 1)
                    headers = {
                        "Content-Length": str(len(chunk_data)),
                        "Content-Range": f"bytes {start}-{end}/{file_size}",
                    }
                    response = self.requests_with_retry(
                        upload_url, method="PUT", headers=headers, data=chunk_data
                    )
                    if response.status_code not in [200, 201, 202]:
                        logger.error(
                            f"The {i+1} block upload failed, status code: {response.status_code}, "
                            f"error info: {response.text}"
                        )
                        return None, None
                    logger.debug(f"The {i+1}/{total_chunks} block upload success")

                if response.status_code in [200, 201]:
                    return info, response.json()
                else:
                    logger.error(
                        f"Error upload large file: {target_folder} {target_filename}"
                    )
                    return None, None
        except Exception as e:
            logger.error(
                f"Error upload large file: {target_folder} {target_filename} {e}"
            )
            return None, None

    def get_root_site_weburl(self) -> bool:
        try:
            site_url = self.build_encoded_url("/v1.0/sites/root")
            response = self.requests_with_retry(site_url)
            if response.status_code == 200:
                site_info = response.json()
                return site_info.get("webUrl")
            else:
                return None
        except Exception as e:
            logger.error(f"Error get root site weburl: {e}")
            return None

    def upload(
        self, local_file: str, target_folder: str, target_filename: str, task_uuid: str
    ) -> Union[dict, None]:
        """
        Upload a local file to the specified folder.

        Args:
            local_file (str): The local file full path.
            target_folder (str): The target path.
            target_filename (str): The target filename.
            task_uuid (str): The UUID of the task.

        Returns:
            dict: The file info of the uploaded file.

        When Exception occurs:
            return None
        """
        try:
            file_size = os.path.getsize(local_file)
            if file_size < 4 * 1024 * 1024:
                upload_info, file_info = self.upload_small(
                    local_file, target_folder, target_filename
                )
            else:
                upload_info, file_info = self.upload_large(
                    local_file,
                    target_folder,
                    target_filename,
                    chunk_size_mb * 1024 * 1024,
                )
            if upload_info and file_info:
                info = {}
                parent_path = file_info.get("parentReference", {}).get("path", "")
                file_path = parent_path.split("/root:")[-1]
                file_name = file_info.get("name", "")
                full_path = f"{parent_path}/{file_name}"

                drive_name = upload_info.get("drive_name", "")
                full_display_path = drive_name + urllib.parse.quote(
                    f"{file_path}/{file_name}", safe=":/"
                )
                info = {
                    "full_path": full_path,
                    "folder": parent_path,
                    "file_name": file_name,
                    "file_display_path": full_display_path,
                }
                logger.debug(
                    f"Upload file success: {target_folder} {target_filename}, "
                    f"full_display_path: {full_display_path}"
                )
                return info
            else:
                logger.error(f"Upload file error: {target_folder} {target_filename}")
                return None
        except Exception as e:
            logger.error(f"Error upload: {e}")
            return None

    def delete_file(self, folder: str, file_name: str, task_uuid: str) -> bool:
        """
        Delete a remote file.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file to delete.
            task_uuid (str): The UUID of the task.

        Returns:
            True.

        When Exception occurs:
            False
        """
        try:
            file_url = self.build_encoded_url(f"/v1.0{folder}/{file_name}")
            file_response = self.requests_with_retry(file_url, method="DELETE")
            if file_response.status_code in [200, 204]:
                logger.debug(f"Delete file success: {file_url}")
                return True
            logger.error(f"Delete file error: {file_url}, {file_response}")
            return False
        except Exception as e:
            logger.error(f"Error delete file: {e}")
            return False

    def monitor_async_operation_status(
        self,
        monitor_url: str,
        operation: str,
        check_interval: int = 2,
        max_checks: int = 60,
    ):
        try:
            for check_count in range(1, max_checks + 1):
                logger.debug(f"operation_url: {monitor_url}")
                response = self.requests_with_retry(
                    monitor_url, method="GET", headers={}, authorization=False
                )
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data.get("status", "")
                    if status == "completed":
                        if operation == "Move":
                            if "resourceLocation" in status_data:
                                return 0, status_data["resourceLocation"]
                            else:
                                err_msg = (
                                    "Operation status check failed, status is completed, "
                                    "but can not find resourceLocation"
                                )
                                logger.error(err_msg)
                                return -1, err_msg
                        else:
                            return 0, "Success"
                    elif status == "failed":
                        err_msg = (
                            f"Operation status check failed, "
                            f"{status_data.get('error', {}).get('message', 'Unknown error')}"
                        )
                        logger.error(err_msg)
                        return -1, err_msg
                    else:
                        # inProgress
                        logger.debug(f"Operation status: {status}")
                        if check_count == max_checks:
                            err_msg = "Operation status check failed, up to the max check counts"
                            logger.error(err_msg)
                            return -1, err_msg
                        time.sleep(check_interval)
                else:
                    err_msg = (
                        f"Operation status check failed, HTTP {response.status_code}"
                    )
                    logger.error(err_msg)
                    return -1, err_msg
        except Exception as e:
            err_msg = f"Operation status check failed, exception: {str(e)}"
            logger.error(err_msg)
            return -1, err_msg

    def move_file(
        self,
        folder: str,
        file_name: str,
        target_folder: str,
        target_filename: str,
        task_uuid: str,
        local_file: str = None,
    ) -> Union[dict, None]:
        """
        Move a file to the specified folder.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file to move.
            target_folder (str): The target path.
            target_filename (str): The target filename.
            task_uuid (str): The UUID of the task.

        Returns:
            dict: The file info of the moved file.

        When Exception occurs:
            return None
        """
        try:
            logger.debug(
                f"Move file: {folder}/{file_name} to {target_folder}/{target_filename}"
            )
            target_info = self.parse_weburl(target_folder)
            if target_info is None:
                logger.error(f"Error in parse weburl: {target_folder}")
                return None

            headers = {"Content-Type": "application/json", "Prefer": "respond-async"}
            payload = {
                "name": target_filename,
                "parentReference": {
                    "driveId": target_info["drive_id"],
                    "path": f"/drive/root:/{target_info['item_path'].strip('/')}",
                },
                "@microsoft.graph.conflictBehavior": "fail",
            }
            file_url = self.build_encoded_url(f"/v1.0{folder}/{file_name}")
            file_response = self.requests_with_retry(
                file_url, method="PATCH", headers=headers, json=payload
            )
            if file_response.status_code not in [200, 202]:
                logger.error(
                    f"Move file failed, file_url: {file_url}, status_code: {file_response.status_code}, "
                    f"detail: {file_response.text}"
                )
                return None

            if file_response.status_code == 202:
                monitor_url = file_response.headers.get("Location")
                if not monitor_url:
                    logger.error(f"Move file failed, can not get monitor status url")
                    return None
                ret, message = self.monitor_async_operation_status(monitor_url, "Move")
                if ret:
                    logger.error(f"Move file failed, detail: {message}")
                    return None

            t_folder = f"/drives/{target_info['drive_id']}/root:/{target_info['item_path'].strip('/')}"
            code, _ = self.get_file_info(t_folder, target_filename)
            if code:
                logger.error(f"Move file failed, can not get target file info")
                return None

            info = {}
            full_path = f"{t_folder}/{target_filename}"
            drive_name = target_info["drive_name"]
            full_display_path = drive_name + urllib.parse.quote(
                f"/{target_info['item_path'].strip('/')}/{file_name}", safe=":/"
            )
            info = {
                "full_path": full_path,
                "folder": t_folder,
                "file_name": target_filename,
                "file_display_path": full_display_path,
            }
            logger.debug(f"Move file success, file info {info}")
            return info
        except Exception as e:
            logger.error(f"Error move file: {e}")
            return None

    def copy_file(
        self,
        folder: str,
        file_name: str,
        target_folder: str,
        target_filename: str,
        task_uuid: str,
        local_file: str = None,
    ) -> Union[dict, None]:
        """
        Copy a file to the specified folder.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file to move.
            target_folder (str): The target path.
            target_filename (str): The target filename.
            task_uuid (str): The UUID of the task.

        Returns:
            dict: The file info of the copied file.

        When Exception occurs:
            return None
        """
        try:
            logger.debug(
                f"Copy file: {folder}/{file_name} to {target_folder}/{target_filename}"
            )
            target_info = self.parse_weburl(target_folder)
            if target_info is None:
                logger.error(f"Error in parse weburl: {target_folder}")
                return None

            headers = {"Content-Type": "application/json", "Prefer": "respond-async"}
            payload = {
                "name": target_filename,
                "parentReference": {
                    "driveId": target_info["drive_id"],
                    "path": f"/drive/root:/{target_info['item_path'].strip('/')}",
                },
                "@microsoft.graph.conflictBehavior": "fail",
            }
            file_url = self.build_encoded_url(f"/v1.0{folder}/{file_name}:/copy")
            file_response = self.requests_with_retry(
                file_url, method="POST", headers=headers, json=payload
            )
            if file_response.status_code not in [200, 202]:
                logger.error(
                    f"Copy file failed, file_url: {file_url}, status_code: {file_response.status_code}, "
                    f"detail: {file_response.text}"
                )
                return None

            if file_response.status_code == 202:
                monitor_url = file_response.headers.get("Location")
                if not monitor_url:
                    logger.error(f"Copy file failed, can not get monitor status url")
                    return None
                ret, message = self.monitor_async_operation_status(monitor_url, "Copy")
                if ret:
                    logger.error(f"Copy file failed, detail: {message}")
                    return None

            t_folder = f"/drives/{target_info['drive_id']}/root:/{target_info['item_path'].strip('/')}"
            code, _ = self.get_file_info(t_folder, target_filename)
            if code:
                logger.error(f"Copy file failed, can not get target file info")
                return None

            info = {}
            full_path = f"{t_folder}/{target_filename}"
            drive_name = target_info["drive_name"]
            full_display_path = drive_name + urllib.parse.quote(
                f"/{target_info['item_path'].strip('/')}/{file_name}", safe=":/"
            )
            info = {
                "full_path": full_path,
                "folder": t_folder,
                "file_name": target_filename,
                "file_display_path": full_display_path,
            }
            logger.debug(f"Copy file success, file info {info}")
            return info
        except Exception as e:
            logger.error(f"Error Copy file: {e}")
            return None

    def check_folder_exist(self, folder: str, task_uuid: str) -> bool:
        """
        Check if a folder exists.

        Args:
            folder (str): The folder relative path.
            task_uuid (str): The UUID of the task.

        Returns:
            True.

        When Exception occurs:
            False
        """
        try:
            logger.info(f"Check folder exist: {folder}")
            info = self.parse_weburl(folder)
            if info is None:
                logger.debug(
                    f"Check folder parse weburl, not find valid driver id for: {folder}"
                )
                return False

            path = f"{info['item_path'].strip('/')}"
            check_url = self.build_encoded_url(
                f"/v1.0/drives/{info['drive_id']}/root:/{path}"
            )
            response = self.requests_with_retry(check_url)
            logger.info(f"Check folder response: {response}")
            if response.status_code == 200:
                logger.info(f"Check folder success: {folder}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error check folder exist: {e}")
            return False

    def check_write_access(self, folder: str, task_uuid: str) -> bool:
        """
        Check if a folder has write access.

        Args:
            folder (str): The folder relative path.
            task_uuid (str): The UUID of the task.

        Returns:
            True.

        When Exception occurs:
            False
        """
        test_file_local_path = "/var/log/protection_action/notification_files/default"
        test_file_name = f"{ProtectionConst.QUARANTINE_NOTIFY_FILE_PREFIX.value}sharepoint_ol_write_test.txt"
        try:
            upload_file_info = self.upload(
                test_file_local_path, folder, test_file_name, task_uuid
            )
            if upload_file_info is None:
                logger.error(
                    f"Sharepoint ol write access test failed: upload file failed"
                )
                return False
            if (
                self.delete_file(
                    upload_file_info.get("folder", ""),
                    upload_file_info.get("file_name", ""),
                    task_uuid,
                )
                == False
            ):
                logger.error(
                    f"Sharepoint ol write access test failed: delete file failed"
                )
                return False
            logger.error(f"Sharepoint ol write access test success")
            return True
        except Exception as e:
            logger.error(f"Sharepoint ol write access test failed: {e}")
            return False

    def create_folder(
        self, folder: str, new_folder_name: str, task_uuid: str
    ) -> Union[dict, None]:
        """
        Create a new folder.

        Args:
            folder (str): The folder relative path.
            new_folder_name (str): The name of new folder.
            task_uuid (str): The UUID of the task.

        Returns:
            dict: The info of new folder.

        When Exception occurs:
            return None
        """
        try:
            logger.info(f"Create folder: {folder}, {new_folder_name}")
            info = self.parse_weburl(folder)
            if info is None:
                logger.error(
                    f"Create folder parse weburl error: {folder}, {new_folder_name}"
                )
                return None

            path = f"{info['item_path'].strip('/')}"
            create_url = self.build_encoded_url(
                f"/v1.0/drives/{info['drive_id']}/root:/{path}:/children"
            )
            headers = {"Content-Type": "application/json"}
            param = {
                "name": new_folder_name,
                "folder": {},
                "@microsoft.graph.conflictBehavior": "replace",
                # "@microsoft.graph.conflictBehavior": "fail"
            }
            response = self.requests_with_retry(
                create_url, method="POST", headers=headers, json=param
            )
            if response.status_code not in [200, 201]:
                logger.error(
                    f"Create folder request error: {folder}, {new_folder_name}"
                )
                return None

            return response.json()
        except Exception as e:
            logger.error(f"Error create folder: {e}")
            return False

    def ensure_field_exists(self, site_id: str, drive_id: str, field_name: str):
        try:
            list_path = f"/v1.0/sites/{site_id}/drives/{drive_id}/list"
            url_list = self.build_encoded_url(list_path)
            resp = self.requests_with_retry(url_list, method="GET")
            if resp.status_code not in (200, 201, 202, 204):
                logger.error(f"Failed to get list info: {resp.status_code}, {resp.text}")
                raise RuntimeError(f"Failed to get list info: {resp.status_code}, {resp.text}")
            list_info = resp.json()
            list_id = list_info["id"]

            create_path = f"/v1.0/sites/{site_id}/lists/{list_id}/columns"
            url_create = self.build_encoded_url(create_path)
            data = {
                "name": field_name,
                "text": {"allowMultipleLines": False}  # Single-line text
            }

            r = self.requests_with_retry(
                url_create,
                method="POST",
                headers={"Content-Type": "application/json"},
                json=data
            )

            if r.status_code in (200, 201):
                logger.info(f"Column '{field_name}' created successfully.")
            elif r.status_code == 409 and "already exists" in r.text:
                logger.info(f"Column '{field_name}' already exists (ignored duplicate creation).")
            else:
                logger.info(f"Failed to create column '{field_name}': {r.status_code}, {r.text}")
                raise RuntimeError(f"Failed to create column '{field_name}': {r.status_code}, {r.text}")

        except Exception as e:
            logger.error(f"Exception while ensuring column exists: {e}")
            raise RuntimeError(f"Exception while ensuring column exists: {e}")

    def update_file_tags_impl(
            self,
            file_uri: str,
            tags: Dict[str, list],
            *,
            file_id: str = "",
            site_id: str = "",
            drive_id: str = "",
            field_name: str = "FortiTag"
    ) -> bool:
        """
        Update file tags, ensuring that the target column exists.
        Combines all tag categories into a single comma-separated string,
        skips empty or too long tag strings, and then updates the file field.
        """
        if not file_id or not isinstance(file_id, str):
            logger.error(f"Invalid file_id: {file_id}")
            return False
        if not site_id or not isinstance(site_id, str):
            logger.error(f"Invalid site_id: {site_id}")
            return False
        if not drive_id or not isinstance(drive_id, str):
            logger.error(f"Invalid drive_id: {drive_id}")
        try:
            if not drive_id:
                url_item_path = f"/v1.0/sites/{site_id}/drive/items/{file_id}"
                url_item = self.build_encoded_url(url_item_path)
                resp = self.requests_with_retry(url_item, method="GET")
                if resp.status_code not in (200, 201, 202, 204):
                    logger.error(f"Failed to get item info: {resp.status_code}, {resp.text}")
                    return False
                item_info = resp.json()
                if not drive_id:
                    drive_id = item_info["parentReference"]["driveId"]

            self.ensure_field_exists(site_id, drive_id, field_name)

            all_values = []
            for v_list in tags.values():
                all_values.extend(v_list)
            tag_string = ",".join(all_values)

            if not tag_string:
                logger.info(f"set_file_tag => no tags to set for {file_uri}, skipped")
                return False
            if len(tag_string) > 255:
                logger.info(f"Skipped because exceeds 255 characters, file_uri: {file_uri}, tag_string: {tag_string}")
                return False

            url_fields_path = f"/v1.0/sites/{site_id}/drive/items/{file_id}/listItem/fields"
            url_fields = self.build_encoded_url(url_fields_path)
            patch_data = {field_name: tag_string}

            r = self.requests_with_retry(
                url_fields,
                method="PATCH",
                headers={"Content-Type": "application/json"},
                json=patch_data
            )

            if r.status_code in (200, 201):
                logger.info(f"Successfully updated tags '{tag_string}' in column '{field_name}'.")
                return True
            else:
                logger.error(
                    f"Failed to update tags '{tag_string}' in column '{field_name}': {r.status_code}, {r.text}")
                return False

        except Exception as e:
            logger.error(f"Exception while updating file tags: {e}")
            return False

    def update_file_tag(
            self,
            file_uri: str,
            tags: Dict[str, List[str]],
            *,
            file_id: str = "",
            site_id: str = "",
            drive_id: str = ""
    ) -> bool:
        """
        External entry function to update file tags.
        Calls update_file_tags_helper to handle the actual update logic.
        """
        return self.update_file_tags_impl(
            file_uri=file_uri,
            tags=tags,
            file_id=file_id,
            site_id=site_id,
            drive_id=drive_id,
            field_name=self.forti_tag
        )

    def fetch_drive_files_for_recycle(self, site_id, drive_id, drive_files):
        try:
            delta_path = f"/v1.0/sites/{site_id}/drives/{drive_id}/root/delta"
            query = {"$top": self.top_count, "$select": "name,parentReference,file"}
            delta_url = self.build_encoded_url(delta_path, query=query)

            files = []
            while delta_url:
                response = self.requests_with_retry(delta_url)
                if response.status_code == 200:
                    data = response.json()
                    files.extend(data.get("value", []))  # Collect items

                    # Check for @odata.nextLink for pagination
                    delta_url = data.get("@odata.nextLink", None)
                else:
                    logger.error(
                        f"Error querying delta API: {response.status_code} - {response.text}"
                    )
                    break

            # After fetching all items, print or return them
            for item in files:
                if "file" in item:  # Check if it's a file
                    parent_path = item.get("parentReference", {}).get("path", "")
                    file_name = item.get("name", "")
                    full_path = f"{parent_path}/{file_name}"
                    drive_files[full_path] = 1

        except Exception as e:
            # logger.error(f"Error fetch_drive_files_with_delta: {e}")
            raise e

    def recycle_group_fill_info(
        self, group_map, activity, site_url_trip, item_url_encode, item_type
    ):
        try:
            if item_type not in ["File", "Folder"]:
                logger_recycle.error(
                    f"Item type is not File or Folder, event: {activity}"
                )
                return

            if site_url_trip not in group_map:
                file_array = set()
                folder_array = set()
                if item_type == "File":
                    file_array.add(item_url_encode)
                else:
                    folder_array.add(item_url_encode)
                group_map[site_url_trip] = {
                    "site_name": site_url_trip,
                    "file_array": file_array,
                    "folder_array": folder_array,
                }
            else:
                if item_type == "File":
                    group_map[site_url_trip]["file_array"].add(item_url_encode)
                else:
                    group_map[site_url_trip]["folder_array"].add(item_url_encode)
        except Exception as e:
            logger_recycle.error(f"Error recycle_group_fill_info: {e}")
            return

    def generate_changed_recycle_groups(self, activities):
        try:
            group_map = {}
            process_events = {}

            for activity in activities:
                event_finished = get_connector_event_collection_history(
                    scan_policy_id=self._uuid,
                    connector_type=StorageType.SHAREPOINT_OL,
                    collection_type=ConnectorCollectionType.RECYCLE,
                    event_id=activity.event_id,
                )
                if event_finished is not None:
                    logger_recycle.debug(
                        f"This event has been processed, skip it, "
                        f"event id: {activity.event_id}"
                    )
                    continue

                self.process_audit_log_item_recycle(activity, group_map, process_events)

            logger_recycle.info(f"Generate group_map: {group_map}")
            return group_map, process_events
        except Exception as e:
            logger_recycle.error(f"Error generate_changed_recycle_groups: {e}")
            return None, {}

    def check_file_quarantine(self, record, task_uuid):
        try:
            # Read record again to get the latest QuarantineStatus
            cache = GlobalCache(task_uuid, is_ddr=self._is_ddr)
            record = cache.get_by_file_uuid(record.get("id"))
            quarantineStatus = (record.get("reserve_json3") or {}).get(
                "quarantine_status", ProtectionQuarantineStatus.INIT
            )
            if quarantineStatus in [
                ProtectionQuarantineStatus.DOING,
                ProtectionQuarantineStatus.SUCCESS,
            ]:
                logger_recycle.info(
                    f"file have been quarantined or quarantining, "
                    f"no need to delete in cache, record: {record}"
                )
                return True

            return False
        except Exception as e:
            logger_recycle.error(f"Error check file quarantine: {e}")
            return False

    def recycle(self, task_uuid: str) -> bool:
        """
        Recycles a task global cache identified by its UUID.

        Args:
            task_uuid (str): The UUID of the scan task.

        Returns:
            bool: True if the task was successfully recycled, False otherwise.

        When Exception occurs:
            return False
        """

        def process_record(record, conn=None, **kwargs) -> bool:
            try:
                file_attributes = record.get("file_attributes", {})
                file_display_path = file_attributes.get("file_display_path", "NA")
                top_site_url = self.get_top_site_url_from_item_url(file_display_path)
                file_uuid = record.get("id")
                file_ext = file_attributes.get("file_ext")
                file_size = file_attributes.get("file_size")
                min_size, max_size = self.get_size_limit()
                supported_file_types = get_supported_file_type(self._scan_file_type)

                if (
                    (
                        file_ext
                        and supported_file_types["ext"]
                        and file_ext not in supported_file_types["ext"]
                    )
                    or (file_size and not (min_size <= file_size <= max_size))
                    or (
                        top_site_url != "NA" and not self.site_should_scan(top_site_url)
                    )
                ):

                    if self.check_file_quarantine(record, task_uuid) == False:
                        cache.delete(file_uuid)
                        delete_file_actions({"file_id": file_uuid, "scan_id": task_uuid},logger)
                        logger_recycle.debug(f"Folder or File type and size not support: {file_display_path} {record} deleted from cache.")
                        time.sleep(configs["recycle_backoff_time"])
                        return True

                changed_groups = kwargs.get("changed_groups", None)
                if changed_groups is not None:
                    file_attributes = record.get("file_attributes", {})
                    file_uuid = record.get("id", "")
                    full_path = record.get("full_path")
                    file_display_path = file_attributes.get("file_display_path", "NA")
                    top_site_url = self.get_top_site_url_from_item_url(
                        file_display_path
                    )

                    if top_site_url != "NA":
                        site_info = changed_groups.get(top_site_url, {})
                        file_array = site_info.get("file_array", None)
                        if file_array is not None:
                            if file_display_path in file_array:
                                if (
                                    self.check_file_exist(
                                        full_path, logger=logger_recycle
                                    )
                                    is False
                                    and self.check_file_quarantine(record, task_uuid)
                                    == False
                                ):
                                    cache.delete(file_uuid)
                                    delete_file_actions({"file_id": file_uuid, "scan_id": task_uuid},logger)
                                    time.sleep(configs["recycle_backoff_time"])
                                    logger_recycle.debug(
                                        f"File not exist: {file_display_path} {record} "
                                        f"deleted from cache by file event."
                                    )
                                    return True

                        folder_array = site_info.get("folder_array", None)
                        if folder_array is not None:
                            for folder in folder_array:
                                if file_display_path.startswith(folder + "/"):
                                    if (
                                        self.check_file_exist(
                                            full_path, logger=logger_recycle
                                        )
                                        is False
                                        and self.check_file_quarantine(
                                            record, task_uuid
                                        )
                                        == False
                                    ):
                                        cache.delete(file_uuid)
                                        delete_file_actions({"file_id": file_uuid, "scan_id": task_uuid},logger)
                                        time.sleep(configs["recycle_backoff_time"])
                                        logger_recycle.debug(
                                            f"File not exist: {file_display_path} {record} "
                                            f"deleted from cache by folder event"
                                        )
                                        return True
                else:
                    full_path = record.get("full_path")
                    if full_path not in self.drive_current_files:
                        file_uuid = record.get("id")
                        logger_recycle.debug(
                            f"File: {full_path} is not in current files, "
                            f"will send request to double check."
                        )
                        if (
                            self.check_file_exist(full_path, logger=logger_recycle)
                            is False
                        ):
                            if self.check_file_quarantine(record, task_uuid) == False:
                                cache.delete(file_uuid)
                                delete_file_actions({"file_id": file_uuid, "scan_id": task_uuid},logger)
                                time.sleep(configs["recycle_backoff_time"])
                                logger_recycle.debug(
                                    f"File not exist: {full_path} {record} "
                                    f"deleted from cache."
                                )
                return True
            except KeyError as e:
                logger_recycle.error(f"KeyError occurred: {e}")
                return False
            except NonRetriableException as e:
                logger_recycle.error(e)
                return False
            except Exception as e:
                logger_recycle.error(e)
                return False

        def generate_drive_site_map(record, conn=None) -> bool:
            try:
                file_attributes = record.get("file_attributes", {})
                drive_id = file_attributes.get("drive_id", "N/A")
                site_id = file_attributes.get("site_id", "N/A")
                if drive_id != "N/A" and site_id != "N/A":
                    self.drive_site_map[drive_id] = site_id
                return True
            except Exception as e:
                logger_recycle.error(e)
                return False

        def recycle_check_all_files():
            try:
                self.drive_site_map = {}
                self.drive_current_files = {}
                cache.process_records(generate_drive_site_map)
                for drive_id, site_id in self.drive_site_map.items():
                    self.drive_current_files = {}
                    self.fetch_drive_files_for_recycle(
                        site_id, drive_id, self.drive_current_files
                    )
                    logger_recycle.debug(
                        f"drive_id: {drive_id} site_id: {site_id}, "
                        f"files count: {len(self.drive_current_files)}."
                    )
                    conditions = {
                        "scan_uuid": task_uuid,
                        "drive_id": drive_id,
                        "sort_method": "asc",
                        "sort_field": "scan_end",
                    }
                    cache.process_records(process_record, conditions=conditions)
            except Exception as e:
                logger_recycle.error(e)
                return False

        try:
            logger_recycle.debug(f"Recycle task({task_uuid}) begin.")
            cache = GlobalCache(task_uuid, is_ddr=self._is_ddr)
            if self.audit_log_monitoring == True:
                if self.is_time_to_fetch_all_files(
                    logger_recycle,
                    ConnectorCollectionType.RECYCLE,
                    self.all_files_fetch_recycle_min_interval_count,
                ):
                    logger_recycle.debug(
                        f"Recycle, do all files recycle, "
                        f"reason is: reach max_recycle_by_audit_log_count"
                    )
                    self.supplement_event_collection_history(
                        ConnectorCollectionType.RECYCLE
                    )
                    recycle_check_all_files()
                else:
                    changed_groups = None
                    event_history = get_connector_event_collection_history(
                        scan_policy_id=self._uuid,
                        connector_type=StorageType.SHAREPOINT_OL,
                        collection_type=ConnectorCollectionType.RECYCLE,
                    )
                    if event_history is not None:
                        start_time = event_history.last_read_time
                        _now = datetime.now(timezone.utc)
                        target_operations = [
                            "FileRecycled",
                            "FileRenamed",
                            "FileMoved",
                            "FolderRecycled",
                            "FolderRenamed",
                            "FolderMoved",
                            "SiteDeleted",
                        ]
                        start_time = start_time.replace(tzinfo=None)
                        end_time = _now.replace(tzinfo=None)
                        logger_recycle.debug(
                            f"Recycle, do event files recycle, start time: {start_time.isoformat()}, "
                            f"end time: {end_time.isoformat()}"
                        )
                        activities = get_fetched_activities(
                            storage_id=self._scan_storage_id,
                            event_type=target_operations,
                            created_at__gt=start_time,
                            created_at__lte=end_time,
                        )
                        logger_recycle.debug(
                            f"Recycle, fetch event counts: {len(activities) if activities else 0}, "
                            f"events: {activities}"
                        )

                        changed_groups, process_events = (
                            self.generate_changed_recycle_groups(activities)
                        )
                        payload = {
                            "scan_policy_id": self._uuid,
                            "connector_type": StorageType.SHAREPOINT_OL,
                            "collection_type": ConnectorCollectionType.RECYCLE,
                            "event_count": len(process_events),
                            "events": process_events,
                            "last_read_time": _now,
                            "created_at": datetime.now(),
                        }
                        create_connector_event_collection_history(payload)
                        cache.process_records(
                            process_record, changed_groups=changed_groups
                        )
                    else:
                        # first time recycle
                        self.supplement_event_collection_history(
                            ConnectorCollectionType.RECYCLE
                        )
                        # recycle_check_all_files()
            else:
                recycle_check_all_files()
            return True
        except Exception as e:
            logger_recycle.error(e)
            return False
        finally:
            logger_recycle.debug(f"Recycle task({task_uuid}) finished.")

    def _persist_file_info(self, file_array) -> Union[str, None]:
        """
        Persists file information to a queue file.

        Args:
            file_array (list): A list of file information dictionaries.

        Returns:
            str: The path of the queue file where the file information is persisted.

        When Exception occurs:
            return None
        """
        try:
            if self._session_key:
                file_name = f"file_info_{self._uuid}_{self._session_key}"
            else:
                file_name = f"file_info_{self._uuid}"

            queue_dir_path = Path(configs["download_queue"]["file_info_queue_path"])
            queue_dir_path.mkdir(parents=True, exist_ok=True)
            queue_path = queue_dir_path / file_name
            queue_path.unlink(missing_ok=True)

            with open(queue_path, "w") as f:
                for file_info in file_array:
                    f.write(json.dumps(file_info) + "\n")
            return str(queue_path)
        except Exception as e:
            logger.error(f"Error _persist_file_info: {e}")
            return None

    def _persist_group_info(self, group_array) -> Union[str, None]:
        """
        Persists group information to a queue file.

        Args:
            group_array (list): A list of file information dictionaries.

        Returns:
            str: The path of the queue file where the group information is persisted.

        When Exception occurs:
            return None
        """
        try:
            if self._session_key:
                file_name = f"group_info_{self._uuid}_{self._session_key}"
            else:
                file_name = f"group_info_{self._uuid}"

            queue_dir_path = Path(configs["download_queue"]["file_info_queue_path"])
            queue_dir_path.mkdir(parents=True, exist_ok=True)
            queue_path = queue_dir_path / file_name
            queue_path.unlink(missing_ok=True)

            with open(queue_path, "w") as f:
                for group_info in group_array:
                    f.write(json.dumps(group_info) + "\n")
            return str(queue_path)
        except Exception as e:
            logger.error(f"Error _persist_group_file_info: {e}")
            return None

    def get_file_permissions(self, site_id, drive_id, item_id) -> dict:
        """
        Get permissions info of file.

        Args:
            drive_id (str): The drive id of the file.
            item_id (str): The item id of the file.
        """
        shared_data = {
            "with_public_shareable_link": False,
            "with_internal_shareable_link": False,
            "with_external_shareable_link": False,
            "with_internal_collaborators": False,
            "with_external_collaborators": False,
        }
        result = {
            "collaborators": {},
            "share_link": [],
            "owners": [],
            "shared_data": shared_data,
        }

        def generate_shared_data(collaborators: list, share_links: list):
            identities = get_storage_identity_by_identifier(
                self._scan_storage_id, collaborators
            )
            if any(i["type"] == UserType.MEMBER for i in identities):
                shared_data["with_internal_collaborators"] = True
            if any(i["type"] == UserType.GUEST for i in identities):
                shared_data["with_external_collaborators"] = True

            for link in share_links:
                scope = link["scope"]
                if scope == "anonymous":
                    link["scope"] = "public"
                    shared_data["with_public_shareable_link"] = True
                elif scope == "organization":
                    # link['scope'] = 'internal'
                    shared_data["with_internal_shareable_link"] = True
                else:  # scope == users
                    identities = get_storage_identity_by_identifier(
                        self._scan_storage_id, link["collaborators"]
                    )
                    if any(i["type"] == UserType.GUEST for i in identities):
                        link["scope"] = "external"
                        shared_data["with_external_shareable_link"] = True
                    else:
                        link["scope"] = "internal"
                        shared_data["with_internal_shareable_link"] = True

        def add_collaborator(identity_set: dict, link=None):
            if "user" in identity_set:
                identity = identity_set["user"]
                ident_id = identity["id"]
            elif "group" in identity_set:
                identity = identity_set["group"]
                ident_id = identity["id"]
            elif "siteGroup" in identity_set:
                identity = identity_set["siteGroup"]
                ident_id = site_id + ":" + identity["id"]
            else:
                return

            # add identity to db if it does not exist
            if not get_storage_identity_by_identifier(
                self._scan_storage_id, [ident_id]
            ):
                if "siteGroup" in identity_set:
                    ident_type = IdentityType.SITEGROUP
                    user_type = "Member"
                elif "user" in identity_set:
                    ident_type = IdentityType.PEOPLE
                    user_type = self.fetch_user_type(ident_id)
                else:
                    ident_type = IdentityType.GROUP
                    user_type = self.fetch_group_user_type(ident_id)
                identity_details = {
                    "id": ident_id,
                    "displayName": identity["displayName"],
                    "mail": identity.get("email"),
                    "userType": user_type,
                }

                add_storage_identity(self._scan_storage_id, [identity_details],
                                     ident_type, version=SyncVersion.INIT)

            if link:
                link["collaborators"].append(ident_id)
            elif role == "owner":
                result["owners"].append(ident_id)
            else:
                result["collaborators"][ident_id] = {
                    "perm_id": perm_id,
                    "permission": role,
                }

        try:
            collab_url = f"https://graph.microsoft.com/v1.0/drives/{drive_id}/items/{item_id}/permissions"
            response = self.requests_with_retry(collab_url)
            data = response.json()
            if response.status_code != 200:
                logger.error(
                    f"Error fetching permissions: {response.status_code}, {data}"
                )
                return result

            permissions = data.get("value", [])
            # logger.debug(f"permission contents: {permissions}")
            for perm in permissions:
                role = perm.get("roles", None)
                role = role[0] if role else ""
                perm_id = perm["id"]

                # link type permissions
                if "link" in perm:
                    scope = perm["link"].get("scope", "")
                    if scope == "existingAccess":
                        continue
                    link_details = {
                        "perm_id": perm_id,
                        "link": perm["link"].get("webUrl", ""),
                        "permission": role,
                        "prevents_download": perm["link"].get(
                            "preventsDownload", False
                        ),
                        "coll_public": scope == "anonymous",
                        "scope": scope,
                        "collaborators": [],
                    }
                    if scope != 'organization':
                        for ident in perm.get('grantedToIdentitiesV2', []):
                            add_collaborator(ident, link=link_details)
                    result['share_link'].append(link_details)

                # user type permissions
                ident = perm.get("grantedToV2", None)
                if ident is not None:
                    add_collaborator(ident)
            generate_shared_data(
                list(result["collaborators"].keys()), result["share_link"]
            )
        except Exception as e:
            logger.exception(
                f"Error in get_file_permissions (Drive ID: {drive_id}, Item ID: {item_id}): {e}"
            )

        # logger.debug(f"processed file permissions: {result}")
        return result

    def get_file_location(self, site_id):

        path = f"/v1.0/sites/{site_id}"
        url = self.build_encoded_url(path)
        site_response = self.requests_with_retry(url)
        if site_response.status_code == 200:
            data = site_response.json()
            composite_site_id = data["id"]
            logger.info(f"composite site id is {composite_site_id}")
            sites, _ = get_storage_sites({"site_id": composite_site_id})
            if not sites:
                logger.info(f"can't find site {composite_site_id}")
                return "UNKNOWN"
            elif sites[0].get("location", "UNKNOWN") == "UNKNOWN":
                query = {"$select": "countryLetterCode"}
                org_url = self.build_encoded_url(f"/v1.0/organization", query=query)
                response = self.requests_with_retry(org_url)
                if response.status_code == 200:
                    info = response.json()
                    org_list = info.get("value", [])
                    if (
                        org_list[0]["countryLetterCode"]
                        in configs["connector"]["sharepoint"][
                            "sharepoint_data_location"
                        ]
                    ):
                        return org_list[0]["countryLetterCode"]
            else:
                return sites[0].get("location")
        return "UNKNOWN"

    def get_file_last_access_time(self, file_display_path, created_time):
        try:
            if self.audit_log_monitoring == False:
                return None

            info = {}
            target_operations = ["FileAccessed"]
            _now = datetime.now(timezone.utc)

            events = get_fetched_activities(
                storage_id=self._scan_storage_id,
                src_display_path=file_display_path,
                event_type=target_operations,
                created_at__lte=_now,
                sort_method="desc",
                sort_field="event_time",
            )
            logger.info(f"<get_file_last_access_time> file_display_path: {file_display_path} len(events): {len(events)}")
            if len(events) > 0:
                info["last_access_time"] = events[0].event_time.strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                info["last_access_operation"] = "eq"
            else:
                start_time = get_earliest_event_time(self._scan_storage_id)
                if start_time is None:
                    return None

                if created_time and start_time <= created_time:
                    last_access_time = _now.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    last_access_time = start_time.strftime("%Y-%m-%d %H:%M:%S")

                info["last_access_time"] = last_access_time
                info["last_access_operation"] = "lt"

            return info
        except Exception as e:
            logger.error(f"Error get_file_last_access_time: {e}")
            return None

    def get_file_info(
        self, folder: str, file_name: str, file_display_path: str = None
    ) -> Tuple[int, Union[Dict, None]]:
        """
        Retrieves information about a file.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file.
            task_uuid (str): The UUID of the task.
            conn: It is only valid in smb connector.

        Returns:
            Tuple[int, Union[Dict, None]]: A tuple containing an integer status code and a dictionary
            with file information. If the file information is successfully retrieved, the status code
            will be 0.
            If an error occurs during the retrieval of file information, the status code will be a
            negative integer and the dictionary will be None.
        """
        file_info = {}
        try:
            file_url = self.build_encoded_url(f"/v1.0{folder}/{file_name}")
            # Make a GET request to check if the file exists and return properties
            file_response = self.requests_with_retry(file_url)
            if file_response.status_code == 200:
                finfo = file_response.json()
                logger.debug(f"get_file_info: file_url: {file_url}, finfo: {finfo}")

                file_info["folder"] = folder
                file_info["file_name"] = file_name
                file_info["size"] = finfo.get("size")
                last_modified_str = finfo.get("lastModifiedDateTime")

                if last_modified_str:
                    last_modified_dt = datetime.strptime(
                        last_modified_str, "%Y-%m-%dT%H:%M:%SZ"
                    )
                    formatted_date = last_modified_dt.strftime("%Y-%m-%d %H:%M:%S")
                    file_info["last_modified"] = formatted_date

                created_time_dt = None
                created_time_dt_str = finfo.get("createdDateTime")
                if created_time_dt_str:
                    created_time_dt = datetime.strptime(created_time_dt_str, "%Y-%m-%dT%H:%M:%SZ")
                    formatted_date = created_time_dt.strftime("%Y-%m-%d %H:%M:%S")
                    file_info["created_time"] = formatted_date

                if file_display_path:
                    access_time_info = self.get_file_last_access_time(file_display_path, created_time_dt)
                    if access_time_info:
                        file_info["last_access_time"] = access_time_info[
                            "last_access_time"
                        ]
                        file_info["last_access_operation"] = access_time_info[
                            "last_access_operation"
                        ]

                file_info["mime_type"] = finfo.get("mime_type")
                file_info["etag"] = finfo.get("eTag")
                user_info = finfo.get("createdBy", {}).get("user", {})
                file_info["user"] = user_info.get("displayName", "UNKNOWN")
                file_info["id"] = user_info.get("id", "UNKNOWN")
                file_info["email"] = user_info.get("email", "UNKNOWN")
                file_info["create_by"] = file_info["user"]
                site_id = finfo.get("parentReference", {}).get("siteId", "")
                drive_id = finfo.get("parentReference", {}).get("driveId", "")
                item_id = finfo.get("id", "")
                file_info["file_id"] = item_id
                file_info.update(self.get_file_permissions(site_id, drive_id, item_id))
                file_info["location"] = self.get_file_location(site_id)
                file_info["encryption"] = True

                return 0, file_info
            elif file_response.status_code == 404:
                logger.info(f"Error get_file_info: File not found {file_url}")
                return ErrorCode.OTHER_ERROR.value, None
            else:
                logger.error(
                    f"Error get_file_info: {file_response.status_code}:{file_response.text}"
                )
                return ErrorCode.CONNECTION_ERROR.value, None
        except requests.exceptions.ConnectionError as e:
            logger.error(e)
            return ErrorCode.CONNECTION_ERROR.value, None
        except requests.exceptions.Timeout as e:
            logger.error(e)
            return ErrorCode.CONNECTION_ERROR.value, None
        except requests.exceptions.RequestException as e:
            logger.error(f"Error get_file_info: {e}")
            return ErrorCode.CONNECTION_ERROR.value, None
        except Exception as e:
            logger.error(f"Error get_file_info: {e}")
            return ErrorCode.OTHER_ERROR.value, None

    def _is_after_cutoff_time(
        self, last_modified_time: str, cutoff_time: datetime
    ) -> bool:
        """
        Checks if the last modified time of a file is after the cutoff time.

        Args:
            last_modified_time (str): The last modified time of the file in the format "YYYY-MM-DD HH:MM:SS".
            cutoff_time (datetime): The cutoff time.

        Returns:
            bool: True if the last modified time is after the cutoff time, False otherwise.
        """
        try:
            last_modified = datetime.strptime(last_modified_time, "%Y-%m-%d %H:%M:%S")
            timezone = pytz.timezone("UTC")
            if last_modified.tzinfo is None:
                last_modified = timezone.localize(last_modified)
            if cutoff_time.tzinfo is None:
                cutoff_time = timezone.localize(cutoff_time)
            return last_modified >= cutoff_time
        except Exception as e:
            logger.error(f"Error _is_after_cutoff_time: {e}")
            return False

    def _check_drive_exist(self, drive_id: str) -> bool:
        """
        Check if a drive exists in sharepoint.

        Args:
            drive_id (str): The id of the drive to check.

        Returns:
            bool: True if the folder exists, False otherwise.
        """

        try:
            # Build the endpoint URL to check if the folder exists
            endpoint = self.build_encoded_url(f"/v1.0/drives/{drive_id}")
            # Make a GET request to check if the folder exists
            drive_response = self.requests_with_retry(endpoint)

            if drive_response.status_code == 200:
                folder_info = drive_response.json()
                return True
            elif drive_response.status_code == 404:
                return False
            else:
                logger.error(f"Error _check_drive_exist: {drive_response.text}")
                return False
        except Exception as e:
            logger.error(e)
            return False

    # extract the tenant name and site name given the site url
    def extract_tenant_and_site(self, site_url):
        try:
            tenant_name = ""
            site_name = ""
            # Regular expression to match tenant_name and site_name in the SharePoint URL
            match = re.match(
                r"https?://([^/]+)(?:/(sites|teams|personal)/([^/]+))?", site_url
            )
            if match:
                tenant_name = match.group(1)
                site_name = match.group(3)
                return tenant_name, site_name
            else:
                return "", ""
        except Exception as e:
            logger.error(e)
            return None, None

    # acquire token for authentication
    def acquire_refresh_token(self, scope):
        try:
            """
            Since MSAL Python 1.23, it will automatically look for token from cache,
            and only send request to Identity Provider when cache misses.
            """
            try:
                if not self.client_app:
                    self.client_app = msal.ConfidentialClientApplication(
                        self.client_id,
                        authority=f"https://login.microsoftonline.com/{self.tenant_id}",
                        client_credential=self.client_secret,
                        token_cache=self.token_cache,
                    )
            except Exception as e:
                logger.error(e)
                # tenant id is invalid
                return False, "Invalid tenant ID, client ID or client secret"

            token_response = self.client_app.acquire_token_for_client(scopes=scope)
            if "access_token" in token_response:
                self.access_token = token_response["access_token"]
                return True, token_response["access_token"]
            else:
                # Extract MSAL error details
                error_code = token_response.get("error", "unknown_error")
                error_description = token_response.get(
                    "error_description", "Unknown authentication error"
                )

                if "AADSTS7000215" in error_description:
                    error_message = "Invalid client secret"
                elif "AADSTS700016" in error_description:
                    error_message = "Invalid client ID"
                elif error_code in ["invalid_client", "unauthorized_client"]:
                    error_message = "Invalid client ID or client secret"
                else:
                    error_message = f"Authentication failed: {error_description}"

                logger.error(
                    f"Token acquisition failed: {error_code}, {error_description}"
                )
                return False, error_message
        except Exception as e:
            logger.error(f"Unexpected error in acquire_refresh_token: {e}")
            return False, f"Unexpected error: {repr(e)}"

    # Function to traverse drive to get total file count, total folder count and total file size
    def get_total_drive_files(self, site_name, site_id, drive_name, drive_id):
        total_drive_file_count = 0
        total_drive_file_size = 0
        try:
            # delta_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/root/delta?$top={self.top_count}&$select=size,file"
            delta_path = f"/v1.0/sites/{site_id}/drives/{drive_id}/root/delta"
            query = {"$top": self.top_count, "$select": "size,file"}
            delta_url = self.build_encoded_url(delta_path, query=query)

            files = []
            while delta_url:
                response = self.requests_with_retry(delta_url, timeout=self.timeout)
                if response.status_code == 200:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, fetch_drive_files_with_delta")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                    data = response.json()
                    files.extend(data.get("value", []))  # Collect items

                    # Check for @odata.nextLink for pagination
                    delta_url = data.get("@odata.nextLink", None)
                else:
                    logger.error(
                        f"Error querying delta API: {response.status_code} - {response.text}"
                    )
                    break

            # After fetching all items, print or return them
            for item in files:
                if "file" in item:  # Check if it's a file
                    file_size = item.get("size", "0")
                    total_drive_file_size = total_drive_file_size + file_size
                    total_drive_file_count = total_drive_file_count + 1
            return total_drive_file_count, total_drive_file_size
        except Exception as e:
            logger.error(f"Error get_total_drive_files: {e}")
            return None

    # Function to traverse drive to get total file count, total folder count and total file size
    def get_total_site_files(self, site_name, site_id):
        # logger.info(f"traverse_total_files: api_path = {api_path}, drive_id = {drive_id}, drive_name = {drive_name}")
        total_site_file_count = 0
        total_site_file_size = 0
        try:
            # Fetch subsites for the current site
            sub_site_info = self.get_sub_sites(site_id)
            if sub_site_info:
                for subsite in sub_site_info:
                    subsite_id = subsite["id"]
                    subsite_name = subsite["name"]
                    logger.info(f"Subsite name: {subsite_name}, id: {subsite_id}")
                    # Recursively get drives from the subsite
                    fl_cnt, fl_size = self.get_total_site_files(
                        subsite_name, subsite_id
                    )
                    total_site_file_count = total_site_file_count + fl_cnt
                    total_site_file_size = total_site_file_size + fl_size

            drive_info = self.get_drive_info(site_id)
            if not drive_info:
                logger.error(
                    f"Error get_total_site_files: get_drive_info, "
                    f"site_name: {site_name}, site_id: {site_id}"
                )
                return
            for drive in drive_info:
                drive_name = drive["name"]
                drive_id = drive["id"]
                fl_cnt, fl_size = self.get_total_drive_files(
                    site_name, site_id, drive_name, drive_id
                )
                total_site_file_count = total_site_file_count + fl_cnt
                total_site_file_size = total_site_file_size + fl_size
            return total_site_file_count, total_site_file_size

        except Exception as e:
            logger.error(f"Error get_total_site_files: {e}")
            return None

    # get the site statistics - total count per site
    def get_sites_stat(self, site_list: List) -> List[Dict[str, Union[str, int]]]:
        site_data = []
        try:
            # fetch the count for each site
            for site_name in site_list:
                site_id = self.get_site_id_by_web_url(site_name)
                if site_id == None:
                    logger.error(
                        f"Error get_sites_stat: site_id is None for site: {site_name}"
                    )
                    continue
                logger.info(f"site name - {site_name}, site_id = {site_id}")
                if not self._check_site_exist(site_id):
                    logger.error(
                        f"Error get_sites_stat: check_site_exist failed for site: {site_name}"
                    )
                    continue
                result = self.get_total_site_files(site_name, site_id)
                if result == None:
                    logger.error(f"Error get_sites_stat: get_total_site_files")
                    continue
                else:
                    file_count, file_size = result
                    site_data.append(
                        {
                            "site_name": site_name,
                            "file_count": file_count,
                            "total_file_size": file_size,
                        }
                    )
            return site_data
        except Exception as e:
            logger.error(f"Error get_sites_stat: {e}")
            return None

    def _check_site_exist(self, site_id: str) -> bool:
        """
        Check if a site exists in sharepoint.

        Args:
            site_id (str): The id of the site to check.

        Returns:
            bool: True if the site exists, False otherwise.
        """
        try:
            # Build the endpoint URL to check if the folder exists
            endpoint = self.build_encoded_url(f"/v1.0/sites/{site_id}")
            # Make a GET request to check if the folder exists
            site_response = self.requests_with_retry(endpoint)

            if site_response.status_code == 200:
                folder_info = site_response.json()
                return True
            elif site_response.status_code == 404:
                return False
            else:
                logger.error(f"Error _check_site_exist: {site_response.text}")
                return False
        except Exception as e:
            logger.error(e)
            return False

    def file_should_be_skipped(self, filepath, filename):
        logger.debug(
            f"Sharepoint-flow:  _scan_skip_files: {self._scan_skip_files}, "
            f"_scan_skip_paths: {self._scan_skip_paths}"
        )
        for skip_file in self._scan_skip_files:
            if filename == skip_file or fnmatch.fnmatch(filename, skip_file):
                logger.info(
                    f"Sharepoint-flow: file {filepath}  {filename} has been skipped "
                    f"due to skip file {skip_file}"
                )
                return True

        full_path = f"{filepath}/{filename}"
        for path in self._scan_skip_paths:
            if filepath.startswith(path) or fnmatch.fnmatch(full_path, path):
                logger.info(
                    f"Sharepoint-flow: file {filepath}  {filename} has been skipped "
                    f"due to skip path {path}"
                )
                return True

        return False

    # Function to retrieve all files using delta query
    def fetch_drive_files_with_delta(
        self, site_name, site_id, drive_name, drive_id, file_array
    ):
        try:
            # delta_url = f"/v1.0/sites/{site_id}/drives/{drive_id}/root/delta?$top={self.top_count}&$select=name,parentReference,file,webUrl"
            delta_path = f"/v1.0/sites/{site_id}/drives/{drive_id}/root/delta"
            query = {
                "$top": self.top_count,
                "$select": "name,parentReference,file,webUrl",
            }
            delta_url = self.build_encoded_url(delta_path, query=query)

            files = []
            while delta_url:
                response = self.requests_with_retry(delta_url)
                if response.status_code == 200:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, fetch_drive_files_with_delta")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                    data = response.json()
                    files.extend(data.get("value", []))  # Collect items

                    # Check for @odata.nextLink for pagination
                    delta_url = data.get("@odata.nextLink", None)
                else:
                    logger.error(
                        f"Error querying delta API: {response.status_code} - {response.text}"
                    )
                    break

            # After fetching all items, print or return them
            for item in files:
                if "file" in item:  # Check if it's a file
                    # parent_path as: /drives/b!m0Cs4olmzUy3vdGBKrzCqNIjlGgqCtJIn_hYhsv0LyTKIp75gvLATJyiL4bRcwoU/root:/floder
                    parent_path = item.get("parentReference", {}).get("path", "")
                    file_path = parent_path.split("/root:")[-1]
                    file_name = item.get("name", "")
                    # logger.debug(f"Delta origin file_name: {file_name}")
                    full_display_path = drive_name + urllib.parse.quote(
                        f"{file_path}/{file_name}", safe=":/"
                    )
                    file_array.append(
                        {
                            "folder": parent_path,
                            "file_name": file_name,
                            "display_path": full_display_path,
                            "site_id": site_id,
                            "drive_id": drive_id,
                        }
                    )
        except Exception as e:
            # logger.error(f"Error fetch_drive_files_with_delta: {e}")
            raise e

    # fetch the mapping between site name and site id
    def fetch_site_map(self):

        try:
            sites, _ = get_storage_sites(conditions={"sid": self._scan_storage_id})
            for site in sites:
                site_name = site["site_url"]
                site_id = site["site_id"]
                self.site_name_id_map[site_name] = site_id

            return self.site_name_id_map
        except Exception as e:
            logger.error(f"Error fetch_site_map: error: {e}")
            raise e

    def is_percent_encoded(self, s) -> bool:
        pattern = r"%[0-9A-Fa-f]{2}"
        return bool(re.search(pattern, s))

    # Function to list sites in sharepoint
    def list_sites(self):
        result = []
        try:
            # sites_url = f"https://graph.microsoft.com/v1.0/sites/getAllSites?$top={self.top_count}"
            sites_path = f"/v1.0/sites/getAllSites"
            query = {"$top": self.top_count}
            sites_url = self.build_encoded_url(sites_path, query=query)
            next_link = sites_url
            while next_link:
                # get the list of sites
                response = self.requests_with_retry(next_link)
                if response.status_code == 200:
                    data = response.json()
                    sites = data.get("value", [])
                    top_level_sites = [
                        site for site in sites if "siteCollection" in site
                    ]
                    for site in top_level_sites:
                        site_name = site.get("webUrl", "Unnamed Site")
                        site_id = site["id"]
                        if self.is_search_site(site_name):
                            logger.debug(f"site-search info: {site}, skip it")
                            continue

                        if self.is_root_site(site_name):
                            site_name_strip = site_name.rstrip("/")
                            logger.debug(
                                f"site-root adjust format: {site_name} to {site_name_strip}"
                            )
                            site_name = site_name_strip

                        if self.is_percent_encoded(site_name):
                            site_name_decode = urllib.parse.unquote(site_name)
                            logger.debug(
                                f"site-encode adjust format: {site_name} to {site_name_decode}"
                            )
                            site_name = site_name_decode

                        # Build a mapping of site names to site IDs for future use
                        self.site_name_id_map[site_name] = site_id
                        result.append(site_name)
                    # Check for next page
                    next_link = data.get("@odata.nextLink", None)
                else:
                    logger.error(
                        f"Error list_sites: {response.status_code}, {response.json()}"
                    )
                    break
            return natsorted(result)
        except Exception as e:
            logger.error(f"Error list_sites: {e}")
            return None

    def fetch_sites(self):
        result = []
        sites_path = f"/v1.0/sites/getAllSites"
        query = {"$top": self.top_count}
        sites_url = self.build_encoded_url(sites_path, query=query)
        next_link = sites_url
        while next_link:
            response = self.requests_with_retry(next_link)
            if response.status_code == 200:
                data = response.json()
                sites = data.get("value", [])
                for site in sites:
                    if "siteCollection" in site:
                        site_name = site.get("webUrl", "Unnamed Site")
                        if self.is_search_site(site_name):
                            logger.debug(f"site-search info: {site}, skip it")
                            continue

                        if self.is_root_site(site_name):
                            site_name_strip = site_name.rstrip("/")
                            logger.debug(
                                f"site-root adjust format: {site_name} to {site_name_strip}"
                            )
                            site["webUrl"] = site_name_strip

                        if self.is_percent_encoded(site_name):
                            site_name_decode = urllib.parse.unquote(site_name)
                            logger.debug(
                                f"site-encode adjust format: {site_name} to {site_name_decode}"
                            )
                            site["webUrl"] = site_name_decode

                        if "dataLocationCode" in site["siteCollection"]:
                            site["location"] = site["siteCollection"][
                                "dataLocationCode"
                            ]
                        result.append(site)
                next_link = data.get("@odata.nextLink", None)
            else:
                raise Exception(
                    f"Failed to get sites list: {response.status_code}, {response.text}"
                )
        return result

    def fetch_user_type(self, user_id):
        base_url = f"/v1.0/users/{user_id}"
        query = {"$select": "userType"}
        url = self.build_encoded_url(base_url, query=query)
        resp = self.requests_with_retry(url, method="GET")
        if resp.status_code == 200:
            return resp.json().get("userType", "")
        else:
            fetch_storage_log.info(
                f"Error getting user type, {resp.status_code}, {resp.text}"
            )
        return ""

    def fetch_group_user_type(self, group_id):
        base_url = f"/v1.0/groups/{group_id}/members"
        query = {"$select": "userType"}
        url = self.build_encoded_url(base_url, query=query)
        resp = self.requests_with_retry(url, method="GET")
        if resp.status_code == 200:
            members = resp.json().get("value", [])
            has_guest = any(m.get("userType") == "Guest" for m in members)
            return "Guest" if has_guest else "Member"
        else:
            fetch_storage_log.info(
                f"Error getting group members, {resp.status_code}, {resp.text}"
            )
        return ""

    def fetch_users(self):
        result = []
        base_url = "/v1.0/users"
        query = {"$top": 999, "$select": "id,displayName,mail,userType"}
        url = self.build_encoded_url(base_url, query=query)
        next_link = url
        while next_link:
            resp = self.requests_with_retry(next_link)
            if resp.status_code == 200:
                data = resp.json()
                user_list = data.get("value", [])
                result.extend(user_list)
                next_link = data.get("@odata.nextLink", None)
            else:
                raise Exception(
                    f"Failed to get user list: {resp.status_code}, {resp.text}"
                )
        return result

    def fetch_groups(self):
        result = []
        base_url = "/v1.0/groups"
        query = {
            "$top": 999,
            "$filter": "groupTypes/any(c:c eq 'Unified')",
            "$select": "id,displayName,mail",
        }
        url = self.build_encoded_url(base_url, query=query)
        next_link = url
        while next_link:
            resp = self.requests_with_retry(next_link)
            if resp.status_code == 200:
                data = resp.json()
                groups = data.get("value", [])
                for group in groups:
                    user_type = self.fetch_group_user_type(group.get("id"))
                    if user_type:
                        group["userType"] = user_type
                        result.append(group)
                next_link = data.get("@odata.nextLink", None)
            else:
                raise Exception(
                    f"Failed to get group list: {resp.status_code}, {resp.text}"
                )
        return result

    def decode_sitename_of_weburl(self, url: str) -> str:
        """
        example:
        input: https://dlp103.sharepoint.com/sites/A_B-C%27D.E/...
        output: https://dlp103.sharepoint.com/sites/A_B-C'D.E/...

        input: https://dlp103.sharepoint.com/sites/sitename/...
        output: https://dlp103.sharepoint.com/sites/sitename/...  (no change)
        """
        try:
            pattern = r"^(https://[^/]+\.sharepoint\.com/sites/)([^/]+)(.*)$"
            match = re.fullmatch(pattern, url)

            if not match:
                return url

            prefix = match.group(1)
            site_name_encoded = match.group(2)
            rest = match.group(3)
            if self.is_percent_encoded(site_name_encoded):
                site_name_decoded = urllib.parse.unquote(site_name_encoded)
                decode_url = f"{prefix}{site_name_decoded}{rest}"
                logger.debug(f"decode_sitename_of_weburl: from {url} to {decode_url}")
                return decode_url

            return url
        except Exception as e:
            logger.error(f"Error decode_sitename_of_weburl: {e}")
            return url

    # Function to get drives in sharepoint
    def get_drive_info(self, site_id):
        drive_info = []
        try:
            # drives_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives?$top={self.top_count}"
            drives_path = f"/v1.0/sites/{site_id}/drives"
            query = {"$top": self.top_count}
            drives_url = self.build_encoded_url(drives_path, query=query)
            next_link = drives_url
            while next_link:
                # get the list of drives
                response = self.requests_with_retry(next_link)
                if response.status_code == 200:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, get_drive_info")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                    data = response.json()
                    drives = data.get("value", [])
                    for drive in drives:
                        if drive.get("name", "Unknown") == "PersonalCacheLibrary":
                            continue
                        drive_name_origin = drive.get("webUrl", "Unnamed Drive")
                        drive_name = self.decode_sitename_of_weburl(drive_name_origin)
                        drive_id = drive["id"]
                        # Build a mapping of drive names to drive IDs for future use
                        self.drive_name_id_map[drive_name] = drive_id
                        drive_info.append({"id": drive_id, "name": drive_name})
                    # Check for next page
                    next_link = data.get("@odata.nextLink", None)
                else:
                    logger.error(
                        f"Error get_drive_info: {response.status_code}, {response.json()}"
                    )
                    break
            return drive_info
        except Exception as e:
            # logger.error(f"Error get_drive_info: {e}")
            raise e

    # Function to list sub sites in sharepoint
    def get_sub_sites(self, site_id):
        sub_site_info = []
        try:
            # sub_sites_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/sites?$top={self.top_count}"
            sub_sites_path = f"/v1.0/sites/{site_id}/sites"
            query = {"$top": self.top_count}
            sub_sites_url = self.build_encoded_url(sub_sites_path, query=query)
            next_link = sub_sites_url
            while next_link:
                # get the list of sites
                response = self.requests_with_retry(next_link)
                if response.status_code == 200:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, get_sub_sites")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                    data = response.json()
                    sub_sites = data.get("value", [])
                    for sub_site in sub_sites:
                        sub_site_name = sub_site.get("webUrl", "Unnamed Sub Site")
                        sub_site_id = sub_site["id"]
                        sub_site_info.append({"id": sub_site_id, "name": sub_site_name})

                    # Check for next page
                    next_link = data.get("@odata.nextLink", None)
                else:
                    logger.error(
                        f"Error get_sub_sites: {response.status_code}, {response.json()}"
                    )
                    break
            return sub_site_info
        except Exception as e:
            # logger.error(f"Error get_sub_sites: {e}")
            raise e

    def is_subscription_audit_log(self, logger) -> bool:
        try:
            query = {"PublisherIdentifier": self.tenant_id}
            endpoint = self.build_encoded_url(
                f"/api/v1.0/{self.tenant_id}/activity/feed/subscriptions/list",
                scheme=self.manage_scheme,
                host=self.manage_host,
                query=query,
            )
            response = self.requests_with_retry(endpoint, scope=self.manage_scope)
            if response.status_code == 200:
                subscriptions = response.json()
                for subscription in subscriptions:
                    if (
                        subscription.get("contentType", "NA") == "Audit.SharePoint"
                        and subscription.get("status", "NA") == "enabled"
                    ):
                        logger.debug(f"Audit.SharePoint is enabled")
                        return True
                logger.debug(f"Audit.SharePoint is not enabled")
                return False
            else:
                logger.error(f"Error is_subscription_audit_log: {response.text}")
                return False
        except Exception as e:
            logger.error(e)
            return False

    def start_subscription_audit_log(self, logger) -> bool:
        try:
            query = {
                "contentType": "Audit.SharePoint",
                "PublisherIdentifier": self.tenant_id,
            }
            endpoint = self.build_encoded_url(
                f"/api/v1.0/{self.tenant_id}/activity/feed/subscriptions/start",
                scheme=self.manage_scheme,
                host=self.manage_host,
                query=query,
            )
            response = self.requests_with_retry(
                endpoint, method="POST", scope=self.manage_scope, json={}
            )
            if response.status_code == 200:
                subscription = response.json()
                logger.debug(
                    f"Start subscription audit log, subscription: {subscription}"
                )
                if (
                    subscription.get("contentType", "NA") == "Audit.SharePoint"
                    and subscription.get("status", "NA") == "enabled"
                ):
                    logger.debug(f"Start subscription audit log success")
                    return True

            logger.error(
                f"Error start subscription audit log, response.status_code: {response.status_code}, "
                f"response.text: {response.text}"
            )
            return False
        except Exception as e:
            logger.error(e)
            return False

    def stop_subscription_audit_log(self, logger) -> bool:
        try:
            query = {
                "contentType": "Audit.SharePoint",
                "PublisherIdentifier": self.tenant_id,
            }
            endpoint = self.build_encoded_url(
                f"/api/v1.0/{self.tenant_id}/activity/feed/subscriptions/stop",
                scheme=self.manage_scheme,
                host=self.manage_host,
                query=query,
            )
            response = self.requests_with_retry(
                endpoint, method="POST", scope=self.manage_scope, json={}
            )
            if response.status_code in [200, 204]:
                logger.debug(f"Stop subscription audit log success")
                return True

            logger.error(
                f"Error stop subscription audit log, response.status_code: {response.status_code}, "
                f"response.text: {response.text}"
            )
            return False
        except Exception as e:
            logger.error(e)
            return False

    def adapt_full_path_format(self, full_path):
        # "Noting to do"
        return full_path

    def is_file_attr_changed(self, record, file_info):
        """
        Check if the file analysis related attr have changed.

        Args:
            record (dict): The record of the file in cache db.
            file_info (dict): The latest file info.

        Returns:
            bool: A boolean indicating if the file analysis related attr have changed.
        """
        try:
            file_attributes = record.get("file_attributes", {})
            file_metadata = record.get("file_metadata", {})
            if (
                self._scan_method != ScanMethod.FULL_SCAN
                and file_attributes.get("file_user_name", "")
                == file_info.get("user", "")
                and file_attributes.get("file_user_email", "")
                == file_info.get("email", "")
                and file_attributes.get("file_user_id", "") == file_info.get("id", "")
                and file_attributes.get("file_location")
                == file_info.get("location", "UNKNOWN")
                and file_attributes.get("file_encryption")
                == file_info.get("encryption")
                and file_attributes.get("last_access_time", "N/A")
                == file_info.get("last_access_time", "N/A")
                and file_attributes.get("last_access_operation", "N/A")
                == file_info.get("last_access_operation", "N/A")
                and file_metadata.get("collaborators", {})
                == file_info.get("collaborators", {})
                and file_metadata.get("share_link", [])
                == file_info.get("share_link", [])
            ):
                logger.debug("The file analysis related attr have not changed")
                return False
            logger.debug("The file analysis related attr have changed")
            return True
        except Exception as e:
            logger.error(e)
            return True

    def fetch_logs(
        self, start_time: datetime, end_time: datetime, **kwargs
    ) -> List[dict]:
        """
        Fetch and convert SharePoint audit events to Storage Activity list.

        Args:
            start_time: Start time for fetching events
            end_time: End time for fetching events
            **kwargs: Additional parameters including target_operations

        Returns:
            List of Storage Activity dict ready for database storage
        """
        try:
            self.exclude_patterns = self._init_exclude_patterns()
            target_operations = kwargs.get("target_operations")
            if not target_operations:
                target_operations = self.target_operations

            fetch_storage_log.debug(
                f"Fetching SharePoint audit events for operations: {target_operations}"
            )

            # Use existing audit log functionality
            events, _, _ = self.fetch_audit_events(
                start_time, end_time, target_operations, fetch_storage_log, check_session=False
            )
            if not events:
                fetch_storage_log.info(
                    "No SharePoint audit events found for Storage Activity processing"
                )
                return []

            fetch_storage_log.debug(f"Before filter, {len(events)} SharePoint audit events")
            filtered_events = self.log_filter_group(events)

            # Convert to Storage Activity list
            activities = []
            for event in filtered_events:
                activity = self._convert_sharepoint_record(event)
                if activity:
                    activities.append(activity)

            fetch_storage_log.debug(
                f"After filter, {len(activities)} SharePoint audit events "
                f"converted to Storage Activity list"
            )

            return activities
        except Exception as e:
            fetch_storage_log.error(
                f"Error fetching Storage Activity events from SharePoint: {e}"
            )
            return []

    def _convert_sharepoint_record(self, audit_event: Dict[str, Any]) -> Optional[dict]:
        """
        Convert SharePoint audit event to Storage Activity dict using strict field mapping.
        Args:
            audit_event: Raw SharePoint audit event
        Returns:
            Storage Activity dict or None if conversion fails
        """
        try:
            event_id = audit_event.get("Id", "")

            # event_time from CreationTime
            event_time = datetime.now(timezone.utc)
            event_time_str = audit_event.get("CreationTime", "")
            if event_time_str:
                try:
                    event_time = datetime.fromisoformat(
                        event_time_str.replace("Z", "+00:00")
                    )
                except Exception:
                    pass

            ddr_fields, status = self._parse_for_ddr_fields_status(audit_event)
            activity = {
                "storage_type": StorageType.SHAREPOINT_OL,
                "storage_id": self._scan_storage_id,
                "event_time": event_time,
                "event_type": audit_event.get("Operation", ""),
                "event_id": event_id,
                "scan_policy_fields": self._parse_for_scan_policy_fields(audit_event),
                "ddr_fields": ddr_fields,
                "raw_data": audit_event,
                "scan_triggered": False,
                "status": status,
                "reserve_json": {}
            }
            logger.info(f"_convert_sharepoint_record activity: {activity}")

            return activity
        except Exception as e:
            fetch_storage_log.error(
                f"Error converting SharePoint audit event to Storage Activity: {e}"
            )
            return None

    def _parse_for_scan_policy_fields(
        self, audit_event: Dict[str, Any]
    ) -> Optional[dict]:
        """
        Parse the audit event for scan policy fields.
        Args:
            audit_event: Raw SharePoint audit event
        Returns:
            Dict of scan policy fields or {} if parsing fails
        """
        try:
            logger.info(f"_parse_for_scan_policy_fields, audit_event: {audit_event}")

            event_data = audit_event.get("EventData", "NA")
            target_res = self.parse_event_data(event_data, mode="target")
            source_res = self.parse_event_data(event_data, mode="source")

            scan_policy_fields = {
                "creation_time": audit_event.get("CreationTime", "NA"),
                "item_type": audit_event.get("ItemType", "NA"),
                "site_url": audit_event.get("SiteUrl", "NA"),
                "src_relative_url": audit_event.get("SourceRelativeUrl", "NA"),
                "dst_relative_url": audit_event.get("DestinationRelativeUrl", "NA"),
                "src_file_name": audit_event.get("SourceFileName", "NA"),
                "dst_file_name": audit_event.get("DestinationFileName", "NA"),
                "event_target_site_url_trip": target_res.get("site_url", "NA"),
                "event_target_relative_url_encode": target_res.get(
                    "relative_url", "NA"
                ),
                "event_target_item_name": target_res.get("item_name", "NA"),
                "event_target_item_url": target_res.get("item_url", "NA"),
                "event_source_site_url_trip": source_res.get("site_url", "NA"),
                "event_source_relative_url_encode": source_res.get(
                    "relative_url", "NA"
                ),
                "event_source_item_name": source_res.get("item_name", "NA"),
                "event_source_item_url": source_res.get("item_url", "NA"),
            }

            src_display_path = ""
            if "NA" not in [
                scan_policy_fields["site_url"],
                scan_policy_fields["src_relative_url"],
                scan_policy_fields["src_file_name"],
            ]:
                src_display_path = urllib.parse.quote(
                    scan_policy_fields["site_url"]
                    + scan_policy_fields["src_relative_url"]
                    + "/"
                    + scan_policy_fields["src_file_name"],
                    safe=":/",
                )
            elif scan_policy_fields["event_source_item_url"] != "NA":
                src_display_path = urllib.parse.quote(
                    scan_policy_fields["event_source_item_url"], safe=":/"
                )

            scan_policy_fields["src_display_path"] = src_display_path

            dst_display_path = ""
            if "NA" not in [
                scan_policy_fields["site_url"],
                scan_policy_fields["dst_relative_url"],
                scan_policy_fields["dst_file_name"],
            ]:
                dst_display_path = urllib.parse.quote(
                    scan_policy_fields["site_url"]
                    + scan_policy_fields["dst_relative_url"]
                    + "/"
                    + scan_policy_fields["dst_file_name"],
                    safe=":/",
                )
            elif scan_policy_fields["event_target_item_url"] != "NA":
                dst_display_path = urllib.parse.quote(
                    scan_policy_fields["event_target_item_url"], safe=":/"
                )

            scan_policy_fields["dst_display_path"] = dst_display_path

            logger.info(
                f"_parse_for_scan_policy_fields, scan_policy_fields: {scan_policy_fields}"
            )
            return scan_policy_fields

        except Exception as e:
            fetch_storage_log.error(
                f"Error parsing scan policy fields from SharePoint audit event: {e}"
            )
            return {}

    def _parse_for_ddr_fields_status(self, audit_event: Dict[str, Any]) -> Optional[dict]:
        """
        Parse the audit event for DDR fields and status.
        Args:
            audit_event: Raw SharePoint audit event
        Returns:
            Tuple of DDR fields and status or {} and 0 if parsing fails
        """
        from storage.util.enum import ActivityStatus, PlatformEventMapper, TrustLevel

        try:
            status = ActivityStatus.NEW

            operation = audit_event.get("Operation", "")
            event_mapping = PlatformEventMapper.get_sharepoint_event_mappings().get(operation)
            scan_required = PlatformEventMapper.is_scan_required(operation, StorageType.SHAREPOINT_OL)
            trust_level = PlatformEventMapper.get_trust_level_by_event_type(StorageType.SHAREPOINT_OL, operation)

            folder = ""
            if scan_required:
                pure_folder = audit_event.get("SourceRelativeUrl", "")
                url = audit_event.get("SiteUrl", "") + urllib.parse.quote(pure_folder, safe=":/")
                info = self.parse_weburl(url)
                if info is not None:
                    folder = f"/drives/{info['drive_id']}/root:{info['item_path'].strip('/')}"
                else:
                    status = ActivityStatus.FAILED
            else:
                status = ActivityStatus.SKIPPED

            user = audit_event.get("UserId", "")
            if trust_level == TrustLevel.UNKNOWN and user == "app@sharepoint":
                trust_level = TrustLevel.EXTERNAL

            file_name = audit_event.get("SourceFileName", "")
            ddr_fields = {
                "display_type": event_mapping if event_mapping else operation,
                "display_path": audit_event.get("ObjectId", ""),
                "file_ext": audit_event.get("SourceFileExtension"),
                "file_size": audit_event.get("FileSizeBytes", 0),
                "file_name": urllib.parse.quote(file_name, safe=":/"),
                "filter": audit_event.get("SiteUrl", "").strip("/"),
                "folder": folder,
                "trust_level": trust_level,
                "actor": user,
                "ip": audit_event.get("ClientIP", "")
            }
            actor_id = audit_event.get("AppAccessContext", {}).get("UserObjectId", "")
            if actor_id:
                ddr_fields["actor_id"] = actor_id

            return ddr_fields, status
        except Exception as e:
            fetch_storage_log.error(f"Error parsing DDR fields from SharePoint audit event: {e}")
            return {}, status

    def _init_exclude_patterns(self):
        return [
            re.compile(r"/Forms/AllItems\.aspx"),
            re.compile(r"/Forms/ByAuthor\.aspx"),
            re.compile(r"__siteIcon__\.*"),
            re.compile(r"/_layouts/15/"),
            re.compile(r"/_vti_bin/"),
        ]

    def _filter_static_resource(self, file_path):
        for pattern in self.exclude_patterns:
            if pattern.search(file_path):
                fetch_storage_log.debug(f"Filtered - Static resource: {file_path}")
                return True
        return False

    def _filter_server_triggered(self, audit_event):
        user_agent = audit_event.get("UserAgent", "")
        if user_agent in ["MSWAC", "OfficeWordWRS", "OfficeExcelWRS", "OfficePowerPointWRS"]:
            fetch_storage_log.debug(f"Filtered - Server triggered event user_agent: {user_agent}")
            return True
        return False

    def _filter_local_access(self, audit_event):
        user_type = audit_event.get("UserType")
        user_agent = audit_event.get("UserAgent", "")
        operation = audit_event.get("Operation", "")

        # Only filter FileDownloaded and FileAccessed events
        if operation not in ["FileDownloaded", "FileAccessed"]:
            return False

        if (user_agent == self.user_agent
            and user_type in [5, 6]): # 5: Application, 6: ServicePrincipal
            fetch_storage_log.debug(f"Filtered - Local tenant service access | Event: {operation}")
            return True

        return False

    def log_filter_group(self, events, operation_priority=None):
        """
        Filter events based on operation priority.
        """
        from storage.util.enum import PlatformEventMapper

        if operation_priority is None:
            operation_priority = PlatformEventMapper.get_sharepoint_filter_priority()

        correlation_groups = {}

        for event in events:
            if self._filter_server_triggered(event):
                continue

            file_path = event.get("ObjectId", "")
            if self._filter_static_resource(file_path):
                continue

            if self._filter_local_access(event):
                continue

            correlation_id = event.get('CorrelationId')
            if correlation_id:
                if correlation_id not in correlation_groups:
                    correlation_groups[correlation_id] = []
                correlation_groups[correlation_id].append(event)

        filtered_events = []

        for correlation_id, group_events in correlation_groups.items():
            if len(group_events) == 1:
                filtered_events.append(group_events[0])
            else:
                best_event = self._select_best_event(group_events, operation_priority)
                if best_event:
                    filtered_events.append(best_event)

        return filtered_events

    def _select_best_event(self, events, priority_map):
        """Choose the best event based on priority"""
        best_event = None
        best_priority = float('inf')
        best_is_unknown = False

        for event in events:
            operation = event.get('Operation', '')
            priority = priority_map.get(operation, 999)
            appname = event.get("AppAccessContext", {}).get("ClientAppName", "").lower()
            is_unknown = (appname == "unknown")

            if priority < best_priority:
                best_priority = priority
                best_event = event
                best_is_unknown = is_unknown
            elif priority == best_priority:
                if is_unknown and not best_is_unknown:
                    # Current is unknown, but best is not, choose current
                    best_event = event
                    best_is_unknown = is_unknown
                elif (is_unknown == best_is_unknown):
                    # Current and best are both unknown or both not unknown, choose the latest one
                    best_event = event
                    best_is_unknown = is_unknown

        return best_event


    def delete_permission(self, drive_id, item_id, perm_id: str) -> tuple[bool, str]:
        err_msg = ""

        if not drive_id or not item_id or not perm_id:
            err_msg = "Missing parameters"
            logger.error(err_msg)
            return False, err_msg
        try:
            file_url = self.build_encoded_url(
                f"/v1.0/drives/{drive_id}/items/{item_id}/permissions/{perm_id}"
            )
            file_response = self.requests_with_retry(file_url, method="DELETE")
            if file_response.status_code == 204:
                return True, ""
            else:
                resp_msg = file_response.json().get('error', {}).get('message', "")
                err_msg = f"{file_response.status_code}: {resp_msg}"
                logger.error(err_msg)
        except Exception as e:
            err_msg = str(e)
            logger.exception(err_msg)
        return False, err_msg

    def modify_permission(self, drive_id, item_id, perm_id, role) -> tuple[bool, str]:
        """ Change permission role. Link permissions cannot be updated. """
        err_msg = ""
        if not drive_id or not item_id or not perm_id:
            err_msg = "Missing parameters"
            logger.error(err_msg)
            return False, err_msg
        if role not in {'read', 'write'}:
            err_msg = f"Unsupported role: {role}"
            logger.error(err_msg)
            return False, err_msg
        try:
            file_url = self.build_encoded_url(f"/v1.0/drives/{drive_id}/items/{item_id}/permissions/{perm_id}")
            file_response = self.requests_with_retry(file_url, method="PATCH", json={'roles': [role]})
            if file_response.status_code == 200:
                return True, ""
            else:
                resp_msg = file_response.json().get('error', {}).get('message', "")
                err_msg = f"{file_response.status_code}: {resp_msg}"
                logger.error(err_msg)
        except Exception as e:
            err_msg = str(e)
            logger.exception(err_msg)
        return False, err_msg
