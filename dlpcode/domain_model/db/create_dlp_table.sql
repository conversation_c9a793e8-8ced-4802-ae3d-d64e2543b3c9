CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS tlsh_gist;
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS scan_policy (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(256),
  scan_scope smallint NOT NULL,
  scan_folders jsonb NOT NULL,
  excluded_scan_folders jsonb NOt NULL,
  storage_type smallint NOT NULL,
  scan_init_info jsonb NOT NULL,
  status smallint NOT NULL,
  scan_schedule jsonb NOT NULL,
  file_size_limit jsonb NOT NULL,
  scan_file_type jsonb NOT NULL,
  scan_interval	smallint NOT NULL,
  analyze_setting jsonb NOT NULL,
  discover_policy jsonb NOT NULL,
  custom_datatype_group VARCHAR(100),
  db_index INT NOT NULL,
  protection_profiles jsonb NOT NULL,
  file_exclusion jsonb,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS scan_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scan_policy_id UUID NOT NULL,
  scan_method smallint NOT NULL,
  start_time timestamp with time zone NOT NULL,
  finish_time timestamp with time zone NOT NULL,
  scan_result smallint NOT NULL,
  fail_reason VARCHAR(256) NOT NULL,
  is_resume_scan boolean NOT NULL,
  counter jsonb NOT NULL,
  created_at timestamp with time zone NOT NULL
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS connector_event_collection_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scan_policy_id UUID NOT NULL,
  connector_type integer NOT NULL,
  collection_type integer NOT NULL,
  event_count integer NOT NULL,
  events jsonb NOT NULL,
  last_read_time timestamp with time zone,
  created_at timestamp default now()::timestamp(3)
);
CREATE INDEX IF NOT EXISTS connector_event_collection_history_events ON connector_event_collection_history USING GIN (events);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS dlp_rule (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(256),
  match_condition jsonb,
  match_condition_relation jsonb,
  match_finger_print INT,
  file_finger_print jsonb,
  policy_id UUID,
  severity INT,
  action jsonb,
  address jsonb,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS dlp_policy (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(256),
  priority INT,
  status INT,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS analyzer_version (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  version VARCHAR(100) NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS storage_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  type smallint NOT NULL,
  identifier VARCHAR(256),
  auth_info jsonb NOT NULL,
  info jsonb,
  ext_info jsonb,
  notes VARCHAR(512),
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);

CREATE INDEX IF NOT EXISTS storage_profiles_type ON storage_profiles (type);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS storage_identity (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sid UUID NOT NULL,
  name VARCHAR(256),
  email VARCHAR(256),
  type smallint,
  identifier VARCHAR(256),
  info jsonb,
  ext_info jsonb,
  version smallint,
  identity_type smallint,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3),

  CONSTRAINT storage_identity_idx UNIQUE (sid, identifier, version)
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS storage_sites (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sid UUID NOT NULL,
  site_id VARCHAR(256),
  site_url VARCHAR(256),
  info jsonb,
  ext_info jsonb,
  version smallint,
  location VARCHAR(256),
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3),

  CONSTRAINT storage_sites_idx UNIQUE (sid, site_id, version)
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS storage_shared_drives (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sid UUID NOT NULL,
  name VARCHAR(256),
  organizer_email VARCHAR(256),
  identifier VARCHAR(256),
  info jsonb,
  ext_info jsonb,
  version smallint,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3),

  CONSTRAINT shared_drives_idx UNIQUE (sid, identifier, version)
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS data_classifier (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(512) NOT NULL,
  description VARCHAR(512),
  predefined_info VARCHAR(100),
  is_predefined BOOLEAN NOT NULL DEFAULT False,
  status BOOLEAN NOT NULL DEFAULT True,
  region jsonb,
  category jsonb,
  match_condition jsonb,
  match_condition_relation jsonb,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3),
  sensitivity INT
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS discover_policy_v2 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(512) NOT NULL,
  description VARCHAR(512),
  storage_type jsonb,
  file_exclusion jsonb,
  predefined_info VARCHAR(100),
  is_predefined BOOLEAN NOT NULL DEFAULT False,
  status BOOLEAN NOT NULL DEFAULT True,
  data_classifier_ids jsonb NOT NULL,
  match_condition jsonb,
  match_condition_relation jsonb,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3),
  risk INT,
  protection_framework jsonb,
  action jsonb,
  notification_id jsonb
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS custom_dtype_group (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(256),
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS custom_data_type (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(256),
  group_uuid UUID,
  definition jsonb,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);

----------------------------------------------------------------------
create table if not exists DLPSession (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);
create index if not exists session_ops_index on DLPSession using gin(info);
----------------------------------------------------------------------
create table if not exists DLPUser (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);
create index if not exists user_ops_index on DLPUser using gin(info);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS dlp_role (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  full_name VARCHAR(100) NOT NULL,
  description VARCHAR(512),
  created_at timestamp default now()::timestamp(3)
);

----------------------------------------------------------------------
create table if not exists DLPLicense (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);
/* No need to add additional index, there is only one data */;
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS ad_config (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ad_server VARCHAR(256) NOT NULL,
  ad_username VARCHAR(256) NOT NULL,
  ad_password VARCHAR(256) NOT NULL,
  ad_group_prefix VARCHAR(256) NOT NULL,
  ad_fetch_interval INT NOT NULL,
  ad_status BOOLEAN NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS pool_config (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  pool_name VARCHAR(256) NOT NULL,
  description VARCHAR(256),
  /* this is an array of email groups */
  email_group_list VARCHAR[] NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS huey_task_tracker
(
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    scan_policy_id uuid NOT NULL,
    session_key uuid NOT NULL,
    backlog_hash character(40) COLLATE pg_catalog."default" NOT NULL,
    stage smallint NOT NULL,
    params bytea NOT NULL,
    pid INT NOT NULL,
    created_at timestamp with time zone NOT NULL,
    CONSTRAINT huey_task_tracker_pkey PRIMARY KEY (id),
    CONSTRAINT huey_task_tracker_idx1 UNIQUE (scan_policy_id, session_key, backlog_hash, stage)
);
CREATE INDEX if NOT EXISTS huey_task_tracker_idx2 ON huey_task_tracker (backlog_hash);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS huey_task_counter
(
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    scan_policy_id uuid NOT NULL,
    session_key uuid NOT NULL,
    namespace character varying COLLATE pg_catalog."default" NOT NULL,
    value integer NOT NULL,
    CONSTRAINT huey_task_counter_pkey PRIMARY KEY (id),
    CONSTRAINT huey_task_counter_idx1 UNIQUE (scan_policy_id, session_key, namespace)
);
----------------------------------------------------------------------
create table if not exists system_event (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);

create index if not exists sys_event_ops_index on system_event using gin(info);
----------------------------------------------------------------------
-- Data scanning Incident
create table if not exists ds_incidents (
  id uuid default uuid_generate_v4(),
  ctime timestamp default now()::timestamp(3),
  utime timestamp default now()::timestamp(3),
  attributes jsonb not null,
  attributes_ext jsonb not null,
  pol_details jsonb not null,
  data_source jsonb not null,
  file_info jsonb not null,
  labels jsonb not null,
  primary key (id)
);

create index if not exists ds_incidents_idx_attr on ds_incidents using gin(attributes);
create index if not exists ds_incidents_idx_attr_ext on ds_incidents using gin(attributes_ext);
create index if not exists ds_incidents_idx_pol on ds_incidents using gin(pol_details);
create index if not exists ds_incidents_idx_ds on ds_incidents using gin(data_source);
create index if not exists ds_incidents_idx_fi on ds_incidents using gin(file_info);
create index if not exists ds_incidents_idx_lab on ds_incidents using gin(labels);

create index if not exists idx_dsincidentspoldetails_btree ON ds_incidents ((pol_details->>'sid'));
create index if not exists idx_dsincidentsattrssev_btree ON ds_incidents ((attributes->>'severity'));
CREATE INDEX IF NOT EXISTS idx_dsincidentsutime ON ds_incidents (utime);

----------------------------------------------------------------------
-- Firewall Incident
-- create table if not exists fw_incidents (
--   id uuid default uuid_generate_v4(),
--   ctime timestamp default now()::timestamp(3),
--   utime timestamp default now()::timestamp(3),
--   attributes jsonb not null,
--   attributes_ext jsonb not null,
--   pol_details jsonb not null,
--   data_source jsonb not null,
--   file_info jsonb not null,
--   channel_details jsonb not null,
--   primary key (id)
-- );

-- create index if not exists fw_incidents_idx_attr on fw_incidents using gin(attributes);
-- create index if not exists fw_incidents_idx_attr_ext on fw_incidents using gin(attributes_ext);
-- create index if not exists fw_incidents_idx_pol on fw_incidents using gin(pol_details);
-- create index if not exists fw_incidents_idx_ds on fw_incidents using gin(data_source);
-- create index if not exists fw_incidents_idx_fi on fw_incidents using gin(file_info);
-- create index if not exists fw_incidents_idx_cd on fw_incidents using gin(channel_details);

----------------------------------------------------------------------

create table if not exists system_certs (
  id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (id)
);
--Begin create index :
create index if not exists system_certs_info on system_certs using gin(info);
----------------------------------------------------------------------

create table if not exists system_ca_certs (
  id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (id)
);
--Begin create index :
create index if not exists system_ca_certs_info on system_ca_certs using gin(info);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS http2_config (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  interface VARCHAR(32) NOT NULL,
  port INT default 8443 NOT NULL,
  serviceenabled boolean default True NOT NULL,
  certid uuid,
  license_status VARCHAR(32) NOT NULL default '',
  info jsonb,
  event_record_enable boolean default False NOT NULL,
  update_time timestamp default CURRENT_TIMESTAMP
);
----------------------------------------------------------------------
create table if not exists dlp_events (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);

create index if not exists dlp_events_ops_index on dlp_events using gin(info);

----------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_1_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_1_1_file_hash ON fileinfo_1_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_1_1_file_tlsh ON fileinfo_1_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_1_1_scan_uuid ON fileinfo_1_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_1_1_full_path ON fileinfo_1_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_1_1_file_name ON fileinfo_1_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_1_1_file_attributes ON fileinfo_1_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_1_1_storage_type ON fileinfo_1_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_1_1_main_class_id ON fileinfo_1_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_1_1_sub_class_id ON fileinfo_1_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_1_1_match_info_notempty ON fileinfo_1_num_1 USING GIN (match_info jsonb_path_ops) where match_info <> '[]' and match_info <> '{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1_1_match_info_empty ON fileinfo_1_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1_1_match_info ON fileinfo_1_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_1_1_file_tag ON fileinfo_1_num_1 USING GIN (file_tag);
CREATE INDEX IF NOT EXISTS fi_1_1_tm ON fileinfo_1_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_1_1_record_id ON fileinfo_1_num_1 (record_id);
create index if not exists fi_1_1_sensitive_data ON fileinfo_1_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_1_1_compliance_data ON fileinfo_1_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_1_1_last_scan_tm ON fileinfo_1_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_1_1_reserve_json3 ON fileinfo_1_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_1_1_filehash_scanuuid ON fileinfo_1_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_1_1_file_metadata ON fileinfo_1_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_1_1_shared_data_internal ON fileinfo_1_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_1_1_shared_data_external ON fileinfo_1_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_1_1_shared_data_public ON fileinfo_1_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));


CREATE TABLE IF NOT EXISTS folder_info_1
(
	id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
	scan_uuid UUID NOT NULL,
	folder_path TEXT NOT NULL UNIQUE,
	folder_tag jsonb NOT NULL,
	tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_1_path_index ON folder_info_1 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_1_tag_index ON folder_info_1 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_2_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_2_1_file_hash ON fileinfo_2_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_2_1_file_tlsh ON fileinfo_2_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_2_1_scan_uuid ON fileinfo_2_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_2_1_full_path ON fileinfo_2_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_2_1_file_name ON fileinfo_2_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_2_1_file_attributes ON fileinfo_2_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_2_1_storage_type ON fileinfo_2_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_2_1_main_class_id ON fileinfo_2_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_2_1_sub_class_id ON fileinfo_2_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_2_1_match_info_notempty ON fileinfo_2_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_2_1_match_info_empty ON fileinfo_2_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_2_1_match_info ON fileinfo_2_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_2_1_tm ON fileinfo_2_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_2_1_record_id ON fileinfo_2_num_1 (record_id);
create index if not exists fi_2_1_sensitive_data ON fileinfo_2_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_2_1_compliance_data ON fileinfo_2_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_2_1_last_scan_tm ON fileinfo_2_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_2_1_reserve_json3 ON fileinfo_2_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_2_1_filehash_scanuuid ON fileinfo_2_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_2_1_file_metadata ON fileinfo_2_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_2_1_shared_data_internal ON fileinfo_2_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_2_1_shared_data_external ON fileinfo_2_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_2_1_shared_data_public ON fileinfo_2_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_2
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_2_path_index ON folder_info_2 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_2_tag_index ON folder_info_2 USING GIN (folder_tag);


-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_3_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_3_1_file_hash ON fileinfo_3_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_3_1_file_tlsh ON fileinfo_3_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_3_1_scan_uuid ON fileinfo_3_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_3_1_full_path ON fileinfo_3_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_3_1_file_name ON fileinfo_3_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_3_1_file_attributes ON fileinfo_3_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_3_1_storage_type ON fileinfo_3_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_3_1_main_class_id ON fileinfo_3_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_3_1_sub_class_id ON fileinfo_3_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_3_1_match_info_notempty ON fileinfo_3_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_3_1_match_info_empty ON fileinfo_3_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_3_1_match_info ON fileinfo_3_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_3_1_tm ON fileinfo_3_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_3_1_record_id ON fileinfo_3_num_1 (record_id);
create index if not exists fi_3_1_sensitive_data ON fileinfo_3_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_3_1_compliance_data ON fileinfo_3_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_3_1_last_scan_tm ON fileinfo_3_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_3_1_reserve_json3 ON fileinfo_3_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_3_1_filehash_scanuuid ON fileinfo_3_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_3_1_file_metadata ON fileinfo_3_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_3_1_shared_data_internal ON fileinfo_3_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_3_1_shared_data_external ON fileinfo_3_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_3_1_shared_data_public ON fileinfo_3_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_3
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_3_path_index ON folder_info_3 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_3_tag_index ON folder_info_3 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_4_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_4_1_file_hash ON fileinfo_4_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_4_1_file_tlsh ON fileinfo_4_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_4_1_scan_uuid ON fileinfo_4_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_4_1_full_path ON fileinfo_4_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_4_1_file_name ON fileinfo_4_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_4_1_file_attributes ON fileinfo_4_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_4_1_storage_type ON fileinfo_4_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_4_1_main_class_id ON fileinfo_4_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_4_1_sub_class_id ON fileinfo_4_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_4_1_match_info_notempty ON fileinfo_4_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_4_1_match_info_empty ON fileinfo_4_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_4_1_match_info ON fileinfo_4_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_4_1_tm ON fileinfo_4_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_4_1_record_id ON fileinfo_4_num_1 (record_id);
create index if not exists fi_4_1_sensitive_data ON fileinfo_4_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_4_1_compliance_data ON fileinfo_4_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_4_1_last_scan_tm ON fileinfo_4_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_4_1_reserve_json3 ON fileinfo_4_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_4_1_filehash_scanuuid ON fileinfo_4_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_4_1_file_metadata ON fileinfo_4_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_4_1_shared_data_internal ON fileinfo_4_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_4_1_shared_data_external ON fileinfo_4_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_4_1_shared_data_public ON fileinfo_4_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_4
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_4_path_index ON folder_info_4 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_4_tag_index ON folder_info_4 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_5_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_5_1_file_hash ON fileinfo_5_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_5_1_file_tlsh ON fileinfo_5_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_5_1_scan_uuid ON fileinfo_5_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_5_1_full_path ON fileinfo_5_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_5_1_file_name ON fileinfo_5_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_5_1_file_attributes ON fileinfo_5_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_5_1_storage_type ON fileinfo_5_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_5_1_main_class_id ON fileinfo_5_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_5_1_sub_class_id ON fileinfo_5_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_5_1_match_info_notempty ON fileinfo_5_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_5_1_match_info_empty ON fileinfo_5_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_5_1_match_info ON fileinfo_5_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_5_1_tm ON fileinfo_5_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_5_1_record_id ON fileinfo_5_num_1 (record_id);
create index if not exists fi_5_1_sensitive_data ON fileinfo_5_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_5_1_compliance_data ON fileinfo_5_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_5_1_last_scan_tm ON fileinfo_5_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_5_1_reserve_json3 ON fileinfo_5_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_5_1_filehash_scanuuid ON fileinfo_5_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_5_1_file_metadata ON fileinfo_5_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_5_1_shared_data_internal ON fileinfo_5_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_5_1_shared_data_external ON fileinfo_5_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_5_1_shared_data_public ON fileinfo_5_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_5
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_5_path_index ON folder_info_5 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_5_tag_index ON folder_info_5 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_6_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_6_1_file_hash ON fileinfo_6_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_6_1_file_tlsh ON fileinfo_6_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_6_1_scan_uuid ON fileinfo_6_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_6_1_full_path ON fileinfo_6_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_6_1_file_name ON fileinfo_6_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_6_1_file_attributes ON fileinfo_6_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_6_1_storage_type ON fileinfo_6_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_6_1_main_class_id ON fileinfo_6_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_6_1_sub_class_id ON fileinfo_6_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_6_1_match_info_notempty ON fileinfo_6_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_6_1_match_info_empty ON fileinfo_6_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_6_1_match_info ON fileinfo_6_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_6_1_tm ON fileinfo_6_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_6_1_record_id ON fileinfo_6_num_1 (record_id);
create index if not exists fi_6_1_sensitive_data ON fileinfo_6_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_6_1_compliance_data ON fileinfo_6_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_6_1_last_scan_tm ON fileinfo_6_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_6_1_reserve_json3 ON fileinfo_6_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_6_1_filehash_scanuuid ON fileinfo_6_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_6_1_file_metadata ON fileinfo_6_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_6_1_shared_data_internal ON fileinfo_6_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_6_1_shared_data_external ON fileinfo_6_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_6_1_shared_data_public ON fileinfo_6_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_6
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_6_path_index ON folder_info_6 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_6_tag_index ON folder_info_6 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_7_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_7_1_file_hash ON fileinfo_7_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_7_1_file_tlsh ON fileinfo_7_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_7_1_scan_uuid ON fileinfo_7_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_7_1_full_path ON fileinfo_7_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_7_1_file_name ON fileinfo_7_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_7_1_file_attributes ON fileinfo_7_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_7_1_storage_type ON fileinfo_7_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_7_1_main_class_id ON fileinfo_7_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_7_1_sub_class_id ON fileinfo_7_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_7_1_match_info_notempty ON fileinfo_7_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_7_1_match_info_empty ON fileinfo_7_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_7_1_match_info ON fileinfo_7_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_7_1_tm ON fileinfo_7_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_7_1_record_id ON fileinfo_7_num_1 (record_id);
create index if not exists fi_7_1_sensitive_data ON fileinfo_7_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_7_1_compliance_data ON fileinfo_7_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_7_1_last_scan_tm ON fileinfo_7_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_7_1_reserve_json3 ON fileinfo_7_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_7_1_filehash_scanuuid ON fileinfo_7_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_7_1_file_metadata ON fileinfo_7_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_7_1_shared_data_internal ON fileinfo_7_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_7_1_shared_data_external ON fileinfo_7_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_7_1_shared_data_public ON fileinfo_7_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_7
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_7_path_index ON folder_info_7 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_7_tag_index ON folder_info_7 USING GIN (folder_tag);


-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_8_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_8_1_file_hash ON fileinfo_8_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_8_1_file_tlsh ON fileinfo_8_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_8_1_scan_uuid ON fileinfo_8_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_8_1_full_path ON fileinfo_8_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_8_1_file_name ON fileinfo_8_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_8_1_file_attributes ON fileinfo_8_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_8_1_storage_type ON fileinfo_8_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_8_1_main_class_id ON fileinfo_8_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_8_1_sub_class_id ON fileinfo_8_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_8_1_match_info_notempty ON fileinfo_8_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_8_1_match_info_empty ON fileinfo_8_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_8_1_match_info ON fileinfo_8_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_8_1_tm ON fileinfo_8_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_8_1_record_id ON fileinfo_8_num_1 (record_id);
create index if not exists fi_8_1_sensitive_data ON fileinfo_8_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_8_1_compliance_data ON fileinfo_8_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_8_1_last_scan_tm ON fileinfo_8_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_8_1_reserve_json3 ON fileinfo_8_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_8_1_filehash_scanuuid ON fileinfo_8_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_8_1_file_metadata ON fileinfo_8_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_8_1_shared_data_internal ON fileinfo_8_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_8_1_shared_data_external ON fileinfo_8_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_8_1_shared_data_public ON fileinfo_8_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_8
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_8_path_index ON folder_info_8 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_8_tag_index ON folder_info_8 USING GIN (folder_tag);


-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_9_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_9_1_file_hash ON fileinfo_9_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_9_1_file_tlsh ON fileinfo_9_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_9_1_scan_uuid ON fileinfo_9_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_9_1_full_path ON fileinfo_9_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_9_1_file_name ON fileinfo_9_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_9_1_file_attributes ON fileinfo_9_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_9_1_storage_type ON fileinfo_9_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_9_1_main_class_id ON fileinfo_9_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_9_1_sub_class_id ON fileinfo_9_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_9_1_match_info_notempty ON fileinfo_9_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_9_1_match_info_empty ON fileinfo_9_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_9_1_match_info ON fileinfo_9_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_9_1_tm ON fileinfo_9_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_9_1_record_id ON fileinfo_9_num_1 (record_id);
create index if not exists fi_9_1_sensitive_data ON fileinfo_9_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_9_1_compliance_data ON fileinfo_9_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_9_1_last_scan_tm ON fileinfo_9_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_9_1_reserve_json3 ON fileinfo_9_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_9_1_filehash_scanuuid ON fileinfo_9_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_9_1_file_metadata ON fileinfo_9_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_9_1_shared_data_internal ON fileinfo_9_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_9_1_shared_data_external ON fileinfo_9_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_9_1_shared_data_public ON fileinfo_9_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_9
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_9_path_index ON folder_info_9 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_9_tag_index ON folder_info_9 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_10_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_10_1_file_hash ON fileinfo_10_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_10_1_file_tlsh ON fileinfo_10_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_10_1_scan_uuid ON fileinfo_10_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_10_1_full_path ON fileinfo_10_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_10_1_file_name ON fileinfo_10_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_10_1_file_attributes ON fileinfo_10_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_10_1_storage_type ON fileinfo_10_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_10_1_main_class_id ON fileinfo_10_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_10_1_sub_class_id ON fileinfo_10_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_10_1_match_info_notempty ON fileinfo_10_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_10_1_match_info_empty ON fileinfo_10_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_10_1_match_info ON fileinfo_10_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_10_1_tm ON fileinfo_10_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_10_1_record_id ON fileinfo_10_num_1 (record_id);
create index if not exists fi_10_1_sensitive_data ON fileinfo_10_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_10_1_compliance_data ON fileinfo_10_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_10_1_last_scan_tm ON fileinfo_10_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_10_1_reserve_json3 ON fileinfo_10_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_10_1_filehash_scanuuid ON fileinfo_10_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_10_1_file_metadata ON fileinfo_10_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_10_1_shared_data_internal ON fileinfo_10_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_10_1_shared_data_external ON fileinfo_10_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_10_1_shared_data_public ON fileinfo_10_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_10
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_10_path_index ON folder_info_10 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_10_tag_index ON folder_info_10 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_11_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_11_1_file_hash ON fileinfo_11_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_11_1_file_tlsh ON fileinfo_11_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_11_1_scan_uuid ON fileinfo_11_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_11_1_full_path ON fileinfo_11_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_11_1_file_name ON fileinfo_11_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_11_1_file_attributes ON fileinfo_11_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_11_1_storage_type ON fileinfo_11_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_11_1_main_class_id ON fileinfo_11_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_11_1_sub_class_id ON fileinfo_11_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_11_1_match_info_notempty ON fileinfo_11_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_11_1_match_info_empty ON fileinfo_11_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_11_1_match_info ON fileinfo_11_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_11_1_tm ON fileinfo_11_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_11_1_record_id ON fileinfo_11_num_1 (record_id);
create index if not exists fi_11_1_sensitive_data ON fileinfo_11_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_11_1_compliance_data ON fileinfo_11_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_11_1_last_scan_tm ON fileinfo_11_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_11_1_reserve_json3 ON fileinfo_11_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_11_1_filehash_scanuuid ON fileinfo_11_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_11_1_file_metadata ON fileinfo_11_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_11_1_shared_data_internal ON fileinfo_11_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_11_1_shared_data_external ON fileinfo_11_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_11_1_shared_data_public ON fileinfo_11_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_11
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_11_path_index ON folder_info_11 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_11_tag_index ON folder_info_11 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_12_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_12_1_file_hash ON fileinfo_12_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_12_1_file_tlsh ON fileinfo_12_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_12_1_scan_uuid ON fileinfo_12_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_12_1_full_path ON fileinfo_12_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_12_1_file_name ON fileinfo_12_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_12_1_file_attributes ON fileinfo_12_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_12_1_storage_type ON fileinfo_12_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_12_1_main_class_id ON fileinfo_12_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_12_1_sub_class_id ON fileinfo_12_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_12_1_match_info_notempty ON fileinfo_12_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_12_1_match_info_empty ON fileinfo_12_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_12_1_match_info ON fileinfo_12_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_12_1_tm ON fileinfo_12_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_12_1_record_id ON fileinfo_12_num_1 (record_id);
create index if not exists fi_12_1_sensitive_data ON fileinfo_12_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_12_1_compliance_data ON fileinfo_12_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_12_1_last_scan_tm ON fileinfo_12_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_12_1_reserve_json3 ON fileinfo_12_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_12_1_filehash_scanuuid ON fileinfo_12_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_12_1_file_metadata ON fileinfo_12_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_12_1_shared_data_internal ON fileinfo_12_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_12_1_shared_data_external ON fileinfo_12_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_12_1_shared_data_public ON fileinfo_12_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_12
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_12_path_index ON folder_info_12 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_12_tag_index ON folder_info_12 USING GIN (folder_tag);


-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_13_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_13_1_file_hash ON fileinfo_13_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_13_1_file_tlsh ON fileinfo_13_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_13_1_scan_uuid ON fileinfo_13_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_13_1_full_path ON fileinfo_13_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_13_1_file_name ON fileinfo_13_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_13_1_file_attributes ON fileinfo_13_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_13_1_storage_type ON fileinfo_13_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_13_1_main_class_id ON fileinfo_13_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_13_1_sub_class_id ON fileinfo_13_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_13_1_match_info_notempty ON fileinfo_13_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_13_1_match_info_empty ON fileinfo_13_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_13_1_match_info ON fileinfo_13_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_13_1_tm ON fileinfo_13_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_13_1_record_id ON fileinfo_13_num_1 (record_id);
create index if not exists fi_13_1_sensitive_data ON fileinfo_13_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_13_1_compliance_data ON fileinfo_13_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_13_1_last_scan_tm ON fileinfo_13_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_13_1_reserve_json3 ON fileinfo_13_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_13_1_filehash_scanuuid ON fileinfo_13_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_13_1_file_metadata ON fileinfo_13_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_13_1_shared_data_internal ON fileinfo_13_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_13_1_shared_data_external ON fileinfo_13_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_13_1_shared_data_public ON fileinfo_13_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_13
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_13_path_index ON folder_info_13 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_13_tag_index ON folder_info_13 USING GIN (folder_tag);


-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_14_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_14_1_file_hash ON fileinfo_14_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_14_1_file_tlsh ON fileinfo_14_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_14_1_scan_uuid ON fileinfo_14_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_14_1_full_path ON fileinfo_14_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_14_1_file_name ON fileinfo_14_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_14_1_file_attributes ON fileinfo_14_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_14_1_storage_type ON fileinfo_14_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_14_1_main_class_id ON fileinfo_14_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_14_1_sub_class_id ON fileinfo_14_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_14_1_match_info_notempty ON fileinfo_14_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_14_1_match_info_empty ON fileinfo_14_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_14_1_match_info ON fileinfo_14_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_14_1_tm ON fileinfo_14_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_14_1_record_id ON fileinfo_14_num_1 (record_id);
create index if not exists fi_14_1_sensitive_data ON fileinfo_14_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_14_1_compliance_data ON fileinfo_14_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_14_1_last_scan_tm ON fileinfo_14_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_14_1_reserve_json3 ON fileinfo_14_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_14_1_filehash_scanuuid ON fileinfo_14_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_14_1_file_metadata ON fileinfo_14_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_14_1_shared_data_internal ON fileinfo_14_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_14_1_shared_data_external ON fileinfo_14_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_14_1_shared_data_public ON fileinfo_14_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_14
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_14_path_index ON folder_info_14 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_14_tag_index ON folder_info_14 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_15_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_15_1_file_hash ON fileinfo_15_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_15_1_file_tlsh ON fileinfo_15_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_15_1_scan_uuid ON fileinfo_15_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_15_1_full_path ON fileinfo_15_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_15_1_file_name ON fileinfo_15_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_15_1_file_attributes ON fileinfo_15_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_15_1_storage_type ON fileinfo_15_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_15_1_main_class_id ON fileinfo_15_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_15_1_sub_class_id ON fileinfo_15_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_15_1_match_info_notempty ON fileinfo_15_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_15_1_match_info_empty ON fileinfo_15_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_15_1_match_info ON fileinfo_15_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_15_1_tm ON fileinfo_15_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_15_1_record_id ON fileinfo_15_num_1 (record_id);
create index if not exists fi_15_1_sensitive_data ON fileinfo_15_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_15_1_compliance_data ON fileinfo_15_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_15_1_last_scan_tm ON fileinfo_15_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_15_1_reserve_json3 ON fileinfo_15_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_15_1_filehash_scanuuid ON fileinfo_15_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_15_1_file_metadata ON fileinfo_15_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_15_1_shared_data_internal ON fileinfo_15_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_15_1_shared_data_external ON fileinfo_15_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_15_1_shared_data_public ON fileinfo_15_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_15
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_15_path_index ON folder_info_15 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_15_tag_index ON folder_info_15 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_16_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_16_1_file_hash ON fileinfo_16_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_16_1_file_tlsh ON fileinfo_16_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_16_1_scan_uuid ON fileinfo_16_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_16_1_full_path ON fileinfo_16_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_16_1_file_name ON fileinfo_16_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_16_1_file_attributes ON fileinfo_16_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_16_1_storage_type ON fileinfo_16_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_16_1_main_class_id ON fileinfo_16_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_16_1_sub_class_id ON fileinfo_16_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_16_1_match_info_notempty ON fileinfo_16_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_16_1_match_info_empty ON fileinfo_16_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_16_1_match_info ON fileinfo_16_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_16_1_tm ON fileinfo_16_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_16_1_record_id ON fileinfo_16_num_1 (record_id);
create index if not exists fi_16_1_sensitive_data ON fileinfo_16_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_16_1_compliance_data ON fileinfo_16_num_1 ((reserve_json1->>'compliance_data'));
CREATE INDEX IF NOT EXISTS fi_16_1_last_scan_tm ON fileinfo_16_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_16_1_reserve_json3 ON fileinfo_16_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_16_1_filehash_scanuuid ON fileinfo_16_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_16_1_file_metadata ON fileinfo_16_num_1 USING GIN (file_metadata);
CREATE INDEX IF NOT EXISTS fi_16_1_shared_data_internal ON fileinfo_16_num_1 (( (reserve_json1->'shared_data'->>'with_internal_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_16_1_shared_data_external ON fileinfo_16_num_1 (( (reserve_json1->'shared_data'->>'with_external_shareable_link')::boolean ));
CREATE INDEX IF NOT EXISTS fi_16_1_shared_data_public ON fileinfo_16_num_1 (( (reserve_json1->'shared_data'->>'with_public_shareable_link')::boolean ));

CREATE TABLE IF NOT EXISTS folder_info_16
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_16_path_index ON folder_info_16 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_16_tag_index ON folder_info_16 USING GIN (folder_tag);

-------------------------------------------------------------------------------

CREATE VIEW file_info_view AS
SELECT * FROM fileinfo_1_num_1
UNION ALL
SELECT * FROM fileinfo_2_num_1
UNION ALL
SELECT * FROM fileinfo_3_num_1
UNION ALL
SELECT * FROM fileinfo_4_num_1
UNION ALL
SELECT * FROM fileinfo_5_num_1
UNION ALL
SELECT * FROM fileinfo_6_num_1
UNION ALL
SELECT * FROM fileinfo_7_num_1
UNION ALL
SELECT * FROM fileinfo_8_num_1
UNION ALL
SELECT * FROM fileinfo_9_num_1
UNION ALL
SELECT * FROM fileinfo_10_num_1
UNION ALL
SELECT * FROM fileinfo_11_num_1
UNION ALL
SELECT * FROM fileinfo_12_num_1
UNION ALL
SELECT * FROM fileinfo_13_num_1
UNION ALL
SELECT * FROM fileinfo_14_num_1
UNION ALL
SELECT * FROM fileinfo_15_num_1
UNION ALL
SELECT * FROM fileinfo_16_num_1;

-------------------------------------------------------------------------------

CREATE VIEW folder_info_view AS
SELECT * FROM folder_info_1
UNION ALL
SELECT * FROM folder_info_2
UNION ALL
SELECT * FROM folder_info_3
UNION ALL
SELECT * FROM folder_info_4
UNION ALL
SELECT * FROM folder_info_5
UNION ALL
SELECT * FROM folder_info_6
UNION ALL
SELECT * FROM folder_info_7
UNION ALL
SELECT * FROM folder_info_8
UNION ALL
SELECT * FROM folder_info_9
UNION ALL
SELECT * FROM folder_info_10
UNION ALL
SELECT * FROM folder_info_11
UNION ALL
SELECT * FROM folder_info_12
UNION ALL
SELECT * FROM folder_info_13
UNION ALL
SELECT * FROM folder_info_14
UNION ALL
SELECT * FROM folder_info_15
UNION ALL
SELECT * FROM folder_info_16;

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_1000_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  storage_id UUID NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  file_tag jsonb,
  match_info_edm jsonb,
  match_info_idm jsonb,
  protection_framework jsonb,
  matched_classifier jsonb,
  sensitivity INT,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp,
  file_metadata jsonb NOT NULL
);

CREATE INDEX IF NOT EXISTS fi_1000_1_file_hash ON fileinfo_1000_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_1000_1_file_tlsh ON fileinfo_1000_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_1000_1_scan_uuid ON fileinfo_1000_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_1000_1_full_path ON fileinfo_1000_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_1000_1_file_name ON fileinfo_1000_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_1000_1_file_attributes ON fileinfo_1000_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_1000_1_storage_type ON fileinfo_1000_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_1000_1_main_class_id ON fileinfo_1000_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_1000_1_sub_class_id ON fileinfo_1000_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_notempty ON fileinfo_1000_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_empty ON fileinfo_1000_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_pii ON fileinfo_1000_num_1 USING GIN (match_info jsonb_path_ops) where match_info @> '[{"categoryid" : "10001"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_pci ON fileinfo_1000_num_1 USING GIN (match_info jsonb_path_ops) where match_info @> '[{"categoryid" : "10002"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_phi ON fileinfo_1000_num_1 USING GIN (match_info jsonb_path_ops) where match_info @> '[{"categoryid" : "10003"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_file_tag_conf ON fileinfo_1000_num_1 USING GIN (file_tag jsonb_path_ops) where  file_tag @> '[{"predefine": "Confidential"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_file_tag_highly_conf ON fileinfo_1000_num_1 USING GIN (file_tag jsonb_path_ops) where  file_tag @> '[{"predefine": "Highly Confidential (or Restricted)"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_tm ON fileinfo_1000_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_1000_1_record_id ON fileinfo_1000_num_1 (record_id);
CREATE INDEX IF NOT EXISTS fi_1000_1_last_scan_tm ON fileinfo_1000_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_1000_1_reserve_json3 ON fileinfo_1000_num_1 USING GIN (reserve_json3);
CREATE INDEX IF NOT EXISTS fi_1000_1_file_metadata ON fileinfo_1000_num_1 USING GIN (file_metadata);

CREATE TABLE IF NOT EXISTS folder_info_1000
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_1000_path_index ON folder_info_1000 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_1000_tag_index ON folder_info_1000 USING GIN (folder_tag);

-------------------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS group_data_1
 (
  group_cn VARCHAR(256) PRIMARY KEY NOT NULL,
  group_members jsonb,
  tm timestamp default now()::timestamp(3)
);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS group_data_2
 (
  group_cn VARCHAR(256) PRIMARY KEY NOT NULL,
  group_members jsonb,
  tm timestamp default now()::timestamp(3)
);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS user_data_1
 (
  user_email VARCHAR(256) PRIMARY KEY NOT NULL,
  sam_account VARCHAR(256) NOT NULL,
  user_dn VARCHAR(256) NOT NULL,
  user_groups jsonb,
  tm timestamp default now()::timestamp(3)
);

CREATE INDEX IF NOT EXISTS ud_1_sam_account ON user_data_1 (sam_account);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS user_data_2
 (
  user_email VARCHAR(256) PRIMARY KEY NOT NULL,
  sam_account VARCHAR(256) NOT NULL,
  user_dn VARCHAR(256) NOT NULL,
  user_groups jsonb,
  tm timestamp default now()::timestamp(3)
);

CREATE INDEX IF NOT EXISTS ud_2_sam_account ON user_data_2 (sam_account);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS index_data
 (
  index_info VARCHAR(256) PRIMARY KEY NOT NULL,
  front_index integer NOT NULL,
  back_index integer NOT NULL,
  tm timestamp default now()::timestamp(3)
);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS tags_info_predefine (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(120) NOT NULL,
  description VARCHAR(512),
  category VARCHAR(256),
  status smallint NOT NULL,
  ext_attr jsonb NOT NULL DEFAULT '{}'
);

-------------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS tags_info_custom (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(120) NOT NULL,
  description VARCHAR(512),
  category VARCHAR(256),
  status smallint NOT NULL,
  created_at timestamp default now()::timestamp(3)
);

-------------------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS tags_info_ml (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(120) NOT NULL,
  description VARCHAR(512),
  category VARCHAR(256),
  status smallint NOT NULL
);
-------------------------------------------------------------------------------
CREATE VIEW tag_info_view AS
SELECT id, name, description, category, status, NULL as created_at FROM tags_info_predefine
UNION ALL
SELECT id, name, description, category, status, created_at FROM tags_info_custom
UNION ALL
SELECT id, name, description, category, status, NULL as created_at FROM tags_info_ml;
-------------------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS huey_task_backlog
(
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    scan_policy_id uuid NOT NULL,
    session_key uuid NOT NULL,
    file_info jsonb NOT NULL,
    backlog_hash character(40) COLLATE pg_catalog."default" NOT NULL,
    params bytea NOT NULL,
    dispatch_count integer NOT NULL,
    created_at timestamp with time zone NOT NULL,
    CONSTRAINT huey_task_backlog_pkey PRIMARY KEY (id),
    CONSTRAINT huey_task_backlog_idx1 UNIQUE (scan_policy_id, session_key, backlog_hash)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS log_setting
(
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    config jsonb not null,
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS scan_progress
(
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    scan_policy_id uuid NOT NULL,
    session_key uuid NOT NULL,
    total_count integer NOT NULL,
    current_count integer NOT NULL,
    ignore_count integer NOT NULL,
    dispatch_count integer NOT NULL,
    current_group_counter integer NOT NULL,
    group_total  integer NOT NULL,
    scan_total_count integer NOT NULL,
    scan_current_count integer NOT NULL,
    scan_ignore_count integer NOT NULL,
    scan_method integer NOT NULL,
    created_at timestamp with time zone NOT NULL,
    CONSTRAINT scan_progress_pkey PRIMARY KEY (id),
    CONSTRAINT scan_progress_idx1 UNIQUE (scan_policy_id, session_key)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS huey_resume_backlog
(
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    scan_policy_id uuid NOT NULL,
    session_key uuid NOT NULL,
    file_info jsonb NOT NULL,
    backlog_hash character(40) COLLATE pg_catalog."default" NOT NULL,
    params bytea NOT NULL,
    dispatch_count integer NOT NULL,
    created_at timestamp with time zone NOT NULL,
    CONSTRAINT huey_resume_backlog_pkey PRIMARY KEY (id),
    CONSTRAINT huey_resume_backlog_idx1 UNIQUE (scan_policy_id, session_key, backlog_hash)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS config_file_backlog
(
    id uuid NOT NULL DEFAULT uuid_generate_v4(),
    scan_policy_id uuid NOT NULL,
    full_path VARCHAR(4096) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    CONSTRAINT config_file_backlog_pkey PRIMARY KEY (id),
    CONSTRAINT config_file_backlog_idx1 UNIQUE (scan_policy_id, full_path)
);
----------------------------------------------------------------------
create table if not exists FdsProxyServer (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);
----------------------------------------------------------------------
create table if not exists fds_auto_upgrade_locker (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);
----------------------------------------------------------------------
create table if not exists FdsAutoUpdateCount (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS log_server (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name varchar(128) NOT NULL,
    description varchar(256) NOT NULL DEFAULT '',
    status boolean NOT NULL default True,
    type varchar(32) NOT NULL default '',
    conn_status varchar(32) NOT NULL default '',
    attributes jsonb NOT NULL,
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
    );

CREATE UNIQUE INDEX  IF NOT EXISTS idx_log_server_name ON log_server(name);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS source_file (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(1024) NOT NULL,
  original_file_path VARCHAR(1024),
  file_type VARCHAR(100),
  template_type VARCHAR(100),
  data_info jsonb,
  file_fingerprint VARCHAR(256)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS edm_template (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(512),
  source_file_id UUID NOT NULL,
  data_field jsonb NOT NULL,
  attributes_ext jsonb NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS edm_rule (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(512),
  edm_template_id UUID NOT NULL,
  match_criteria jsonb NOT NULL,
  status boolean NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS idm_template (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(512),
  source_file_id UUID NOT NULL,
  attributes_ext jsonb NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS edm_hash_data (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  source_file_id UUID NOT NULL,
  record jsonb NOT NULL
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS schema_version (
    id VARCHAR(50) PRIMARY KEY DEFAULT 'current_version',
    version VARCHAR(50) NOT NULL,
    description VARCHAR(256),
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_total_scanned_files AS
SELECT row_to_json(result_set) AS result
FROM (
    SELECT
        COUNT(*) AS alltimecount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '1 day' THEN 1 ELSE 0 END) AS onedaycount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '7 days' THEN 1 ELSE 0 END) AS oneweekcount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '14 days' THEN 1 ELSE 0 END) AS twoweekcount
    FROM file_info_view
    WHERE last_scan_tm is not NULL
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_sensitive_file_owners AS
SELECT row_to_json(result_set) AS result
FROM (
WITH filtered AS (
  SELECT file_metadata
  FROM file_info_view
  WHERE reserve_json1->>'sensitive_data' = 'true'
)
SELECT COUNT(DISTINCT owner) as sensitivefileownerscount
FROM filtered,
LATERAL jsonb_array_elements_text(file_metadata->'owners') AS owner(owner))result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_files_to_scan AS
WITH files_to_scan_data AS (
    SELECT jsonb_agg(fscandata) AS filestoscandata  -- Use JSONB for compatibility
    FROM (
        SELECT
            storage_type,
            COUNT(*) AS storagecount,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) AS percentage
        FROM file_info_view
	WHERE last_scan_tm is not NULL
        GROUP BY storage_type
    ) fscandata
),
storage_sum AS (
    SELECT SUM((fscandata->>'storagecount')::INTEGER) AS total_storage_count
    FROM files_to_scan_data, jsonb_array_elements(filestoscandata) AS fscandata
)
SELECT json_build_object(
        'total_files', COALESCE(storage_sum.total_storage_count, 0),  -- Handle NULL case
        'total_percentage', CASE
            WHEN COALESCE(storage_sum.total_storage_count, 0) = 0 THEN 0
            ELSE 100.0
         END,
        'files_to_scan', files_to_scan_data.filestoscandata
) AS result
FROM files_to_scan_data, storage_sum;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_24hours AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('hour', NOW() - INTERVAL '24 Hours'),
            date_trunc('hour', NOW()),
            INTERVAL '1 hour'
        ) AS hour
    ),
    file_counts AS (
        SELECT
            date_trunc('hour', last_scan_tm) AS hour,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm is not NULL AND last_scan_tm >= NOW() - INTERVAL '24 Hours'
        GROUP BY date_trunc('hour', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.hour)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.hour = fc.hour
    ORDER BY ds.hour
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_7days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '7 days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm is not NULL AND last_scan_tm >= NOW() - INTERVAL '7 days'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_14days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '14 days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm is not NULL AND last_scan_tm >= NOW() - INTERVAL '14 days'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_sensitive_24hours AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('hour', NOW() - INTERVAL '24 hours'),
            date_trunc('hour', NOW()),
            INTERVAL '1 hour'
        ) AS hour
    ),
    file_counts AS (
        SELECT
            date_trunc('hour', last_scan_tm) AS hour,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '24 hours' AND reserve_json1->>'sensitive_data'='true'
        GROUP BY date_trunc('hour', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.hour)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.hour = fc.hour
    ORDER BY ds.hour
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_sensitive_7days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '7 Days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '7 Days' AND reserve_json1->>'sensitive_data'='true'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_sensitive_14days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '14 Days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '14 Days' AND reserve_json1->>'sensitive_data'='true'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_sensitive_files_labeled AS
SELECT row_to_json(result_set) AS result
FROM (
    SELECT
        COUNT(*) AS alltimecount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '1 day' THEN 1 ELSE 0 END) AS onedaycount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '7 days' THEN 1 ELSE 0 END) AS oneweekcount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '14 days' THEN 1 ELSE 0 END) AS twoweekcount
    FROM file_info_view
    WHERE reserve_json1->>'sensitive_data' = 'true'
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_sensitive_files_distribution AS
WITH storage_data AS
    (SELECT storage_type, count(*) as storagecount, round(count(*) * 100.0 / SUM(count(*)) OVER(),1) AS percentage
     FROM file_info_view where reserve_json1->>'sensitive_data'='true'
     group by storage_type),
total_count AS (
        SELECT COUNT(*) AS totalcount FROM file_info_view
    )
SELECT json_build_object(
       'total_sensitive_count', SUM(storage_data.storagecount),
        'total_percentage', CASE
                WHEN COALESCE(SUM(storage_data.storagecount), 0) = 0 THEN 0
                ELSE 100.0
                END,
    'storage_counts', json_agg(row_to_json(storage_data))
) AS result
FROM storage_data;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_compliance_files AS
WITH all_compliance AS (
    SELECT
        id::text AS file_id,
        jsonb_array_elements_text(reserve_json1->'protection_framework') AS compliance
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
compliance_counts AS (
    SELECT
        compliance,
        COUNT(DISTINCT file_id) AS count
    FROM all_compliance
    GROUP BY compliance
),
compliance_category_count AS (
    SELECT COUNT(*) AS category_count FROM compliance_counts
),
total_count AS (
    SELECT COUNT(DISTINCT id) AS total_count
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
limited_compliance AS (
    SELECT compliance, count
    FROM compliance_counts
    ORDER BY count DESC
    LIMIT 5
),
limited_with_percentage AS (
    SELECT
        compliance,
        count,
        ROUND((count::numeric / NULLIF((SELECT total_count FROM total_count), 0)) * 100, 2) AS percentage
    FROM limited_compliance
),
compliance_files AS (
    SELECT * FROM limited_with_percentage
),
total_compliance AS (
    SELECT
        (SELECT total_count FROM total_count) AS count,
        CASE WHEN (SELECT total_count FROM total_count) = 0 THEN 0 ELSE 100.0 END AS percentage
),
total_scanned AS (
    SELECT
        COUNT(DISTINCT id) AS count,
        CASE
            WHEN COUNT(DISTINCT id) = 0 THEN 0
            ELSE ROUND(
                (SELECT total_count FROM total_count)::numeric
                / NULLIF(COUNT(DISTINCT id)::numeric, 0) * 100,
                2
            )
        END AS percentage
    FROM file_info_view
)
SELECT json_build_object(
    'compliance_data', json_agg(row_to_json(compliance_files)),
    'total_compliance', (SELECT row_to_json(total_compliance) FROM total_compliance),
    'total_scanned', (SELECT row_to_json(total_scanned) FROM total_scanned)
) AS result
FROM compliance_files;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_compliance_files_show_more AS
WITH all_compliance AS (
    SELECT
        id::text AS file_id,
        jsonb_array_elements_text(reserve_json1->'protection_framework') AS compliance
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
compliance_counts AS (
    SELECT
        compliance,
        COUNT(DISTINCT file_id) AS count
    FROM all_compliance
    GROUP BY compliance
),
compliance_category_count AS (
    SELECT COUNT(*) AS category_count FROM compliance_counts
),
total_count AS (
    SELECT COUNT(DISTINCT id) AS total_count
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
limited_compliance AS (
    SELECT compliance, count
    FROM compliance_counts
    ORDER BY count DESC
    LIMIT 15
),
limited_with_percentage AS (
    SELECT
        compliance,
        count,
        ROUND((count::numeric / NULLIF((SELECT total_count FROM total_count), 0)) * 100, 2) AS percentage
    FROM limited_compliance
),
compliance_files AS (
    SELECT * FROM limited_with_percentage
),
total_compliance AS (
    SELECT
        (SELECT total_count FROM total_count) AS count,
        CASE WHEN (SELECT total_count FROM total_count) = 0 THEN 0 ELSE 100.0 END AS percentage
),
total_scanned AS (
    SELECT
        COUNT(DISTINCT id) AS count,
        CASE
            WHEN COUNT(DISTINCT id) = 0 THEN 0
            ELSE ROUND(
                (SELECT total_count FROM total_count)::numeric
                / NULLIF(COUNT(DISTINCT id)::numeric, 0) * 100,
                2
            )
        END AS percentage
    FROM file_info_view
)
SELECT json_build_object(
    'compliance_data', json_agg(row_to_json(compliance_files)),
    'total_compliance', (SELECT row_to_json(total_compliance) FROM total_compliance),
    'total_scanned', (SELECT row_to_json(total_scanned) FROM total_scanned)
) AS result
FROM compliance_files;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_ai_categories AS
WITH total_count AS (
    SELECT COUNT(*) AS totalcount FROM file_info_view
),
ai_info AS (
    SELECT
        sub_class_id,
        COUNT(*) AS categorycount,
        ROUND(COUNT(*) * 100.0 / NULLIF((SELECT totalcount FROM total_count), 0), 2) AS percentage
    FROM file_info_view
    WHERE sub_class_id IS NOT NULL
    GROUP BY sub_class_id
    ORDER BY percentage DESC
    LIMIT 10
)
SELECT json_build_object(
    'total_scanned_files', (SELECT totalcount FROM total_count),
    'total_ai_count', SUM(ai_info.categorycount),
    'total_percentage', ROUND(SUM(ai_info.categorycount) * 100.0 / NULLIF((SELECT totalcount FROM total_count), 0), 2),
    'ai_counts', json_agg(row_to_json(ai_info))
) AS result
FROM ai_info;
----------------------------------------------------------------------
-- Functions to create the Analysis Dashboard MVs dynamically
-- Top n scans widget
DROP FUNCTION IF EXISTS create_adb_top_sp_mv(text, text, text, text);
CREATE OR REPLACE FUNCTION create_adb_top_sp_mv(
    time_filter TEXT,    -- Example: '24 hours, 7 days, 14 days'
    time_trunc TEXT,     -- Example: 'hour', 'day'
    limit_param TEXT,     -- Number of top scan policies (Example: 5, 10)
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'AND pol_details->>''sid'' = ''' || scan_uuid || '''';
    ELSE
        in_clause := ''; -- No filter
    END IF;

    -- Dynamically generate the materialized view name
    mv_name := format('adb_top_sp_%s_%s_%s', lower(limit_param), lower(replace(time_filter, ' ', '_')), lower(scan_uuid));

    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
        WITH now_trunc AS (
            SELECT date_trunc(%L, NOW()) AS time_unit
        ),
        time_series AS (
            SELECT generate_series(
                (SELECT time_unit FROM now_trunc) - INTERVAL %L,
                (SELECT time_unit FROM now_trunc),
                INTERVAL ''1 %s''
            ) AS time_point
        ),
        all_scans AS (
        SELECT
            date_trunc(%L, utime) AS time_point,
            ds_incidents.pol_details->>''pname'' AS discover_policy,
            COUNT(*) AS count
        FROM ds_incidents
        WHERE utime > NOW() - INTERVAL %L %s
        GROUP BY 1, 2
        ),
        total_scans AS (
            SELECT SUM(count) AS total_count FROM all_scans
        ),
        top_scans AS (
            SELECT ds_incidents.pol_details->>''pname'' as discover_policy, COUNT(*) AS count
            FROM ds_incidents
            WHERE utime > NOW() - INTERVAL %L %s
            GROUP BY discover_policy
            ORDER BY COUNT(*) DESC
            LIMIT %s
        ),
        top_scans_total AS (
            SELECT SUM(count) AS total_count,
            ROUND(SUM(count) * 100.0 / NULLIF((SELECT total_count FROM total_scans), 0), 2) AS total_percentage
            FROM top_scans
        ),
        discover_policy_count AS (
            SELECT COUNT(DISTINCT discover_policy) AS policy_count FROM all_scans
        ),
        others_scans_aggregated AS (
            SELECT
                time_point,
                ''Others'' AS discover_policy,
                SUM(count) AS count
            FROM all_scans
            WHERE discover_policy NOT IN (SELECT discover_policy FROM top_scans)
            GROUP BY time_point
        ),
        summary_result AS (
        SELECT json_agg(row_to_json(summary_data)) AS summary
            FROM (
        -- Top Scans
        SELECT
            discover_policy,
            count,
            ROUND(count * 100.0 / NULLIF((SELECT total_count FROM total_scans), 0), 2) AS percentage
        FROM top_scans
        UNION ALL
        -- Other Scans
        SELECT
            ''Others'' AS discover_policy,
            COALESCE(SUM(count), 0) AS count,
            ROUND(COALESCE(SUM(count), 0) * 100.0 / NULLIF((SELECT total_count FROM total_scans), 0), 2) AS percentage
        FROM all_scans
        WHERE discover_policy NOT IN (SELECT discover_policy FROM top_scans)
        ) summary_data
        ),
        time_trend AS (
            SELECT json_agg(row_to_json(time_data)) AS time_trend
            FROM (
                SELECT
                EXTRACT(EPOCH FROM pd.time_point)::bigint AS time_unit,
                pd.discover_policy,
                COALESCE(alls.count, 0) AS count,
                CASE
                    WHEN t.total_count > 0 THEN ROUND(COALESCE(alls.count, 0) * 100.0 / t.total_count, 2)
                    ELSE 0
                END AS percentage
                FROM (
                    SELECT time_point, discover_policy
                    FROM time_series
                    CROSS JOIN (
                        SELECT discover_policy FROM top_scans
                        UNION
                        SELECT ''Others''
                    ) ts
                ) pd
                LEFT JOIN (
                SELECT * FROM all_scans WHERE discover_policy IN (SELECT discover_policy FROM top_scans)
                UNION ALL
                SELECT * FROM others_scans_aggregated
                ) alls
                ON pd.time_point = alls.time_point AND pd.discover_policy = alls.discover_policy
                JOIN total_scans t ON true
                ORDER BY pd.time_point, pd.discover_policy
            ) time_data
        )
        SELECT json_build_object(
            ''summary'', summary_result.summary,
            ''time_trend'', time_trend.time_trend,
            ''discovery_policy_count'', (SELECT policy_count FROM discover_policy_count),
            ''topn_count'', (
                SELECT json_build_object(
                ''count'', tst.total_count,
                ''percentage'', tst.total_percentage
            )
            FROM top_scans_total tst
            ),
            ''total_count'', (
            SELECT json_build_object(
                ''count'', total_scans.total_count,
                ''percentage'', 100.0
            )
            FROM total_scans
            )
        ) AS result
        FROM summary_result, time_trend;',
        mv_name, time_trunc, time_filter, time_trunc, -- Time truncation
        time_trunc, time_filter, in_clause,  -- Filters
        time_filter, in_clause, limit_param  -- Top N limit
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Sensitive files by file types widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_sf_all AS

        WITH total_count AS (
            SELECT COUNT(*) AS totalcount FROM file_info_view
        ),
        sensitive_files AS (
            SELECT file_attributes->>'file_ext' AS filetype, COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>'sensitive_data'='true'
            GROUP BY filetype
            ORDER by count DESC
            LIMIT 5
        ),
        total_sensitive_files AS (
            SELECT SUM(count) AS total_sensitive FROM sensitive_files
        )
        SELECT json_build_object(
            'file_type_summary', (SELECT json_agg(row_to_json(result_set)) FROM (
                SELECT COALESCE(sf.filetype, 'Unknown') AS filetype, sf.count,
                ROUND(sf.count * 100.0 / tc.totalcount, 2) AS percentage
                FROM total_count tc, sensitive_files sf WHERE tc.totalcount != 0
            ) result_set),
            'total_sensitive_files', (SELECT total_sensitive FROM total_sensitive_files),
            'total_sensitive_percentage', (SELECT ROUND(total_sensitive * 100.0 / total_count.totalcount, 2)
            FROM total_sensitive_files, total_count),
            'total_scanned_files', (SELECT totalcount FROM total_count)
        ) AS result;
----------------------------------------------------------------------
-- Sensitive files by file types show more widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_sfsm_all AS

        WITH total_count AS (
            SELECT count(*) AS totalcount
            FROM file_info_view
        ),

        -- Count sensitive files by category
        sensitive_files AS (
            SELECT
                file_attributes->>'file_cat' AS filetype,
                COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>'sensitive_data'='true'
            GROUP BY filetype
        ),

        -- Count sensitive files by category and extension
        sensitive_files_ext AS (
            SELECT
                file_attributes->>'file_cat' AS filetype,
                file_attributes->>'file_ext' AS fileext,
                COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>'sensitive_data'='true'
            GROUP BY filetype, fileext
        ),

        -- Total count of sensitive files
        total_sensitive_files AS (
            SELECT SUM(count) AS total_sensitive
            FROM sensitive_files
        ),

        -- Percentage summary for file types
        file_type_summary AS (
            SELECT
                COALESCE(sf.filetype, 'Unknown') AS filetype,
                sf.count,
                ROUND(sf.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM total_count tc
            CROSS JOIN sensitive_files sf
            WHERE tc.totalcount != 0
        ),

        -- Percentage summary for file types with extensions
        file_type_ext_details AS (
            SELECT
                COALESCE(sfe.filetype, 'Unknown') AS filetype,
                COALESCE(sfe.fileext, 'Unknown') AS fileext,
                sfe.count,
                ROUND(sfe.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM total_count tc
            CROSS JOIN sensitive_files_ext sfe
            WHERE tc.totalcount != 0
        )

        -- Final JSON Output
        SELECT json_build_object(
            'file_type_summary', (SELECT json_agg(row_to_json(file_type_summary)) FROM file_type_summary),
            'file_type_ext_details', (SELECT json_agg(row_to_json(file_type_ext_details)) FROM file_type_ext_details),
            'total_scanned_files', (SELECT totalcount FROM total_count),
            'total_sensitive_files', (SELECT total_sensitive FROM total_sensitive_files),
            'total_sensitive_percentage', (
                SELECT ROUND(total_sensitive * 100.0 / NULLIF(total_count.totalcount, 0), 2)
                FROM total_sensitive_files, total_count
            )
        ) AS result;
----------------------------------------------------------------------
-- Severity widget
DROP FUNCTION IF EXISTS create_adb_sev_mv(text, text, text, text);
CREATE OR REPLACE FUNCTION create_adb_sev_mv(
    risk TEXT,                -- Example: '1', '2', '3', '4'
    time_filter TEXT,         -- Example: '14 days'
    time_trunc TEXT,          -- Example: 'day', 'hour', 'week'
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'AND pol_details->>''sid'' = ''' || scan_uuid || '''';
    ELSE
        in_clause := ''; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_sev_%s_%s_%s', lower(risk), lower(replace(time_filter, ' ', '_')), lower(scan_uuid));


    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
	  WITH time_series AS (
            SELECT generate_series(
                date_trunc(%L, NOW() - INTERVAL %L),
                date_trunc(%L, NOW()),
                (CASE
                    WHEN %L  = ''hour'' THEN INTERVAL ''1 hour''
                    WHEN %L  = ''day'' THEN INTERVAL ''1 day''
                    WHEN %L  = ''week'' THEN INTERVAL ''1 week''
                    ELSE INTERVAL ''1 day''
                END)
            ) AS time_unit
        ),

        -- Create base data with 0 counts for missing intervals
        base_data AS (
            SELECT
                floor(extract(epoch from ts.time_unit)) AS time_unit,
                 %L AS risk,
                0 AS count
            FROM time_series ts
        ),

        -- Actual counts from ds_incidents
        actual_data AS (
            SELECT
                floor(extract(epoch from date_trunc(%L, utime))) AS time_unit,
                ds_incidents.attributes->>''risk'' AS risk,
                count(*) AS count
            FROM ds_incidents
            WHERE ds_incidents.attributes->>''risk'' = %L
                AND utime >= NOW() - INTERVAL %L
                %s
            GROUP BY time_unit, risk
        )

        -- Final JSON output
        SELECT json_agg(row_to_json(result_set)) AS result
        FROM (
            SELECT bd.time_unit, bd.risk, COALESCE(ad.count, bd.count) AS count
            FROM base_data bd
            LEFT JOIN actual_data ad
                ON bd.time_unit = ad.time_unit
                AND bd.risk = ad.risk
            ORDER BY bd.time_unit, bd.risk
        ) result_set;',
        mv_name, time_trunc, time_filter, time_trunc, -- time-series parameters
        time_trunc, time_trunc, time_trunc,  -- interval selection
        risk, time_trunc, risk, time_filter, in_clause -- risk and conditions
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Compliance files widget
DROP FUNCTION IF EXISTS create_adb_comp_mv(text, text, text);
CREATE OR REPLACE FUNCTION create_adb_comp_mv(
    time_filter TEXT,         -- Example: '24 hours, 7 days, 14 days'
    time_trunc TEXT,          -- Example: 'day, hour'
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'AND pol_details->>''sid'' = ''' || scan_uuid || '''';
    ELSE
        in_clause := ''; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_comp_%s_%s', lower(replace(time_filter, ' ', '_')), lower(scan_uuid));
    EXECUTE format('DROP MATERIALIZED VIEW IF EXISTS %I', mv_name);
    EXECUTE format(

        'CREATE MATERIALIZED VIEW %I AS
        WITH compliance_counts AS (
        -- Get top 5 compliance types within the time window
        SELECT
            compliance,
            COUNT(*) AS count
        FROM (
            SELECT
                jsonb_array_elements_text(f.reserve_json1->''protection_framework'') AS compliance
                FROM file_info_view f
                WHERE f.last_scan_tm > (NOW() - INTERVAL %L) %s
                AND f.reserve_json1->>''compliance_data'' = ''true''
        ) sub
        GROUP BY compliance
        ORDER BY count DESC
        LIMIT 5
        ),
        time_series AS (
        SELECT generate_series(
            date_trunc(%L, NOW() - INTERVAL %L),
            date_trunc(%L, NOW()),
            INTERVAL ''1 %s''
            ) AS time_unit
        ),
        compliance_time_series AS (
            SELECT
            floor(EXTRACT(EPOCH FROM ts.time_unit)) AS time_unit,
            cc.compliance,
            COUNT(f.id) AS count
            FROM
            time_series ts
            CROSS JOIN compliance_counts cc
            LEFT JOIN file_info_view f
            ON date_trunc(%L, f.last_scan_tm) = ts.time_unit
            AND f.last_scan_tm > (NOW() - INTERVAL %L)
            AND f.reserve_json1->>''compliance_data'' = ''true''
            LEFT JOIN LATERAL jsonb_array_elements_text(f.reserve_json1->''protection_framework'') jf(compliance_val)
            ON TRUE
            AND jf.compliance_val = cc.compliance
            GROUP BY ts.time_unit, cc.compliance
            ORDER BY ts.time_unit, cc.compliance
        )
        SELECT json_agg(
            json_build_object(
                ''time_unit'', time_unit,
                ''compliance'', compliance,
                ''count'', count
            )
        ) AS result
        FROM compliance_time_series;',
        mv_name, time_filter, in_clause, time_trunc, time_filter, time_trunc, time_trunc, -- Time-series
        time_trunc, time_filter -- Filtering conditions
);
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- AI file categories widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_ai_all AS
        WITH time_filtered_data AS (
            SELECT main_class_id, sub_class_id, scan_uuid
            FROM file_info_view
        ),
        total_count AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count AS (
            SELECT
                main_class_id,
                COUNT(*) AS count
            FROM time_filtered_data
            WHERE main_class_id IS NOT NULL
            GROUP BY main_class_id
        ),
        total_classified_files AS (
            SELECT SUM(count) AS total_classified
            FROM class_count
        )
        SELECT json_build_object(
            'class_summary', (
                SELECT json_agg(row_to_json(result_set))
                FROM (
                    SELECT
                        cc.main_class_id,
                        cc.count,
                        ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
                    FROM class_count cc
                    CROSS JOIN total_count tc
                    WHERE tc.totalcount != 0
                ) result_set
            ),
            'total_scanned_files', (SELECT totalcount FROM total_count),
            'total_classified_files', (SELECT total_classified FROM total_classified_files),
            'total_classified_percentage', (
                SELECT ROUND(total_classified * 100.0 / NULLIF(total_count.totalcount, 0), 2)
                FROM total_classified_files, total_count
            )
        ) AS result;
----------------------------------------------------------------------
-- AI file categories show more widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_aism_all AS
        WITH time_filtered_data AS (
            SELECT main_class_id, sub_class_id, scan_uuid
            FROM file_info_view
        ),
        total_count_main AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count_main AS (
            SELECT main_class_id, COUNT(*) AS count
            FROM time_filtered_data
            WHERE main_class_id IS NOT NULL
            GROUP BY main_class_id
        ),
        main_class_summary AS (
            SELECT
                cc.main_class_id,
                cc.count,
                ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM class_count_main cc
            CROSS JOIN total_count_main tc
            WHERE tc.totalcount != 0
        ),
        total_count_sub AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count_sub AS (
            SELECT sub_class_id, COUNT(*) AS count
            FROM time_filtered_data
            WHERE sub_class_id IS NOT NULL
            GROUP BY sub_class_id
        ),
        sub_class_summary AS (
            SELECT
                cc.sub_class_id,
                cc.count,
                ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM class_count_sub cc
            CROSS JOIN total_count_sub tc
            WHERE tc.totalcount != 0
        ),
        total_classified_files AS (
            SELECT SUM(count) AS total_classified
            FROM class_count_main
        )

        SELECT json_build_object(
            'main_class_summary', (SELECT json_agg(row_to_json(main_class_summary)) FROM main_class_summary),
            'sub_class_summary', (SELECT json_agg(row_to_json(sub_class_summary)) FROM sub_class_summary),
            'total_scanned_files', (SELECT totalcount FROM total_count_main),
            'total_classified_files', (SELECT total_classified FROM total_classified_files),
            'total_classified_percentage', (
                SELECT ROUND(total_classified * 100.0 / NULLIF(total_count_main.totalcount, 0), 2)
                FROM total_classified_files, total_count_main
            )
        ) AS result;
----------------------------------------------------------------------
-- Sensitive file owners widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_sensitive_file_owners AS
WITH owner_files AS (
    SELECT
        f.storage_type,
        jsonb_array_elements_text(f.file_metadata->'owners') AS owner_id
    FROM file_info_view f
    WHERE f.reserve_json1->>'sensitive_data' = 'true' AND f.storage_type IN (2, 3, 5)
),
total_count AS (
    SELECT COUNT(*) AS totalcount FROM owner_files
),
owner_counts AS (
    SELECT
        owner_id,
        COUNT(*) AS total_files
    FROM owner_files
    GROUP BY owner_id
),
owner_top_storage AS (
    SELECT DISTINCT ON (owner_id)
        owner_id,
        storage_type
    FROM owner_files
    GROUP BY owner_id, storage_type
    ORDER BY owner_id, COUNT(*) DESC
),
final_data AS (
    SELECT
        oc.owner_id,
        sp.scan_init_info->>'storage_id' AS storage_id,
        oc.total_files
    FROM owner_counts oc
    LEFT JOIN owner_top_storage ots ON oc.owner_id = ots.owner_id
    LEFT JOIN scan_policy sp ON sp.storage_type = ots.storage_type
    ORDER BY oc.total_files DESC
    LIMIT 10
)
SELECT json_build_object(
    'owner_summary', (
        SELECT json_agg(row_to_json(fd))
        FROM final_data fd
    )
) AS result;
----------------------------------------------------------------------
-- Sensitive file owners show more widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_sensitive_file_owners_show_more AS
WITH owner_files AS (
    SELECT
        f.storage_type,
        jsonb_array_elements_text(f.file_metadata->'owners') AS owner_id
    FROM file_info_view f
    WHERE f.reserve_json1->>'sensitive_data' = 'true' AND f.storage_type IN (2, 3, 5)
),
total_count AS (
    SELECT COUNT(*) AS totalcount FROM owner_files
),
owner_counts AS (
    SELECT
        owner_id,
        COUNT(*) AS total_files
    FROM owner_files
    GROUP BY owner_id
),
owner_top_storage AS (
    SELECT DISTINCT ON (owner_id)
        owner_id,
        storage_type
    FROM owner_files
    GROUP BY owner_id, storage_type
    ORDER BY owner_id, COUNT(*) DESC
),
final_data AS (
    SELECT
        oc.owner_id,
        sp.scan_init_info->>'storage_id' AS storage_id,
        oc.total_files
    FROM owner_counts oc
    LEFT JOIN owner_top_storage ots ON oc.owner_id = ots.owner_id
    LEFT JOIN scan_policy sp ON sp.storage_type = ots.storage_type
    ORDER BY oc.total_files DESC
    LIMIT 100
)
SELECT json_build_object(
    'owner_summary', (
        SELECT json_agg(row_to_json(fd))
        FROM final_data fd
    )
) AS result;
----------------------------------------------------------------------
-- Dormant sensitive files widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_dormant_sensitive_files AS
WITH dormant_files AS (
    SELECT
        f.id,
        f.file_name,
        f.file_metadata->'owners' AS owner,
        f.storage_type,
        sp.scan_init_info->>'storage_id' AS storage_id,
        ROUND(EXTRACT(EPOCH FROM NULLIF(f.file_attributes->>'last_access_time', 'N/A')::timestamp)) AS last_accessed_time,
        ROUND(EXTRACT(EPOCH FROM (NOW() - (f.file_attributes->>'last_access_time')::timestamp)) / 86400) AS days_idle
    FROM file_info_view f
    LEFT JOIN scan_policy sp
        ON sp.storage_type = f.storage_type
    WHERE f.tag_tm IS NOT NULL AND f.file_attributes->>'last_access_time' != 'N/A'
      AND f.reserve_json1->>'sensitive_data' = 'true'
),
total_dormant AS (
    SELECT COUNT(*) AS total_files FROM dormant_files
),
top_dormant AS (
    SELECT *
    FROM dormant_files
    ORDER BY last_accessed_time ASC
    LIMIT 10
)
SELECT json_build_object(
    'dormant_files', (
        SELECT json_agg(row_to_json(df))
        FROM top_dormant df
    )
) AS result;
----------------------------------------------------------------------
-- Dormant sensitive files show more widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_dormant_sensitive_files_show_more AS
WITH dormant_files AS (
    SELECT
        f.id,
        f.file_name,
        f.file_metadata->'owners' AS owner,
        f.storage_type,
        sp.scan_init_info->>'storage_id' AS storage_id,
        ROUND(EXTRACT(EPOCH FROM NULLIF(f.file_attributes->>'last_access_time', 'N/A')::timestamp)) AS last_accessed_time,
        ROUND(EXTRACT(EPOCH FROM (NOW() - (f.file_attributes->>'last_access_time')::timestamp)) / 86400) AS days_idle
    FROM file_info_view f
    LEFT JOIN scan_policy sp
        ON sp.storage_type = f.storage_type
    WHERE f.tag_tm IS NOT NULL AND f.file_attributes->>'last_access_time' != 'N/A'
      AND f.reserve_json1->>'sensitive_data' = 'true'
),
total_dormant AS (
    SELECT COUNT(*) AS total_files FROM dormant_files
),
top_dormant AS (
    SELECT *
    FROM dormant_files
    ORDER BY last_accessed_time ASC
    LIMIT 100
)
SELECT json_build_object(
    'dormant_files', (
        SELECT json_agg(row_to_json(df))
        FROM top_dormant df
    )
) AS result;
----------------------------------------------------------------------
-- Top 10 shared sensitive file owners widget
DROP FUNCTION IF EXISTS create_adb_top_shared_owners_mv(text);
CREATE OR REPLACE FUNCTION create_adb_top_shared_owners_mv(
    share_type TEXT DEFAULT 'all'  -- allowed: 'internal', 'external', 'public', or 'all'
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    where_clause TEXT;
BEGIN
    -- Determine filter based on share_type
    IF share_type = 'internal' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean = true';
    ELSIF share_type = 'external' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean = true';
    ELSIF share_type = 'public' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean = true';
    ELSE
        where_clause := '((reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean = true OR (reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean = true OR (reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean = true)';  -- all shared=public+internal+external
    END IF;

    -- Materialized view name based on share_type
    mv_name := format('adb_top_shared_owners_%s', lower(share_type));

    -- Create or refresh the materialized view
    EXECUTE format(
    'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
    WITH owners AS (
    SELECT
        f.storage_type,
        jsonb_array_elements_text(f.file_metadata->''owners'') AS owner_id
    FROM file_info_view f
    WHERE f.storage_type IN (2, 3, 5) AND %s
    ),
    owner_counts AS (
    SELECT
        o.owner_id,
        o.storage_type,
        COUNT(*) AS total_files
    FROM owners o
    GROUP BY o.owner_id, o.storage_type
    ),
    owner_with_storage_id AS (
    SELECT
        oc.owner_id,
        sp.scan_init_info->>''storage_id'' AS storage_id,
        oc.total_files
    FROM owner_counts oc
    LEFT JOIN scan_policy sp
    ON sp.storage_type = oc.storage_type
    ),
    total_owner_files AS (
    SELECT SUM(total_files) AS total_files_count FROM owner_with_storage_id
    ),
    top_owners AS (
    SELECT owner_id, storage_id, total_files
    FROM owner_with_storage_id
    ORDER BY total_files DESC
    LIMIT 10
    )
    SELECT json_build_object(
    ''owner_summary'', (
        SELECT json_agg(row_to_json(top))
        FROM top_owners top
    )
    ) AS result;', mv_name, where_clause);
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Top 10 shared sensitive file owners show more widget
DROP FUNCTION IF EXISTS create_adb_top_shared_owners_sm_mv(text);
CREATE OR REPLACE FUNCTION create_adb_top_shared_owners_sm_mv(
    share_type TEXT DEFAULT 'all'  -- allowed: 'internal', 'external', 'public', or 'all'
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    where_clause TEXT;
BEGIN
    -- Determine filter based on share_type
    IF share_type = 'internal' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean = true';
    ELSIF share_type = 'external' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean = true';
    ELSIF share_type = 'public' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean = true';
    ELSE
        where_clause := '((reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean = true OR (reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean = true OR (reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean = true)';  -- public+internal+external
    END IF;

    -- Materialized view name based on share_type
    mv_name := format('adb_top_shared_owners_sm_%s', lower(share_type));

    -- Create or refresh the materialized view
    EXECUTE format(
    'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
    WITH owners AS (
    SELECT
        f.storage_type,
        jsonb_array_elements_text(f.file_metadata->''owners'') AS owner_id
    FROM file_info_view f
    WHERE f.storage_type IN (2, 3, 5) AND %s
    ),
    owner_counts AS (
    SELECT
        o.owner_id,
        o.storage_type,
        COUNT(*) AS total_files
    FROM owners o
    GROUP BY o.owner_id, o.storage_type
    ),
    owner_with_storage_id AS (
    SELECT
        oc.owner_id,
        sp.scan_init_info->>''storage_id'' AS storage_id,
        oc.total_files
    FROM owner_counts oc
    LEFT JOIN scan_policy sp
    ON sp.storage_type = oc.storage_type
    ),
    total_owner_files AS (
    SELECT SUM(total_files) AS total_files_count FROM owner_with_storage_id
    ),
    top_owners AS (
    SELECT owner_id, storage_id, total_files
    FROM owner_with_storage_id
    ORDER BY total_files DESC
    LIMIT 100
    )
    SELECT json_build_object(
    ''owner_summary'', (
        SELECT json_agg(row_to_json(top))
        FROM top_owners top
    )
    ) AS result;', mv_name, where_clause);
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Drop MVs beginning with a given prefix function
DROP FUNCTION IF EXISTS drop_materialized_views_by_prefix(text);
CREATE OR REPLACE FUNCTION drop_materialized_views_by_prefix(mv_prefix TEXT)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
BEGIN
    -- Loop through materialized views with names starting with the provided prefix
    FOR mv_name IN
        SELECT matviewname
        FROM pg_catalog.pg_matviews
        WHERE matviewname LIKE mv_prefix || '\_%' ESCAPE '\'
    LOOP
        BEGIN
            EXECUTE format('DROP MATERIALIZED VIEW IF EXISTS %I', mv_name);
            RAISE NOTICE 'DROPPED: %', mv_name;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'DROP MV failed for %', mv_name;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS ftnt_devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(255) NOT NULL,
    description VARCHAR(256) NOT NULL default '',
    info JSONB,
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS api_key (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     name VARCHAR(255) UNIQUE NOT NULL,
     status BOOLEAN NOT NULL DEFAULT FALSE,
     type INT NOT NULL DEFAULT 1,
     key TEXT NOT NULL,
     attributes JSONB,
     created_at TIMESTAMP(3) DEFAULT now()::TIMESTAMP(3),
     updated_at TIMESTAMP(3) DEFAULT now()::TIMESTAMP(3)
);
----------------------------------------------------------------------
CREATE INDEX IF NOT EXISTS idx_api_key_name ON api_key(name);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS protection_profile (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(256),
  profile_type smallint NOT NULL,
  profile jsonb NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
CREATE INDEX IF NOT EXISTS protection_profile_profile_type ON protection_profile (profile_type);
CREATE INDEX IF NOT EXISTS protection_profile_profile ON protection_profile USING GIN (profile);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS report_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID DEFAULT NULL,
    query_id UUID DEFAULT NULL,
    name VARCHAR(255) UNIQUE NOT NULL,
    version_id INTEGER NOT NULL DEFAULT 0,
    status INT DEFAULT 0,
    format JSONB NOT NULL,
    report_type JSONB NOT NULL,
    report_details JSONB NOT NULL,
    attributes JSONB,
    period VARCHAR(255),
    schedule_info JSONB,
    notes TEXT,
    reportgen_count INT DEFAULT 0,
    created_by VARCHAR(255) NOT NULL,
    status_update_time TIMESTAMP(3) WITH TIME ZONE,
    created_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone,
    updated_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS report_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    format INT NOT NULL,
    path VARCHAR(255) NOT NULL,
    report_count INT,
    file_size INT,
    result INT NOT NULL,
    attributes JSONB,
    expiration_time TIMESTAMP(3) with time zone,
    created_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS device_access_stats (
   id                       UUID           PRIMARY KEY DEFAULT uuid_generate_v4(),
   ip                       INET           NOT NULL,
   device_identifier        TEXT           NULL,
   device_type              VARCHAR(32)     NOT NULL,

   req_query_tag_total_count         BIGINT         NOT NULL DEFAULT 0,
   req_query_tag_total_count_24h     BIGINT         NOT NULL DEFAULT 0,

   files_in_req_total_count          BIGINT         NOT NULL DEFAULT 0,
   files_in_req_total_count_24h      BIGINT         NOT NULL DEFAULT 0,

   file_with_tags_total_count        BIGINT         NOT NULL DEFAULT 0,
   file_with_tags_total_count_24h    BIGINT         NOT NULL DEFAULT 0,

   req_query_config_total_count      BIGINT         NOT NULL DEFAULT 0,
   req_query_config_total_count_24h  BIGINT         NOT NULL DEFAULT 0,

   req_upload_total_count            BIGINT         NOT NULL DEFAULT 0,
   req_upload_total_count_24h        BIGINT         NOT NULL DEFAULT 0,

   req_ztna_sync_total_count         BIGINT         NOT NULL DEFAULT 0,
   req_ztna_sync_total_count_24h     BIGINT         NOT NULL DEFAULT 0,


   last_file_tag_query_time         timestamp(3) without time zone,
   last_upload_time                 timestamp(3) without time zone,
   last_config_query_time           timestamp(3) without time zone,
   last_ztna_sync_time              timestamp(3) without time zone,

   created_at               TIMESTAMP(3)   NOT NULL DEFAULT now()::TIMESTAMP(3),
   updated_at               TIMESTAMP(3)   NOT NULL DEFAULT now()::TIMESTAMP(3),

   CONSTRAINT uq_device UNIQUE (device_identifier, device_type)
);

CREATE INDEX IF NOT EXISTS idx_device_stats_identifier ON device_access_stats (device_identifier);
CREATE INDEX IF NOT EXISTS idx_device_stats_type ON device_access_stats (device_type);
CREATE INDEX IF NOT EXISTS idx_device_stats_ip ON device_access_stats (ip);
CREATE INDEX IF NOT EXISTS idx_device_stats_identifier_ip ON device_access_stats (device_identifier, ip);
CREATE INDEX IF NOT EXISTS idx_device_stats_type_ip ON device_access_stats (device_type, ip);
CREATE INDEX IF NOT EXISTS idx_device_stats_identifier_type_ip ON device_access_stats (device_identifier, device_type, ip);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS reports_retention_policy (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    retention_all INT,
    once INT,
    daily INT,
    weekly INT,
    monthly INT,
    created_at TIMESTAMP(3) DEFAULT now()::TIMESTAMP(3)
);
----------------------------------------------------------------------
CREATE OR REPLACE FUNCTION refresh_report_expirations()
RETURNS TRIGGER AS $$
DECLARE
    retention RECORD;
    type_mapping CONSTANT JSONB := '{"1": "once", "2": "daily", "3": "weekly", "4": "monthly"}'::JSONB;
    type_key TEXT;
    retention_days INT;
BEGIN
    RAISE NOTICE 'Trigger function started.';

    -- Fetch the latest retention policy

    SELECT * INTO retention
    FROM reports_retention_policy
    ORDER BY created_at DESC
    LIMIT 1;

    RAISE NOTICE 'Fetched retention policy: %', retention;

    -- Update expiration_time for each report_file based on its type
    UPDATE report_files
    SET expiration_time = created_at + (
        CASE
            WHEN retention.retention_all IS NOT NULL THEN retention.retention_all
            WHEN (attributes ->> 'type')::INT = 1 THEN COALESCE(retention.once, 30)
            WHEN (attributes ->> 'type')::INT = 2 THEN COALESCE(retention.daily, 30)
            WHEN (attributes ->> 'type')::INT = 3 THEN COALESCE(retention.weekly, 30)
            WHEN (attributes ->> 'type')::INT = 4 THEN COALESCE(retention.monthly, 30)
            ELSE 30
        END
    ) * INTERVAL '1 day'
    ;
    RAISE NOTICE 'Updated report_files with new expiration_time.';
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
CREATE TRIGGER trigger_refresh_expirations
AFTER INSERT OR UPDATE ON reports_retention_policy
FOR EACH STATEMENT
EXECUTE FUNCTION refresh_report_expirations();
----------------------------------------------------------------------
create TABLE  IF NOT EXISTS http2_event (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tm TIMESTAMPTZ(3) DEFAULT now()::TIMESTAMPTZ(3),
    device_type VARCHAR(50) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    device_ip INET NOT NULL,
    endpoint  VARCHAR(255) NOT NULL,
    status_code INT NOT NULL,
    message JSONB  NOT NULL,
    created_at TIMESTAMPTZ(3) DEFAULT now()::TIMESTAMPTZ(3),
    updated_at TIMESTAMPTZ(3) DEFAULT now()::TIMESTAMPTZ(3)
);
create index if not exists http2_event_ops_index on http2_event using gin(message);
----------------------------------------------------------------------
-- Table for files and scan incidents queries
CREATE TABLE IF NOT EXISTS query_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    version_id INTEGER NOT NULL DEFAULT 0,
    query_type INT NOT NULL,
    query_string JSONB NOT NULL,
    created_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone,
    updated_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS notification_connectors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    notifier_type VARCHAR(64) NOT NULL,
    config JSONB NOT NULL,
    extra JSONB NOT NULL DEFAULT '{}',
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    notifier_type VARCHAR(64) NOT NULL,
    notification_type VARCHAR(64) NOT NULL,
    template_type VARCHAR(64) NOT NULL,
    template JSONB NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    connector_id UUID NULL REFERENCES notification_connectors(id),
    extra JSONB NOT NULL DEFAULT '{}',
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS notification_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_key VARCHAR(128) NOT NULL,
    ticket_link VARCHAR(1024) NOT NULL,
    ticket_id VARCHAR(128) NOT NULL,
    notifier_type VARCHAR(64) NOT NULL,
    recipient_email VARCHAR(255),
    recipient_name VARCHAR(255),
    ticket_creator VARCHAR(255),
    ticket_creator_email VARCHAR(255),
    status VARCHAR(64),
    extra JSONB,
    template_id UUID NULL REFERENCES notification_templates(id),
    updated_at timestamp DEFAULT NOW(),
    created_at timestamp default now()::timestamp(3),
    CONSTRAINT _notification_ticket_notifier_uc UNIQUE (ticket_id, notifier_type)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS storage_activity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    storage_id UUID NULL REFERENCES storage_profiles(id) ON DELETE CASCADE,
    storage_type SMALLINT NOT NULL,
    event_time TIMESTAMP(3) NOT NULL,
    event_id VARCHAR(255),
    event_type VARCHAR(128),
    scan_policy_fields jsonb,
    ddr_fields jsonb,
    raw_data jsonb NOT NULL,
    reserve_json jsonb,
    status SMALLINT NOT NULL DEFAULT 0,
    scan_triggered BOOLEAN DEFAULT FALSE ,
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3),
    CONSTRAINT unique_event UNIQUE (event_id)
);

CREATE INDEX IF NOT EXISTS fetched_activity_raw_data ON storage_activity USING GIN (raw_data);
CREATE INDEX IF NOT EXISTS fetched_activity_ddr ON storage_activity USING GIN (ddr_fields);
CREATE INDEX IF NOT EXISTS fetched_activity_scan ON storage_activity USING GIN (scan_policy_fields);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS file_action (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_id UUID NOT NULL,
    scan_id UUID NOT NULL,
    action SMALLINT NOT NULL,
    action_type SMALLINT NOT NULL,
    action_detail jsonb NOT NULL,
    status SMALLINT NOT NULL,
    failed_reason TEXT,
    ctime timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS ddr_policy (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(512) NOT NULL,
  description VARCHAR(512),
  storage_type jsonb NOT NULL,  -- Array of storage_type
  file_exclusion jsonb,
  event_type VARCHAR(128) NOT NULL,
  trust_level JSONB NOT NULL,
  predefined_info VARCHAR(100),
  is_predefined BOOLEAN NOT NULL DEFAULT False,
  enabled BOOLEAN NOT NULL DEFAULT True,
  data_classifier_ids jsonb NOT NULL,   -- Array of data_classifier UUIDs
  match_condition jsonb,
  match_condition_relation jsonb,
  risk INT,
  protection_framework jsonb,
  action jsonb,
  notification_id UUID NOT NULL REFERENCES notification_templates(id),
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS ddr_task (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(512) NOT NULL,
  description VARCHAR(512),
  enabled BOOLEAN DEFAULT TRUE,
  scan_folders jsonb NOT NULL,
  scan_scope smallint NOT NULL,
  storage_type smallint NOT NULL,
  excluded_scan_folders jsonb NOt NULL,
  storage_id UUID NOT NULL REFERENCES storage_profiles(id),
  file_size_limit jsonb NOT NULL,
  scan_file_type jsonb NOT NULL,
  scan_interval smallint NOT NULL,
  analyze_setting jsonb NOT NULL,
  ddr_policy_ids jsonb NOT NULL,    -- Array of ddr_policy UUIDs
  db_index INT NOT NULL,
  trigger_events jsonb NOT NULL,
  protection_profiles jsonb NOT NULL,
  last_pull_time timestamp default now()::timestamp(3),
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------

DO $$
DECLARE
    i INT;
    table_name TEXT;
    index_prefix TEXT;
BEGIN
    FOR i IN 1..16 LOOP  -- Create 16 tables
        table_name := 'ddr_fileinfo_' || i;

        -- create table
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS %I (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                record_id SERIAL UNIQUE,
                file_hash VARCHAR(64) NOT NULL,
                file_tlsh tlsh,
                scan_uuid UUID NOT NULL,
                full_path VARCHAR(4096) NOT NULL,
                file_name VARCHAR(256) NOT NULL,
                file_attributes jsonb NOT NULL,
                storage_type smallint NOT NULL,
                storage_id UUID NOT NULL,
                main_class_id VARCHAR(16),
                sub_class_id VARCHAR(16),
                class_details jsonb,
                match_info jsonb,
                match_info_custom jsonb,
                match_info_edm jsonb,
                match_info_idm jsonb,
                protection_framework jsonb,
                matched_classifier jsonb,
                sensitivity INT,
                file_tag jsonb,
                reserve_int1 INT,
                reserve_int2 INT,
                reserve_str1 VARCHAR(64),
                reserve_str2 VARCHAR(64),
                reserve_json1 jsonb,
                reserve_json2 jsonb,
                reserve_json3 jsonb,
                reserve_json4 jsonb,
                tm timestamp default CURRENT_TIMESTAMP,
                tag_tm timestamp,
                last_scan_tm timestamp,
                file_metadata jsonb NOT NULL
            )', table_name);

        -- set index prefix
        index_prefix := 'ddr_fi_' || i || '_';

        -- create indexes
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (file_hash)', 
                       index_prefix || 'file_hash', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIST (file_tlsh gist_tlsh_ops)', 
                       index_prefix || 'file_tlsh', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (scan_uuid)', 
                       index_prefix || 'scan_uuid', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (full_path)', 
                       index_prefix || 'full_path', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (file_name)', 
                       index_prefix || 'file_name', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIN (file_attributes)', 
                       index_prefix || 'file_attributes', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (storage_type)', 
                       index_prefix || 'storage_type', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (main_class_id)', 
                       index_prefix || 'main_class_id', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (sub_class_id)', 
                       index_prefix || 'sub_class_id', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIN (match_info) where match_info <> ''[]''::jsonb', 
                       index_prefix || 'match_info_notempty', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIN (match_info jsonb_path_ops) where match_info = ''[]'' or match_info=''{}''::jsonb', 
                       index_prefix || 'match_info_empty', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIN (match_info)', 
                       index_prefix || 'match_info', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (tm)', 
                       index_prefix || 'tm', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (record_id)', 
                       index_prefix || 'record_id', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I ((reserve_json1->>''sensitive_data''))', 
                       index_prefix || 'sensitive_data', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I ((reserve_json1->>''compliance_data''))', 
                       index_prefix || 'compliance_data', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (last_scan_tm)', 
                       index_prefix || 'last_scan_tm', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIN (reserve_json3)', 
                       index_prefix || 'reserve_json3', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I(file_hash, scan_uuid)', 
                       index_prefix || 'filehash_scanuuid', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIN (file_metadata)', 
                       index_prefix || 'file_metadata', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (( (reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean ))', 
                       index_prefix || 'shared_data_internal', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (( (reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean ))', 
                       index_prefix || 'shared_data_external', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (( (reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean ))', 
                       index_prefix || 'shared_data_public', table_name);
    END LOOP;
END $$;

----------------------------------------------------------------------
-- DDR Incident
create table if not exists ddr_incidents (
  id uuid default uuid_generate_v4(),
  ctime timestamp default now()::timestamp(3),
  utime timestamp default now()::timestamp(3),
  attributes jsonb not null,
  attributes_ext jsonb not null,
  pol_details jsonb not null,
  data_source jsonb not null,
  file_info jsonb not null,
  activity jsonb not null,
  primary key (id)
);

create index if not exists ddr_incidents on ddr_incidents using gin(attributes);
create index if not exists ddr_incidents on ddr_incidents using gin(attributes_ext);
create index if not exists ddr_incidents_idx_pol on ddr_incidents using gin(pol_details);
create index if not exists ddr_incidents_idx_ds on ddr_incidents using gin(data_source);
create index if not exists ddr_incidents_idx_fi on ddr_incidents using gin(file_info);
create index if not exists ddr_incidents_idx_act on ddr_incidents using gin(activity);

create index if not exists idx_dsincidentspoldetails_btree ON ddr_incidents ((pol_details->>'sid'));
create index if not exists idx_dsincidentsattrssev_btree ON ddr_incidents ((attributes->>'severity'));
CREATE INDEX IF NOT EXISTS idx_dsincidentsutime ON ddr_incidents (utime);
