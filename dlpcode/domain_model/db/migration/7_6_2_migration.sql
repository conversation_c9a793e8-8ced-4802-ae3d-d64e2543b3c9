----------------------------------------------------------------------
-- Dashboard 7.6.2 new widgets
----------------------------------------------------------------------
-- Total scanned files widget
CREATE MATERIALIZED VIEW IF NOT EXISTS db_total_scanned_files AS
SELECT row_to_json(result_set) AS result
FROM (
    SELECT
        COUNT(*) AS alltimecount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '1 day' THEN 1 ELSE 0 END) AS onedaycount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '7 days' THEN 1 ELSE 0 END) AS oneweekcount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '14 days' THEN 1 ELSE 0 END) AS twoweekcount
    FROM file_info_view
    WHERE last_scan_tm is not NULL
) result_set;
----------------------------------------------------------------------
-- Sensitive file owners widget
CREATE MATERIALIZED VIEW IF NOT EXISTS db_sensitive_file_owners AS
SELECT row_to_json(result_set) AS result
FROM (
WITH filtered AS (
  SELECT file_metadata
  FROM file_info_view
  WHERE reserve_json1->>'sensitive_data' = 'true'
)
SELECT COUNT(DISTINCT owner) as sensitivefileownerscount
FROM filtered,
LATERAL jsonb_array_elements_text(file_metadata->'owners') AS owner(owner))result_set;
----------------------------------------------------------------------
-- Analysis Dashboard 7.6.2 new widgets
----------------------------------------------------------------------
-- Sensitive file owners widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_sensitive_file_owners AS
WITH owner_files AS (
    SELECT
        f.storage_type,
        jsonb_array_elements_text(f.file_metadata->'owners') AS owner_id
    FROM file_info_view f
    WHERE f.reserve_json1->>'sensitive_data' = 'true' AND f.storage_type IN (2, 3, 5)
),
total_count AS (
    SELECT COUNT(*) AS totalcount FROM owner_files
),
owner_counts AS (
    SELECT
        owner_id,
        COUNT(*) AS total_files
    FROM owner_files
    GROUP BY owner_id
),
owner_top_storage AS (
    SELECT DISTINCT ON (owner_id)
        owner_id,
        storage_type
    FROM owner_files
    GROUP BY owner_id, storage_type
    ORDER BY owner_id, COUNT(*) DESC
),
final_data AS (
    SELECT
        oc.owner_id,
        sp.scan_init_info->>'storage_id' AS storage_id,
        oc.total_files
    FROM owner_counts oc
    LEFT JOIN owner_top_storage ots ON oc.owner_id = ots.owner_id
    LEFT JOIN scan_policy sp ON sp.storage_type = ots.storage_type
    ORDER BY oc.total_files DESC
    LIMIT 10
)
SELECT json_build_object(
    'owner_summary', (
        SELECT json_agg(row_to_json(fd))
        FROM final_data fd
    )
) AS result;
----------------------------------------------------------------------
-- Sensitive file owners show more widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_sensitive_file_owners_show_more AS
WITH owner_files AS (
    SELECT
        f.storage_type,
        jsonb_array_elements_text(f.file_metadata->'owners') AS owner_id
    FROM file_info_view f
    WHERE f.reserve_json1->>'sensitive_data' = 'true' AND f.storage_type IN (2, 3, 5)
),
total_count AS (
    SELECT COUNT(*) AS totalcount FROM owner_files
),
owner_counts AS (
    SELECT
        owner_id,
        COUNT(*) AS total_files
    FROM owner_files
    GROUP BY owner_id
),
owner_top_storage AS (
    SELECT DISTINCT ON (owner_id)
        owner_id,
        storage_type
    FROM owner_files
    GROUP BY owner_id, storage_type
    ORDER BY owner_id, COUNT(*) DESC
),
final_data AS (
    SELECT
        oc.owner_id,
        sp.scan_init_info->>'storage_id' AS storage_id,
        oc.total_files
    FROM owner_counts oc
    LEFT JOIN owner_top_storage ots ON oc.owner_id = ots.owner_id
    LEFT JOIN scan_policy sp ON sp.storage_type = ots.storage_type
    ORDER BY oc.total_files DESC
    LIMIT 100
)
SELECT json_build_object(
    'owner_summary', (
        SELECT json_agg(row_to_json(fd))
        FROM final_data fd
    )
) AS result;
----------------------------------------------------------------------
-- Dormant sensitive files widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_dormant_sensitive_files AS
WITH dormant_files AS (
    SELECT
        f.id,
        f.file_name,
        f.file_metadata->'owners' AS owner,
        f.storage_type,
        sp.scan_init_info->>'storage_id' AS storage_id,
        ROUND(EXTRACT(EPOCH FROM NULLIF(f.file_attributes->>'last_access_time', 'N/A')::timestamp)) AS last_accessed_time,
        ROUND(EXTRACT(EPOCH FROM (NOW() - (f.file_attributes->>'last_access_time')::timestamp)) / 86400) AS days_idle
    FROM file_info_view f
    LEFT JOIN scan_policy sp
        ON sp.storage_type = f.storage_type
    WHERE f.tag_tm IS NOT NULL AND f.file_attributes->>'last_access_time' != 'N/A'
    AND f.reserve_json1->>'sensitive_data' = 'true'
),
total_dormant AS (
    SELECT COUNT(*) AS total_files FROM dormant_files
),
top_dormant AS (
    SELECT *
    FROM dormant_files
    ORDER BY last_accessed_time ASC
    LIMIT 10
)
SELECT json_build_object(
    'dormant_files', (
        SELECT json_agg(row_to_json(df))
        FROM top_dormant df
    )
) AS result;
----------------------------------------------------------------------
-- Dormant sensitive files show more widget
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_dormant_sensitive_files_show_more AS
WITH dormant_files AS (
    SELECT
        f.id,
        f.file_name,
        f.file_metadata->'owners' AS owner,
        f.storage_type,
        sp.scan_init_info->>'storage_id' AS storage_id,
        ROUND(EXTRACT(EPOCH FROM NULLIF(f.file_attributes->>'last_access_time', 'N/A')::timestamp)) AS last_accessed_time,
        ROUND(EXTRACT(EPOCH FROM (NOW() - (f.file_attributes->>'last_access_time')::timestamp)) / 86400) AS days_idle
    FROM file_info_view f
    LEFT JOIN scan_policy sp
        ON sp.storage_type = f.storage_type
    WHERE f.tag_tm IS NOT NULL AND f.file_attributes->>'last_access_time' != 'N/A'
    AND f.reserve_json1->>'sensitive_data' = 'true'
),
total_dormant AS (
    SELECT COUNT(*) AS total_files FROM dormant_files
),
top_dormant AS (
    SELECT *
    FROM dormant_files
    ORDER BY last_accessed_time ASC
    LIMIT 100
)
SELECT json_build_object(
    'dormant_files', (
        SELECT json_agg(row_to_json(df))
        FROM top_dormant df
    )
) AS result;
----------------------------------------------------------------------
-- Top 10 shared sensitive file owners widget
DROP FUNCTION IF EXISTS create_adb_top_shared_owners_mv(text);
CREATE OR REPLACE FUNCTION create_adb_top_shared_owners_mv(
    share_type TEXT DEFAULT 'all'  -- allowed: 'internal', 'external', 'public', or 'all'
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    where_clause TEXT;
BEGIN
    -- Determine filter based on share_type
    IF share_type = 'internal' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean = true';
    ELSIF share_type = 'external' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean = true';
    ELSIF share_type = 'public' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean = true';
    ELSE
        where_clause := '((reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean = true OR (reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean = true OR (reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean = true)';  -- public+internal+external
    END IF;

    -- Materialized view name based on share_type
    mv_name := format('adb_top_shared_owners_%s', lower(share_type));

    -- Create or refresh the materialized view
    EXECUTE format(
    'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
    WITH owners AS (
    SELECT
        f.storage_type,
        jsonb_array_elements_text(f.file_metadata->''owners'') AS owner_id
    FROM file_info_view f
    WHERE f.storage_type IN (2, 3, 5) AND %s
    ),
    owner_counts AS (
    SELECT
        o.owner_id,
        o.storage_type,
        COUNT(*) AS total_files
    FROM owners o
    GROUP BY o.owner_id, o.storage_type
    ),
    owner_with_storage_id AS (
    SELECT
        oc.owner_id,
        sp.scan_init_info->>''storage_id'' AS storage_id,
        oc.total_files
    FROM owner_counts oc
    LEFT JOIN scan_policy sp
    ON sp.storage_type = oc.storage_type
    ),
    total_owner_files AS (
    SELECT SUM(total_files) AS total_files_count FROM owner_with_storage_id
    ),
    top_owners AS (
    SELECT owner_id, storage_id, total_files
    FROM owner_with_storage_id
    ORDER BY total_files DESC
    LIMIT 10
    )
    SELECT json_build_object(
    ''owner_summary'', (
        SELECT json_agg(row_to_json(top))
        FROM top_owners top
    )
    ) AS result;', mv_name, where_clause);
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Top 10 shared sensitive file owners show more widget
DROP FUNCTION IF EXISTS create_adb_top_shared_owners_sm_mv(text);
CREATE OR REPLACE FUNCTION create_adb_top_shared_owners_sm_mv(
    share_type TEXT DEFAULT 'all'  -- allowed: 'internal', 'external', 'public', or 'all'
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    where_clause TEXT;
BEGIN
    -- Determine filter based on share_type
    IF share_type = 'internal' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean = true';
    ELSIF share_type = 'external' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean = true';
    ELSIF share_type = 'public' THEN
        where_clause := '(reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean = true';
    ELSE
        where_clause := '((reserve_json1->''shared_data''->>''with_public_shareable_link'')::boolean = true OR (reserve_json1->''shared_data''->>''with_internal_shareable_link'')::boolean = true OR (reserve_json1->''shared_data''->>''with_external_shareable_link'')::boolean = true)';  -- public+internal+external
    END IF;

    -- Materialized view name based on share_type
    mv_name := format('adb_top_shared_owners_sm_%s', lower(share_type));

    -- Create or refresh the materialized view
    EXECUTE format(
    'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
    WITH owners AS (
    SELECT
        f.storage_type,
        jsonb_array_elements_text(f.file_metadata->''owners'') AS owner_id
    FROM file_info_view f
    WHERE f.storage_type IN (2, 3, 5) AND %s
    ),
    owner_counts AS (
    SELECT
        o.owner_id,
        o.storage_type,
        COUNT(*) AS total_files
    FROM owners o
    GROUP BY o.owner_id, o.storage_type
    ),
    owner_with_storage_id AS (
    SELECT
        oc.owner_id,
        sp.scan_init_info->>''storage_id'' AS storage_id,
        oc.total_files
    FROM owner_counts oc
    LEFT JOIN scan_policy sp
    ON sp.storage_type = oc.storage_type
    ),
    total_owner_files AS (
    SELECT SUM(total_files) AS total_files_count FROM owner_with_storage_id
    ),
    top_owners AS (
    SELECT owner_id, storage_id, total_files
    FROM owner_with_storage_id
    ORDER BY total_files DESC
    LIMIT 100
    )
    SELECT json_build_object(
    ''owner_summary'', (
        SELECT json_agg(row_to_json(top))
        FROM top_owners top
    )
    ) AS result;', mv_name, where_clause);
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Dashboard-modified MVs
----------------------------------------------------------------------
DROP MATERIALIZED VIEW "db_files_to_scan";
CREATE MATERIALIZED VIEW IF NOT EXISTS db_files_to_scan AS
WITH files_to_scan_data AS (
    SELECT jsonb_agg(fscandata) AS filestoscandata  -- Use JSONB for compatibility
    FROM (
        SELECT
            storage_type,
            COUNT(*) AS storagecount,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) AS percentage
        FROM file_info_view
        WHERE last_scan_tm is not NULL
        GROUP BY storage_type
    ) fscandata
),
storage_sum AS (
    SELECT SUM((fscandata->>'storagecount')::INTEGER) AS total_storage_count
    FROM files_to_scan_data, jsonb_array_elements(filestoscandata) AS fscandata
)
SELECT json_build_object(
        'total_files', COALESCE(storage_sum.total_storage_count, 0),  -- Handle NULL case
        'total_percentage', CASE
            WHEN COALESCE(storage_sum.total_storage_count, 0) = 0 THEN 0
            ELSE 100.0
         END,
        'files_to_scan', files_to_scan_data.filestoscandata
) AS result
FROM files_to_scan_data, storage_sum;
----------------------------------------------------------------------
DROP MATERIALIZED VIEW "db_scan_results_24hours";
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_24hours AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('hour', NOW() - INTERVAL '24 Hours'),
            date_trunc('hour', NOW()),
            INTERVAL '1 hour'
        ) AS hour
    ),
    file_counts AS (
        SELECT
            date_trunc('hour', last_scan_tm) AS hour,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm is not NULL AND last_scan_tm >= NOW() - INTERVAL '24 Hours'
        GROUP BY date_trunc('hour', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.hour)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.hour = fc.hour
    ORDER BY ds.hour
) result_set;
----------------------------------------------------------------------
DROP MATERIALIZED VIEW "db_scan_results_7days";
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_7days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '7 days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm is not NULL AND last_scan_tm >= NOW() - INTERVAL '7 days'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
DROP MATERIALIZED VIEW "db_scan_results_14days";
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_14days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '14 days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm is not NULL AND last_scan_tm >= NOW() - INTERVAL '14 days'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
DROP MATERIALIZED VIEW "db_compliance_files";
CREATE MATERIALIZED VIEW IF NOT EXISTS db_compliance_files AS
WITH all_compliance AS (
    SELECT
        id::text AS file_id,
        jsonb_array_elements_text(reserve_json1->'protection_framework') AS compliance
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
compliance_counts AS (
    SELECT
        compliance,
        COUNT(DISTINCT file_id) AS count
    FROM all_compliance
    GROUP BY compliance
),
compliance_category_count AS (
    SELECT COUNT(*) AS category_count FROM compliance_counts
),
total_count AS (
    SELECT COUNT(DISTINCT id) AS total_count
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
limited_compliance AS (
    SELECT compliance, count
    FROM compliance_counts
    ORDER BY count DESC
    LIMIT 5
),
limited_with_percentage AS (
    SELECT
        compliance,
        count,
        ROUND((count::numeric / NULLIF((SELECT total_count FROM total_count), 0)) * 100, 2) AS percentage
    FROM limited_compliance
),
compliance_files AS (
    SELECT * FROM limited_with_percentage
),
total_compliance AS (
    SELECT
        (SELECT total_count FROM total_count) AS count,
        CASE WHEN (SELECT total_count FROM total_count) = 0 THEN 0 ELSE 100.0 END AS percentage
),
total_scanned AS (
    SELECT
        COUNT(DISTINCT id) AS count,
        CASE
            WHEN COUNT(DISTINCT id) = 0 THEN 0
            ELSE ROUND(
                (SELECT total_count FROM total_count)::numeric
                / NULLIF(COUNT(DISTINCT id)::numeric, 0) * 100,
                2
            )
        END AS percentage
    FROM file_info_view
)
SELECT json_build_object(
    'compliance_data', json_agg(row_to_json(compliance_files)),
    'total_compliance', (SELECT row_to_json(total_compliance) FROM total_compliance),
    'total_scanned', (SELECT row_to_json(total_scanned) FROM total_scanned)
) AS result
FROM compliance_files;
----------------------------------------------------------------------
DROP MATERIALIZED VIEW "db_compliance_files_show_more";
CREATE MATERIALIZED VIEW IF NOT EXISTS db_compliance_files_show_more AS
WITH all_compliance AS (
    SELECT
        id::text AS file_id,
        jsonb_array_elements_text(reserve_json1->'protection_framework') AS compliance
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
compliance_counts AS (
    SELECT
        compliance,
        COUNT(DISTINCT file_id) AS count
    FROM all_compliance
    GROUP BY compliance
),
compliance_category_count AS (
    SELECT COUNT(*) AS category_count FROM compliance_counts
),
total_count AS (
    SELECT COUNT(DISTINCT id) AS total_count
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
limited_compliance AS (
    SELECT compliance, count
    FROM compliance_counts
    ORDER BY count DESC
    LIMIT 15
),
limited_with_percentage AS (
    SELECT
        compliance,
        count,
        ROUND((count::numeric / NULLIF((SELECT total_count FROM total_count), 0)) * 100, 2) AS percentage
    FROM limited_compliance
),
compliance_files AS (
    SELECT * FROM limited_with_percentage
),
total_compliance AS (
    SELECT
        (SELECT total_count FROM total_count) AS count,
        CASE WHEN (SELECT total_count FROM total_count) = 0 THEN 0 ELSE 100.0 END AS percentage
),
total_scanned AS (
    SELECT
        COUNT(DISTINCT id) AS count,
        CASE
            WHEN COUNT(DISTINCT id) = 0 THEN 0
            ELSE ROUND(
                (SELECT total_count FROM total_count)::numeric
                / NULLIF(COUNT(DISTINCT id)::numeric, 0) * 100,
                2
            )
        END AS percentage
    FROM file_info_view
)
SELECT json_build_object(
    'compliance_data', json_agg(row_to_json(compliance_files)),
    'total_compliance', (SELECT row_to_json(total_compliance) FROM total_compliance),
    'total_scanned', (SELECT row_to_json(total_scanned) FROM total_scanned)
) AS result
FROM compliance_files;
----------------------------------------------------------------------
-- Analysis Dashboard-modified MVs
----------------------------------------------------------------------
-- AI file categories widget
DROP MATERIALIZED VIEW "adb_ai_all";
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_ai_all AS
        WITH time_filtered_data AS (
            SELECT main_class_id, sub_class_id, scan_uuid
            FROM file_info_view
        ),
        total_count AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count AS (
            SELECT
                main_class_id,
                COUNT(*) AS count
            FROM time_filtered_data
            WHERE main_class_id IS NOT NULL
            GROUP BY main_class_id
        ),
        total_classified_files AS (
            SELECT SUM(count) AS total_classified
            FROM class_count
        )
        SELECT json_build_object(
            'class_summary', (
                SELECT json_agg(row_to_json(result_set))
                FROM (
                    SELECT
                        cc.main_class_id,
                        cc.count,
                        ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
                    FROM class_count cc
                    CROSS JOIN total_count tc
                    WHERE tc.totalcount != 0
                ) result_set
            ),
            'total_scanned_files', (SELECT totalcount FROM total_count),
            'total_classified_files', (SELECT total_classified FROM total_classified_files),
            'total_classified_percentage', (
                SELECT ROUND(total_classified * 100.0 / NULLIF(total_count.totalcount, 0), 2)
                FROM total_classified_files, total_count
            )
        ) AS result;
----------------------------------------------------------------------
-- AI file categories show more widget
DROP MATERIALIZED VIEW "adb_aism_all";
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_aism_all AS
        WITH time_filtered_data AS (
            SELECT main_class_id, sub_class_id, scan_uuid
            FROM file_info_view
        ),
        total_count_main AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count_main AS (
            SELECT main_class_id, COUNT(*) AS count
            FROM time_filtered_data
            WHERE main_class_id IS NOT NULL
            GROUP BY main_class_id
        ),
        main_class_summary AS (
            SELECT
                cc.main_class_id,
                cc.count,
                ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM class_count_main cc
            CROSS JOIN total_count_main tc
            WHERE tc.totalcount != 0
        ),
        total_count_sub AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count_sub AS (
            SELECT sub_class_id, COUNT(*) AS count
            FROM time_filtered_data
            WHERE sub_class_id IS NOT NULL
            GROUP BY sub_class_id
        ),
        sub_class_summary AS (
            SELECT
                cc.sub_class_id,
                cc.count,
                ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM class_count_sub cc
            CROSS JOIN total_count_sub tc
            WHERE tc.totalcount != 0
        ),
        total_classified_files AS (
            SELECT SUM(count) AS total_classified
            FROM class_count_main
        )

        SELECT json_build_object(
            'main_class_summary', (SELECT json_agg(row_to_json(main_class_summary)) FROM main_class_summary),
            'sub_class_summary', (SELECT json_agg(row_to_json(sub_class_summary)) FROM sub_class_summary),
            'total_scanned_files', (SELECT totalcount FROM total_count_main),
            'total_classified_files', (SELECT total_classified FROM total_classified_files),
            'total_classified_percentage', (
                SELECT ROUND(total_classified * 100.0 / NULLIF(total_count_main.totalcount, 0), 2)
                FROM total_classified_files, total_count_main
            )
        ) AS result;
----------------------------------------------------------------------
-- Sensitive files by file types widget
DROP MATERIALIZED VIEW "adb_sf_all";
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_sf_all AS

        WITH total_count AS (
            SELECT COUNT(*) AS totalcount FROM file_info_view
        ),
        sensitive_files AS (
            SELECT file_attributes->>'file_ext' AS filetype, COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>'sensitive_data'='true'
            GROUP BY filetype
            ORDER by count DESC
            LIMIT 5
        ),
        total_sensitive_files AS (
            SELECT SUM(count) AS total_sensitive FROM sensitive_files
        )
        SELECT json_build_object(
            'file_type_summary', (SELECT json_agg(row_to_json(result_set)) FROM (
                SELECT COALESCE(sf.filetype, 'Unknown') AS filetype, sf.count,
                ROUND(sf.count * 100.0 / tc.totalcount, 2) AS percentage
                FROM total_count tc, sensitive_files sf WHERE tc.totalcount != 0
            ) result_set),
            'total_sensitive_files', (SELECT total_sensitive FROM total_sensitive_files),
            'total_sensitive_percentage', (SELECT ROUND(total_sensitive * 100.0 / total_count.totalcount, 2)
            FROM total_sensitive_files, total_count),
            'total_scanned_files', (SELECT totalcount FROM total_count)
        ) AS result;
----------------------------------------------------------------------
-- Sensitive files by file types show more widget
DROP MATERIALIZED VIEW "adb_sfsm_all";
CREATE MATERIALIZED VIEW IF NOT EXISTS adb_sfsm_all AS

        WITH total_count AS (
            SELECT count(*) AS totalcount
            FROM file_info_view
        ),

        -- Count sensitive files by category
        sensitive_files AS (
            SELECT
                file_attributes->>'file_cat' AS filetype,
                COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>'sensitive_data'='true'
            GROUP BY filetype
        ),

        -- Count sensitive files by category and extension
        sensitive_files_ext AS (
            SELECT
                file_attributes->>'file_cat' AS filetype,
                file_attributes->>'file_ext' AS fileext,
                COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>'sensitive_data'='true'
            GROUP BY filetype, fileext
        ),

        -- Total count of sensitive files
        total_sensitive_files AS (
            SELECT SUM(count) AS total_sensitive
            FROM sensitive_files
        ),

        -- Percentage summary for file types
        file_type_summary AS (
            SELECT
                COALESCE(sf.filetype, 'Unknown') AS filetype,
                sf.count,
                ROUND(sf.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM total_count tc
            CROSS JOIN sensitive_files sf
            WHERE tc.totalcount != 0
        ),

        -- Percentage summary for file types with extensions
        file_type_ext_details AS (
            SELECT
                COALESCE(sfe.filetype, 'Unknown') AS filetype,
                COALESCE(sfe.fileext, 'Unknown') AS fileext,
                sfe.count,
                ROUND(sfe.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM total_count tc
            CROSS JOIN sensitive_files_ext sfe
            WHERE tc.totalcount != 0
        )

        -- Final JSON Output
        SELECT json_build_object(
            'file_type_summary', (SELECT json_agg(row_to_json(file_type_summary)) FROM file_type_summary),
            'file_type_ext_details', (SELECT json_agg(row_to_json(file_type_ext_details)) FROM file_type_ext_details),
            'total_scanned_files', (SELECT totalcount FROM total_count),
            'total_sensitive_files', (SELECT total_sensitive FROM total_sensitive_files),
            'total_sensitive_percentage', (
                SELECT ROUND(total_sensitive * 100.0 / NULLIF(total_count.totalcount, 0), 2)
                FROM total_sensitive_files, total_count
            )
        ) AS result;
----------------------------------------------------------------------
-- Severity widget
DROP FUNCTION IF EXISTS create_adb_sev_mv(text, text, text, text);
CREATE OR REPLACE FUNCTION create_adb_sev_mv(
    risk TEXT,                -- Example: '1', '2', '3', '4'
    time_filter TEXT,         -- Example: '14 days'
    time_trunc TEXT,          -- Example: 'day', 'hour', 'week'
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'AND pol_details->>''sid'' = ''' || scan_uuid || '''';
    ELSE
        in_clause := ''; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_sev_%s_%s_%s', lower(risk), lower(replace(time_filter, ' ', '_')), lower(scan_uuid));


    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
	  WITH time_series AS (
            SELECT generate_series(
                date_trunc(%L, NOW() - INTERVAL %L),
                date_trunc(%L, NOW()),
                (CASE
                    WHEN %L  = ''hour'' THEN INTERVAL ''1 hour''
                    WHEN %L  = ''day'' THEN INTERVAL ''1 day''
                    WHEN %L  = ''week'' THEN INTERVAL ''1 week''
                    ELSE INTERVAL ''1 day''
                END)
            ) AS time_unit
        ),

        -- Create base data with 0 counts for missing intervals
        base_data AS (
            SELECT
                floor(extract(epoch from ts.time_unit)) AS time_unit,
                 %L AS risk,
                0 AS count
            FROM time_series ts
        ),

        -- Actual counts from ds_incidents
        actual_data AS (
            SELECT
                floor(extract(epoch from date_trunc(%L, utime))) AS time_unit,
                ds_incidents.attributes->>''risk'' AS risk,
                count(*) AS count
            FROM ds_incidents
            WHERE ds_incidents.attributes->>''risk'' = %L
                AND utime >= NOW() - INTERVAL %L
                %s
            GROUP BY time_unit, risk
        )

        -- Final JSON output
        SELECT json_agg(row_to_json(result_set)) AS result
        FROM (
            SELECT bd.time_unit, bd.risk, COALESCE(ad.count, bd.count) AS count
            FROM base_data bd
            LEFT JOIN actual_data ad
                ON bd.time_unit = ad.time_unit
                AND bd.risk = ad.risk
            ORDER BY bd.time_unit, bd.risk
        ) result_set;',
        mv_name, time_trunc, time_filter, time_trunc, -- time-series parameters
        time_trunc, time_trunc, time_trunc,  -- interval selection
        risk, time_trunc, risk, time_filter, in_clause -- risk and conditions
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Compliance files widget
DROP FUNCTION IF EXISTS create_adb_comp_mv(text, text, text);
CREATE OR REPLACE FUNCTION create_adb_comp_mv(
    time_filter TEXT,         -- Example: '24 hours, 7 days, 14 days'
    time_trunc TEXT,          -- Example: 'day, hour'
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'AND pol_details->>''sid'' = ''' || scan_uuid || '''';
    ELSE
        in_clause := ''; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_comp_%s_%s', lower(replace(time_filter, ' ', '_')), lower(scan_uuid));
    EXECUTE format('DROP MATERIALIZED VIEW IF EXISTS %I', mv_name);
    EXECUTE format(

        'CREATE MATERIALIZED VIEW %I AS
        WITH compliance_counts AS (
        -- Get top 5 compliance types within the time window
        SELECT
            compliance,
            COUNT(*) AS count
        FROM (
            SELECT
                jsonb_array_elements_text(f.reserve_json1->''protection_framework'') AS compliance
                FROM file_info_view f
                WHERE f.last_scan_tm > (NOW() - INTERVAL %L) %s
                AND f.reserve_json1->>''compliance_data'' = ''true''
        ) sub
        GROUP BY compliance
        ORDER BY count DESC
        LIMIT 5
        ),
        time_series AS (
        SELECT generate_series(
            date_trunc(%L, NOW() - INTERVAL %L),
            date_trunc(%L, NOW()),
            INTERVAL ''1 %s''
            ) AS time_unit
        ),
        compliance_time_series AS (
            SELECT
            floor(EXTRACT(EPOCH FROM ts.time_unit)) AS time_unit,
            cc.compliance,
            COUNT(f.id) AS count
            FROM
            time_series ts
            CROSS JOIN compliance_counts cc
            LEFT JOIN file_info_view f
            ON date_trunc(%L, f.last_scan_tm) = ts.time_unit
            AND f.last_scan_tm > (NOW() - INTERVAL %L)
            AND f.reserve_json1->>''compliance_data'' = ''true''
            LEFT JOIN LATERAL jsonb_array_elements_text(f.reserve_json1->''protection_framework'') jf(compliance_val)
            ON TRUE
            AND jf.compliance_val = cc.compliance
            GROUP BY ts.time_unit, cc.compliance
            ORDER BY ts.time_unit, cc.compliance
        )
        SELECT json_agg(
            json_build_object(
                ''time_unit'', time_unit,
                ''compliance'', compliance,
                ''count'', count
            )
        ) AS result
        FROM compliance_time_series;',
        mv_name, time_filter, in_clause, time_trunc, time_filter, time_trunc, time_trunc, -- Time-series
        time_trunc, time_filter -- Filtering conditions
);
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
create TABLE  IF NOT EXISTS http2_event (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tm TIMESTAMPTZ(3) DEFAULT now()::TIMESTAMPTZ(3),
    device_type VARCHAR(50) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    device_ip INET NOT NULL,
    endpoint  VARCHAR(255) NOT NULL,
    status_code INT NOT NULL,
    message JSONB  NOT NULL,
    created_at TIMESTAMPTZ(3) DEFAULT now()::TIMESTAMPTZ(3),
    updated_at TIMESTAMPTZ(3) DEFAULT now()::TIMESTAMPTZ(3)
);
create index if not exists http2_event_ops_index on http2_event using gin(message);
----------------------------------------------------------------------
-- Table for files and scan incidents queries
CREATE TABLE IF NOT EXISTS query_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    version_id INTEGER NOT NULL DEFAULT 0,
    query_type INT NOT NULL,
    query_string JSONB NOT NULL,
    created_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone,
    updated_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS data_classifier (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(512) NOT NULL,
  description VARCHAR(512),
  predefined_info VARCHAR(100),
  is_predefined BOOLEAN NOT NULL DEFAULT False,
  status BOOLEAN NOT NULL DEFAULT True,
  region jsonb,
  category jsonb,
  match_condition jsonb,
  match_condition_relation jsonb,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3),
  sensitivity INT
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS discover_policy_v2 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(512) NOT NULL,
  description VARCHAR(512),
  storage_type jsonb,
  file_exclusion jsonb,
  predefined_info VARCHAR(100),
  is_predefined BOOLEAN NOT NULL DEFAULT False,
  status BOOLEAN NOT NULL DEFAULT True,
  data_classifier_ids jsonb NOT NULL,
  match_condition jsonb,
  match_condition_relation jsonb,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3),
  risk INT,
  protection_framework jsonb,
  action jsonb,
  notification_id jsonb
);

----------------------------------------------------------------------
-- report tasks table, add notification ID, query ID
ALTER TABLE report_tasks ADD COLUMN IF NOT EXISTS notification_id UUID DEFAULT NULL;
ALTER TABLE report_tasks ADD COLUMN IF NOT EXISTS query_id UUID DEFAULT NULL;
CREATE TABLE IF NOT EXISTS notification_connectors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    notifier_type VARCHAR(64) NOT NULL,
    config JSONB NOT NULL,
    extra JSONB NOT NULL DEFAULT '{}',
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    notifier_type VARCHAR(64) NOT NULL,
    notification_type VARCHAR(64) NOT NULL,
    template_type VARCHAR(64) NOT NULL,
    template JSONB NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    connector_id UUID NULL REFERENCES notification_connectors(id),
    extra JSONB NOT NULL DEFAULT '{}',
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS notification_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_key VARCHAR(128) NOT NULL,
    ticket_link VARCHAR(1024) NOT NULL,
    ticket_id VARCHAR(128) NOT NULL,
    notifier_type VARCHAR(64) NOT NULL,
    recipient_email VARCHAR(255),
    recipient_name VARCHAR(255),
    ticket_creator VARCHAR(255),
    ticket_creator_email VARCHAR(255),
    status VARCHAR(64),
    extra JSONB,
    template_id UUID NULL REFERENCES notification_templates(id),
    updated_at timestamp DEFAULT NOW(),
    created_at timestamp default now()::timestamp(3),
    CONSTRAINT _notification_ticket_notifier_uc UNIQUE (ticket_id, notifier_type)
);
----------------------------------------------------------------------
ALTER TABLE scan_policy ADD COLUMN IF NOT EXISTS file_exclusion JSONB;
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS file_action (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_id UUID NOT NULL,
    scan_id UUID NOT NULL,
    action SMALLINT NOT NULL,
    action_type SMALLINT NOT NULL,
    action_detail jsonb NOT NULL,
    status SMALLINT NOT NULL,
    failed_reason TEXT,
    ctime timestamp default now()::timestamp(3)
);
