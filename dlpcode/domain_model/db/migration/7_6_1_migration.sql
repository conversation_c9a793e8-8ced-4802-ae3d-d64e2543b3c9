/*
 This br7.6.1 migration script is used to create/update the schema_version table
*/

--------------------------------------------------------------------------------
ALTER TABLE scan_history ADD COLUMN IF NOT EXISTS fail_reason VARCHAR(256) NOT NULL DEFAULT '';
ALTER TABLE tags_info_predefine ADD COLUMN IF NOT EXISTS  ext_attr jsonb NOT NULL DEFAULT '{}';

ALTER TABLE scan_progress
ADD COLUMN IF NOT EXISTS current_group_counter integer NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS  group_total integer NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS  scan_total_count integer NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS  scan_current_count integer NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS  scan_ignore_count integer NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS  scan_method integer NOT NULL DEFAULT 0;


----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS connector_event_collection_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scan_policy_id UUID NOT NULL,
  connector_type integer NOT NULL,
  collection_type integer NOT NULL,
  event_count integer NOT NULL,
  events jsonb NOT NULL,
  first_event_time timestamp with time zone NOT NULL,
  last_event_time timestamp with time zone NOT NULL,
  created_at timestamp default now()::timestamp(3)
);
CREATE INDEX IF NOT EXISTS connector_event_collection_history_events ON connector_event_collection_history USING GIN (events);
-----------------------------------------------------------------------

-- Firewall Incident
-- create table if not exists fw_incidents (
--   id uuid default uuid_generate_v4(),
--   ctime timestamp default now()::timestamp(3),
--   utime timestamp default now()::timestamp(3),
--   attributes jsonb not null,
--   attributes_ext jsonb not null,
--   pol_details jsonb not null,
--   data_source jsonb not null,
--   file_info jsonb not null,
--   channel_details jsonb not null,
--   primary key (id)
-- );

-- create index if not exists fw_incidents_idx_attr on fw_incidents using gin(attributes);
-- create index if not exists fw_incidents_idx_attr_ext on fw_incidents using gin(attributes_ext);
-- create index if not exists fw_incidents_idx_pol on fw_incidents using gin(pol_details);
-- create index if not exists fw_incidents_idx_ds on fw_incidents using gin(data_source);
-- create index if not exists fw_incidents_idx_fi on fw_incidents using gin(file_info);
-- create index if not exists fw_incidents_idx_cd on fw_incidents using gin(channel_details);

----------------------------------------------------------------------
create table if not exists system_ca_certs (
  id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (id)
);
--Begin create index :
create index if not exists system_ca_certs_info on system_ca_certs using gin(info);
-------------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS schema_version (
    id VARCHAR(50) PRIMARY KEY DEFAULT 'current_version',
    version VARCHAR(50) NOT NULL,
    description VARCHAR(256),
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);

----------------------------------------------------------------------
create table if not exists FdsProxyServer (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);

--------------------------------------------------------------------------------

create table if not exists fds_auto_upgrade_locker (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);
----------------------------------------------------------------------
create table if not exists FdsAutoUpdateCount (
  _id uuid default uuid_generate_v4(),
  tm timestamp default now()::timestamp(3),
  info jsonb not null,
  primary key (_id)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS log_server (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name varchar(128) NOT NULL,
    description varchar(256) NOT NULL DEFAULT '',
    status boolean NOT NULL default True,
    type varchar(32) NOT NULL default '',
    conn_status varchar(32) NOT NULL default '',
    attributes jsonb NOT NULL,
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
    );

CREATE UNIQUE INDEX  IF NOT EXISTS idx_log_server_name ON log_server(name);
----------------------------------------------------------------------

DO $$
    BEGIN
        IF EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'http2_config'
              AND column_name = 'info'
        ) THEN
            RAISE NOTICE 'info column already exists in http2_config table, check/add license_status column';
            ALTER TABLE http2_config ADD COLUMN IF NOT EXISTS license_status VARCHAR(32) NOT NULL DEFAULT '';
        ELSE
            RAISE NOTICE 'info column NOT exists in http2_config table, drop and create http2_config table';
            DROP TABLE IF EXISTS http2_config;
            CREATE TABLE IF NOT EXISTS http2_config(
               id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
               interface VARCHAR(32) NOT NULL,
               port INT default 8443 NOT NULL,
               serviceenabled boolean default True NOT NULL,
               certid uuid,
               license_status VARCHAR(32) NOT NULL default '',
               info jsonb,
               update_time timestamp default CURRENT_TIMESTAMP
            );
        END IF;
    END $$;

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS source_file (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(1024) NOT NULL,
  original_file_path VARCHAR(1024),
  file_type VARCHAR(100),
  template_type VARCHAR(100),
  data_info jsonb,
  file_fingerprint VARCHAR(256)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS edm_template (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(512),
  source_file_id UUID NOT NULL,
  data_field jsonb NOT NULL,
  attributes_ext jsonb NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS edm_rule (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(512),
  edm_template_id UUID NOT NULL,
  match_criteria jsonb NOT NULL,
  status boolean NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS idm_template (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(512),
  source_file_id UUID NOT NULL,
  attributes_ext jsonb NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS edm_hash_data (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  source_file_id UUID NOT NULL,
  record jsonb NOT NULL
);

----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS ftnt_devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(255) NOT NULL,
    description VARCHAR(256) NOT NULL default '',
    info JSONB,
    created_at timestamp default now()::timestamp(3),
    updated_at timestamp default now()::timestamp(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS api_key (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     name VARCHAR(255) UNIQUE NOT NULL,
     status BOOLEAN NOT NULL DEFAULT FALSE,
     type INT NOT NULL DEFAULT 1,
     key TEXT NOT NULL,
     attributes JSONB,
     created_at TIMESTAMP(3) DEFAULT now()::TIMESTAMP(3),
     updated_at TIMESTAMP(3) DEFAULT now()::TIMESTAMP(3)
);
----------------------------------------------------------------------
CREATE INDEX IF NOT EXISTS idx_api_key_name ON api_key(name);


-- Drop indexes from file_info_view tables
DROP INDEX IF EXISTS fi_1_1_match_info_pii;
DROP INDEX IF EXISTS fi_1_1_match_info_pci;
DROP INDEX IF EXISTS fi_1_1_match_info_phi;
DROP INDEX IF EXISTS fi_1_1_file_tag_conf;
DROP INDEX IF EXISTS fi_1_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_2_1_match_info_pii;
DROP INDEX IF EXISTS fi_2_1_match_info_pci;
DROP INDEX IF EXISTS fi_2_1_match_info_phi;
DROP INDEX IF EXISTS fi_2_1_file_tag_conf;
DROP INDEX IF EXISTS fi_2_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_3_1_match_info_pii;
DROP INDEX IF EXISTS fi_3_1_match_info_pci;
DROP INDEX IF EXISTS fi_3_1_match_info_phi;
DROP INDEX IF EXISTS fi_3_1_file_tag_conf;
DROP INDEX IF EXISTS fi_3_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_4_1_match_info_pii;
DROP INDEX IF EXISTS fi_4_1_match_info_pci;
DROP INDEX IF EXISTS fi_4_1_match_info_phi;
DROP INDEX IF EXISTS fi_4_1_file_tag_conf;
DROP INDEX IF EXISTS fi_4_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_5_1_match_info_pii;
DROP INDEX IF EXISTS fi_5_1_match_info_pci;
DROP INDEX IF EXISTS fi_5_1_match_info_phi;
DROP INDEX IF EXISTS fi_5_1_file_tag_conf;
DROP INDEX IF EXISTS fi_5_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_6_1_match_info_pii;
DROP INDEX IF EXISTS fi_6_1_match_info_pci;
DROP INDEX IF EXISTS fi_6_1_match_info_phi;
DROP INDEX IF EXISTS fi_6_1_file_tag_conf;
DROP INDEX IF EXISTS fi_6_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_7_1_match_info_pii;
DROP INDEX IF EXISTS fi_7_1_match_info_pci;
DROP INDEX IF EXISTS fi_7_1_match_info_phi;
DROP INDEX IF EXISTS fi_7_1_file_tag_conf;
DROP INDEX IF EXISTS fi_7_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_8_1_match_info_pii;
DROP INDEX IF EXISTS fi_8_1_match_info_pci;
DROP INDEX IF EXISTS fi_8_1_match_info_phi;
DROP INDEX IF EXISTS fi_8_1_file_tag_conf;
DROP INDEX IF EXISTS fi_8_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_9_1_match_info_pii;
DROP INDEX IF EXISTS fi_9_1_match_info_pci;
DROP INDEX IF EXISTS fi_9_1_match_info_phi;
DROP INDEX IF EXISTS fi_9_1_file_tag_conf;
DROP INDEX IF EXISTS fi_9_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_10_1_match_info_pii;
DROP INDEX IF EXISTS fi_10_1_match_info_pci;
DROP INDEX IF EXISTS fi_10_1_match_info_phi;
DROP INDEX IF EXISTS fi_10_1_file_tag_conf;
DROP INDEX IF EXISTS fi_10_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_11_1_match_info_pii;
DROP INDEX IF EXISTS fi_11_1_match_info_pci;
DROP INDEX IF EXISTS fi_11_1_match_info_phi;
DROP INDEX IF EXISTS fi_11_1_file_tag_conf;
DROP INDEX IF EXISTS fi_11_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_12_1_match_info_pii;
DROP INDEX IF EXISTS fi_12_1_match_info_pci;
DROP INDEX IF EXISTS fi_12_1_match_info_phi;
DROP INDEX IF EXISTS fi_12_1_file_tag_conf;
DROP INDEX IF EXISTS fi_12_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_13_1_match_info_pii;
DROP INDEX IF EXISTS fi_13_1_match_info_pci;
DROP INDEX IF EXISTS fi_13_1_match_info_phi;
DROP INDEX IF EXISTS fi_13_1_file_tag_conf;
DROP INDEX IF EXISTS fi_13_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_14_1_match_info_pii;
DROP INDEX IF EXISTS fi_14_1_match_info_pci;
DROP INDEX IF EXISTS fi_14_1_match_info_phi;
DROP INDEX IF EXISTS fi_14_1_file_tag_conf;
DROP INDEX IF EXISTS fi_14_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_15_1_match_info_pii;
DROP INDEX IF EXISTS fi_15_1_match_info_pci;
DROP INDEX IF EXISTS fi_15_1_match_info_phi;
DROP INDEX IF EXISTS fi_15_1_file_tag_conf;
DROP INDEX IF EXISTS fi_15_1_file_tag_highly_conf;

DROP INDEX IF EXISTS fi_16_1_match_info_pii;
DROP INDEX IF EXISTS fi_16_1_match_info_pci;
DROP INDEX IF EXISTS fi_16_1_match_info_phi;
DROP INDEX IF EXISTS fi_16_1_file_tag_conf;
DROP INDEX IF EXISTS fi_16_1_file_tag_highly_conf;
----------------------------------------------------------------------
-- The fileinfo table must be changed first before doing those operations related view

ALTER TABLE fileinfo_1_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_1_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_1_1_last_scan_tm ON fileinfo_1_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_1_1_reserve_json3 ON fileinfo_1_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_2_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_2_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_2_1_last_scan_tm ON fileinfo_2_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_2_1_reserve_json3 ON fileinfo_2_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_3_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_3_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_3_1_last_scan_tm ON fileinfo_3_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_3_1_reserve_json3 ON fileinfo_3_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_4_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_4_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_4_1_last_scan_tm ON fileinfo_4_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_4_1_reserve_json3 ON fileinfo_4_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_5_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_5_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_5_1_last_scan_tm ON fileinfo_5_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_5_1_reserve_json3 ON fileinfo_5_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_6_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_6_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_6_1_last_scan_tm ON fileinfo_6_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_6_1_reserve_json3 ON fileinfo_6_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_7_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_7_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_7_1_last_scan_tm ON fileinfo_7_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_7_1_reserve_json3 ON fileinfo_7_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_8_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_8_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_8_1_last_scan_tm ON fileinfo_8_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_8_1_reserve_json3 ON fileinfo_8_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_9_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_9_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_9_1_last_scan_tm ON fileinfo_9_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_9_1_reserve_json3 ON fileinfo_9_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_10_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_10_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_10_1_last_scan_tm ON fileinfo_10_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_10_1_reserve_json3 ON fileinfo_10_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_11_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_11_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_11_1_last_scan_tm ON fileinfo_11_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_11_1_reserve_json3 ON fileinfo_11_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_12_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_12_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_12_1_last_scan_tm ON fileinfo_12_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_12_1_reserve_json3 ON fileinfo_12_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_13_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_13_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_13_1_last_scan_tm ON fileinfo_13_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_13_1_reserve_json3 ON fileinfo_13_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_14_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_14_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_14_1_last_scan_tm ON fileinfo_14_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_14_1_reserve_json3 ON fileinfo_14_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_15_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_15_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_15_1_last_scan_tm ON fileinfo_15_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_15_1_reserve_json3 ON fileinfo_15_num_1 USING GIN (reserve_json3);

ALTER TABLE fileinfo_16_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
UPDATE fileinfo_16_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
CREATE INDEX IF NOT EXISTS fi_16_1_last_scan_tm ON fileinfo_16_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_16_1_reserve_json3 ON fileinfo_16_num_1 USING GIN (reserve_json3);

CREATE INDEX IF NOT EXISTS fi_1_1_filehash_scanuuid ON fileinfo_1_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_2_1_filehash_scanuuid ON fileinfo_2_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_3_1_filehash_scanuuid ON fileinfo_3_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_4_1_filehash_scanuuid ON fileinfo_4_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_5_1_filehash_scanuuid ON fileinfo_5_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_6_1_filehash_scanuuid ON fileinfo_6_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_7_1_filehash_scanuuid ON fileinfo_7_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_8_1_filehash_scanuuid ON fileinfo_8_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_9_1_filehash_scanuuid ON fileinfo_9_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_10_1_filehash_scanuuid ON fileinfo_10_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_11_1_filehash_scanuuid ON fileinfo_11_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_12_1_filehash_scanuuid ON fileinfo_12_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_13_1_filehash_scanuuid ON fileinfo_13_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_14_1_filehash_scanuuid ON fileinfo_14_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_15_1_filehash_scanuuid ON fileinfo_15_num_1(file_hash, scan_uuid);
CREATE INDEX IF NOT EXISTS fi_16_1_filehash_scanuuid ON fileinfo_16_num_1(file_hash, scan_uuid);

-- ALTER TABLE fileinfo_1000_num_1 ADD COLUMN IF NOT EXISTS last_scan_tm TIMESTAMP;
-- UPDATE fileinfo_1000_num_1 SET last_scan_tm = COALESCE(last_scan_tm, tm);
-- CREATE INDEX IF NOT EXISTS fi_1000_1_last_scan_tm ON fileinfo_1000_num_1 (last_scan_tm);
---------------------------------------------------------------------

-- Recreate view file_info_view due to a new field last_scan_tm adding to the fileinfo_xxx table
CREATE OR REPLACE VIEW file_info_view AS
SELECT * FROM fileinfo_1_num_1
UNION ALL
SELECT * FROM fileinfo_2_num_1
UNION ALL
SELECT * FROM fileinfo_3_num_1
UNION ALL
SELECT * FROM fileinfo_4_num_1
UNION ALL
SELECT * FROM fileinfo_5_num_1
UNION ALL
SELECT * FROM fileinfo_6_num_1
UNION ALL
SELECT * FROM fileinfo_7_num_1
UNION ALL
SELECT * FROM fileinfo_8_num_1
UNION ALL
SELECT * FROM fileinfo_9_num_1
UNION ALL
SELECT * FROM fileinfo_10_num_1
UNION ALL
SELECT * FROM fileinfo_11_num_1
UNION ALL
SELECT * FROM fileinfo_12_num_1
UNION ALL
SELECT * FROM fileinfo_13_num_1
UNION ALL
SELECT * FROM fileinfo_14_num_1
UNION ALL
SELECT * FROM fileinfo_15_num_1
UNION ALL
SELECT * FROM fileinfo_16_num_1;

---------------------------------------------------------------------
--New indexes on file_info_view tables
CREATE INDEX IF NOT EXISTS fi_1_1_match_info ON fileinfo_1_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_1_1_sensitive_data ON fileinfo_1_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_1_1_compliance_data ON fileinfo_1_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_2_1_match_info ON fileinfo_2_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_2_1_sensitive_data ON fileinfo_2_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_2_1_compliance_data ON fileinfo_2_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_3_1_match_info ON fileinfo_3_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_3_1_sensitive_data ON fileinfo_3_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_3_1_compliance_data ON fileinfo_3_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_4_1_match_info ON fileinfo_4_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_4_1_sensitive_data ON fileinfo_4_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_4_1_compliance_data ON fileinfo_4_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_5_1_match_info ON fileinfo_5_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_5_1_sensitive_data ON fileinfo_5_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_5_1_compliance_data ON fileinfo_5_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_6_1_match_info ON fileinfo_6_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_6_1_sensitive_data ON fileinfo_6_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_6_1_compliance_data ON fileinfo_6_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_7_1_match_info ON fileinfo_7_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_7_1_sensitive_data ON fileinfo_7_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_7_1_compliance_data ON fileinfo_7_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_8_1_match_info ON fileinfo_8_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_8_1_sensitive_data ON fileinfo_8_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_8_1_compliance_data ON fileinfo_8_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_9_1_match_info ON fileinfo_9_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_9_1_sensitive_data ON fileinfo_9_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_9_1_compliance_data ON fileinfo_9_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_10_1_match_info ON fileinfo_10_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_10_1_sensitive_data ON fileinfo_10_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_10_1_compliance_data ON fileinfo_10_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_11_1_match_info ON fileinfo_11_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_11_1_sensitive_data ON fileinfo_11_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_11_1_compliance_data ON fileinfo_11_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_12_1_match_info ON fileinfo_12_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_12_1_sensitive_data ON fileinfo_12_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_12_1_compliance_data ON fileinfo_12_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_13_1_match_info ON fileinfo_13_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_13_1_sensitive_data ON fileinfo_13_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_13_1_compliance_data ON fileinfo_13_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_14_1_match_info ON fileinfo_14_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_14_1_sensitive_data ON fileinfo_14_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_14_1_compliance_data ON fileinfo_14_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_15_1_match_info ON fileinfo_15_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_15_1_sensitive_data ON fileinfo_15_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_15_1_compliance_data ON fileinfo_15_num_1 ((reserve_json1->>'compliance_data'));

CREATE INDEX IF NOT EXISTS fi_16_1_match_info ON fileinfo_16_num_1 USING GIN (match_info);
CREATE INDEX IF NOT EXISTS fi_16_1_sensitive_data ON fileinfo_16_num_1 ((reserve_json1->>'sensitive_data'));
CREATE INDEX IF NOT EXISTS fi_16_1_compliance_data ON fileinfo_16_num_1 ((reserve_json1->>'compliance_data'));
----------------------------------------------------------------------
-- Data scanning Incident
create table if not exists ds_incidents (
  id uuid default uuid_generate_v4(),
  ctime timestamp default now()::timestamp(3),
  utime timestamp default now()::timestamp(3),
  attributes jsonb not null,
  attributes_ext jsonb not null,
  pol_details jsonb not null,
  data_source jsonb not null,
  file_info jsonb not null,
  labels jsonb not null,
  primary key (id)
);

create index if not exists ds_incidents_idx_attr on ds_incidents using gin(attributes);
create index if not exists ds_incidents_idx_attr_ext on ds_incidents using gin(attributes_ext);
create index if not exists ds_incidents_idx_pol on ds_incidents using gin(pol_details);
create index if not exists ds_incidents_idx_ds on ds_incidents using gin(data_source);
create index if not exists ds_incidents_idx_fi on ds_incidents using gin(file_info);
create index if not exists ds_incidents_idx_lab on ds_incidents using gin(labels);

create index if not exists idx_dsincidentspoldetails_btree ON ds_incidents ((pol_details->>'sid'));
create index if not exists idx_dsincidentsattrssev_btree ON ds_incidents ((attributes->>'severity'));
CREATE INDEX IF NOT EXISTS idx_dsincidentsutime ON ds_incidents (utime);
----------------------------------------------------------------------
-- Drop MVs for Dashboard v1
DROP MATERIALIZED VIEW IF EXISTS pii_total_percentage;
DROP MATERIALIZED VIEW IF EXISTS pci_total_percentage;
DROP MATERIALIZED VIEW IF EXISTS phi_total_percentage;
DROP MATERIALIZED VIEW IF EXISTS total_count_by_compliance;
DROP MATERIALIZED VIEW IF EXISTS confidential_count_and_percentage;
DROP MATERIALIZED VIEW IF EXISTS highly_confidential_count_and_percentage;
DROP MATERIALIZED VIEW IF EXISTS pii_total_count_and_percentage_by_storage_and_file_category;
DROP MATERIALIZED VIEW IF EXISTS pci_total_count_and_percentage_by_storage_and_file_category;
DROP MATERIALIZED VIEW IF EXISTS phi_total_count_and_percentage_by_storage_and_file_category;
DROP MATERIALIZED VIEW IF EXISTS total_files_by_compliance_count_and_percentage_by_storage_fc;
DROP MATERIALIZED VIEW IF EXISTS conf_total_count_and_percentage_by_storage_and_fc;
DROP MATERIALIZED VIEW IF EXISTS highly_conf_total_count_and_percentage_by_storage_and_fc;
DROP MATERIALIZED VIEW IF EXISTS file_types_distribution;
DROP MATERIALIZED VIEW IF EXISTS catalogued_file_count_by_time;
DROP MATERIALIZED VIEW IF EXISTS catalogued_files_by_storage;
DROP MATERIALIZED VIEW IF EXISTS catalogued_file_count;
DROP MATERIALIZED VIEW IF EXISTS uncatalogued_file_count;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_files_to_scan AS
WITH files_to_scan_data AS (
    SELECT jsonb_agg(fscandata) AS filestoscandata  -- Use JSONB for compatibility
    FROM (
        SELECT
            storage_type,
            COUNT(*) AS storagecount,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) AS percentage
        FROM file_info_view

        GROUP BY storage_type
    ) fscandata
),
storage_sum AS (
    SELECT SUM((fscandata->>'storagecount')::INTEGER) AS total_storage_count
    FROM files_to_scan_data, jsonb_array_elements(filestoscandata) AS fscandata
)
SELECT json_build_object(
        'total_files', COALESCE(storage_sum.total_storage_count, 0),  -- Handle NULL case
        'total_percentage',  CASE
            WHEN COALESCE(storage_sum.total_storage_count, 0) = 0 THEN 0
            ELSE 100.0
         END,
        'files_to_scan', files_to_scan_data.filestoscandata
) AS result
FROM files_to_scan_data, storage_sum;

----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_24hours AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('hour', NOW() - INTERVAL '24 Hours'),
            date_trunc('hour', NOW()),
            INTERVAL '1 hour'
        ) AS hour
    ),
    file_counts AS (
        SELECT
            date_trunc('hour', last_scan_tm) AS hour,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '24 Hours'
        GROUP BY date_trunc('hour', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.hour)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.hour = fc.hour
    ORDER BY ds.hour
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_7days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '7 days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '7 days'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_14days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '14 days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '14 days'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_sensitive_24hours AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('hour', NOW() - INTERVAL '24 hours'),
            date_trunc('hour', NOW()),
            INTERVAL '1 hour'
        ) AS hour
    ),
    file_counts AS (
        SELECT
            date_trunc('hour', last_scan_tm) AS hour,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '24 hours' AND reserve_json1->>'sensitive_data'='true'
        GROUP BY date_trunc('hour', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.hour)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.hour = fc.hour
    ORDER BY ds.hour
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_sensitive_7days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '7 Days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '7 Days' AND reserve_json1->>'sensitive_data'='true'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_scan_results_sensitive_14days AS
SELECT json_agg(row_to_json(result_set)) AS result
FROM (
    WITH date_series AS (
        SELECT generate_series(
            date_trunc('day', NOW() - INTERVAL '14 Days'),
            date_trunc('day', NOW()),
            INTERVAL '1 day'
        ) AS day
    ),
    file_counts AS (
        SELECT
            date_trunc('day', last_scan_tm) AS day,
            count(*) AS count
        FROM file_info_view
        WHERE last_scan_tm >= NOW() - INTERVAL '14 Days' AND reserve_json1->>'sensitive_data'='true'
        GROUP BY date_trunc('day', last_scan_tm)
    )
    SELECT
        floor(EXTRACT(EPOCH from ds.day)) AS time,
        COALESCE(fc.count, 0) AS count
    FROM date_series ds
    LEFT JOIN file_counts fc ON ds.day = fc.day
    ORDER BY ds.day
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_sensitive_files_labeled AS
SELECT row_to_json(result_set) AS result
FROM (
    SELECT
        COUNT(*) AS alltimecount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '1 day' THEN 1 ELSE 0 END) AS onedaycount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '7 days' THEN 1 ELSE 0 END) AS oneweekcount,
        SUM(CASE WHEN last_scan_tm > NOW() - INTERVAL '14 days' THEN 1 ELSE 0 END) AS twoweekcount
    FROM file_info_view
    WHERE reserve_json1->>'sensitive_data' = 'true'
) result_set;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_sensitive_files_distribution AS
WITH storage_data AS
    (SELECT storage_type, count(*) as storagecount, round(count(*) * 100.0 / SUM(count(*)) OVER(),1) AS percentage
     FROM file_info_view where reserve_json1->>'sensitive_data'='true'
     group by storage_type),
total_count AS (
        SELECT COUNT(*) AS totalcount FROM file_info_view
    )
SELECT json_build_object(
       'total_sensitive_count', SUM(storage_data.storagecount),
        'total_percentage', CASE
                WHEN COALESCE(SUM(storage_data.storagecount), 0) = 0 THEN 0
                ELSE 100.0
                END,
    'storage_counts', json_agg(row_to_json(storage_data))
) AS result
FROM storage_data;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_compliance_files AS
WITH all_compliance AS (
    SELECT DISTINCT
        file_info->>'id' AS file_id,
        jsonb_array_elements_text(labels->'Compliance') AS compliance
    FROM ds_incidents
    WHERE file_info->>'id' IN (
        SELECT id::text
        FROM file_info_view
        WHERE reserve_json1->>'compliance_data' = 'true'
    )
),
compliance_counts AS (
    SELECT
        compliance,
        COUNT(DISTINCT file_id) AS count
    FROM all_compliance
    GROUP BY compliance
),
compliance_category_count AS (
    SELECT COUNT(*) AS category_count FROM compliance_counts
),
total_count AS (
    SELECT COUNT(DISTINCT id) AS total_count
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
limited_compliance AS (
    SELECT compliance, count
    FROM compliance_counts
    ORDER BY count DESC
    LIMIT 5
),
limited_with_percentage AS (
    SELECT
        compliance,
        count,
        ROUND((count::numeric / NULLIF((SELECT total_count FROM total_count), 0)) * 100, 2) AS percentage
    FROM limited_compliance
),
compliance_files AS (
    SELECT * FROM limited_with_percentage
),
total_compliance AS (
    SELECT
        (SELECT total_count FROM total_count) AS count,
        CASE WHEN (SELECT total_count FROM total_count) = 0 THEN 0 ELSE 100.0 END AS percentage
),
total_scanned AS (
    SELECT
        COUNT(DISTINCT id) AS count,
        CASE
            WHEN COUNT(DISTINCT id) = 0 THEN 0
            ELSE ROUND(
                (SELECT total_count FROM total_count)::numeric
                / NULLIF(COUNT(DISTINCT id)::numeric, 0) * 100,
                2
            )
        END AS percentage
    FROM file_info_view
)
SELECT json_build_object(
    'compliance_data', json_agg(row_to_json(compliance_files)),
    'total_compliance', (SELECT row_to_json(total_compliance) FROM total_compliance),
    'total_scanned', (SELECT row_to_json(total_scanned) FROM total_scanned)
) AS result
FROM compliance_files;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_compliance_files_show_more AS
WITH all_compliance AS (
    SELECT DISTINCT
        file_info->>'id' AS file_id,
        jsonb_array_elements_text(labels->'Compliance') AS compliance
    FROM ds_incidents
    WHERE file_info->>'id' IN (
        SELECT id::text
        FROM file_info_view
        WHERE reserve_json1->>'compliance_data' = 'true'
    )
),
compliance_counts AS (
    SELECT
        compliance,
        COUNT(DISTINCT file_id) AS count
    FROM all_compliance
    GROUP BY compliance
),
compliance_category_count AS (
    SELECT COUNT(*) AS category_count FROM compliance_counts
),
total_count AS (
    SELECT COUNT(DISTINCT id) AS total_count
    FROM file_info_view
    WHERE reserve_json1->>'compliance_data' = 'true'
),
limited_compliance AS (
    SELECT compliance, count
    FROM compliance_counts
    ORDER BY count DESC
    LIMIT 15
),
limited_with_percentage AS (
    SELECT
        compliance,
        count,
        ROUND((count::numeric / NULLIF((SELECT total_count FROM total_count), 0)) * 100, 2) AS percentage
    FROM limited_compliance
),
compliance_files AS (
    SELECT * FROM limited_with_percentage
),
total_compliance AS (
    SELECT
        (SELECT total_count FROM total_count) AS count,
        CASE WHEN (SELECT total_count FROM total_count) = 0 THEN 0 ELSE 100.0 END AS percentage
),
total_scanned AS (
    SELECT
        COUNT(DISTINCT id) AS count,
        CASE
            WHEN COUNT(DISTINCT id) = 0 THEN 0
            ELSE ROUND(
                (SELECT total_count FROM total_count)::numeric
                / NULLIF(COUNT(DISTINCT id)::numeric, 0) * 100,
                2
            )
        END AS percentage
    FROM file_info_view
)
SELECT json_build_object(
    'compliance_data', json_agg(row_to_json(compliance_files)),
    'total_compliance', (SELECT row_to_json(total_compliance) FROM total_compliance),
    'total_scanned', (SELECT row_to_json(total_scanned) FROM total_scanned)
) AS result
FROM compliance_files;
----------------------------------------------------------------------
CREATE MATERIALIZED VIEW IF NOT EXISTS db_ai_categories AS
WITH total_count AS (
    SELECT COUNT(*) AS totalcount FROM file_info_view
),
ai_info AS (
    SELECT
        sub_class_id,
        COUNT(*) AS categorycount,
        ROUND(COUNT(*) * 100.0 / NULLIF((SELECT totalcount FROM total_count), 0), 2) AS percentage
    FROM file_info_view
    WHERE sub_class_id IS NOT NULL
    GROUP BY sub_class_id
    ORDER BY percentage DESC
    LIMIT 10
)
SELECT json_build_object(
    'total_scanned_files', (SELECT totalcount FROM total_count),
    'total_ai_count', SUM(ai_info.categorycount),
    'total_percentage', ROUND(SUM(ai_info.categorycount) * 100.0 / NULLIF((SELECT totalcount FROM total_count), 0), 2),
    'ai_counts', json_agg(row_to_json(ai_info))
) AS result
FROM ai_info;
----------------------------------------------------------------------
-- Functions to create the Analysis Dashboard MVs dynamically
-- Top n scans widget
DROP FUNCTION IF EXISTS create_adb_top_sp_mv(text, text, text, text);
CREATE OR REPLACE FUNCTION create_adb_top_sp_mv(
    time_filter TEXT,    -- Example: '24 hours, 7 days, 14 days'
    time_trunc TEXT,     -- Example: 'hour', 'day'
    limit_param TEXT,     -- Number of top scan policies (Example: 5, 10)
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'AND pol_details->>''sid'' = ''' || scan_uuid || '''';
    ELSE
        in_clause := ''; -- No filter
    END IF;

    -- Dynamically generate the materialized view name
    mv_name := format('adb_top_sp_%s_%s_%s', lower(limit_param), lower(replace(time_filter, ' ', '_')), lower(scan_uuid));

    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
        WITH now_trunc AS (
            SELECT date_trunc(%L, NOW()) AS time_unit
        ),
        time_series AS (
            SELECT generate_series(
                (SELECT time_unit FROM now_trunc) - INTERVAL %L,
                (SELECT time_unit FROM now_trunc),
                INTERVAL ''1 %s''
            ) AS time_point
        ),
        all_scans AS (
        SELECT
            date_trunc(%L, utime) AS time_point,
            ds_incidents.pol_details->>''pname'' AS discover_policy,
            COUNT(*) AS count
        FROM ds_incidents
        WHERE utime > NOW() - INTERVAL %L %s
        GROUP BY 1, 2
        ),
        total_scans AS (
            SELECT SUM(count) AS total_count FROM all_scans
        ),
        top_scans AS (
            SELECT ds_incidents.pol_details->>''pname'' as discover_policy, COUNT(*) AS count
            FROM ds_incidents
            WHERE utime > NOW() - INTERVAL %L %s
            GROUP BY discover_policy
            ORDER BY COUNT(*) DESC
            LIMIT %s
        ),
        top_scans_total AS (
            SELECT SUM(count) AS total_count,
            ROUND(SUM(count) * 100.0 / NULLIF((SELECT total_count FROM total_scans), 0), 2) AS total_percentage
            FROM top_scans
        ),
        discover_policy_count AS (
            SELECT COUNT(DISTINCT discover_policy) AS policy_count FROM all_scans
        ),
        others_scans_aggregated AS (
            SELECT
                time_point,
                ''Others'' AS discover_policy,
                SUM(count) AS count
            FROM all_scans
            WHERE discover_policy NOT IN (SELECT discover_policy FROM top_scans)
            GROUP BY time_point
        ),
        summary_result AS (
        SELECT json_agg(row_to_json(summary_data)) AS summary
            FROM (
        -- Top Scans
        SELECT
            discover_policy,
            count,
            ROUND(count * 100.0 / NULLIF((SELECT total_count FROM total_scans), 0), 2) AS percentage
        FROM top_scans
        UNION ALL
        -- Other Scans
        SELECT
            ''Others'' AS discover_policy,
            COALESCE(SUM(count), 0) AS count,
            ROUND(COALESCE(SUM(count), 0) * 100.0 / NULLIF((SELECT total_count FROM total_scans), 0), 2) AS percentage
        FROM all_scans
        WHERE discover_policy NOT IN (SELECT discover_policy FROM top_scans)
        ) summary_data
        ),
        time_trend AS (
            SELECT json_agg(row_to_json(time_data)) AS time_trend
            FROM (
                SELECT
                EXTRACT(EPOCH FROM pd.time_point)::bigint AS time_unit,
                pd.discover_policy,
                COALESCE(alls.count, 0) AS count,
                CASE
                    WHEN t.total_count > 0 THEN ROUND(COALESCE(alls.count, 0) * 100.0 / t.total_count, 2)
                    ELSE 0
                END AS percentage
                FROM (
                    SELECT time_point, discover_policy
                    FROM time_series
                    CROSS JOIN (
                        SELECT discover_policy FROM top_scans
                        UNION
                        SELECT ''Others''
                    ) ts
                ) pd
                LEFT JOIN (
                SELECT * FROM all_scans WHERE discover_policy IN (SELECT discover_policy FROM top_scans)
                UNION ALL
                SELECT * FROM others_scans_aggregated
                ) alls
                ON pd.time_point = alls.time_point AND pd.discover_policy = alls.discover_policy
                JOIN total_scans t ON true
                ORDER BY pd.time_point, pd.discover_policy
            ) time_data
        )
        SELECT json_build_object(
            ''summary'', summary_result.summary,
            ''time_trend'', time_trend.time_trend,
            ''discovery_policy_count'', (SELECT policy_count FROM discover_policy_count),
            ''topn_count'', (
                SELECT json_build_object(
                ''count'', tst.total_count,
                ''percentage'', tst.total_percentage
            )
            FROM top_scans_total tst
            ),
            ''total_count'', (
            SELECT json_build_object(
                ''count'', total_scans.total_count,
                ''percentage'', 100.0
            )
            FROM total_scans
            )
        ) AS result
        FROM summary_result, time_trend;',
        mv_name, time_trunc, time_filter, time_trunc, -- Time truncation
        time_trunc, time_filter, in_clause,  -- Filters
        time_filter, in_clause, limit_param  -- Top N limit
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Sensitive files by file types widget
DROP FUNCTION IF EXISTS create_adb_sf_mv(text);
CREATE OR REPLACE FUNCTION create_adb_sf_mv(
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'scan_uuid in (''' || scan_uuid || ''')';
    ELSE
        in_clause := '1=1'; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_sf_%s', lower(scan_uuid));


    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS

        WITH total_count AS (
            SELECT COUNT(*) AS totalcount FROM file_info_view WHERE %s
        ),
        sensitive_files AS (
            SELECT file_attributes->>''file_ext'' AS filetype, COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>''sensitive_data''=''true'' AND %s
            GROUP BY filetype
	    ORDER by count DESC
            LIMIT 5
        ),
        total_sensitive_files AS (
            SELECT SUM(count) AS total_sensitive FROM sensitive_files
        )
        SELECT json_build_object(
            ''file_type_summary'', (SELECT json_agg(row_to_json(result_set)) FROM (
                SELECT COALESCE(sf.filetype, ''Unknown'') AS filetype, sf.count,
                ROUND(sf.count * 100.0 / tc.totalcount, 2) AS percentage
                FROM total_count tc, sensitive_files sf WHERE tc.totalcount != 0
            ) result_set),
            ''total_sensitive_files'', (SELECT total_sensitive FROM total_sensitive_files),
            ''total_sensitive_percentage'', (SELECT ROUND(total_sensitive * 100.0 / total_count.totalcount, 2)
            FROM total_sensitive_files, total_count),
            ''total_scanned_files'', (SELECT totalcount FROM total_count)
        ) AS result;',
        mv_name, in_clause, in_clause
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Sensitive files by file types show more widget
DROP FUNCTION IF EXISTS create_adb_sfsm_mv(text);
CREATE OR REPLACE FUNCTION create_adb_sfsm_mv(
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'scan_uuid in (''' || scan_uuid || ''')';
    ELSE
        in_clause := '1=1'; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_sfsm_%s', lower(scan_uuid));


    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS

        WITH total_count AS (
            SELECT count(*) AS totalcount
            FROM file_info_view
            WHERE %s
        ),

        -- Count sensitive files by category
        sensitive_files AS (
            SELECT
                file_attributes->>''file_cat'' AS filetype,
                COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>''sensitive_data''=''true''
                AND %s
            GROUP BY filetype
        ),

        -- Count sensitive files by category and extension
        sensitive_files_ext AS (
            SELECT
                file_attributes->>''file_cat'' AS filetype,
                file_attributes->>''file_ext'' AS fileext,
                COUNT(*) AS count
            FROM file_info_view
            WHERE reserve_json1->>''sensitive_data''=''true''
                AND %s
            GROUP BY filetype, fileext
        ),

        -- Total count of sensitive files
        total_sensitive_files AS (
            SELECT SUM(count) AS total_sensitive
            FROM sensitive_files
        ),

        -- Percentage summary for file types
        file_type_summary AS (
            SELECT
                COALESCE(sf.filetype, ''Unknown'') AS filetype,
                sf.count,
                ROUND(sf.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM total_count tc
            CROSS JOIN sensitive_files sf
            WHERE tc.totalcount != 0
        ),

        -- Percentage summary for file types with extensions
        file_type_ext_details AS (
            SELECT
                COALESCE(sfe.filetype, ''Unknown'') AS filetype,
                COALESCE(sfe.fileext, ''Unknown'') AS fileext,
                sfe.count,
                ROUND(sfe.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM total_count tc
            CROSS JOIN sensitive_files_ext sfe
            WHERE tc.totalcount != 0
        )

        -- Final JSON Output
        SELECT json_build_object(
            ''file_type_summary'', (SELECT json_agg(row_to_json(file_type_summary)) FROM file_type_summary),
            ''file_type_ext_details'', (SELECT json_agg(row_to_json(file_type_ext_details)) FROM file_type_ext_details),
            ''total_scanned_files'', (SELECT totalcount FROM total_count),
            ''total_sensitive_files'', (SELECT total_sensitive FROM total_sensitive_files),
            ''total_sensitive_percentage'', (
                SELECT ROUND(total_sensitive * 100.0 / NULLIF(total_count.totalcount, 0), 2)
                FROM total_sensitive_files, total_count
            )
        ) AS result;',
        mv_name, in_clause, in_clause, in_clause
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Severity widget
DROP FUNCTION IF EXISTS create_adb_sev_mv(text, text, text, text);
CREATE OR REPLACE FUNCTION create_adb_sev_mv(
    severity TEXT,            -- Example: '0', '1', '2', '3'
    time_filter TEXT,         -- Example: '14 days'
    time_trunc TEXT,          -- Example: 'day', 'hour', 'week'
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'AND pol_details->>''sid'' = ''' || scan_uuid || '''';
    ELSE
        in_clause := ''; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_sev_%s_%s_%s', lower(severity), lower(replace(time_filter, ' ', '_')), lower(scan_uuid));


    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
        WITH time_series AS (
            SELECT generate_series(
                date_trunc(%L, NOW() - INTERVAL %L),
                date_trunc(%L, NOW()),
                (CASE
                    WHEN %L = ''hour'' THEN INTERVAL ''1 hour''
                    WHEN %L = ''day'' THEN INTERVAL ''1 day''
                    WHEN %L = ''week'' THEN INTERVAL ''1 week''
                    ELSE INTERVAL ''1 day''
                END)
            ) AS time_unit
        ),

        -- Create base data with 0 counts for missing intervals
        base_data AS (
            SELECT
                floor(extract(epoch from ts.time_unit)) AS time_unit,
                %L AS severity,
                0 AS count
            FROM time_series ts
        ),

        -- Actual counts from ds_incidents
        actual_data AS (
            SELECT
                floor(extract(epoch from date_trunc(%L, utime))) AS time_unit,
                ds_incidents.attributes->>''severity'' AS severity,
                count(*) AS count
            FROM ds_incidents
            WHERE ds_incidents.attributes->>''severity'' = %L
                AND utime >= NOW() - INTERVAL %L
                %s
            GROUP BY time_unit, severity
        )

        -- Final JSON output
        SELECT json_agg(row_to_json(result_set)) AS result
        FROM (
            SELECT bd.time_unit, bd.severity, COALESCE(ad.count, bd.count) AS count
            FROM base_data bd
            LEFT JOIN actual_data ad
                ON bd.time_unit = ad.time_unit
                AND bd.severity = ad.severity
            ORDER BY bd.time_unit, bd.severity
        ) result_set;',
        mv_name, time_trunc, time_filter, time_trunc, -- time-series parameters
        time_trunc, time_trunc, time_trunc,  -- interval selection
        severity, time_trunc, severity, time_filter, in_clause -- severity and conditions
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Compliance files widget
DROP FUNCTION IF EXISTS create_adb_comp_mv(text, text, text);
CREATE OR REPLACE FUNCTION create_adb_comp_mv(
    time_filter TEXT,         -- Example: '24 hours, 7 days, 14 days'
    time_trunc TEXT,          -- Example: 'day, hour'
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'AND pol_details->>''sid'' = ''' || scan_uuid || '''';
    ELSE
        in_clause := ''; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_comp_%s_%s', lower(replace(time_filter, ' ', '_')), lower(scan_uuid));
    EXECUTE format('DROP MATERIALIZED VIEW IF EXISTS %I', mv_name);
    EXECUTE format(

        'CREATE MATERIALIZED VIEW %I AS
WITH compliance_counts AS (
    SELECT
        compliance,
        COUNT(*) AS count
    FROM (
       SELECT jsonb_array_elements_text(labels->''Compliance'') AS compliance
        FROM ds_incidents
        WHERE utime > (NOW() - INTERVAL %L) %s
    ) sub
    GROUP BY compliance
    ORDER BY count DESC
    LIMIT 5
),
time_series AS (
    SELECT generate_series(
        date_trunc(%L, NOW() - INTERVAL %L),
        date_trunc(%L, NOW()),
        INTERVAL ''1 %s''
    ) AS time_unit
),
compliance_time_series AS (
    SELECT
        floor(EXTRACT(EPOCH from ts.time_unit)) as time_unit,
        cc.compliance as compliance,
        COUNT(di.utime) AS count
    FROM
        time_series ts
        CROSS JOIN compliance_counts cc
        LEFT JOIN ds_incidents di ON
            date_trunc(%L, di.utime) = ts.time_unit AND
            cc.compliance = ANY (
                SELECT jsonb_array_elements_text(di.labels->''Compliance'')
            )
    GROUP BY ts.time_unit, cc.compliance
    ORDER BY ts.time_unit, cc.compliance
)
SELECT json_agg(json_build_object(''time_unit'', time_unit, ''compliance'', compliance, ''count'', count)) AS result
FROM compliance_time_series;',
        mv_name, time_filter, in_clause, time_trunc, time_filter, time_trunc, time_trunc, -- Time-series
        time_trunc -- Filtering conditions
);
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- AI file categories widget
DROP FUNCTION IF EXISTS create_adb_ai_mv(text);
CREATE OR REPLACE FUNCTION create_adb_ai_mv(
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'scan_uuid in (''' || scan_uuid || ''')';
    ELSE
        in_clause := '1=1'; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_ai_%s', lower(scan_uuid));


    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
        WITH time_filtered_data AS (
            SELECT main_class_id, sub_class_id, scan_uuid
            FROM file_info_view
            WHERE %s
        ),
        total_count AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count AS (
            SELECT
                main_class_id,
                COUNT(*) AS count
            FROM time_filtered_data
            WHERE main_class_id IS NOT NULL
            GROUP BY main_class_id
        ),
        total_classified_files AS (
            SELECT SUM(count) AS total_classified
            FROM class_count
        )
        SELECT json_build_object(
            ''class_summary'', (
                SELECT json_agg(row_to_json(result_set))
                FROM (
                    SELECT
                        cc.main_class_id,
                        cc.count,
                        ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
                    FROM class_count cc
                    CROSS JOIN total_count tc
                    WHERE tc.totalcount != 0
                ) result_set
            ),
            ''total_scanned_files'', (SELECT totalcount FROM total_count),
            ''total_classified_files'', (SELECT total_classified FROM total_classified_files),
            ''total_classified_percentage'', (
                SELECT ROUND(total_classified * 100.0 / NULLIF(total_count.totalcount, 0), 2)
                FROM total_classified_files, total_count
            )
        ) AS result;',
        mv_name, in_clause
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- AI file categories show more widget
DROP FUNCTION IF EXISTS create_adb_aism_mv(text);
CREATE OR REPLACE FUNCTION create_adb_aism_mv(
    scan_uuid TEXT DEFAULT 'All' -- All represents all scans
)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
    in_clause TEXT;
BEGIN
    -- Conditionally set in_clause
    IF scan_uuid IS NOT NULL AND scan_uuid <> 'All' THEN
        in_clause := 'scan_uuid in (''' || scan_uuid || ''')';
    ELSE
        in_clause := '1=1'; -- No filter
    END IF;
    -- Dynamically generate the materialized view name
    mv_name := format('adb_aism_%s', lower(scan_uuid));


    EXECUTE format(

        'CREATE MATERIALIZED VIEW IF NOT EXISTS %I AS
        WITH time_filtered_data AS (
            SELECT main_class_id, sub_class_id, scan_uuid
            FROM file_info_view
            WHERE %s
        ),
        total_count_main AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count_main AS (
            SELECT main_class_id, COUNT(*) AS count
            FROM time_filtered_data
            WHERE main_class_id IS NOT NULL
            AND %s
            GROUP BY main_class_id
        ),
        main_class_summary AS (
            SELECT
                cc.main_class_id,
                cc.count,
                ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM class_count_main cc
            CROSS JOIN total_count_main tc
            WHERE tc.totalcount != 0
        ),
        total_count_sub AS (
            SELECT COUNT(*) AS totalcount
            FROM time_filtered_data
        ),
        class_count_sub AS (
            SELECT sub_class_id, COUNT(*) AS count
            FROM time_filtered_data
            WHERE sub_class_id IS NOT NULL
            AND %s
            GROUP BY sub_class_id
        ),
        sub_class_summary AS (
            SELECT
                cc.sub_class_id,
                cc.count,
                ROUND(cc.count * 100.0 / NULLIF(tc.totalcount, 0), 2) AS percentage
            FROM class_count_sub cc
            CROSS JOIN total_count_sub tc
            WHERE tc.totalcount != 0
        ),
        total_classified_files AS (
            SELECT SUM(count) AS total_classified
            FROM class_count_main
        )

        SELECT json_build_object(
            ''main_class_summary'', (SELECT json_agg(row_to_json(main_class_summary)) FROM main_class_summary),
            ''sub_class_summary'', (SELECT json_agg(row_to_json(sub_class_summary)) FROM sub_class_summary),
            ''total_scanned_files'', (SELECT totalcount FROM total_count_main),
            ''total_classified_files'', (SELECT total_classified FROM total_classified_files),
            ''total_classified_percentage'', (
                SELECT ROUND(total_classified * 100.0 / NULLIF(total_count_main.totalcount, 0), 2)
                FROM total_classified_files, total_count_main
            )
        ) AS result;',
        mv_name, in_clause, in_clause, in_clause
    );
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
-- Drop MVs beginning with a given prefix function
DROP FUNCTION IF EXISTS drop_materialized_views_by_prefix(text);
CREATE OR REPLACE FUNCTION drop_materialized_views_by_prefix(mv_prefix TEXT)
RETURNS VOID AS $$
DECLARE
    mv_name TEXT;
BEGIN
    -- Loop through materialized views with names starting with the provided prefix
    FOR mv_name IN
        SELECT matviewname
        FROM pg_catalog.pg_matviews
        WHERE matviewname LIKE mv_prefix || '\_%' ESCAPE '\'
    LOOP
        BEGIN
            EXECUTE format('DROP MATERIALIZED VIEW IF EXISTS %I', mv_name);
            RAISE NOTICE 'DROPPED: %', mv_name;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'DROP MV failed for %', mv_name;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS fileinfo_1000_num_1
 (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  record_id SERIAL UNIQUE,
  file_hash VARCHAR(64) NOT NULL,
  file_tlsh tlsh,
  scan_uuid UUID NOT NULL,
  full_path VARCHAR(4096) NOT NULL,
  file_name VARCHAR(256) NOT NULL,
  file_attributes jsonb NOT NULL,
  storage_type smallint NOT NULL,
  main_class_id VARCHAR(16),
  sub_class_id VARCHAR(16),
  class_details jsonb,
  match_info jsonb,
  match_info_custom jsonb,
  file_tag jsonb,
  reserve_int1 INT,
  reserve_int2 INT,
  reserve_str1 VARCHAR(64),
  reserve_str2 VARCHAR(64),
  reserve_json1 jsonb,
  reserve_json2 jsonb,
  reserve_json3 jsonb,
  reserve_json4 jsonb,
  tm timestamp default CURRENT_TIMESTAMP,
  tag_tm timestamp,
  last_scan_tm timestamp
);


CREATE INDEX IF NOT EXISTS fi_1000_1_file_hash ON fileinfo_1000_num_1 (file_hash);
CREATE INDEX IF NOT EXISTS fi_1000_1_file_tlsh ON fileinfo_1000_num_1 USING GIST (file_tlsh gist_tlsh_ops);
CREATE INDEX IF NOT EXISTS fi_1000_1_scan_uuid ON fileinfo_1000_num_1 (scan_uuid);
CREATE INDEX IF NOT EXISTS fi_1000_1_full_path ON fileinfo_1000_num_1 (full_path);
CREATE INDEX IF NOT EXISTS fi_1000_1_file_name ON fileinfo_1000_num_1 (file_name);
CREATE INDEX IF NOT EXISTS fi_1000_1_file_attributes ON fileinfo_1000_num_1 USING GIN (file_attributes);
CREATE INDEX IF NOT EXISTS fi_1000_1_storage_type ON fileinfo_1000_num_1 (storage_type);
CREATE INDEX IF NOT EXISTS fi_1000_1_main_class_id ON fileinfo_1000_num_1 (main_class_id);
CREATE INDEX IF NOT EXISTS fi_1000_1_sub_class_id ON fileinfo_1000_num_1 (sub_class_id);
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_notempty ON fileinfo_1000_num_1 USING GIN (match_info) where match_info <> '[]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_empty ON fileinfo_1000_num_1 USING GIN (match_info jsonb_path_ops) where match_info = '[]' or match_info='{}'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_pii ON fileinfo_1000_num_1 USING GIN (match_info jsonb_path_ops) where match_info @> '[{"categoryid" : "10001"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_pci ON fileinfo_1000_num_1 USING GIN (match_info jsonb_path_ops) where match_info @> '[{"categoryid" : "10002"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_match_info_phi ON fileinfo_1000_num_1 USING GIN (match_info jsonb_path_ops) where match_info @> '[{"categoryid" : "10003"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_file_tag_conf ON fileinfo_1000_num_1 USING GIN (file_tag jsonb_path_ops) where  file_tag @> '[{"predefine": "Confidential"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_file_tag_highly_conf ON fileinfo_1000_num_1 USING GIN (file_tag jsonb_path_ops) where  file_tag @> '[{"predefine": "Highly Confidential (or Restricted)"}]'::jsonb;
CREATE INDEX IF NOT EXISTS fi_1000_1_tm ON fileinfo_1000_num_1 (tm);
CREATE INDEX IF NOT EXISTS fi_1000_1_record_id ON fileinfo_1000_num_1 (record_id);
CREATE INDEX IF NOT EXISTS fi_1000_1_last_scan_tm ON fileinfo_1000_num_1 (last_scan_tm);
CREATE INDEX IF NOT EXISTS fi_1000_1_reserve_json3 ON fileinfo_1000_num_1 USING GIN (reserve_json3);

CREATE TABLE IF NOT EXISTS folder_info_1000
(
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        scan_uuid UUID NOT NULL,
        folder_path TEXT NOT NULL UNIQUE,
        folder_tag jsonb NOT NULL,
        tm timestamp default CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS folder_info_1000_path_index ON folder_info_1000 USING GIN (folder_path gin_trgm_ops);
CREATE INDEX IF NOT EXISTS folder_info_1000_tag_index ON folder_info_1000 USING GIN (folder_tag);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS report_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    version_id INTEGER NOT NULL DEFAULT 0,
    status INT DEFAULT 0,
    format JSONB NOT NULL,
    report_type JSONB NOT NULL,
    report_details JSONB NOT NULL,
    attributes JSONB,
    period VARCHAR(255),
    schedule_info JSONB,
    notes TEXT,
    reportgen_count INT DEFAULT 0,
    created_by VARCHAR(255) NOT NULL,
    status_update_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone,
    updated_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS report_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    format INT NOT NULL,
    report_count INT,
    path VARCHAR(255) NOT NULL,
    file_size INT,
    result INT NOT NULL,
    attributes JSONB,
    expiration_time TIMESTAMP(3) with time zone,
    created_at TIMESTAMP(3) with time zone DEFAULT now()::TIMESTAMP(3) with time zone
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS device_access_stats (
   id                       UUID           PRIMARY KEY DEFAULT uuid_generate_v4(),
   ip                       INET           NOT NULL,
   device_identifier        TEXT           NULL,
   device_type              VARCHAR(32)     NOT NULL,

   req_query_tag_total_count         BIGINT         NOT NULL DEFAULT 0,
   req_query_tag_total_count_24h     BIGINT         NOT NULL DEFAULT 0,

   files_in_req_total_count          BIGINT         NOT NULL DEFAULT 0,
   files_in_req_total_count_24h      BIGINT         NOT NULL DEFAULT 0,

   file_with_tags_total_count        BIGINT         NOT NULL DEFAULT 0,
   file_with_tags_total_count_24h    BIGINT         NOT NULL DEFAULT 0,

   req_query_config_total_count      BIGINT         NOT NULL DEFAULT 0,
   req_query_config_total_count_24h  BIGINT         NOT NULL DEFAULT 0,

   req_upload_total_count            BIGINT         NOT NULL DEFAULT 0,
   req_upload_total_count_24h        BIGINT         NOT NULL DEFAULT 0,

   req_ztna_sync_total_count         BIGINT         NOT NULL DEFAULT 0,
   req_ztna_sync_total_count_24h     BIGINT         NOT NULL DEFAULT 0,


   last_file_tag_query_time         timestamp(3) without time zone,
   last_upload_time                 timestamp(3) without time zone,
   last_config_query_time           timestamp(3) without time zone,
   last_ztna_sync_time              timestamp(3) without time zone,

   created_at               TIMESTAMP(3)   NOT NULL DEFAULT now()::TIMESTAMP(3),
   updated_at               TIMESTAMP(3)   NOT NULL DEFAULT now()::TIMESTAMP(3),

   CONSTRAINT uq_device UNIQUE (device_identifier, device_type)
);

CREATE INDEX IF NOT EXISTS idx_device_stats_identifier ON device_access_stats (device_identifier);
CREATE INDEX IF NOT EXISTS idx_device_stats_type ON device_access_stats (device_type);
CREATE INDEX IF NOT EXISTS idx_device_stats_ip ON device_access_stats (ip);
CREATE INDEX IF NOT EXISTS idx_device_stats_identifier_ip ON device_access_stats (device_identifier, ip);
CREATE INDEX IF NOT EXISTS idx_device_stats_type_ip ON device_access_stats (device_type, ip);
CREATE INDEX IF NOT EXISTS idx_device_stats_identifier_type_ip ON device_access_stats (device_identifier, device_type, ip);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS reports_retention_policy (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    retention_all INT,
    once INT,
    daily INT,
    weekly INT,
    monthly INT,
    created_at TIMESTAMP(3) DEFAULT now()::TIMESTAMP(3)
);
----------------------------------------------------------------------
CREATE TABLE IF NOT EXISTS protection_profile (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description VARCHAR(256),
  profile_type smallint NOT NULL,
  profile jsonb NOT NULL,
  created_at timestamp default now()::timestamp(3),
  updated_at timestamp default now()::timestamp(3)
);
CREATE INDEX IF NOT EXISTS protection_profile_profile_type ON protection_profile (profile_type);
CREATE INDEX IF NOT EXISTS protection_profile_profile ON protection_profile USING GIN (profile);
----------------------------------------------------------------------
ALTER TABLE scan_policy ADD COLUMN IF NOT EXISTS protection_profiles jsonb NOT NULL DEFAULT '{}';
----------------------------------------------------------------------
CREATE OR REPLACE FUNCTION refresh_report_expirations()
RETURNS TRIGGER AS $$
DECLARE
    retention RECORD;
    type_mapping CONSTANT JSONB := '{"1": "once", "2": "daily", "3": "weekly", "4": "monthly"}'::JSONB;
    type_key TEXT;
    retention_days INT;
BEGIN
    RAISE NOTICE 'Trigger function started.';

    -- Fetch the latest retention policy

    SELECT * INTO retention
    FROM reports_retention_policy
    ORDER BY created_at DESC
    LIMIT 1;

    RAISE NOTICE 'Fetched retention policy: %', retention;

    -- Update expiration_time for each report_file based on its type
    UPDATE report_files
    SET expiration_time = created_at + (
        CASE
            WHEN retention.retention_all IS NOT NULL THEN retention.retention_all
            WHEN (attributes ->> 'type')::INT = 1 THEN COALESCE(retention.once, 30)
            WHEN (attributes ->> 'type')::INT = 2 THEN COALESCE(retention.daily, 30)
            WHEN (attributes ->> 'type')::INT = 3 THEN COALESCE(retention.weekly, 30)
            WHEN (attributes ->> 'type')::INT = 4 THEN COALESCE(retention.monthly, 30)
            ELSE 30
        END
    ) * INTERVAL '1 day'
    ;
    RAISE NOTICE 'Updated report_files with new expiration_time.';
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;
----------------------------------------------------------------------
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'trigger_refresh_expirations'
    ) THEN
        CREATE TRIGGER trigger_refresh_expirations
        AFTER INSERT OR UPDATE ON reports_retention_policy
        FOR EACH ROW
        EXECUTE FUNCTION refresh_report_expirations();
    END IF;
END
$$;
----------------------------------------------------------------------
DROP TABLE if exists icap_devices;
DROP TABLE if exists icap_config;
----------------------------------------------------------------------
